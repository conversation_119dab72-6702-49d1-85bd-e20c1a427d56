'use strict'

/**
 * User.js service
 *
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 */

const crypto = require('crypto')
const bcrypt = require('bcryptjs')

const { sanitizeEntity, getAbsoluteServerUrl } = require('accel-utils')
const { ObjectId } = require('mongodb')
const axios = require('axios')
const pluginConfig = strapi.config.get('plugins.usersPermissions') || { branchMode: 'normal' }

module.exports = {
  /**
   * Promise to count users
   *
   * @return {Promise}
   */

  count (params) {
    return strapi.query('user', 'users-permissions').count(params)
  },

  /**
   * Promise to search count users
   *
   * @return {Promise}
   */

  countSearch (params) {
    return strapi.query('user', 'users-permissions').countSearch(params)
  },

  /**
   * Promise to add a/an user.
   * @return {Promise}
   */
  async add (values) {
    if (values.password) {
      values.password = await strapi.plugins['users-permissions'].services.user.hashPassword(
        values
      )
    }

    return strapi.query('user', 'users-permissions').create(values)
  },

  /**
   * Promise to edit a/an user.
   * @return {Promise}
   */
  async edit (params, values) {
    if (values.password) {
      values.password = await strapi.plugins['users-permissions'].services.user.hashPassword(
        values
      )
    }

    return strapi.query('user', 'users-permissions').update(params, values)
  },

  /**
   * Promise to fetch a/an user.
   * @return {Promise}
   */
  fetch (params, populate) {
    return strapi.query('user', 'users-permissions').findOne(params, populate)
  },

  /**
   * Promise to fetch authenticated user.
   * @return {Promise}
   */
  fetchAuthenticatedUser (id) {
    return strapi.query('user', 'users-permissions').findOne({ id }, [
      'role', 'roles', 'pBranch', 'pBranches'
    ])
  },

  /**
   * Promise to fetch all users.
   * @return {Promise}
   */
  fetchAll (params, populate) {
    return strapi.query('user', 'users-permissions').find(params, populate)
  },

  hashPassword (user = {}) {
    return new Promise((resolve, reject) => {
      if (!user.password || this.isHashed(user.password)) {
        resolve(null)
      } else {
        bcrypt.hash(`${user.password}`, 10, (err, hash) => {
          if (err) {
            return reject(err)
          }
          resolve(hash)
        })
      }
    })
  },

  isHashed (password) {
    if (typeof password !== 'string' || !password) {
      return false
    }

    return password.split('$').length === 4
  },

  /**
   * Promise to remove a/an user.
   * @return {Promise}
   */
  async remove (params) {
    return strapi.query('user', 'users-permissions').delete(params)
  },

  async removeAll (params) {
    return strapi.query('user', 'users-permissions').delete(params)
  },

  validatePassword (password, hash) {
    return bcrypt.compare(password, hash)
  },

  async sendConfirmationEmail (user) {
    const userPermissionService = strapi.plugins['users-permissions'].services.userspermissions
    const pluginStore = await strapi.store({
      type: 'plugin',
      name: 'users-permissions',
    })

    const settings = await pluginStore
      .get({ key: 'email' })
      .then(storeEmail => storeEmail['email_confirmation'].options)

    const userInfo = sanitizeEntity(user, {
      model: strapi.query('user', 'users-permissions').model,
    })

    const confirmationToken = crypto.randomBytes(20).toString('hex')

    await this.edit({ id: user.id }, { confirmationToken })

    settings.message = await userPermissionService.template(settings.message, {
      URL: `${getAbsoluteServerUrl(strapi.config)}/auth/email-confirmation`,
      USER: userInfo,
      CODE: confirmationToken,
    })

    settings.object = await userPermissionService.template(settings.object, { USER: userInfo })

    // Send an email to the user.
    await strapi.plugins['email'].services.email.send({
      to: user.email,
      from:
        settings.from.email && settings.from.name
          ? `${settings.from.name} <${settings.from.email}>`
          : undefined,
      replyTo: settings.response_email,
      subject: settings.object,
      text: settings.message,
      html: settings.message,
    })
  },

  // 用户创建信息预处理
  async beforeCreateNewUser (ctx, userData, thirdPartyData) {

    // For Extension ...

    return {
      ctx,
      userData,
      thirdPartyData
    }
  },

  // 创建新用户 - 账号注册、微信小程序注册、微信公众号注册
  async createNewUser (ctx, userData, thirdPartyData) {
    const newArgs = await this.beforeCreateNewUser(ctx, userData, thirdPartyData)
    userData = newArgs.userData
    thirdPartyData = newArgs.thirdPartyData
    const iPlugin = strapi.plugins['users-permissions']
    // 未配置角色使用默认角色
    const defaultRole = await iPlugin.services['user'].getDefaultRole()
    const roleData = {
      role: defaultRole,
      roles: [defaultRole],
    }
    if (userData.role) {
      roleData.role = userData.role
      roleData.roles = userData.roles || [userData.role]
    }

    // 新账号租户生成顺序
    // 1. 用户数据中指定的租户
    // 2. 子域名租户 - 仅在租户模式为子域名时有效
    // 3. 创建默认新租户
    let branchId = userData.pBranch
    if (!branchId) {
      // 使用子域名租户
      if (pluginConfig && pluginConfig.branchMode === 'domain') {
        const iPlugin = strapi.plugins['users-permissions']
        const branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
        branchId = branch.id
      }
      // 创建默认新租户
      else {
        const branch = await strapi.query('branch', 'users-permissions').create(
          {
            name: `企业-${Math.random().toString(36).slice(2, 6)}`,
            type: ObjectId().toString(),
          }
        )
        branchId = branch.id
      }
    }

    // 构造新用户数据
    const newUserData = {
      provider: 'uc',
      confirmed: true,
      ...userData,
      ...roleData,
      // 租户信息
      pBranch: branchId,
      pBranches: [branchId],
      pBranchConfigs: [
        {
          branchId: branchId,
          role: roleData.role.id,
          roles: [roleData.role.id],
          blocked: false,
          username: userData.username,
          phone: userData.phone,
          email: userData.email,
        }
      ],
      // 其他字段
      // username: userData.username,
      // phone: userData.phone,
      // email: userData.email,
      // password: userData.password,
    }
    if (newUserData.avatar?.toString().length !== 24) {
      delete newUserData.avatar
    }
    let user = await strapi.query('user', 'users-permissions').create(newUserData)

    // 附加头像文件
    if (userData.avatar) {
      let url = userData.avatar
      if (userData.avatar.length !== 24) {
        // 微信头像等第三方图片需要重新上传
        const avatar = userData.avatar
        const fileName = `wechat-avatar.jpg`
        const avatarRes = await axios({
          method: 'get',
          url: avatar,
          responseType: 'stream',
        })
        const iUploadPlugin = strapi.plugins['upload']
        let uploadInfo = await iUploadPlugin.services['object-storage'].uploadFileToBucket(fileName, avatarRes.data)
        url = `https://${uploadInfo.Location}`
      }
      let uploadFile
      uploadFile = await strapi.query('file', 'upload').create({
        name: 'avatar',
        mime: 'image/jpeg',
        size: '',
        ext: '.jgp',
        url: url,
        credit: 10,
        created_by: user.id,
        updated_by: user.id,
      })
      user = await strapi.query('user', 'users-permissions').update(
        { id: user.id },
        { avatar: uploadFile.id }
      )
    }

    // 附加 thirdParties
    if (thirdPartyData) {
      user = await iPlugin.services['user'].update({
        id: user.id,
      }, {
        thirdParties: [
          {
            ...thirdPartyData,
            account: user.id,
          },
        ],
      })
    }

    return user
  },

  // 获取默认角色
  async getDefaultRole () {
    // 获取系统默认角色
    const pluginStore = strapi.store({
      type: 'plugin',
      name: 'users-permissions',
    })
    const settings = await pluginStore.get({ key: 'advanced' })
    return await strapi.query('role', 'users-permissions').findOne({ type: settings.default_role }, [])
  },

  // 标准增删改查
  async find (params) {
    return await strapi.query('user', 'users-permissions').find(params)
  },

  async findOne (params) {
    return await strapi.query('user', 'users-permissions').findOne(params)
  },

  async updateOne (id, body) {
    return strapi.query('user', 'users-permissions').update({ id }, body)
  },

  async update (params, body) {
    return strapi.query('user', 'users-permissions').update(params, body)
  },

  async create (body) {
    return strapi.query('user', 'users-permissions').create(body)
  },

  async delete (id) {
    return strapi.query('user', 'users-permissions').delete({ id })
  },

  // 历史数据兼容处理 - 生成各租户配置数据
  // 默认各租户角色与可用角色均为当前用户的状态
  async syncUserBranchConfigs (user, force = false) {
    if (!user.pBranch) return user
    let pBranches, pBranchConfigs
    if (
      force ||
      !user.pBranches || !user.pBranchConfigs
      || user.pBranches.length === 0 || user.pBranchConfigs.length === 0
    ) {
      if (!user.pBranches || user.pBranches.length === 0) {
        pBranches = [user.pBranch.id]
      } else {
        pBranches = user.pBranches.map(e => e.id)
      }
      if (!user.pBranchConfigs) {
        pBranchConfigs = pBranches.map(id => {
          return {
            branchId: id,
            role: user.role?.id,
            roles: user.roles?.map(e => e.id),
            blocked: user.blocked,
            username: user.username,
            phone: user.phone,
            email: user.email,
          }
        })
      } else {
        pBranchConfigs = user.pBranchConfigs
      }
      user = await strapi.query('user', 'users-permissions').update({ id: user.id }, {
        pBranches: pBranches,
        pBranchConfigs: pBranchConfigs
      })
    }
    return user
  },

  // 生成用户登录认证数据
  getFullAuthData (user, tokenData = {}, jwtOptions = {}) {
    const sanitizeUser = {
      _id: user.id,
      id: user.id,
      username: user.username,
      description: user.description,
      phone: user.phone,
      email: user.email,
      role: user.role,
      roles: user.roles,
      avatar: user.avatar,
      customId: user.customId,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    }
    return {
      jwt: strapi.plugins['users-permissions'].services.jwt.issue({
        id: user.id,
        ...tokenData,
      }, jwtOptions),
      user: sanitizeUser
    }
  },
}
