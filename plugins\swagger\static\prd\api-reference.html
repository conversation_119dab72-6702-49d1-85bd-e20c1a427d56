<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Strapi Swagger Plugin 完整 API 参考文档，包含所有端点、参数说明和示例代码">
    <meta name="keywords" content="Strapi, Swagger, API参考, 接口文档, 端点">
    <title>API 参考 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="layout">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>📚 Strapi Swagger</h1>
                <p>API 参考文档</p>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-back">
                    <a href="index.html">← 返回首页</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">API 端点</div>
                    <a href="#swagger-ui" class="nav-item active">
                        <i>📖</i>
                        <span>Swagger UI</span>
                    </a>
                    <a href="#swagger-json" class="nav-item">
                        <i>📄</i>
                        <span>OpenAPI JSON</span>
                    </a>
                    <a href="#markdown-docs" class="nav-item">
                        <i>📚</i>
                        <span>Markdown 文档</span>
                    </a>
                    <a href="#static-resources" class="nav-item">
                        <i>📁</i>
                        <span>静态资源</span>
                    </a>
                    <a href="#authentication" class="nav-item">
                        <i>🔐</i>
                        <span>认证说明</span>
                    </a>
                    <a href="#error-handling" class="nav-item">
                        <i>❌</i>
                        <span>错误处理</span>
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <div class="content-body">
            <div class="toc">
                <h3>📋 API 目录</h3>
                <ul>
                    <li><a href="#swagger-ui">Swagger UI 端点</a></li>
                    <li><a href="#swagger-json">OpenAPI JSON 端点</a></li>
                    <li><a href="#markdown-docs">Markdown 文档端点</a></li>
                    <li><a href="#static-resources">静态资源端点</a></li>
                    <li><a href="#authentication">认证说明</a></li>
                    <li><a href="#error-handling">错误处理</a></li>
                </ul>
            </div>

            <section class="section" id="swagger-ui">
                <h2>📖 Swagger UI 端点</h2>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs</span>
                    </div>
                    <p><strong>描述：</strong>访问完整的 Swagger UI 文档界面</p>

                    <h4>响应示例</h4>
                    <div class="response-example">
                        <span class="response-status status-200">200 OK</span>
                        <p>返回完整的 Swagger UI HTML 页面</p>
                    </div>
                </div>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs/{module}</span>
                    </div>
                    <p><strong>描述：</strong>访问指定模块的 Swagger UI 文档</p>

                    <h4>路径参数</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>描述</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>module</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>模块名称</td>
                                <td>base, user, product</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>请求示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
GET /api-docs/base
GET /api-docs/user
GET /api-docs/product
                        </pre>
                    </div>
                </div>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs/{module}/{controller}</span>
                    </div>
                    <p><strong>描述：</strong>访问指定模块下特定控制器的文档</p>

                    <h4>路径参数</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>描述</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>module</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>模块名称</td>
                                <td>base</td>
                            </tr>
                            <tr>
                                <td><code>controller</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>控制器名称</td>
                                <td>user, parent</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>请求示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
GET /api-docs/base/user
GET /api-docs/base/parent
                        </pre>
                    </div>
                </div>
            </section>

            <section class="section" id="swagger-json">
                <h2>📄 OpenAPI JSON 端点</h2>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs/swagger.json</span>
                    </div>
                    <p><strong>描述：</strong>获取完整的 OpenAPI 3.0 JSON 规范</p>

                    <h4>查询参数</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>描述</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>module</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>过滤指定模块</td>
                                <td>base</td>
                            </tr>
                            <tr>
                                <td><code>controller</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>过滤指定控制器</td>
                                <td>user</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>响应示例</h4>
                    <div class="response-example">
                        <span class="response-status status-200">200 OK</span>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
{
  "openapi": "3.0.0",
  "info": {
    "title": "API Documentation",
    "version": "1.0.0",
    "description": "基于 Strapi 的 API 服务"
  },
  "servers": [
    {
      "url": "http://localhost:8108",
      "description": "开发环境"
    }
  ],
  "paths": {
    "/api/users": {
      "get": {
        "tags": ["用户管理"],
        "summary": "获取用户列表",
        "parameters": [
          {
            "in": "query",
            "name": "_limit",
            "schema": {
              "type": "integer"
            },
            "description": "每页数量"
          }
        ],
        "responses": {
          "200": {
            "description": "成功获取用户列表"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "User": {
        "type": "object",
        "properties": {
          "id": {
            "type": "integer"
          },
          "username": {
            "type": "string"
          }
        }
      }
    }
  }
}
                            </pre>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs/swagger.json/{module}</span>
                    </div>
                    <p><strong>描述：</strong>获取指定模块的 OpenAPI JSON</p>

                    <h4>请求示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 获取 base 模块的 JSON
GET /api-docs/swagger.json/base

# 获取 user 模块的 JSON
GET /api-docs/swagger.json/user
                        </pre>
                    </div>
                </div>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-docs/swagger.json/{module}/{controller}</span>
                    </div>
                    <p><strong>描述：</strong>获取指定控制器的 OpenAPI JSON</p>

                    <h4>请求示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 获取 base 模块下 user 控制器的 JSON
GET /api-docs/swagger.json/base/user

# 获取 base 模块下 parent 控制器的 JSON
GET /api-docs/swagger.json/base/parent
                        </pre>
                    </div>
                </div>
            </section>

            <section class="section" id="markdown-docs">
                <h2>📚 Markdown 文档端点</h2>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/api-doc/markdown/{filename}</span>
                    </div>
                    <p><strong>描述：</strong>访问 Markdown 格式的文档</p>

                    <h4>路径参数</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>描述</th>
                                <th>可用值</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>filename</code></td>
                                <td>string</td>
                                <td>是</td>
                                <td>文档文件名</td>
                                <td>README.md, 前端AI接口开发约定.md, 技术规范与实现.md, 自定义接口注释指南.md</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>请求示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 访问使用指南
GET /api-doc/markdown/README.md

# 访问前端 AI 开发约定
GET /api-doc/markdown/前端AI接口开发约定.md

# 访问技术规范
GET /api-doc/markdown/技术规范与实现.md
                        </pre>
                    </div>

                    <h4>响应示例</h4>
                    <div class="response-example">
                        <span class="response-status status-200">200 OK</span>
                        <p>返回渲染后的 HTML 页面，包含 Markdown 内容和样式</p>
                    </div>
                </div>
            </section>

            <section class="section" id="static-resources">
                <h2>📁 静态资源端点</h2>

                <div class="card">
                    <div class="endpoint-header">
                        <span class="method-badge method-get">GET</span>
                        <span class="endpoint-url">/plugins/swagger/static/{resource}</span>
                    </div>
                    <p><strong>描述：</strong>访问 Swagger UI 静态资源</p>

                    <h4>支持的资源类型</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>资源类型</th>
                                <th>路径示例</th>
                                <th>Content-Type</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>JavaScript</td>
                                <td>/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui-bundle.js</td>
                                <td>application/javascript</td>
                            </tr>
                            <tr>
                                <td>CSS</td>
                                <td>/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui.css</td>
                                <td>text/css</td>
                            </tr>
                            <tr>
                                <td>图像</td>
                                <td>/plugins/swagger/static/swagger-ui/5.0.0/favicon-32x32.png</td>
                                <td>image/png</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="section" id="authentication">
                <h2>🔐 认证说明</h2>

                <div class="auth-info">
                    <h4>🛡️ 环境限制</h4>
                    <p>默认情况下，API 文档仅在开发环境 (NODE_ENV !== 'production') 下可用。</p>
                </div>

                <h3>生产环境启用</h3>
                <p>如需在生产环境中启用文档，设置环境变量：</p>
                <div class="code-block">
                    <button class="copy-button" onclick="copyCode(this)">复制</button>
                    <pre>
ENABLE_API_DOCS=true
                    </pre>
                </div>

                <h3>访问控制</h3>
                <p>插件本身不提供身份认证功能，建议在反向代理或中间件层面添加访问控制：</p>
                <div class="code-block">
                    <button class="copy-button" onclick="copyCode(this)">复制</button>
                    <pre>
# Nginx 配置示例
location /api-docs {
    auth_basic "API Documentation";
    auth_basic_user_file /etc/nginx/.htpasswd;
    proxy_pass http://localhost:8108;
}
                    </pre>
                </div>
            </section>

            <section class="section" id="error-handling">
                <h2>❌ 错误处理</h2>

                <h3>常见错误响应</h3>

                <div class="response-example">
                    <span class="response-status status-400">400 Bad Request</span>
                    <p><strong>原因：</strong>请求参数错误或不支持的模块/控制器</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
{
  "error": "Invalid module or controller",
  "message": "The specified module or controller does not exist"
}
                        </pre>
                    </div>
                </div>

                <div class="response-example">
                    <span class="response-status status-404">404 Not Found</span>
                    <p><strong>原因：</strong>请求的端点不存在</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
{
  "error": "Not Found",
  "message": "The requested resource was not found"
}
                        </pre>
                    </div>
                </div>

                <div class="response-example">
                    <span class="response-status status-500">500 Internal Server Error</span>
                    <p><strong>原因：</strong>服务器内部错误，通常是配置问题</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
{
  "error": "Internal Server Error",
  "message": "An unexpected error occurred while generating documentation"
}
                        </pre>
                    </div>
                </div>

                <h3>调试提示</h3>
                <ul>
                    <li>检查服务器控制台日志获取详细错误信息</li>
                    <li>确认模块和控制器名称的正确性</li>
                    <li>验证 Swagger 配置文件的语法</li>
                    <li>检查相关文件的读取权限</li>
                </ul>
            </section>
        </main>
    </div>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2024 Strapi Swagger Plugin. 完整 API 参考文档</p>
        </div>
    </footer>

    <script src="scripts.js"></script>
    <script>
        function copyCode(button) {
            const codeBlock = button.nextSibling;
            const text = codeBlock.textContent.trim();

            navigator.clipboard.writeText(text).then(function() {
                button.textContent = '成功';
                button.classList.add('copied');

                setTimeout(function() {
                    button.textContent = '复制';
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>
</body>
</html>
