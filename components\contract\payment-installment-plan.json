{"collectionName": "components_contract_payment-installment-plan", "info": {"name": "PaymentInstallmentPlan", "label": "支付计划", "icon": "comment", "description": ""}, "options": {}, "attributes": {"amount": {"label": "金额", "format": "rmb", "type": "number"}, "pay": {"label": "已付金额", "format": "rmb", "type": "number"}, "status": {"label": "支付状态", "type": "string", "size": 3, "options": [{"label": "待支付", "value": "待支付"}, {"label": "已付清", "value": "已付清"}]}, "mode": {"label": "渠道", "type": "string"}, "arNo": {"label": "编号", "type": "string"}, "time": {"label": "日期", "type": "date"}, "targets": {"label": "回款开通产品列表", "type": "component", "repeatable": true, "displayMode": "table", "component": "contract.target", "viewTableConfig": {"columns": ["school", "goods", "calculatedUnitPrice", "unitPrice", "quantity", "beginTime", "endTime", "totalPrice", "remark"]}}}}