module.exports = {
  collectionName: 'telephone-record',
  info: {
    name: 'TelephoneRecord',
    label: '电话录音',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    startTime: {
      label: '开始拨打时间',
      type: 'datetime',
      editable: false
    },
    phone: {
      label: '电话号码',
      type: 'string',
      editable: false
    },
    status: {
      label: '拨打状态',
      type: 'string',
      editable: false,
      options: [
        { label: '已接通', value: '1' },
        { label: '未接通', value: '2' }
      ]
    },
    duration: {
      label: '通话时长',
      type: 'number',
      editable: false
    },
    missedCallDuration: {
      label: '响铃时长',
      type: 'number',
      editable: false
    },
    fileSize: {
      label: '文件大小',
      type: 'number',
      editable: false
    },
    fileUrl: {
      label: '文件地址',
      type: 'string',
      editable: false
    },
    isIncoming: {
      label: '电话呼叫类型',
      type: 'string',
      options: [
        { label: '呼入', value: '1' },
        { label: '呼出', value: '2' }
      ],
      editable: false
    },
    operator: {
      label: '操作人',
      plugin: "users-permissions",
      model: "user",
      mainField: 'username',
      editable: false,
      meta: {
        query: {
          provider: 'yxWeCom'
        }
      },
    },
    groupType: {
      label: '类型',
      type: 'string',
      required: true,
      options: [
        { label: '直营', value: 'sales', },
        { label: '运营', value: 'service', },
      ],
    },
    file16KUrl: {
      label: '16k文件地址',
      type: 'string',
      editable: false
    },
    speechTaskId: {
      label: '任务id',
      type: 'string',
      editable: false
    },
    speechTaskStatus: {
      label: '任务状态',
      type: 'string',
      options: [
        { label: '转写中', value: 'Running' },
        { label: '转写成功', value: 'Success' },
        { label: '转写失败', value: 'Failure' },
      ],
      editable: false
    },
    speechTaskResult: {
      label: '任务结果',
      type: 'string',
      editable: false
    },
    speechTaskResultText: {
      label: '任务结果文本',
      type: 'string',
      editable: false
    }
  }
}
