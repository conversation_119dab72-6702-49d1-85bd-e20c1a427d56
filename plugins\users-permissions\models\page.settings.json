{"collectionName": "users-permissions_view", "info": {"name": "Page", "label": "页面", "description": "页面是一个完整的客户端用户界面"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"content-manager": {"visible": true}}, "attributes": {"isPreset": {"label": "是否预置", "type": "boolean", "required": true, "configurable": false, "editable": false, "default": false}, "isSystem": {"label": "是否系统管理", "type": "boolean", "required": true, "configurable": false, "editable": false, "default": false}, "name": {"label": "视图名称", "type": "string", "required": true, "configurable": false}, "cover": {"label": "视图封面", "model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}, "configurable": false}, "sId": {"label": "视图编号", "type": "string", "required": true, "configurable": false}, "icon": {"label": "视图图标", "type": "string", "required": true, "configurable": false}, "parent": {"label": "父级视图", "type": "string", "required": true, "configurable": false}, "meta": {"label": "附加数据", "type": "json", "editable": true}, "configs": {"label": "视图配置", "type": "json", "editable": true}, "groups": {"label": "视图关联功能", "via": "pages", "collection": "group", "plugin": "users-permissions"}, "hideMenu": {"label": "隐藏菜单项", "type": "boolean", "editable": true, "default": false}}}