const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('contract')
const axios = require('axios');
const jwt = require('jsonwebtoken');
const { ObjectId } = require('mongodb');
const yxhuobanApi = require('../utils/huoban');
const huobanConfig = strapi.config.server.huoban;

const schoolFieldsMap = huobanConfig.school_table.fieldsMap
const saasFieldsMap = huobanConfig.saas_table.fieldsMap

async function getSaasSchoolList (schoolId) {
  let schoolData = await yxhuobanApi.getSchoolById(schoolId)
  if (!schoolData) {
    return {
      code: 1,
      msg: '表格【合作学校】中无该学校，请先在伙伴云添加数据',
      data: {
        errorType: 1,
        errorMsg: `表格【合作学校】中无该学校(${schoolId})，请先在伙伴云添加数据`,
      }
    }
  }
  let schoolFields = schoolData.fields
  let school = {
    xufeiTag2023: getItemOptionName(schoolFields[schoolFieldsMap['2023应续标签']]),
    schoolId: schoolFields[schoolFieldsMap['学校ID']],
    schoolName: schoolFields[schoolFieldsMap['学校名称']],
    type: getItemOptionName(schoolFields[schoolFieldsMap['账号类型']]),
  }

  let schoolRecord = await yxhuobanApi.getSaasSchoolRecodeList(schoolData.item_id)
  if (!schoolRecord) {
    return {
      code: 1,
      msg: '表格【应续SaaS列表】中无该学校，请先在伙伴云添加数据' ,
      data: {
        errorType: 2,
        errorMsg: `表格【应续SaaS列表】中无该学校(${schoolId})，请先在伙伴云添加数据`
      }
    }
  }
  let recordFields = schoolRecord.fields
  let record = {
    itemId: schoolRecord.item_id,
    ...school,
    followUpState: getItemOptionName(recordFields[saasFieldsMap['跟进状态']]),
    renewalState: getItemOptionName(recordFields[saasFieldsMap['续约状态']]),
    degreeState: getItemOptionName(recordFields[saasFieldsMap['风险程度']]),
    isNew: getItemOptionName(recordFields[saasFieldsMap['是否新款']]),
    newAmount: recordFields[saasFieldsMap['预计新款金额']],
    returnDate: recordFields[saasFieldsMap['预计回款日期']],
    recordByAgent: recordFields[saasFieldsMap['渠道经理B跟进记录-商']],
    recordBySchool: recordFields[saasFieldsMap['渠道经理B跟进记录-校']],
    zhifuResult: getItemOptionName(recordFields[saasFieldsMap['直服沟通结果']]),
    recordByZhifu: recordFields[saasFieldsMap['直服跟进记录-校']],
    recordByYezhi: recordFields[saasFieldsMap['阅卷跟进记录(同步)']],
  }

  console.log(record)
  return { code: 0, msg: '获取成功', data: record }
}

async function updateYezhiRecord (itemId, record) {
  return await yxhuobanApi.updateSaasSchoolItem(itemId, record)
}

function getItemOptionName (item) {
  if (item && item.length > 0) {
    return item[0].name
  }
  return null
}

async function getFollowUp(ctx) {
  let key = ctx.request.header['access-key']
  if (!key) {
    return ctx.forbidden('no access-key')
  }

  const accessKey = await strapi.services['access-key'].findOne({ key: key })
  if (!accessKey) {
    // 不存在或禁用。
    return ctx.forbidden('Invalid access-key')
  }

  if (!accessKey.status) {
    return ctx.forbidden('access-key 被禁用，请联系管理员')
  }

  let { schoolId } = ctx.request.query
  if (!schoolId) {
    return ctx.wrapper.send({code : 1, msg : '参数错误', data: {errorType : 0, errorMsg: '参数错误'}})
  }
  let result = await getSaasSchoolList(schoolId)
  return ctx.wrapper.send(result)
}

async function updateFollowUp(ctx) {
  let key = ctx.request.header['access-key']
  if (!key) {
    return ctx.forbidden('no access-key')
  }

  const accessKey = await strapi.services['access-key'].findOne({ key: key })
  if (!accessKey) {
    // 不存在或禁用。
    return ctx.forbidden('Invalid access-key')
  }

  if (!accessKey.status) {
    return ctx.forbidden('access-key 被禁用，请联系管理员')
  }

  let { schoolId, records, isCover } = ctx.request.body
  if (!schoolId || !records || !records.length) {
    return ctx.wrapper.send({code : 1, msg : '参数错误', data: {errorType : 0, errorMsg: '参数错误'}})
  }

  let result = await getSaasSchoolList(schoolId)
  if (result.code !== 0) {
    return ctx.wrapper.send(result)
  }
  let data = result.data;
  let newRecord = records.join('\n');
  if (data.recordByYezhi && !isCover) {// 存在记录，并且不覆盖。
    newRecord = newRecord + '\n' + data.recordByYezhi.replace(/<br>/g, '\n');
  }

  let updateResult = await updateYezhiRecord(data.itemId, newRecord)
  console.log(updateResult)
  return ctx.wrapper.send(updateResult)
}

module.exports = {
  getFollowUp,
  updateFollowUp
}
