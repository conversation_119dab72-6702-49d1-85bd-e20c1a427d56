function isServiceGroup<PERSON>eader(user) {
  return user.role.type === 'service-group-leader' || user.roles.find(item => item.type === 'service-group-leader')
}
function isServiceGroupMember(user) {
  return user.role.type === 'service-group-member' || user.roles.find(item => item.type === 'service-group-member')
}

function isServiceAdmin(user) {
  return user.role.type === 'service-admin' || user.roles.find(item => item.type === 'service-admin')
}

function isSalesGroupLeader(user) {
  return user.role.type === 'sales-group-leader' || user.roles.find(item => item.type === 'sales-group-leader')
}

function isSalesGroupMember(user) {
  return user.role.type === 'sales-group-member' || user.roles.find(item => item.type === 'sales-group-member')
}

function isSalesAdmin(user) {
  return user.role.type === 'sales-admin' || user.roles.find(item => item.type === 'sales-admin')
}

function isSalesManager(user) {
  return user.role.type === 'sales-manager' || user.roles.find(item => item.type === 'sales-manager')
}

function isSalesObserver(user) {
  return user.role.type === 'sales-observer' || user.roles.find(item => item.type === 'sales-observer')
}

function isSales(user) {
  return isSalesAdmin(user) || isSalesGroupLeader(user) || isSalesGroupMember(user) || isSalesManager(user) || isSalesObserver(user)
}

function isService(user) {
  return isServiceAdmin(user) || isServiceGroupLeader(user) || isServiceGroupMember(user)
}

function isLeader(user) {
  return isServiceGroupLeader(user) || isSalesGroupLeader(user)
}

function isMember(user) {
  return isServiceGroupMember(user) || isSalesGroupMember(user)
}

function isAdmin(user) {
  return isServiceAdmin(user) || isSalesAdmin(user) || user.role.type === 'qun-admin'
}

function isServiceWorker(user) {
  return user.role.type === 'service-worker' || user.roles.find(item => item.type === 'service-worker')
}

function isAgentAdmin(user) {
  return user.role.type === 'agent-admin' || user.roles.find(item => item.type === 'agent-admin')
}

module.exports = {
  isServiceGroupLeader,
  isServiceGroupMember,
  isServiceAdmin,
  isSalesGroupLeader,
  isSalesGroupMember,
  isSalesAdmin,
  isSalesManager,
  isSalesObserver,
  isSales,
  isService,
  isLeader,
  isMember,
  isAdmin,
  isServiceWorker,
  isAgentAdmin
}
