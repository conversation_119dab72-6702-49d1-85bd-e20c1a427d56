{"collectionName": "users-permissions_user-variable", "info": {"name": "UserVariable", "label": "用户变量", "description": "以 Key:Value(JSON) 形式为用户提供服务端持续化存储变量数据"}, "options": {"draftAndPublish": false, "timestamps": true}, "attributes": {"user": {"label": "用户", "model": "user", "plugin": "users-permissions", "required": true}, "key": {"label": "Key", "type": "string", "required": true}, "value": {"label": "Value", "type": "string", "required": true}, "version": {"label": "Version", "type": "number", "required": true}}}