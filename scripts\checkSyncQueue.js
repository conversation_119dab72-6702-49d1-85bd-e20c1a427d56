const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://localhost:27017/testwly-boss-back'
  // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
  : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

let db, dbClient

(async function () {
  dbClient = await MongoClient.connect(DB_URL)
  db = dbClient.db()

  try {

    logger('sync start')
    await main()
    logger('sync end')


  } catch (e) {
    logger(e.stack || 'err')
  } finally {
    await dbClient.close()
    setTimeout(() => {
      process.exit(1)
    }, 5000)
  }
})()

async function main () {
  const queueNames = [
    'huoban-queue',
    'huoban-msg-queue',
    'mingdao-queue',
  ]
  for (const queueName of queueNames) {
    const queueInfo = await db.collection(queueName).findOne({ status: 'prepared', createdAt: { $lt: moment().subtract(30, 'm').toDate() } })
    if (queueInfo) {
      await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=05f30d67-fec2-427e-88ad-cfd8615c7c83', {
        msgtype: 'text',
        text: {
          content: `${queueName} 同步超时`,
          "mentioned_list": ["xijunpeng"],
        }
      })
    }
  }
}

function logger (...msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sleep (time) {
  return new Promise((resolve) => setTimeout(resolve, time))
}
