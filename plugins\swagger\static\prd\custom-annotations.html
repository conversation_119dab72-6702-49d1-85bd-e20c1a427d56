<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="学习如何使用 @swagger 注释为 Strapi API 添加自定义文档，包含完整示例和最佳实践">
    <meta name="keywords" content="Strapi, Swagger, 注释, API文档, JSDoc, OpenAPI">
    <title>自定义接口注释 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    
    <div class="layout">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1>📚 Swagger Plugin</h1>
                <p>产品文档中心</p>
            </div>
            
            <div class="nav-back">
                <a href="index.html">← 返回首页</a>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">概览</div>
                    <a href="index.html" class="nav-item">
                        <i>🏠</i>产品概览
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">功能特性</div>
                    <a href="features.html" class="nav-item">
                        <i>✨</i>功能特性
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">文档指南</div>
                    <a href="installation.html" class="nav-item">
                        <i>🛠️</i>安装配置
                    </a>
                    <a href="usage.html" class="nav-item">
                        <i>📖</i>使用指南
                    </a>
                    <a href="custom-annotations.html" class="nav-item active">
                        <i>📝</i>自定义注释
                    </a>
                    <a href="api-reference.html" class="nav-item">
                        <i>🔧</i>API 参考
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">支持</div>
                    <a href="troubleshooting.html" class="nav-item">
                        <i>🔍</i>故障排除
                    </a>
                    <a href="release-notes.html" class="nav-item">
                        <i>📋</i>发版记录
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">外部链接</div>
                    <a href="http://localhost:8108/api-docs" class="nav-item" target="_blank">
                        <i>🌐</i>实时文档
                    </a>
                    <a href="https://swagger.io/specification/" class="nav-item" target="_blank">
                        <i>📚</i>OpenAPI 规范
                    </a>
                </div>
            </div>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="content">

            <div class="content-body">
                <!-- 概述 -->
                <section class="section">
                    <h2>概述</h2>
                    <p>本文档定义了项目中自定义接口的 <code>@swagger</code> 注释规范，用于自动生成 Swagger API 文档。</p>
                    
                    <div class="tip-box">
                        <h4>💡 重要说明</h4>
                        <ul>
                            <li class="success"><strong>仅为自定义接口添加注释</strong>：标准 CRUD 接口会自动生成文档</li>
                            <li class="success"><strong>必须使用真实的 API 路径</strong>：从 routes.js 中获取准确路径</li>
                            <li class="success"><strong>路径格式</strong>：使用 <code>{id}</code> 而不是 <code>:id</code></li>
                            <li class="success"><strong>YAML 格式</strong>：严格遵循 OpenAPI 3.0 YAML 语法</li>
                        </ul>
                    </div>
                </section>

                <!-- 基础格式 -->
                <section class="section">
                    <h2>基础格式</h2>
                    <p>Swagger 插件通过扫描 JSDoc 中的 <code>@swagger</code> 注释来识别自定义接口，格式必须是标准的 <strong>OpenAPI 3.0 YAML 格式</strong>：</p>
                    
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /api/endpoint:
 *   method:
 *     tags:
 *       - 标签名称
 *     summary: 简短描述
 *     description: 详细描述
 *     parameters:
 *       - in: query
 *         name: param1
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数说明
 *     responses:
 *       200:
 *         description: 成功
 */
async function customEndpoint(ctx) {
  // 实现逻辑
}
                        </pre>
                    </div>
                </section>

                <!-- 接口类型模板 -->
                <section class="section">
                    <h2>接口类型模板</h2>
                    
                    <div class="grid-2">
                        <div class="feature-card">
                            <h3><span class="method-badge get">GET</span>查询接口</h3>
                            <p>用于获取数据的接口，支持查询参数和路径参数。</p>
                            <ul>
                                <li>✅ 查询参数：<code>in: query</code></li>
                                <li>✅ 路径参数：<code>in: path</code></li>
                                <li>✅ 返回数据结构定义</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3><span class="method-badge post">POST</span>创建接口</h3>
                            <p>用于创建新资源的接口，支持 JSON 和文件上传。</p>
                            <ul>
                                <li>✅ JSON 数据：<code>application/json</code></li>
                                <li>✅ 文件上传：<code>multipart/form-data</code></li>
                                <li>✅ 必填字段验证</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3><span class="method-badge put">PUT</span>更新接口</h3>
                            <p>用于完整更新资源的接口。</p>
                            <ul>
                                <li>✅ 资源 ID 路径参数</li>
                                <li>✅ 完整数据更新</li>
                                <li>✅ 业务冲突处理</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3><span class="method-badge delete">DELETE</span>删除接口</h3>
                            <p>用于删除资源的接口。</p>
                            <ul>
                                <li>✅ 资源 ID 参数</li>
                                <li>✅ 删除确认响应</li>
                                <li>✅ 关联数据处理</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- GET 接口示例 -->
                <section class="section">
                    <h2>GET 接口示例</h2>
                    <p>基础查询接口的完整注释示例：</p>
                    
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /subjectActive/getStudentSingleSubjectData:
 *   get:
 *     tags:
 *       - 选科管理
 *     summary: 获取学生单科数据
 *     description: 根据学生ID和活动ID获取学生的选科详细数据
 *     parameters:
 *       - in: query
 *         name: studentId
 *         required: true
 *         schema:
 *           type: string
 *         description: 学生ID
 *         example: "123456"
 *       - in: query
 *         name: activityId
 *         required: true
 *         schema:
 *           type: string
 *         description: 选科活动ID
 *         example: "act_001"
 *     responses:
 *       200:
 *         description: 成功返回学生选科数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 studentId:
 *                   type: string
 *                   description: 学生ID
 *                 studentName:
 *                   type: string
 *                   description: 学生姓名
 *                 selectedSubjects:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: 已选科目列表
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 学生或活动不存在
 */
async function getStudentSingleSubjectData(ctx) {
  // 实现逻辑
}
                        </pre>
                    </div>
                </section>

                <!-- POST 接口示例 -->
                <section class="section">
                    <h2>POST 接口示例</h2>
                    <p>创建和更新接口的完整注释示例：</p>
                    
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /subjectActive/updateStudentSubject:
 *   post:
 *     tags:
 *       - 选科管理
 *     summary: 更新学生选科
 *     description: 更新指定学生在某个选科活动中的科目选择
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - studentId
 *               - activityId
 *               - subjects
 *             properties:
 *               studentId:
 *                 type: string
 *                 description: 学生ID
 *                 example: "123456"
 *               activityId:
 *                 type: string
 *                 description: 选科活动ID
 *                 example: "act_001"
 *               subjects:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 选择的科目列表
 *                 example: ["数学", "物理", "化学"]
 *               reason:
 *                 type: string
 *                 description: 选择理由（可选）
 *                 example: "根据个人兴趣选择"
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 参数错误
 *       403:
 *         description: 选科活动已结束
 *       409:
 *         description: 科目时间冲突
 */
async function updateStudentSubject(ctx) {
  // 实现逻辑
}
                        </pre>
                    </div>
                </section>

                <!-- 认证和权限 -->
                <section class="section">
                    <h2>认证和权限</h2>
                    <p>接口的访问权限通过 <code>security</code> 字段控制：</p>
                    
                    <h3>需要认证（默认）</h3>
                    <p>大部分接口都需要 JWT 认证，无需额外声明：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /api/user/profile:
 *   get:
 *     # 默认需要 JWT 认证
 *     responses:
 *       401:
 *         description: 未授权
 */
                        </pre>
                    </div>

                    <h3>公开接口</h3>
                    <p>无需认证的公开接口需要明确声明：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /open/api/school-info:
 *   get:
 *     security: []    # 明确声明无需认证
 *     responses:
 *       200:
 *         description: 成功
 */
                        </pre>
                    </div>

                    <h3>Access Key 认证</h3>
                    <p>外部系统接口使用 Access Key 认证：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /external/api/sync-students:
 *   post:
 *     security:
 *       - accessKey: []    # 使用 access-key 认证
 *     responses:
 *       401:
 *         description: Access Key 无效
 */
                        </pre>
                    </div>
                </section>

                <!-- 标签命名规范 -->
                <section class="section">
                    <h2>标签命名规范</h2>
                    <p>根据项目业务模块使用中文标签，保持文档的可读性：</p>
                    
                    <div class="tech-stack">
                        <span class="tech-badge">基础信息-用户管理</span>
                        <span class="tech-badge">基础信息-班级管理</span>
                        <span class="tech-badge">基础信息-学校管理</span>
                        <span class="tech-badge">选科管理</span>
                        <span class="tech-badge">成长发展</span>
                        <span class="tech-badge">教师发展活动</span>
                        <span class="tech-badge">考勤管理</span>
                        <span class="tech-badge">文件上传</span>
                        <span class="tech-badge">系统配置</span>
                        <span class="tech-badge">权限管理</span>
                        <span class="tech-badge">开放接口</span>
                        <span class="tech-badge">外部接口</span>
                    </div>

                    <div class="tip-box">
                        <h4>📋 路径前缀约定</h4>
                        <ul>
                            <li><code>/open/*</code> - 公开接口，自动使用 "开放接口" 标签</li>
                            <li><code>/external/*</code> - 外部系统接口，自动使用 "外部接口" 标签</li>
                            <li>其他路径 - 使用业务相关标签</li>
                        </ul>
                    </div>
                </section>

                <!-- 常见错误响应 -->
                <section class="section">
                    <h2>常见错误响应</h2>
                    
                    <div class="warning-box">
                        <h4>🚨 标准错误响应代码</h4>
                        <div class="table">
                            <code>400</span>
                            <span>请求参数错误</span>
                            
                            <code>401</span>
                            <span>未授权访问</span>
                            
                            <code>403</span>
                            <span>权限不足</span>
                            
                            <code>404</span>
                            <span>资源不存在</span>
                            
                            <code>409</span>
                            <span>业务冲突</span>
                            
                            <code>422</span>
                            <span>数据验证失败</span>
                            
                            <code>500</span>
                            <span>服务器内部错误</span>
                        </div>
                    </div>
                </section>

                <!-- 生成指南 -->
                <section class="section">
                    <h2>Claude Code 自动生成指南</h2>
                    <p>当使用 Claude Code 为现有接口添加注释时，遵循以下步骤：</p>
                    
                    <div class="features">
                        <div class="feature-card">
                            <h4>分析路由</h4>
                            <p>检查 routes.js 文件，确定准确的路径和 HTTP 方法</p>
                        </div>
                        
                        <div class="feature-card">
                            <h4>分析函数</h4>
                            <p>理解函数参数、业务逻辑和返回值结构</p>
                        </div>
                        
                        <div class="feature-card">
                            <h4>确定标签</h4>
                            <p>根据业务模块选择合适的标签分类</p>
                        </div>
                        
                        <div class="feature-card">
                            <h4>生成注释</h4>
                            <p>包含完整的参数、响应和示例数据</p>
                        </div>
                    </div>
                </section>

                <!-- 质量要求 -->
                <section class="section">
                    <h2>质量要求和注意事项</h2>
                    
                    <div class="grid-2">
                        <div class="feature-card">
                            <h3>✅ 必须做到</h3>
                            <ul>
                                <li class="success">路径必须与 routes.js 完全一致</li>
                                <li class="success">所有必填参数标记 <code>required: true</code></li>
                                <li class="success">使用准确的数据类型和格式</li>
                                <li class="success">提供真实可用的示例值</li>
                                <li class="success">包含成功和常见错误响应</li>
                                <li class="success">确保 YAML 格式正确</li>
                            </ul>
                        </div>

                        <div class="feature-card">
                            <h3>❌ 禁止事项</h3>
                            <ul>
                                <li class="error">不要为 CRUD 接口添加注释</li>
                                <li class="error">不要使用错误的路径格式（:id → {id}）</li>
                                <li class="error">不要遗漏必填参数的 required 标记</li>
                                <li class="error">不要使用不存在的标签名称</li>
                                <li class="error">不要提供无效或错误的示例</li>
                                <li class="error">不要与实际代码逻辑不一致</li>
                            </ul>
                        </div>
                    </div>
                </section>

                <!-- 总结 -->
                <section class="section">
                    <h2>总结</h2>
                    <div class="tip-box">
                        <h4>📋 文档生成要点</h4>
                        <p>遵循本规范可确保生成的 Swagger 文档质量和一致性，便于 API 使用者理解和调用接口。记住：</p>
                        <ul>
                            <li><strong>准确性</strong>：路径、参数、响应必须与实际代码一致</li>
                            <li><strong>完整性</strong>：包含所有必要的参数和响应信息</li>
                            <li><strong>可读性</strong>：提供清晰的描述和有意义的示例</li>
                            <li><strong>规范性</strong>：遵循 OpenAPI 3.0 标准和项目约定</li>
                        </ul>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <script src="scripts.js"></script>
</body>
</html>