const { CurdRouter } = require('accel-utils')
const { getFollowerNowGroup, } = require('../../crm/services/customer-service')
const {
  getBaseQuery,
} = require('../../crm/services/customer-service')
const curdRouter = new (class extends CurdRouter {

    // ...
    // ...

    _getQueryByUser(query, user) {
        query.facilitator_null = false
        if (['sales-group-leader', 'sales-group-member'].includes(user.role.type)) {
            query.facilitator = user.mingdaoId
        }
    }

    async count(ctx) {
        const user = ctx.state.user
        const { query } = this._parseCtx(ctx)
        this._getQueryByUser(query, user)
        getBaseQuery(query)
        return super.count(ctx)
    }

    async find(ctx) {
        const user = ctx.state.user
        const { query } = this._parseCtx(ctx)

        this._getQueryByUser(query, user)
        getBaseQuery(query)

        const customerServices = await super.find(ctx)
        const groups = await strapi.query('manager-group').find({})
        // 填充服务数据
        for (let i = 0; i < customerServices.length; i++) {
            customerServices[i] = getFollowerNowGroup(customerServices[i], groups)
        }
        return customerServices
    }

    async update(ctx) {
        const { data } = this._parseCtx(ctx)
        data.operator = [ctx.state.user.mingdaoId]
        // 明道云 mongo 跟进记录联合查询, 更新子表过滤 mongo 数据
        if (data.customerSalesFollowRecords) {
            data.customerSalesFollowRecords = data.customerSalesFollowRecords.filter(e => e.id?.length !== 24)
        }
        const result = await super.update(ctx)
        return result
    }

    async updateMany(ctx) {
        const { data: { filter, data } } = this._parseCtx(ctx)
        data.operator = [ctx.state.user.mingdaoId]
        const result = await super.updateMany(ctx)
        return result
    }
})('facilitate-customer')

module.exports = {
    ...curdRouter.createHandlers(),
}
