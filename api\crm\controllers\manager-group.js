const _ = require('lodash')
const { CurdRouter } = require('accel-utils')
const { createCacheFunction } = require('../../crm-mid/utils/cache')
const { syncQxUserByDepartment } = require('../services/manger')

const curdRouter = new (class extends CurdRouter {
  // ...
  async update(ctx) {
    const { data } = this._parseCtx(ctx)
    const result = await super.update(ctx)
    // syncQxUserByDepartment(ctx);
    // 如果更新组员或者单元 则同步更新明道云用户关联
    if (data?.members || data?.unit) syncQxUserByDepartment(ctx)
    return result
  }

  async create(ctx) {
    const { data } = this._parseCtx(ctx)
    const result = await super.create(ctx)
    // 如果更新组员或者单元 则同步更新明道云用户关联
    if (data?.members || data?.unit) syncQxUserByDepartment(ctx)
    return result
  }
})('manager-group')

async function getUserGroup () {
  let groups = await strapi.query('manager-group').find({})
  // leader 和 members 转换为 user表里的user。
  let allUserMingdaoIds = []

  groups.forEach(item => {
    let leaders = item.leader || [] // 单记录也是数组
    leaders.forEach(item => {
      allUserMingdaoIds.push(item.id)
    })
    let members = item.members || []
    members.forEach(item => {
      allUserMingdaoIds.push(item.id)
    })
  })

  let users = await strapi.query('user', 'users-permissions').find({
    mingdaoId_in: allUserMingdaoIds,
    _limit: 9999
  })

  let userMap = {}
  users.forEach(item => {
    userMap[item.mingdaoId] = item
  })

  return groups.map(item => {
    return {
      ...item,
      leader: ((item.leader || []).filter(item => userMap[item.id]).map(item => userMap[item.id]))[0] || null,
      members: (item.members || []).filter(item => userMap[item.id]).map(item => userMap[item.id])
    }
  })
}

module.exports = {
  ...curdRouter.createHandlers(),
  getUserGroup: createCacheFunction(getUserGroup, 5 * 60 * 60 * 1000)
}
