const { CurdRouter } = require('accel-utils')
const moment = require('moment')
const axios = require('axios')

// 检查是否已开通题库组卷权益和学科网权益
async function checkExamPermission(schoolId) {
  try {
    const bossApiUrl = strapi.config.server.bossApi.url
    const response = await axios.get(
      `${bossApiUrl}/external/api/customer/app_usage/get_by_school_id`,
      {
        params: {
          schoolId,
          productCategory: 'p_10' // 题库组卷的产品分类编码
        },
        headers: {
          'apikey': strapi.config.server.bossApi.apikey
        },
        timeout: 10000
      }
    )

    if (response.data && response.data.code === 1 && response.data.data) {
      const { appUsages } = response.data.data
      // 检查是否有有效的题库组卷权益
      if (appUsages && appUsages.length > 0) {
        // 查找有效的权益（状态为1即在有效期内）
        const validUsage = appUsages.find(usage => {
          return usage.status === 1
        })

        if (validUsage) {
          // 检查是否已开通学科网
          let hasXkw = false
          if (validUsage.params && Array.isArray(validUsage.params)) {
            const xkwParam = validUsage.params.find(param =>
              param.name === '启用学科网' &&
              param.value &&
              (Array.isArray(param.value) ? param.value.includes('是') : param.value === '是')
            )
            hasXkw = !!xkwParam
          }

          return {
            hasPermission: true,
            hasXkwEnabled: hasXkw,
            message: hasXkw ? '已开通题库组卷和学科网权益' : '已开通题库组卷权益',
            usage: validUsage
          }
        }
      }
    }

    return {
      hasPermission: false,
      hasXkwEnabled: false,
      message: '未开通题库组卷权益'
    }
  } catch (error) {
    console.error('检查题库组卷权益异常:', error)
    // 如果接口调用失败，返回错误信息
    return {
      hasPermission: false,
      hasXkwEnabled: false,
      message: '检查权益失败: ' + (error.message || '接口异常'),
      error
    }
  }
}

// 学科网服务开通函数
async function openXkwService(existingUsage, schoolId, schoolName, userId, userName, applyRemark) {
  try {
    // 查询学校信息获取customerId
    const customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId }, [])
    if (!customerService || !customerService.customerId) {
      return {
        success: false,
        message: '未找到学校对应的客户信息'
      }
    }

    // 保留原有的题库组卷参数，并添加/修改学科网参数
    const updatedParams = existingUsage.params ? [...existingUsage.params] : []

    // 查找是否已有"启用学科网"参数
    const xkwParamIndex = updatedParams.findIndex(param => param.name === '启用学科网')

    if (xkwParamIndex >= 0) {
      // 更新现有参数
      updatedParams[xkwParamIndex] = {
        name: '启用学科网',
        value: ['是']
      }
    } else {
      // 添加新参数
      updatedParams.push({
        name: '启用学科网',
        value: ['是']
      })
    }

    // 构建更新请求体
    const updateData = {
      customerId: customerService.customerId,
      usage: {
        type: existingUsage.type,
        enabled: existingUsage.enabled || 1,
        isTrial: existingUsage.isTrial || 0,
        beginTime: existingUsage.beginDate,
        endTime: existingUsage.endDate,
        params: updatedParams
      },
      enabled: 1,
      remark: applyRemark ? `学科网组卷服务申请自助开通 - ${applyRemark}` : `学科网组卷服务申请自助开通`,
      handler: {
        user_id: userId,
        name: userName
      },
      source: {
        type: 'order',
        data: {
          order_id: '',
          order_no: ''
        }
      }
    }

    // 调用外部API更新权益
    const bossApiUrl = strapi.config.server.bossApi.url
    const response = await axios.post(
      `${bossApiUrl}/external/api/customer/app_usage/update`,
      updateData,
      {
        headers: {
          'apikey': strapi.config.server.bossApi.apikey,
          'token': strapi.config.server.bossApi.token
        },
        timeout: 30000
      }
    )

    console.log('学科网权益开通请求:', updateData, '响应:', response.data)

    if (response.status === 200 && response.data && response.data.code === 1) {
      return {
        success: true,
        message: '学科网权益开通成功',
        data: response.data
      }
    } else {
      return {
        success: false,
        message: response.data?.message || '学科网权益开通失败',
        data: response.data
      }
    }
  } catch (error) {
    console.error('学科网服务开通异常:', error)
    return {
      success: false,
      message: error.message || '学科网权益开通异常',
      error
    }
  }
}

// 发送企信通知
async function sendWechatNotification(apply, status, reviewerName, remark) {
  try {
    const webhookUrl = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7db179cc-ed0f-4533-abe7-e1bb73c58c43'
    
    // 根据状态设置不同的通知内容
    let statusText = ''
    let color = 'info'
    switch (status) {
      case 'approved':
        statusText = '✅ 已通过'
        color = 'info'
        break
      case 'rejected':
        statusText = '❌ 已拒绝'
        color = 'warning'
        break
      default:
        statusText = status
    }
    
    // 构建markdown消息
    const content = `## 学科网服务申请审核通知
    
**申请学校：** ${apply.schoolName}
**学校ID：** ${apply.schoolId}
**申请人：** ${apply.applyUser || '-'}
**申请时间：** ${moment(apply.createdAt).format('YYYY-MM-DD HH:mm:ss')}
**审核状态：** ${statusText}
**审核人：** ${reviewerName || '-'}
**审核时间：** ${moment().format('YYYY-MM-DD HH:mm:ss')}
${remark ? `**备注：** ${remark}` : ''}

[查看详情](${strapi.config.server.frontendUrl || 'https://boss.itixiao.com'}/crm/xkw-service-apply)`
    
    const message = {
      msgtype: 'markdown',
      markdown: {
        content
      }
    }
    
    // 发送通知
    const response = await axios.post(webhookUrl, message, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 5000
    })
    
    if (response.data && response.data.errcode === 0) {
      console.log('企信通知发送成功:', apply.schoolName, status)
      return true
    } else {
      console.error('企信通知发送失败:', response.data)
      return false
    }
  } catch (error) {
    console.error('发送企信通知异常:', error.message)
    // 通知失败不影响主流程
    return false
  }
}

const curdRouter = new (class extends CurdRouter {
  async update(ctx) {
    const { id } = ctx.params
    const data = ctx.request.body

    // 获取当前申请记录
    const currentApply = await strapi.services['xkw-service-apply'].findOne({ id })
    if (!currentApply) {
      return ctx.wrapper.error('NOT_FOUND', '申请记录不存在')
    }

    // 判断是否从待审核状态更新为已通过
    const isApproving = currentApply.applyStatus === 'pending' && data.applyStatus === 'approved'

    if (isApproving) {
      // 先检查是否已开通题库组卷权益
      const permissionCheck = await checkExamPermission(currentApply.schoolId)

      if (!permissionCheck.hasPermission) {
        // 未开通题库组卷，不允许开通学科网组卷
        return ctx.wrapper.error('PERMISSION_DENIED',
          `无法开通学科网组卷服务: ${permissionCheck.message}。请先开通题库组卷权益`)
      }

      // 检查是否已开通学科网
      if (permissionCheck.hasXkwEnabled) {
        // 已开通学科网，直接更新状态为已通过
        data.applyStatus = 'approved'
        data.reviewer = ctx.state.user.id
        data.remark = (data.remark || '') + `\n学科网权益已开通，无需重复开通`
      } else {
        // 未开通学科网，调用权益开通接口
        const openResult = await openXkwService(
          permissionCheck.usage,
          currentApply.schoolId,
          currentApply.schoolName,
          ctx.state.user.id,
          ctx.state.user.username,
          data.remark || currentApply.remark
        )

        if (openResult.success) {
          // 开通成功，更新为已通过
          data.applyStatus = 'approved'
          data.reviewer = ctx.state.user.id
          data.remark = (data.remark || '') + `\n权益开通成功: ${openResult.message}`
        } else {
          // 开通失败，保持待审核状态
          return ctx.wrapper.error('HANDLE_ERROR', `权益开通失败: ${openResult.message}`)
        }
      }
    }

    // 如果更新状态为已拒绝，自动记录审核人
    if (data.applyStatus === 'rejected') {
      data.reviewer = ctx.state.user.id
    }

    const result = await strapi.services['xkw-service-apply'].update({ id }, data)
    
    // 如果审核状态发生变更，发送企信通知
    if (currentApply.applyStatus !== data.applyStatus && 
        (data.applyStatus === 'approved' || data.applyStatus === 'rejected')) {
      // 异步发送通知，不影响主流程
      sendWechatNotification(
        result,
        data.applyStatus,
        ctx.state.user.username,
        data.remark
      ).catch(err => {
        console.error('发送企信通知失败:', err)
      })
    }
    
    return result
  }
})('xkw-service-apply')

module.exports = {
  ...curdRouter.createHandlers(),
}
