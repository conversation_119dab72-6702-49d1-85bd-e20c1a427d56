const { createCacheFunction } = require('../utils/cache')

async function getYjAppUsageStatisticLocal () {
  const filter = {
    isTest: { $ne: true },
    deleted: { $ne: true },
    obsolete: { $ne: true }
  }

  const totalCount = await strapi.query('customer-service').model.countDocuments(filter)
  const vipCount = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '付费校'
  })
  const noVipCount = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '体验校'
  })
  const due30Count = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '付费校',
    yjEndTime: {
      $lte: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    }
  })
  const due60Count = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '付费校',
    yjEndTime: {
      $gt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      $lte: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000)
    }
  })
  const due90Count = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '付费校',
    yjEndTime: {
      $gt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      $lte: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
    }
  })
  const reNewCount = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '应续校'
  })
  const lostCount = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    yjTag: '流失校'
  })

  const waitAssignCount = await strapi.query('customer-service').model.countDocuments({
    ...filter,
    exclusiveArea: { $ne: true },
    exclusiveSchool: { $ne: true },
    specialArea: { $ne: true },
    specialAgent: { $ne: true },
    noTouching: { $ne: true },
    directServiceManager: { $eq: null }
  })
  return {
    totalCount,
    vipCount,
    noVipCount,
    due30Count,
    due60Count,
    due90Count,
    reNewCount,
    lostCount,
    waitAssignCount
  }
}

module.exports = {
  getYjAppUsageStatisticByLocal: createCacheFunction(getYjAppUsageStatisticLocal, 60 * 5 * 1000),
}
