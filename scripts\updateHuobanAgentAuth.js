const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const BOSS_DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://boss:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testboss'
    : '*************************************************************************************************'

const SERVER_URL = (process.env.NODE_ENV !== 'production')
    ? 'http://localhost:3015'
    : 'https://wly-boss-api-lan.iyunxiao.com'

let db, dbClient
let bossDb, bossDbClient

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    bossDbClient = await MongoClient.connect(BOSS_DB_URL)
    bossDb = bossDbClient.db()

    try {
        let start = Date.now()
        logger('sync start')
        await main()
        logger('sync end')
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {
        await dbClient.close()
        await bossDbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('sync agent auth start')

    const cache = await db.collection('db-cache').findOne({
        key: 'updateHuobanAgentAuth',
        type: 'cache',
    });
    let id = cache?.content;
    if (cache) {
        await updateHuoban(id);
    } else {
        id = '66f4c9840001490831694666';
        const cache = await db.collection('db-cache').insertOne({
            key: 'updateHuobanAgentAuth',
            type: 'cache',
            content: id
        });
        await updateHuoban(id);
    }

    logger('sync agent auth end')
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function updateHuoban(lastSyncObjectId, retry = 3) {
    try {
        const limit = 3
        let curCount = 0

        while (true) {
            const conditions = {
                target: 'Customer',
                _id: { '$gt': ObjectId(lastSyncObjectId) }
            }

            const logs = await bossDb.collection('@AgentAuthActionLog').find(conditions).sort({ _id: 1 }).limit(limit).toArray();
            if (logs.length === 0) { break }
            const signs = logs.map(log => log.sign)
            let auths = await bossDb.collection('@AgentAuthCustomer').find({
                sign: { $in: signs }
            }).toArray()
            let customerIds = auths.map(e => ObjectId(e.customer.id))
            let customers = await bossDb.collection('@Customer').find({ _id: { $in: customerIds } }).project({
                _id: 1,
                school_id_20: 1,
                name: 1,
            }).toArray()
            let agentIds = auths.map(e => ObjectId(e.agent.id))
            let agents = await bossDb.collection('@Agent').find({ _id: { $in: agentIds } }).project({
                _id: 1,
                no: 1,
                company: 1,
                user_id: 1,
            }).toArray()

            // 本地测试使用
            // let logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@AgentAuthActionLog',
            //     filter: {
            //         target: 'Customer',
            //         _id: { '$gt': lastSyncObjectId }
            //     },
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });
            // if (logs.length === 0) { break }
            // const signs = logs.map(log => log.sign)
            // let auths = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@AgentAuthCustomer',
            //     filter: {
            //         sign: { $in: signs }
            //     },
            // }).then(({ data }) => { return data });
            // let customerIds = auths.map(e => ObjectId(e.customer.id))
            // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@Customer',
            //     filter: {
            //         _id: { $in: customerIds } 
            //     },
            // }).then(({ data }) => { return data });
            // let agentIds = auths.map(e => ObjectId(e.agent.id))
            // let agents = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@Agent',
            //     filter: {
            //         _id: { $in: agentIds } 
            //     },
            // }).then(({ data }) => { return data });

            for (const auth of auths) {
                const curCustomer = customers.find(e => e._id.toString() === auth.customer.id)
                const curAgent = agents.find(e => e._id.toString() === auth.agent.id)
                auth.customer.schoolId = curCustomer.school_id_20
                auth.agent.no = curAgent.no
                auth.id = auth._id.toString()
            }

            const res = await axios.post(`${SERVER_URL}/huoban/agent-auth/update`, { infos: auths })

            lastSyncObjectId = logs.length > 0 ? logs[logs.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('db-cache').updateOne({
                key: 'updateHuobanAgentAuth',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });

            if (logs.length < limit) { break }

            await sleep(3 * 1000);
            // console.log(customerServices.length)
        }
    } catch (e) {
        logger(`retry: ${retry}`)
        logger(e.message)
        if (retry > 0) {
            await sleep(3 * 60 * 1000);
            return await updateHuoban(lastSyncObjectId, --retry);
        }
        throw e;
    }
}

async function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
