'use strict'

/**
 * An asynchronous bootstrap function that runs before
 * your application gets started.
 *
 * This gives you an opportunity to set up your data model,
 * run jobs, or perform some special logic.
 */
const _ = require('lodash')
const uuid = require('uuid/v4')
const defaultConfig = require('../permission')
const permissionInit = require('./permissionInit')

module.exports = async () => {

  // users-permissions 初始化
  const pluginStore = strapi.store({
    type: 'plugin',
    name: 'users-permissions',
  })

  const grantConfig = {
    email: {
      enabled: true,
      icon: 'envelope',
    },
    discord: {
      enabled: false,
      icon: 'discord',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/discord/callback`,
      scope: ['identify', 'email'],
    },
    facebook: {
      enabled: false,
      icon: 'facebook-square',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/facebook/callback`,
      scope: ['email'],
    },
    google: {
      enabled: false,
      icon: 'google',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/google/callback`,
      scope: ['email'],
    },
    github: {
      enabled: false,
      icon: 'github',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/github/callback`,
      scope: ['user', 'user:email'],
    },
    microsoft: {
      enabled: false,
      icon: 'windows',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/microsoft/callback`,
      scope: ['user.read'],
    },
    twitter: {
      enabled: false,
      icon: 'twitter',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/twitter/callback`,
    },
    instagram: {
      enabled: false,
      icon: 'instagram',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/instagram/callback`,
      scope: ['user_profile'],
    },
    vk: {
      enabled: false,
      icon: 'vk',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/vk/callback`,
      scope: ['email'],
    },
    twitch: {
      enabled: false,
      icon: 'twitch',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/twitch/callback`,
      scope: ['user:read:email'],
    },
    linkedin: {
      enabled: false,
      icon: 'linkedin',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/linkedin/callback`,
      scope: ['r_liteprofile', 'r_emailaddress'],
    },
    cognito: {
      enabled: false,
      icon: 'aws',
      key: '',
      secret: '',
      subdomain: 'my.subdomain.com',
      callback: `${strapi.config.server.url}/auth/cognito/callback`,
      scope: ['email', 'openid', 'profile'],
    },
    reddit: {
      enabled: false,
      icon: 'reddit',
      key: '',
      secret: '',
      state: true,
      callback: `${strapi.config.server.url}/auth/reddit/callback`,
      scope: ['identity'],
    },
    auth0: {
      enabled: false,
      icon: '',
      key: '',
      secret: '',
      subdomain: 'my-tenant.eu',
      callback: `${strapi.config.server.url}/auth/auth0/callback`,
      scope: ['openid', 'email', 'profile'],
    },
    cas: {
      enabled: false,
      icon: 'book',
      key: '',
      secret: '',
      callback: `${strapi.config.server.url}/auth/cas/callback`,
      scope: ['openid email'], // scopes should be space delimited
      subdomain: 'my.subdomain.com/cas',
    },
  }
  const prevGrantConfig = (await pluginStore.get({ key: 'grant' })) || {}
  // store grant auth config to db
  // when plugin_users-permissions_grant is not existed in db
  // or we have added/deleted provider here.
  if (!prevGrantConfig || !_.isEqual(_.keys(prevGrantConfig), _.keys(grantConfig))) {
    // merge with the previous provider config.
    _.keys(grantConfig).forEach(key => {
      if (key in prevGrantConfig) {
        grantConfig[key] = _.merge(grantConfig[key], prevGrantConfig[key])
      }
    })
    await pluginStore.set({ key: 'grant', value: grantConfig })
  }

  if (!(await pluginStore.get({ key: 'email' }))) {
    const value = {
      reset_password: {
        display: 'Email.template.reset_password',
        icon: 'sync',
        options: {
          from: {
            name: 'Administration Panel',
            email: '<EMAIL>',
          },
          response_email: '',
          object: 'Reset password',
          message: `<p>We heard that you lost your password. Sorry about that!</p>

<p>But don’t worry! You can use the following link to reset your password:</p>
<p><%= URL %>?code=<%= TOKEN %></p>

<p>Thanks.</p>`,
        },
      },
      email_confirmation: {
        display: 'Email.template.email_confirmation',
        icon: 'check-square',
        options: {
          from: {
            name: 'Administration Panel',
            email: '<EMAIL>',
          },
          response_email: '',
          object: 'Account confirmation',
          message: `<p>Thank you for registering!</p>

<p>You have to confirm your email address. Please click on the link below.</p>

<p><%= URL %>?confirmation=<%= CODE %></p>

<p>Thanks.</p>`,
        },
      },
    }

    await pluginStore.set({ key: 'email', value })
  }

  // 插件通用配置管理
  const storeSettings = await pluginStore.get({ key: 'advanced' })
  const localSettings = strapi.config.permission.settings || {}
  if (!storeSettings) {
    const value = Object.assign({}, defaultConfig.settings, localSettings)
    await pluginStore.set({ key: 'advanced', value })
  } else if (localSettings.local_setting_first) {
    if (!_.isEqual(storeSettings, localSettings)) {
      const value = Object.assign({}, storeSettings, localSettings)
      await pluginStore.set({ key: 'advanced', value })
    }
  }

  if (process.env.CALLBACK) {
    const syncCallback = require('./syncCallback')
    // 同步微信公众号消息回调请求
    console.time('Users-Permissions Sync Wechat Event Callback Start')
    await syncCallback('prodDev', /^\/wechat\/event/)
    console.timeEnd('Users-Permissions Sync Wechat Event Callback Start')
    // 同步微信支付回调请求
    console.time('Users-Permissions Sync Pay Callback Start')
    await syncCallback('testDev', /^\/pay\/actions\/callbackPayOrder/)
    console.timeEnd('Users-Permissions Sync Pay Callback Start')
    // 同步短信服务回调
    console.time('Users-Permissions Sync SMS Callback Start')
    await syncCallback('testDev', /^\/sms\/actions\/callbackSms/)
    console.timeEnd('Users-Permissions Sync SMS Callback Start')
  }

  if (!process.env.NO_INIT) {
    // 初始化功能视图权限体系
    await permissionInit()
    // 初始化角色权限表
    await strapi.plugins['users-permissions'].services.userspermissions.initialize()
    // // 升级用户
    // await upgradeUser()
  }

  if (!_.get(strapi.plugins['users-permissions'], 'config.jwtSecret')) {
    const jwtSecret = uuid()
    _.set(strapi.plugins['users-permissions'], 'config.jwtSecret', jwtSecret)

    strapi.reload.isWatching = false

    await strapi.fs.writePluginFile(
      'users-permissions',
      'config/jwt.js',
      `module.exports = {\n  jwtSecret: process.env.JWT_SECRET || '${jwtSecret}'\n};`
    )

    strapi.reload.isWatching = true
  }
}
