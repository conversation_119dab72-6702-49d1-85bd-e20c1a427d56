#!/bin/bash

set -x
# 清理已有项目文件
rm -rf ./.git
rm -rf ./.gitmodules
rm -rf ./.idea
# 清理可能冲突本地目录
rm -rf ./packages/strapi
rm -rf ./packages/accel-utils
rm -rf ./plugins/users-permissions
rm -rf ./plugins/log
rm -rf ./plugins/upload

git init
git <NAME_EMAIL>:zhxy/strapi.git packages/strapi
git <NAME_EMAIL>:zhxy/accel-utils.git packages/accel-utils
git <NAME_EMAIL>:zhxy/accel-plugin-users-permissions.git plugins/users-permissions
git <NAME_EMAIL>:zhxy/accel-plugin-log.git plugins/log
git <NAME_EMAIL>:zhxy/accel-plugin-upload.git plugins/upload
git add .
git commit -m "Initial commit"

if [ "$1" ]; then

  # 获取 origin 远程仓库 URL
  ORIGIN_URL=$(git remote get-url origin 2> /dev/null)

  # 如果 origin 远程仓库存在
  if [ $? -eq 0 ]; then
    echo "Current origin URL: $ORIGIN_URL"
    echo "Changing origin to: $1"
    git remote set-url origin "$1"
  else
    echo "No origin remote found."
    echo "Adding origin with URL: $1"
    git remote add origin "$1"
  fi
  # 推送到远程仓库
  git push -u origin master
fi
