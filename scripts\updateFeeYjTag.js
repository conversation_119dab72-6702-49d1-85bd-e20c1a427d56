const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://localhost:27017/testwly-boss-back'
    // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const BOSS_DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://boss:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testboss'
    : '*************************************************************************************************'

const SERVER_URL = (process.env.NODE_ENV !== 'production')
    ? 'http://localhost:3015'
    : 'https://wly-boss-api-lan.iyunxiao.com'

let db, dbClient
let bossDb, bossDbClient
let now = new Date();

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    bossDbClient = await MongoClient.connect(BOSS_DB_URL)
    bossDb = bossDbClient.db()

    try {
        let start = Date.now()
        const lock = await db.collection('db-cache').findOne({
            key: 'updateFeeYjTag-lock',
            type: 'lock',
        });
        if (!lock) {
            await db.collection('db-cache').insertOne({
                key: 'updateFeeYjTag-lock',
                type: 'lock',
                expireAt: moment().add(10, 'm').toDate()
            });

            logger('sync start')
            await main()
            logger('sync end')

            await db.collection('db-cache').deleteOne({
                key: 'updateFeeYjTag-lock',
                type: 'lock',
            });
        }
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {

        await dbClient.close()
        // await schoolDbClient.close()
        await bossDbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('updateFeeYjTag start')
    await updateFeeYjTag()
    logger('updateFeeYjTag end')
}

async function updateFeeYjTag() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'updateFeeYjTag-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            // _id: ObjectId('672b368e0001378402109ee8'),
            // customer_id: '5a41b043000006e627a005a6',
            // customer_name: '横州市百合中学',
            // app_category: 'singleSchool',
            // app_version_type: 3,
            // enabled: 1,
            // is_trial: 0,
            // status: 'active',
            // is_base: 0,
            // begin_time: ISODate('2022-09-30T16:00:00.000Z'),
            // end_time: ISODate('2024-11-12T15:59:59.999Z'),
            // params: [ { name: '访问周期', value: [ '2020-09-14', '2024-11-12' ] } ],
            // source: { type: 'add', data: null },
            // submitter: { user_id: '6662df80000004a70245cd07', name: '张东茹' },
            // remark: 'ZX-240913-174单章暂时开通',
            // create_time: ISODate('2024-11-06T09:27:42.276Z'),
            // update_time: ISODate('2024-11-06T09:27:42.276Z'),
            // _schemaid: 791
            id = '675ffb20000137b409431168';
            await db.collection('db-cache').insertOne({
                key: 'updateFeeYjTag-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 500
        let lastSyncObjectId = id
        // 测试使用
        // const appVersions = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
        //     coll: '@CustomerAppVersion',
        //     filter: { deleted: { $ne: 1 } },
        //     sort: { _id: 1 },
        //     limit: limit
        // }).then(({ data }) => { return data });
        const appVersions = await bossDb.collection('@CustomerAppVersion').find({ deleted: { $ne: 1 }, }).toArray()
        const appVersionMap = {}
        for (const appVersion of appVersions) {
            appVersionMap[appVersion.type] = appVersion
        }
        while (true) {
            const logCond = {
                enabled: 1,
                is_trial: 0,
                // status: 'active',
                _id: { '$gt': ObjectId(lastSyncObjectId) },
            }
            let logs = await bossDb.collection('@CustomerAppUsageConfig').find(logCond).sort({ _id: 1 }).limit(limit).toArray();
            // 测试使用
            // let logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@CustomerAppUsageConfig',
            //     filter: logCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });
            if (logs.length === 0) { break }
            lastSyncObjectId = logs.length > 0 ? logs[logs.length - 1]._id.toString() : lastSyncObjectId

            logs = logs.filter(e => e.app_version_type !== 1
                && appVersionMap[e.app_version_type].product_category === 'saas'
                && new Date(e.end_time) > now)
            for (const log of logs) {
                await axios.post(`${SERVER_URL}/external/customer-services/changeSchoolTag`, {
                    customerId: log.customer_id,
                    yjTag: '付费校',
                });
            }
            await db.collection('db-cache').updateOne({
                key: 'updateFeeYjTag-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            if (logs.length < limit) { break }
        }
    } catch (e) {
        logger(e)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `更新付费校标签 err, 原因:${e}`,
            }
        });
    }
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
