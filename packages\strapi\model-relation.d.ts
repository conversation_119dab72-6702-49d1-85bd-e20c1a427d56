import './model'

interface Association {
  /**
   * 关系字段名称 - key
   */
  alias: string,
  /**
   * 关联类型
   */
  type: 'model' | 'collection',
  /**
   * 关联模块 uid
   */
  targetUid: string,
  /**
   * 关联模块名称 - 单个模式
   * @description 对应 attribute 上的 model collection
   */
  model: string,
  /**
   * 关联模块名称 - 集合模式
   * @description 对应 attribute 上的 model collection
   */
  collection: string,
  /**
   * via 为建立关联关系的模型字段
   */
  via?: string,
  plugin?: string,
  /**
   * 填充关联数据字段列表
   * @default true
   */
  populate?: string[],
  /**
   * 自动填充关联数据详情
   * @default true
   */
  autoPopulate: boolean,

  /**
   * 关系模式
   */
  nature: |
    // 基础模式 one <-> many
    'oneWay' | 'manyWay'
    | 'oneToOne' | 'oneToMany' | 'manyToOne' | 'manyToMany'
    // 动态区域模式 one many  <-> oneMorph manyMorph
    | 'oneToOneMorph' | 'oneMorphToOne'
    | 'manyToOneMorph' | 'oneMorphToMany'
    | 'manyToManyMorph' | 'manyMorphToMany'
    | 'manyMorphToOne' | 'oneToManyMorph'

  // 基础模式 - 关联对象的 via.dominant 为 true 则其为 false
  dominant?: boolean,

  // ???
  filter?: { [key: string]: any },
  associationModel?: Model
}
