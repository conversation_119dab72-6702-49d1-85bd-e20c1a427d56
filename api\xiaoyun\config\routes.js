const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/xiaoyun-quns',
      controller: 'xiaoyun-qun'
    }),
    {
      method: 'GET',
      path: '/qun/user/info',
      handler: 'qun.userInfo',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/list',
      handler: 'qun.userQunList',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/list_all',
      handler: 'qun.qunList',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/tags',
      handler: 'qun.getQunTags',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/tags_all',
      handler: 'qun.getAllQunTags',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/extra_info',
      handler: 'qun.getQunExtraInfo',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/extra_info_list',
      handler: 'qun.getQunExtraInfoList',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/wx_users',
      handler: 'qun.getWxUsersByRole',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/qr_code/:id',
      handler: 'qun.qrCode',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/info',
      handler: 'qun.qunInfo',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/customer',
      handler: 'qun.getCustomerByQid',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/add_admin',
      handler: 'qun-manager.addAdmin',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/del_admin',
      handler: 'qun-manager.delAdmin',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/change_name',
      handler: 'qun-manager.changeName',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/notice',
      handler: 'qun-manager.notice',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/config_mod_name',
      handler: 'qun-manager.configModName',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/config_invite',
      handler: 'qun-manager.configInvite',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/invite',
      handler: 'qun-manager.inviteUser',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/kick',
      handler: 'qun-manager.kickUser',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/transfer_owner',
      handler: 'qun-manager.transferOwner',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/qun/manager/send_msg',
      handler: 'qun-manager.sendMsg',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun-tags/xiaoyun',
      handler: 'qun-tag.getXiaoyunTags',
      config: {
        policies: [], prefix: '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/qun-checks',
      controller: 'qun-check'
    }),
    ...createDefaultRoutes({
      basePath: '/qun-tags',
      controller: 'qun-tag'
    }),
    ...createDefaultRoutes({
      basePath: '/qun-users',
      controller: 'qun-user'
    }),
    ...createDefaultRoutes({
      basePath: '/qun-action-logs',
      controller: 'qun-action-log'
    }),
    ...createDefaultRoutes({
      basePath: '/qun-statistics',
      controller: 'qun-statistic'
    }),
    {
      method: 'GET',
      path: '/qun/user_statistics',
      handler: 'qun-statistic.userStatistic',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/qun/team_statistics',
      handler: 'qun-statistic.teamStatistic',
      config: {
        policies: [], prefix: '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/qun-qrcodes',
      controller: 'qun-qrcode'
    }),
    ...createDefaultRoutes({
      basePath: '/qun-robot-stats',
      controller: 'qun-robot-stat'
    }),
    ...createDefaultRoutes({
      basePath: '/no-robot-quns',
      controller: 'no-robot-qun'
    }),
    {
      method: 'GET',
      path: '/qun_robot/no_robot',
      handler: 'qun-robot-stat.getNoRobotQun',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      'method': 'GET',
      'path': '/qx-user/avatar/:id',
      'handler': 'qun.getQxUserAvatar',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/qx-user/avatar/thumb/:id',
      'handler': 'qun.getQxUserAvatarThumb',
      'config': {
        'policies': [], 'prefix': '',
      }
    }
  ]
}
