module.exports = {
  kind: 'collectionType',
  collectionName: strapi.config.server.mingdaoConfig.biddingS2ProjectId,
  connection: 'mingdao',
  info: {
    name: 'BiddingS2Project',
    label: '投标项目',
    description: '',
  },
  options: {},
  pluginOptions: {},
  attributes: {
    // 项目名称
    title: {
      ref: '678fbba69e13a09bfffe931b'
    },
    // 招标项目
    biddingS1Project: {
      ref: '678fbbbc9e13a09bfffe9326'
    },
    // 项目负责人
    owner: {
      ref: '678fbf379e13a09bfffe938d'
    },
    // 项目进展
    progress: {
      ref: '678fc1509e13a09bfffe93f5'
    },
    // 项目编号
    code: {
      ref: '678fc1e79e13a09bfffe93f9'
    }
  }
}
