'use strict';

/**
 * Converts the standard Strapi REST query params to a moe usable format for querying
 * You can read more here: https://strapi.io/documentation/developer-docs/latest/developer-resources/content-api/content-api.html#filters
 */

const _ = require('lodash');
const dayjs = require('dayjs')
const quarterOfYear = require("dayjs/plugin/quarterOfYear")
dayjs.extend(quarterOfYear)
const {
  constants: { DP_PUB_STATES },
} = require('./content-types');
const { isString } = require('lodash')

const BOOLEAN_OPERATORS = ['or', 'and'];
const QUERY_OPERATORS = ['_where', '_or', '_and'];

/**
 * Global converter
 * @param {Object} params
 * @param defaults
 */
const convertRestQueryParams = (params = {}, defaults = {}) => {
  if (typeof params !== 'object' || params === null) {
    throw new Error(
      `convertRestQueryParams expected an object got ${params === null ? 'null' : typeof params}`
    );
  }

  let finalParams = Object.assign({ start: 0, limit: 100 }, defaults);

  if (Object.keys(params).length === 0) {
    return finalParams;
  }

  if (_.has(params, '_sort')) {
    Object.assign(finalParams, convertSortQueryParams(params._sort));
  }

  if (_.has(params, '_start')) {
    Object.assign(finalParams, convertStartQueryParams(params._start));
  }

  if (_.has(params, '_limit')) {
    Object.assign(finalParams, convertLimitQueryParams(params._limit));
  }

  if (_.has(params, '_publicationState')) {
    Object.assign(finalParams, convertPublicationStateParams(params._publicationState));
  }

  if (_.has(params, '_projection')) {
    Object.assign(finalParams, {
      projection: _.get(params, '_projection'),
    })
  }

  const whereParams = convertExtraRootParams(
    _.omit(params, ['_sort', '_start', '_limit', '_where', '_publicationState', '_projection'])
  );

  const whereClauses = [];

  if (_.keys(whereParams).length > 0) {
    whereClauses.push(...convertWhereParams(whereParams));
  }

  if (_.has(params, '_where')) {
    whereClauses.push(...convertWhereParams(params._where));
  }

  Object.assign(finalParams, { where: whereClauses });

  return finalParams;
};

/**
 * Convert params prefixed with _ by removing the prefix after we have handle the internal params
 * NOTE: This is only a temporary patch for v3 to handle extra params coming from plugins
 * @param {object} params
 * @returns {object}
 */
const convertExtraRootParams = params => {
  return Object.entries(params).reduce((acc, [key, value]) => {
    if (_.startsWith(key, '_') && !QUERY_OPERATORS.includes(key)) {
      acc[key.slice(1)] = value;
    } else {
      acc[key] = value;
    }

    return acc;
  }, {});
};

/**
 * Sort query parser
 * @param {string} sortQuery - ex: id:asc,price:desc
 */
const convertSortQueryParams = sortQuery => {
  if (typeof sortQuery !== 'string') {
    throw new Error(`convertSortQueryParams expected a string, got ${typeof sortQuery}`);
  }

  const sortKeys = [];

  sortQuery.split(',').forEach(part => {
    // split field and order param with default order to ascending
    const [field, order = 'asc'] = part.split(':');

    if (field.length === 0) {
      throw new Error('Field cannot be empty');
    }

    if (!['asc', 'desc'].includes(order.toLocaleLowerCase())) {
      throw new Error('order can only be one of asc|desc|ASC|DESC');
    }

    sortKeys.push({ field, order: order.toLowerCase() });
  });

  return {
    sort: sortKeys,
  };
};

/**
 * Start query parser
 * @param {string} startQuery - ex: id:asc,price:desc
 */
const convertStartQueryParams = startQuery => {
  const startAsANumber = _.toNumber(startQuery);

  if (!_.isInteger(startAsANumber) || startAsANumber < 0) {
    throw new Error(`convertStartQueryParams expected a positive integer got ${startAsANumber}`);
  }

  return {
    start: startAsANumber,
  };
};

/**
 * Limit query parser
 * @param {string} limitQuery - ex: id:asc,price:desc
 */
const convertLimitQueryParams = limitQuery => {
  const limitAsANumber = _.toNumber(limitQuery);

  if (!_.isInteger(limitAsANumber) || (limitAsANumber !== -1 && limitAsANumber < 0)) {
    throw new Error(`convertLimitQueryParams expected a positive integer got ${limitAsANumber}`);
  }

  return {
    limit: limitAsANumber,
  };
};

/**
 * publicationState query parser
 * @param {string} publicationState - eg: 'live', 'preview'
 */
const convertPublicationStateParams = publicationState => {
  if (!DP_PUB_STATES.includes(publicationState)) {
    throw new Error(
      `convertPublicationStateParams expected a value from: ${DP_PUB_STATES.join(
        ', '
      )}. Got ${publicationState} instead`
    );
  }

  return { publicationState };
};

// List of all the possible filters
const VALID_REST_OPERATORS = [
  'eq',
  'ne',
  'in',
  'nin',
  'contains',
  'ncontains',
  'containss',
  'ncontainss',
  'lt',
  'lte',
  'gt',
  'gte',
  'null',
  'dateRange'
];

/**
 * Parse where params
 */
const convertWhereParams = whereParams => {
  let finalWhere = [];

  if (Array.isArray(whereParams)) {
    return whereParams.reduce((acc, whereParam) => {
      return acc.concat(convertWhereParams(whereParam));
    }, []);
  }

  Object.keys(whereParams).forEach(whereClause => {
    const { field, operator = 'eq', value } = convertWhereClause(
      whereClause,
      whereParams[whereClause]
    );

    // Extend Operator - dateRange
    if (operator === 'dateRange') {
      const now = dayjs()
      let startDate, endDate
      const [rangeKey, rangeOperator, rangeValue] = value.split('_')
      if (isString(rangeValue) && isNaN(+rangeValue)) {
        throw new Error(`Invalid range value: ${rangeValue} ${value}`)
      }
      switch (rangeKey) {
        case 'Today':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.startOf('day')
              break
            case 'lte':
              endDate = now.endOf('day')
              break
            case 'gt':
              startDate = now.endOf('day')
              break
            case 'gte':
              startDate = now.startOf('day')
              break
            default:
              startDate = now.startOf('day')
              endDate = now.endOf('day')
          }
          break
        case 'Yesterday':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(1, 'day').startOf('day')
              break
            case 'lte':
              endDate = now.subtract(1, 'day').endOf('day')
              break
            case 'gt':
              startDate = now.subtract(1, 'day').endOf('day')
              break
            case 'gte':
              startDate = now.subtract(1, 'day').startOf('day')
              break
            default:
              startDate = now.subtract(1, 'day').startOf('day')
              endDate = now.subtract(1, 'day').endOf('day')
          }
          break
        case 'Tomorrow':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(1, 'day').startOf('day')
              break
            case 'lte':
              endDate = now.add(1, 'day').endOf('day')
              break
            case 'gt':
              startDate = now.add(1, 'day').endOf('day')
              break
            case 'gte':
              startDate = now.add(1, 'day').startOf('day')
              break
            default:
              startDate = now.add(1, 'day').startOf('day')
              endDate = now.add(1, 'day').endOf('day')
          }
          break
        case 'ThisWeek':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.startOf('week')
              break
            case 'lte':
              endDate = now.endOf('week')
              break
            case 'gt':
              startDate = now.endOf('week')
              break
            case 'gte':
              startDate = now.startOf('week')
              break
            default:
              startDate = now.startOf('week')
              endDate = now.endOf('week')
          }
          break
        case 'LastWeek':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(1, 'week').startOf('week')
              break
            case 'lte':
              endDate = now.subtract(1, 'week').endOf('week')
              break
            case 'gt':
              startDate = now.subtract(1, 'week').endOf('week')
              break
            case 'gte':
              startDate = now.subtract(1, 'week').startOf('week')
              break
            default:
              startDate = now.subtract(1, 'week').startOf('week')
              endDate = now.subtract(1, 'week').endOf('week')
          }
          break
        case 'NextWeek':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(1, 'week').startOf('week')
              break
            case 'lte':
              endDate = now.add(1, 'week').endOf('week')
              break
            case 'gt':
              startDate = now.add(1, 'week').endOf('week')
              break
            case 'gte':
              startDate = now.add(1, 'week').startOf('week')
              break
            default:
              startDate = now.add(1, 'week').startOf('week')
              endDate = now.add(1, 'week').endOf('week')
          }
          break
        case 'ThisMonth':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.startOf('month')
              break
            case 'lte':
              endDate = now.endOf('month')
              break
            case 'gt':
              startDate = now.endOf('month')
              break
            case 'gte':
              startDate = now.startOf('month')
              break
            default:
              startDate = now.startOf('month')
              endDate = now.endOf('month')
          }
          break
        case 'LastMonth':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(1, 'month').startOf('month')
              break
            case 'lte':
              endDate = now.subtract(1, 'month').endOf('month')
              break
            case 'gt':
              startDate = now.subtract(1, 'month').endOf('month')
              break
            case 'gte':
              startDate = now.subtract(1, 'month').startOf('month')
              break
            default:
              startDate = now.subtract(1, 'month').startOf('month')
              endDate = now.subtract(1, 'month').endOf('month')
          }
          break
        case 'NextMonth':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(1, 'month').startOf('month')
              break
            case 'lte':
              endDate = now.add(1, 'month').endOf('month')
              break
            case 'gt':
              startDate = now.add(1, 'month').endOf('month')
              break
            case 'gte':
              startDate = now.add(1, 'month').startOf('month')
              break
            default:
              startDate = now.add(1, 'month').startOf('month')
              endDate = now.add(1, 'month').endOf('month')
          }
          break
        case 'LastEnum':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(+rangeValue, 'day').startOf('day')
              break
            case 'lte':
              endDate = now.subtract(+rangeValue, 'day').endOf('day')
              break
            case 'gt':
              startDate = now.subtract(+rangeValue, 'day').endOf('day')
              break
            case 'gte':
              startDate = now.subtract(+rangeValue, 'day').startOf('day')
              break
            default:
              startDate = now.subtract(+rangeValue, 'day').startOf('day')
              endDate = now.subtract(1, 'day').endOf('day')
          }
          break
        case 'NextEnum':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(+rangeValue, 'day').startOf('day')
              break
            case 'lte':
              endDate = now.add(+rangeValue, 'day').endOf('day')
              break
            case 'gt':
              startDate = now.add(+rangeValue, 'day').endOf('day')
              break
            case 'gte':
              startDate = now.add(+rangeValue, 'day').startOf('day')
              break
            default:
              startDate = now.add(1, 'day').startOf('day')
              endDate = now.add(+rangeValue, 'day').endOf('day')
          }
          break
        case 'ThisQuarter':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.startOf('quarter')
              break
            case 'lte':
              endDate = now.endOf('quarter')
              break
            case 'gt':
              startDate = now.endOf('quarter')
              break
            case 'gte':
              startDate = now.startOf('quarter')
              break
            default:
              startDate = now.startOf('quarter')
              endDate = now.endOf('quarter')
          }
          break
        case 'LastQuarter':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(1, 'quarter').startOf('quarter')
              break
            case 'lte':
              endDate = now.subtract(1, 'quarter').endOf('quarter')
              break
            case 'gt':
              startDate = now.subtract(1, 'quarter').endOf('quarter')
              break
            case 'gte':
              startDate = now.subtract(1, 'quarter').startOf('quarter')
              break
            default:
              startDate = now.subtract(1, 'quarter').startOf('quarter')
              endDate = now.subtract(1, 'quarter').endOf('quarter')
          }
          break
        case 'NextQuarter':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(1, 'quarter').startOf('quarter')
              break
            case 'lte':
              endDate = now.add(1, 'quarter').endOf('quarter')
              break
            case 'gt':
              startDate = now.add(1, 'quarter').endOf('quarter')
              break
            case 'gte':
              startDate = now.add(1, 'quarter').startOf('quarter')
              break
            default:
              startDate = now.add(1, 'quarter').startOf('quarter')
              endDate = now.add(1, 'quarter').endOf('quarter')
          }
          break
        case 'ThisYear':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.startOf('year')
              break
            case 'lte':
              endDate = now.endOf('year')
              break
            case 'gt':
              startDate = now.endOf('year')
              break
            case 'gte':
              startDate = now.startOf('year')
              break
            default:
              startDate = now.startOf('year')
              endDate = now.endOf('year')
          }
          break
        case 'LastYear':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(1, 'year').startOf('year')
              break
            case 'lte':
              endDate = now.subtract(1, 'year').endOf('year')
              break
            case 'gt':
              startDate = now.subtract(1, 'year').endOf('year')
              break
            case 'gte':
              startDate = now.subtract(1, 'year').startOf('year')
              break
            default:
              startDate = now.subtract(1, 'year').startOf('year')
              endDate = now.subtract(1, 'year').endOf('year')
          }
          break
        case 'NextYear':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(1, 'year').startOf('year')
              break
            case 'lte':
              endDate = now.add(1, 'year').endOf('year')
              break
            case 'gt':
              startDate = now.add(1, 'year').endOf('year')
              break
            case 'gte':
              startDate = now.add(1, 'year').startOf('year')
              break
            default:
              startDate = now.add(1, 'year').startOf('year')
              endDate = now.add(1, 'year').endOf('year')
          }
          break
        case 'Last7Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(7, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.subtract(7, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.subtract(7, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.subtract(7, 'days').startOf('day')
              break
            default:
              startDate = now.subtract(7, 'days')
              endDate = now
          }
          break
        case 'Last14Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(14, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.subtract(14, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.subtract(14, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.subtract(14, 'days').startOf('day')
              break
            default:
              startDate = now.subtract(14, 'days')
              endDate = now
          }
          break
        case 'Last30Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.subtract(30, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.subtract(30, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.subtract(30, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.subtract(30, 'days').startOf('day')
              break
            default:
              startDate = now.subtract(30, 'days')
              endDate = now
          }
          break
        case 'Next7Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(7, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.add(7, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.add(7, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.add(7, 'days').startOf('day')
              break
            default:
              startDate = now
              endDate = now.add(7, 'days')
          }
          break
        case 'Next14Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(14, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.add(14, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.add(14, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.add(14, 'days').startOf('day')
              break
            default:
              startDate = now
              endDate = now.add(14, 'days')
          }
          break
        case 'Next30Day':
          switch (rangeOperator) {
            case 'lt':
              endDate = now.add(30, 'days').startOf('day')
              break
            case 'lte':
              endDate = now.add(30, 'days').endOf('day')
              break
            case 'gt':
              startDate = now.add(30, 'days').endOf('day')
              break
            case 'gte':
              startDate = now.add(30, 'days').startOf('day')
              break
            default:
          }
          startDate = now
          endDate = now.add(30, 'days')
          break
        default:
          // ...
          console.error('Unsupported rangeKey')
      }
      if (startDate && startDate.isValid()) {
        let operator = 'gte'
        if (['gt', 'gte'].includes(rangeOperator)) {
          operator = rangeOperator
        }
        finalWhere.push({
          field: field,
          operator: operator,
          value: startDate.toISOString(),
        })
      }
      if (endDate && endDate.isValid()) {
        let operator = 'lte'
        if (['lt', 'lte'].includes(rangeOperator)) {
          operator = rangeOperator
        }
        finalWhere.push({
          field: field,
          operator: operator,
          value: endDate.toISOString(),
        })
      }
      return
    }

    finalWhere.push({
      field,
      operator,
      value,
    });
  });

  return finalWhere;
};

/**
 * Parse single where param
 * @param {string} whereClause - Any possible where clause e.g: id_ne text_ncontains
 * @param {string} value - the value of the where clause e.g id_ne=value
 */
const convertWhereClause = (whereClause, value) => {
  const separatorIndex = whereClause.lastIndexOf('_');

  // eq operator
  if (separatorIndex === -1) {
    return { field: whereClause, value };
  }

  // split field and operator
  const field = whereClause.substring(0, separatorIndex);
  const operator = whereClause.slice(separatorIndex + 1);

  if (BOOLEAN_OPERATORS.includes(operator) && field === '') {
    return { field: null, operator, value: [].concat(value).map(convertWhereParams) };
  }

  // the field as underscores
  if (!VALID_REST_OPERATORS.includes(operator)) {
    return { field: whereClause, value };
  }

  return { field, operator, value };
};

module.exports = {
  convertRestQueryParams,
  VALID_REST_OPERATORS,
  QUERY_OPERATORS,
};
