// Prototypes
const CurdModel = require('./prototypes/curdModel')
const CurdService = require('./prototypes/curdService')
const CurdRouter = require('./prototypes/curdRouter')
const UserCurdRouter = require('./prototypes/userCurdRouter')
const BranchCurdRouter = require('./prototypes/branchCurdRouter')
const DomainCurdRouter = require('./prototypes/domainCurdRouter')
const PublicCurdRouter = require('./prototypes/publicCurdRouter')

const strapiUtils = require('./lib')

const { parseCtxData, parseCtxUserAndBranch, dataFieldProcess } = require('./collection-utils')
const {
  jwtGen,
  jwtVerify,
  passwordBcrypt,
  passwordCompare,
  createRandomToken,
  sha1,
} = require('./verify')

const { createDefaultRoutes, createDefaultPermissions } = require('./helper')

module.exports = {
  // Prototypes
  CurdModel,
  CurdService,
  CurdRouter,
  UserCurdRouter,
  BranchCurdRouter,
  DomainCurdRouter,
  PublicCurdRouter,
  // Utils
  parseCtxData,
  parseCtxUserAndBranch,
  dataFieldProcess,
  // Verify
  jwtGen,
  jwtVerify,
  passwordBcrypt,
  passwordCompare,
  createRandomToken,
  sha1,
  // Route
  createDefaultRoutes,
  // Permission
  createDefaultPermissions,
  // Strapi Utils
  ...strapiUtils,
}
