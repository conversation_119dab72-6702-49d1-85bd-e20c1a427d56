'use strict';
const _ = require('lodash');

module.exports = async (validData, existingEntry, attributes) => {
    for (const key in validData) {
        if (isEqual(validData[key], existingEntry[key], attributes[key], key)) {
            delete validData[key];
        }
    }
    if (Object.keys(validData).length === 1 && (validData.hasOwnProperty('updatedAt') || validData.hasOwnProperty('utime'))) {
        delete validData.updatedAt;
        delete validData.utime;
    }
    function isEqual(value1, value2, attribute, key) {
        // console.log('---- isEqual', value1?.toString(), value2?.toString());
        const attributeType = attribute?.type;
        // 不更新创建时间
        if (key === 'createdAt' || key === 'ctime') return true
        // 如果两个值相等，则返回true
        if (JSON.stringify(value1) === JSON.stringify(value2)) return true
        // '' null undefined 都视为相同
        if ((_.isNil(value1) || value1 === '') && (_.isNil(value2) || value2 === '')) return true

        const type1 = typeof value1
        // const type2 = typeof value2

        // 模型时间类型转为时间戳比较
        if (attributeType === 'datetime') return new Date(value1 || null)?.getTime() == new Date(value2 || null)?.getTime()
        // 明道云关联字段和子表对比
        if (attributeType === 'relation') {
            return value1?.map(item => item?.id || item)?.sort()?.toString() === value2?.map(item => item?.id || item)?.sort()?.toString()
        }
        // if (attributeType === 'component') {
        //     return value1?.map(item => item.id || item)?.sort()?.toString() === value2?.map(item => item.id || item)?.sort()?.toString()
        // }

        // 对象/数组 值类型转为字符串比较 [] {} null undefined 都视为相同
        if (type1 === 'object' && _.isEmpty(value1) && _.isEmpty(value2)) return true
        // if (type1 === 'object' && JSON.stringify(value1) === JSON.stringify(value2)) return true

        // 未来云关联字段
        // if (attribute?.collection) {
        //     return value1?.map(item => item.id || item?.toString())?.sort()?.toString() === value2?.map(item => item?.toString())?.sort()?.toString()
        // }
        if (attribute?.model) {
            return (value1?.id || value1?.toString()) === value2?.toString()
        }

        // // 如果值类型不同，则返回false
        // if (type1 !== type2) return false
        return false
    }
};
