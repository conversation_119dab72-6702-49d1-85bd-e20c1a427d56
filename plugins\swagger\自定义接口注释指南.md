# 自定义接口 Swagger 注释指南

## 概述

本指南用于帮助 AI 自动生成符合项目规范的 @swagger 注释。对于使用 `createDefaultRoutes` 创建的标准 CRUD 接口，系统会自动生成文档，无需手动添加注释。

## 访问文档

启动服务后访问：`http://localhost:3015/api-docs`

## 自定义接口注释模板

### 基础模板

```javascript
/**
 * @swagger
 * /api/custom-endpoint:
 *   method:
 *     tags:
 *       - 标签名称
 *     summary: 简短描述
 *     description: 详细描述
 *     parameters:
 *       - in: query
 *         name: param1
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数说明
 *     responses:
 *       200:
 *         description: 成功
 */
async function customEndpoint(ctx) {
  // 实现逻辑
}
```

### 带请求体的接口

```javascript
/**
 * @swagger
 * /api/custom-create:
 *   post:
 *     tags:
 *       - 标签名称
 *     summary: 创建资源
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - type
 *             properties:
 *               name:
 *                 type: string
 *                 description: 名称
 *                 example: "示例名称"
 *               type:
 *                 type: string
 *                 enum: ['type1', 'type2']
 *                 description: 类型
 *     responses:
 *       200:
 *         description: 创建成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                 message:
 *                   type: string
 */
```

### 文件上传接口

```javascript
/**
 * @swagger
 * /api/upload:
 *   post:
 *     tags:
 *       - 文件管理
 *     summary: 上传文件
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 文件
 *               description:
 *                 type: string
 *                 description: 文件描述
 *     responses:
 *       200:
 *         description: 上传成功
 */
```

### 带认证的接口

```javascript
/**
 * @swagger
 * /api/user/profile:
 *   get:
 *     tags:
 *       - 用户管理
 *     summary: 获取用户信息
 *     security:
 *       - bearerAuth: []    # JWT 认证
 *     responses:
 *       200:
 *         description: 成功
 *       401:
 *         description: 未授权
 */
```

### 开放接口（无需认证）

```javascript
/**
 * @swagger
 * /open/api/public-data:
 *   get:
 *     tags:
 *       - 开放接口
 *     summary: 获取公开数据
 *     security: []    # 无需认证
 *     responses:
 *       200:
 *         description: 成功
 */
```

### Access Key 认证接口

```javascript
/**
 * @swagger
 * /external/api/webhook:
 *   post:
 *     tags:
 *       - 外部接口
 *     summary: Webhook 回调
 *     security:
 *       - accessKey: []    # access-key 认证
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 成功
 */
```

## 标签使用规则

1. **使用中文标签**：如 "用户管理"、"订单管理"、"产品管理" 等
2. **标签已预定义**：查看 `plugins/swagger/services/tags.js` 中的 `defaultTags`
3. **特殊路径标签**：
   - `/open/*` 路径使用 "开放接口" 标签
   - `/external/*` 路径使用 "外部接口" 标签

## 参数类型

### 查询参数 (query)
```yaml
parameters:
  - in: query
    name: page
    schema:
      type: integer
      default: 1
    description: 页码
```

### 路径参数 (path)
```yaml
parameters:
  - in: path
    name: id
    required: true
    schema:
      type: string
    description: 资源ID
```

### 请求头参数 (header)
```yaml
parameters:
  - in: header
    name: X-Request-ID
    schema:
      type: string
    description: 请求ID
```

## 数据类型

- `string` - 字符串
- `integer` - 整数
- `number` - 数字（含小数）
- `boolean` - 布尔值
- `array` - 数组
- `object` - 对象

### 格式化类型
- `format: date` - 日期 (2024-01-01)
- `format: date-time` - 日期时间
- `format: email` - 邮箱
- `format: password` - 密码
- `format: binary` - 二进制文件

## 注意事项

1. **仅为自定义接口添加注释**：标准 CRUD 接口会自动生成文档
2. **路径格式**：使用 `{id}` 而不是 `:id`
3. **必填字段**：在 `required` 数组中列出
4. **示例值**：使用 `example` 提供示例
5. **枚举值**：使用 `enum` 限定可选值

## 外部配置扩展

如需扩展配置，可在项目根目录创建：
- `config/swagger.js` - 扩展基础配置
- `config/swagger-tags.js` - 扩展标签定义
- `config/swagger-extensions.yaml` - 扩展插件文档