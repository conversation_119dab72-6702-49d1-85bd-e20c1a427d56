/**
 * Swagger 配置文件
 * 支持外部配置覆盖
 */

const fs = require('fs');
const path = require('path');

// 缓存外部配置
let cachedExternalConfig = null;
let cachedSwaggerConfig = null;

/**
 * 内置的默认配置
 */
const defaultDefinition = {
  openapi: '3.0.0',
  info: {
    title: 'API Documentation',
    version: '1.0.0',
    description: 'API documentation generated by Swagger'
  },
  tags: [
    // 静态标签定义 - 这些标签会始终显示在文档中
    // 大部分标签通过外部配置动态生成
  ],
  servers: [
    {
      url: `http://localhost:${process.env.NODE_PORT || 1337}`,
      description: 'Development server'
    }
  ],
  components: {
    securitySchemes: {
      bearerAuth: {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        description: 'JWT 认证令牌'
      }
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          code: {
            type: 'integer',
            description: '错误代码'
          },
          message: {
            type: 'string',
            description: '错误信息'
          }
        }
      },
      Pagination: {
        type: 'object',
        properties: {
          page: {
            type: 'integer',
            description: '当前页码',
            example: 1
          },
          pageSize: {
            type: 'integer',
            description: '每页数量',
            example: 10
          },
          total: {
            type: 'integer',
            description: '总数',
            example: 100
          }
        }
      }
    }
  },
  security: [
    {
      bearerAuth: []
    }
  ]
};

/**
 * 默认的 API 扫描路径
 */
const defaultApis = [
  './api/**/config/routes.js',
  './api/**/config/route.js',
  './api/**/controllers/*.js',
  './plugins/swagger/services/extensions.yaml'
];

/**
 * 深度合并两个对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
function deepMerge(target, source) {
  const output = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
          output[key] = deepMerge(target[key], source[key]);
        } else {
          output[key] = source[key];
        }
      } else {
        output[key] = source[key];
      }
    }
  }

  return output;
}

/**
 * 加载外部配置
 * @returns {Object} 外部配置对象
 */
function loadExternalConfig() {
  const externalConfigPaths = [
    // 项目根目录的配置文件
    path.join(process.cwd(), 'config', 'swagger.js'),
    // 项目根目录的 JSON 配置
    path.join(process.cwd(), 'config', 'swagger.json')
  ];

  for (const configPath of externalConfigPaths) {
    try {
      if (fs.existsSync(configPath)) {
        const ext = path.extname(configPath);
        let externalConfig;

        if (ext === '.js') {
          // 清除 require 缓存以支持热重载
          delete require.cache[configPath];
          externalConfig = require(configPath);
        } else if (ext === '.json') {
          const content = fs.readFileSync(configPath, 'utf8');
          externalConfig = JSON.parse(content);
        }

        console.log(`已加载外部 Swagger 配置: ${configPath}`);
        return externalConfig;
      }
    } catch (error) {
      console.error(`加载外部配置失败 (${configPath}):`, error);
    }
  }

  return {};
}

/**
 * 获取最终的 Swagger 配置（带缓存）
 * @param {boolean} forceReload 是否强制重新加载
 * @returns {Object} 合并后的配置
 */
function getSwaggerConfig(forceReload = false) {
  // 如果已有缓存且不需要强制重载，直接返回缓存
  if (cachedSwaggerConfig && !forceReload) {
    return cachedSwaggerConfig;
  }

  // 如果外部配置已缓存且不需要强制重载，使用缓存的外部配置
  const externalConfig = (cachedExternalConfig && !forceReload)
    ? cachedExternalConfig
    : (cachedExternalConfig = loadExternalConfig());

  // 深度合并 definition
  const definition = deepMerge(defaultDefinition, externalConfig.definition || {});

  // 合并 apis 数组
  let apis = [...defaultApis];
  if (externalConfig.apis && Array.isArray(externalConfig.apis)) {
    // 如果外部配置指定了 replaceApis: true，则完全替换
    if (externalConfig.replaceApis) {
      apis = externalConfig.apis;
    } else {
      // 否则追加外部配置的 apis
      apis = [...apis, ...externalConfig.apis];
    }
  }

  // 缓存合并后的配置
  cachedSwaggerConfig = {
    definition,
    apis
  };

  return cachedSwaggerConfig;
}

// 导出配置
module.exports = getSwaggerConfig();

// 同时导出工具函数供其他模块使用
module.exports.getSwaggerConfig = getSwaggerConfig;
module.exports.loadExternalConfig = loadExternalConfig;
module.exports.defaultDefinition = defaultDefinition;
module.exports.defaultApis = defaultApis;
