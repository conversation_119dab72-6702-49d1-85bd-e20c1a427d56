const { Curd<PERSON>outer } = require('accel-utils')
const { getQueryKeyChange, getManagerBySchoolId } = require('../services/customer-service')
const customerHfsModel = require('../models/customer-service-hfs')
const axios = require('axios')
const _ = require('lodash')
const { ObjectId } = require('mongodb')
const moment = require('moment')

const curdRouter = new (class extends CurdRouter {

  _appendBaseFilter (ctx) {
    const query = ctx.request.query
    query.deleted_ne = 'true'

    getQueryKeyChange(query)
  }

  async find (ctx) {
    this._appendBaseFilter(ctx)
    const data = await super.find(ctx)
    const filterFields = ['id', 'createdAt', 'updatedAt', ...Object.keys(customerHfsModel.attributes)]
    return data.map(item => {
      Object.keys(item).forEach(key => {
        if (!filterFields.includes(key)) {
          delete item[key]
        }
      })
      return item
    })
  }

  async count (ctx) {
    this._appendBaseFilter(ctx)
    return await super.count(ctx)
  }

})('customer-service-hfs')

async function openAppUsages (ctx) {
  const { schoolId, appUsages, remark, platform = 'hfsjzjx' } = ctx.request.body

  // 参数验证
  if (!schoolId || +schoolId <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：schoolId 无效')
  }

  if (!appUsages || !Array.isArray(appUsages) || appUsages.length === 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：appUsages 必须是非空数组')
  }

  // 查询学校信息
  const customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId })
  if (!customerService) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '未找到学校信息')
  }

  const productInfos = []
  let minStartTime = null
  let maxEndTime = null

  // 构建产品信息列表
  for (const appUsage of appUsages) {
    if (!appUsage.usage || !appUsage.usage.type) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：usage.type 不能为空')
    }

    const usage = appUsage.usage

    // 验证日期格式
    if (!usage.beginTime || !moment(usage.beginTime).isValid()) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：beginTime 格式不正确')
    }

    if (!usage.endTime || !moment(usage.endTime).isValid()) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：endTime 格式不正确')
    }

    // 记录最早开始时间和最晚结束时间
    const beginMoment = moment(usage.beginTime)
    const endMoment = moment(usage.endTime)

    if (!minStartTime || beginMoment.isBefore(minStartTime)) {
      minStartTime = beginMoment
    }

    if (!maxEndTime || endMoment.isAfter(maxEndTime)) {
      maxEndTime = endMoment
    }

    productInfos.push({
      customerId: customerService.customerId,
      usage: {
        type: usage.type,
        enabled: usage.enabled !== undefined ? usage.enabled : 1,
        isTrial: usage.isTrial !== undefined ? usage.isTrial : 1,
        beginTime: usage.beginTime,
        endTime: usage.endTime,
        params: usage.params || []
      },
      enabled: 1,
      remark: appUsage.remark || remark || '申请开通服务',
      handler: {
        user_id: ctx.state.user.id,
        name: ctx.state.user.username
      },
      source: {
        type: 'order',
        data: {
          order_id: '',
          order_no: ''
        }
      }
    })
  }

  // 调用 Boss API 开通服务
  const errorList = []
  const successList = []

  for (const product of productInfos) {
    try {
      const response = await axios.post(
        `${strapi.config.server.bossApi.url}/external/api/customer/app_usage/update`,
        product,
        {
          headers: {
            'apikey': strapi.config.server.bossApi.apikey,
            'token': strapi.config.server.bossApi.token,
          }
        }
      )

      console.log('产品开通请求:', product, '响应:', response.data)

      if (response.status === 200 && response.data && response.data.code === 1) {
        successList.push({
          type: product.usage.type,
          message: '开通成功'
        })
      } else {
        errorList.push({
          type: product.usage.type,
          message: response.data?.message || '开通失败'
        })
      }
    } catch (error) {
      console.error('产品开通异常:', product.usage.type, error.message)
      errorList.push({
        type: product.usage.type,
        message: error.message || '开通异常'
      })
    }
  }

  // 如果有成功的产品，更新本地记录
  if (successList.length > 0 && minStartTime && maxEndTime) {
    try {
      // 当 platform 为 single 或空时，代表开通单品，不更新 siteType
      const updateData = {}
      if (platform && platform !== 'single') {
        updateData.siteType = platform === 'yxzhixue' ? 'yxzhixue' : 'hfs'
      }
      // siteStartTime: minStartTime.format('YYYY-MM-DD'),
      // siteEndTime: maxEndTime.format('YYYY-MM-DD')

      // 只有当有需要更新的字段时才执行更新
      if (Object.keys(updateData).length > 0) {
        await strapi.query('customer-service-hfs').update(
          { id: customerService.id },
          updateData
        )
      }
    } catch (error) {
      console.error('更新本地记录失败:', error)
    }
  }

  // 构建返回结果
  if (errorList.length === 0) {
    return ctx.wrapper.succ({
      message: '全部开通成功',
      results: {
        successList
      },
      startTime: minStartTime ? minStartTime.format('YYYY-MM-DD') : null,
      endTime: maxEndTime ? maxEndTime.format('YYYY-MM-DD') : null
    })
  } else if (successList.length > 0) {
    return ctx.wrapper.succ({
      message: '部分开通成功',
      results: {
        success: successList,
        failed: errorList
      },
      startTime: minStartTime ? minStartTime.format('YYYY-MM-DD') : null,
      endTime: maxEndTime ? maxEndTime.format('YYYY-MM-DD') : null
    })
  } else {
    return ctx.wrapper.error('HANDLE_ERROR', '全部开通失败')
  }
}

module.exports = {
  ...curdRouter.createHandlers(),
  openAppUsages
}
