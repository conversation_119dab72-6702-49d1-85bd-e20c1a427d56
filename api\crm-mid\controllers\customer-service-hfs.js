const { <PERSON>urd<PERSON>outer } = require('accel-utils')
const { getQueryKeyChange, getManagerBySchoolId } = require('../services/customer-service')
const customerHfsModel = require('../models/customer-service-hfs')
const axios = require('axios')
const _ = require('lodash')
const { ObjectId } = require('mongodb')
const moment = require('moment')

const curdRouter = new (class extends CurdRouter {

  _appendBaseFilter (ctx) {
    const query = ctx.request.query
    query.deleted_ne = 'true'

    getQueryKeyChange(query)
  }

  async find (ctx) {
    this._appendBaseFilter(ctx)
    const data = await super.find(ctx)
    const filterFields = ['id', 'createdAt', 'updatedAt', ...Object.keys(customerHfsModel.attributes)]
    return data.map(item => {
      Object.keys(item).forEach(key => {
        if (!filterFields.includes(key)) {
          delete item[key]
        }
      })
      return item
    })
  }

  async count (ctx) {
    this._appendBaseFilter(ctx)
    return await super.count(ctx)
  }

})('customer-service-hfs')

async function openHfsJzjx (ctx) {
  const { schoolId, platform = 'hfsjzjx' } = ctx.request.body
  if (!schoolId || +schoolId <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId })
  if (!customerService) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '未找到学校信息')
  }

  // 开通阅卷、作业系统、教学平台、题库组卷、学情诊断
  const yjTag = customerService.yjTag
  if (!['付费校', '体验校', '流失校'].includes(yjTag)) {
    return ctx.wrapper.error('PARAMETERS_ERROR', `学校标签为【${yjTag ? yjTag : '空'}】，不支持试用`)
  }

  // 体验版、流失校。 开通 + 30天。
  // 付费校 。 根据阅卷到期时间
  // 到期时间小于 7月31 。 开通
  // 到期时间大于等于7月31 。 直接开通到7月31

  let expireDate = moment().startOf('day')
  let hasYj = false

  if (yjTag === '体验校' || yjTag === '流失校') {
    // 体验版、流失校开通30天
    expireDate = moment().add(30, 'days').startOf('day')
    // 体验校、流失校 开通阅卷
    hasYj = true
  } else {
    // 付费校根据阅卷到期时间
    const yjEndTime = customerService.yjEndTime
    if (yjEndTime && moment(yjEndTime).isValid() && moment(yjEndTime).isAfter(moment())) {
      expireDate = moment(yjEndTime).startOf('day')
    } else {
      // 如果没有阅卷到期时间，不开通
      return ctx.wrapper.error('PARAMETERS_ERROR', '学校为付费校，阅卷到期时间有误，不支持试用')
    }
  }

  // 检查到期时间是否小于7月31
  const julyDeadline = moment().year() + '-07-31'
  const julyDeadlineMoment = moment(julyDeadline).startOf('day')

  try {
    // 如果到期时间大于等于7月31，则直接开通到7月31
    if (expireDate.isAfter(julyDeadlineMoment) || expireDate.isSame(julyDeadlineMoment)) {
      expireDate = julyDeadlineMoment
    }

    // 调用开通服务的API
    const formattedExpireDate = expireDate.format('YYYY-MM-DD')
    const result = await updateAppUsage(ctx, customerService.customerId, formattedExpireDate, hasYj, platform)

    // 更新学校的好分数精准教学服务信息
    await strapi.query('customer-service-hfs').update(
      { id: customerService.id },
      {
        siteType: 'hfs',
        siteStartTime: moment().format('YYYY-MM-DD'),
        siteEndTime: formattedExpireDate
      }
    )

    return ctx.wrapper.succ({ message: result, expireDate: formattedExpireDate })
  } catch (error) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '服务开通失败: ' + error.message)
  }
}

//  试用开通， 阅卷、作业、教学、组卷（教研）、学情、测验、智阅、校本资源库

const hfsjzjx = {
  type: 10055,
  enabled: 1,
  isTrial: 1,
  params: []
}

const yxzhixue = {
  type: 10067,
  enabled: 1,
  isTrial: 1,
  params: []
}

const usages = {
  yj: {
    type: 10035, // 专业B版
    enabled: 1,
    isTrial: 1,
    params: []
  },
  zuoye: {
    type: 10023,
    enabled: 1,
    isTrial: 1,
    params: []
  },
  jiaoxue: {
    type: 10044,
    enabled: 1,
    isTrial: 1,
    params: [{
      name: '网盘',
      value: ['否']
    }]
  },
  zujuan: { // 教研、组卷
    type: 10042,
    enabled: 1,
    isTrial: 1,
    params: [{
      name: '启用学科网',
      value: ['否']
    }]
  },
  xingqing: {
    type: 10043,
    enabled: 1,
    isTrial: 1,
    params: []
  },
  zhiyue: {
    type: 10051,
    enabled: 1,
    isTrial: 1,
    params: [{
      name: 'LEVEL',
      value: ['L3']
    }]
  },
  ceyan: {
    type: 10063,
    enabled: 1,
    isTrial: 1,
    params: []
  },
  xbzyk: {
    type: 10060,
    enabled: 1,
    isTrial: 1,
    params: []
  }
}

async function updateAppUsage (ctx, customerId, endTime, hasYj, platform) {
  const productInfos = []
  let usage = null
  if (platform === 'hfsjzjx') {
    usage = hfsjzjx
  } else if (platform === 'yxzhixue') {
    usage = yxzhixue
  } else {
    usage = hfsjzjx
  }

  productInfos.push({
    customerId: customerId,
    usage: {
      ...usage,
      beginTime: moment().format('YYYY-MM-DD'),
      endTime: endTime
    },
    enabled: 1,
    remark: '申请试用好分数精准教学',
    handler: {
      user_id: ctx.state.user.id,
      name: ctx.state.user.username
    },
    source: {
      type: 'order',
      data: {
        order_id: '',
        order_no: ''
      }
    }
  })

  for (let usageKey of Object.keys(usages)) {
    if (!hasYj && usageKey === 'yj') continue
    productInfos.push({
      customerId: customerId,
      usage: {
        ...usages[usageKey],
        beginTime: moment().format('YYYY-MM-DD'),
        endTime: endTime
      },
      enabled: 1,
      remark: '申请试用好分数精准教学',
      handler: {
        user_id: ctx.state.user.id,
        name: ctx.state.user.username
      },
      source: {
        type: 'order',
        data: {
          order_id: '',
          order_no: ''
        }
      }
    })
  }
  let errorList = []

  for (let product of productInfos) {
    let response = await axios.post(`${strapi.config.server.bossApi.url}/external/api/customer/app_usage/update`, product, {
      headers: {
        'apikey': strapi.config.server.bossApi.apikey,
        'token': strapi.config.server.bossApi.token,
      }
    })
    console.log(product, response.data)
    let responseData
    if (response.status === 200) {
      responseData = response?.data
    }
    if (!responseData || responseData?.code === 0) {
      errorList.push(`${product.usage.type} 开通失败`)
    }
  }
  if (errorList.length) {
    return '部分开通失败 ' + errorList.join(',')
  } else {
    return '开通成功'
  }
}

async function openAppUsages (ctx) {
  const { schoolId, appUsages, remark } = ctx.request.body

  // 参数验证
  if (!schoolId || +schoolId <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：schoolId 无效')
  }

  if (!appUsages || !Array.isArray(appUsages) || appUsages.length === 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：appUsages 必须是非空数组')
  }

  // 查询学校信息
  const customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId })
  if (!customerService) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '未找到学校信息')
  }

  const productInfos = []
  let minStartTime = null
  let maxEndTime = null

  // 构建产品信息列表
  for (const appUsage of appUsages) {
    if (!appUsage.usage || !appUsage.usage.type) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：usage.type 不能为空')
    }

    const usage = appUsage.usage

    // 验证日期格式
    if (!usage.beginTime || !moment(usage.beginTime).isValid()) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：beginTime 格式不正确')
    }

    if (!usage.endTime || !moment(usage.endTime).isValid()) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误：endTime 格式不正确')
    }

    // 记录最早开始时间和最晚结束时间
    const beginMoment = moment(usage.beginTime)
    const endMoment = moment(usage.endTime)

    if (!minStartTime || beginMoment.isBefore(minStartTime)) {
      minStartTime = beginMoment
    }

    if (!maxEndTime || endMoment.isAfter(maxEndTime)) {
      maxEndTime = endMoment
    }

    productInfos.push({
      customerId: customerService.customerId,
      usage: {
        type: usage.type,
        enabled: usage.enabled !== undefined ? usage.enabled : 1,
        isTrial: usage.isTrial !== undefined ? usage.isTrial : 1,
        beginTime: usage.beginTime,
        endTime: usage.endTime,
        params: usage.params || []
      },
      enabled: 1,
      remark: appUsage.remark || remark || '申请开通服务',
      handler: {
        user_id: ctx.state.user.id,
        name: ctx.state.user.username
      },
      source: {
        type: 'order',
        data: {
          order_id: '',
          order_no: ''
        }
      }
    })
  }

  // 调用 Boss API 开通服务
  const errorList = []
  const successList = []

  for (const product of productInfos) {
    try {
      const response = await axios.post(
        `${strapi.config.server.bossApi.url}/external/api/customer/app_usage/update`,
        product,
        {
          headers: {
            'apikey': strapi.config.server.bossApi.apikey,
            'token': strapi.config.server.bossApi.token,
          }
        }
      )

      console.log('产品开通请求:', product, '响应:', response.data)

      if (response.status === 200 && response.data && response.data.code !== 0) {
        successList.push({
          type: product.usage.type,
          message: '开通成功'
        })
      } else {
        errorList.push({
          type: product.usage.type,
          message: response.data?.message || '开通失败'
        })
      }
    } catch (error) {
      console.error('产品开通异常:', product.usage.type, error.message)
      errorList.push({
        type: product.usage.type,
        message: error.message || '开通异常'
      })
    }
  }

  // 如果有成功的产品，更新本地记录
  if (successList.length > 0 && minStartTime && maxEndTime) {
    try {
      await strapi.query('customer-service-hfs').update(
        { id: customerService.id },
        {
          siteType: 'hfs',
          siteStartTime: minStartTime.format('YYYY-MM-DD'),
          siteEndTime: maxEndTime.format('YYYY-MM-DD')
        }
      )
    } catch (error) {
      console.error('更新本地记录失败:', error)
    }
  }

  // 构建返回结果
  if (errorList.length === 0) {
    return ctx.wrapper.succ({
      message: '全部开通成功',
      results: {
        successList
      },
      startTime: minStartTime ? minStartTime.format('YYYY-MM-DD') : null,
      endTime: maxEndTime ? maxEndTime.format('YYYY-MM-DD') : null
    })
  } else if (successList.length > 0) {
    return ctx.wrapper.succ({
      message: '部分开通成功',
      results: {
        success: successList,
        failed: errorList
      },
      startTime: minStartTime ? minStartTime.format('YYYY-MM-DD') : null,
      endTime: maxEndTime ? maxEndTime.format('YYYY-MM-DD') : null
    })
  } else {
    return ctx.wrapper.error('HANDLE_ERROR', '全部开通失败')
  }
}

module.exports = {
  ...curdRouter.createHandlers(),
  openHfsJzjx,
  openAppUsages
}
