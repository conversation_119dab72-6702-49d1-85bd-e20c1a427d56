const { CurdRouter } = require('accel-utils');
const axios = require('axios');
const _ = require('lodash');
const { ObjectId } = require('mongodb');

const curdRouter = new CurdRouter('order')

async function createOrder(ctx) {
    try {
        let { boss, wly, isAgent } = ctx.request.body;
        let schools = [];
        let schoolIds = _.flatten(wly.payments.map(e => e.targets.map(e => e.bossSchoolId)));
        if (schoolIds.length > 0) {
            schools = await strapi.query('boss-school').find({ id: { $in: schoolIds } });
            if (schoolIds.length !== schools.length) return ctx.badRequest('未匹配到学校')
        }

        boss.creator = {
            user_id: ctx.state.user.id,
            name: ctx.state.user.username,
            base: isAgent ? 'agent' : '',
        }
        let createResponse = await axios.post(`${strapi.config.server.bossSs.url}/external/api/order/create`, boss, {
            headers: {
                'apikey': strapi.config.server.bossSs.apikey,
                'token': strapi.config.server.bossSs.token
            }
        });
        let createData;
        if (createResponse.status === 200) {
            if (!createResponse?.data?.data || !createResponse?.data?.data?.orderId) {
                console.log('createResponse', createResponse)
                return ctx.badRequest(`创建订单失败, ${createResponse?.data?.data?.msg}`)
            }
            createData = createResponse?.data?.data.orderId;
        }

        let getResponse = await axios.get(`${strapi.config.server.bossSs.url}/api/order/get_detail?id=${createData}`);
        let getData;

        if (getResponse.status === 200) {
            getData = getResponse.data;
        }

        let order = await strapi.query('order').findOne({ id: createData });
        if (!order) {
            let paymentTargets = [], targets = [];
            let payments = wly.payments.map(e => {
                e.__component = 'contract.payment-installment-plan';
                e.status = '待支付';
                e.targets = e.targets.map(t => {
                    let curSchool = schools.find(s => s.id === t.bossSchoolId);
                    return {
                        __component: 'contract.target',
                        school: curSchool ? {
                            'label': curSchool.showName,
                            'value': curSchool
                        } : null,
                        'beginTime': t.beginTime ? new Date(t.beginTime) : null,
                        'endTime': t.endTime ? new Date(t.endTime) : null,
                        'quantity': t.quantity,
                        'calculatedUnitPrice': t.calculatedUnitPrice,
                        'unitPrice': t.unitPrice,
                        'totalPrice': t.totalPrice,
                        'goods': t.goods,
                        'productParam': t.productParam
                    };
                });
                return e;
            });
            paymentTargets = _.flatten(payments.map(e => e.targets));

            targets = wly?.targets?.map(t => {
                let curSchool = schools.find(s => s.id === t.bossSchoolId);
                return {
                    __component: 'contract.target',
                    school: curSchool ? {
                        'label': curSchool.showName,
                        'value': curSchool
                    } : null,
                    'beginTime': t.beginTime ? new Date(t.beginTime) : null,
                    'endTime': t.endTime ? new Date(t.endTime) : null,
                    'quantity': t.quantity,
                    'calculatedUnitPrice': t.calculatedUnitPrice,
                    'unitPrice': t.unitPrice,
                    'totalPrice': t.totalPrice,
                    'goods': t.goods,
                    'productParam': t.productParam
                };
            });

            const orderData = {
                id: getData.id,
                no: getData.no,
                type: getData.type,
                goodsTarget: _.isEmpty(wly.targets) ? paymentTargets : targets,
                productParam: getData.type,
                approveStatus: getData?.approve?.status,
                approveBeginTime: getData?.approve?.begin_time && new Date(getData?.approve?.begin_time),
                paymentStatus: getData?.payment?.status,
                paymentInstallmentPlans: payments,
                approveProcessId: getData?.approve?.process_id,
                // processRecord: getData.type,
            }
            order = await strapi.query('order').create(orderData);
        }

        return createResponse?.data?.data.orderId

    } catch (e) {
        return ctx.badRequest(e.message || e)
    }
}

async function orderPay(ctx) {
    try {
        let data = ctx.request.body;
        let response = await axios.post(`${strapi.config.server.bossSs.url}/external/api/order/pay`, data, {
            headers: {
                'apikey': strapi.config.server.bossSs.apikey,
                'token': strapi.config.server.bossSs.token
            }
        });
        let responseData;
        if (response.status === 200) {
            responseData = response?.data;
        }
        let order = await strapi.query('order').findOne({ id: data.orderId });
        if (order) {
            let amount = data.amount;
            for (let payment of _.sortBy(order.paymentInstallmentPlans, ['time', 'age'])) {
                if (payment.status !== '已付清' && amount > 0) {
                    let needPay = payment.amount - (payment.pay || 0);
                    await strapi.components['contract.payment-installment-plan'].updateOne({ _id: payment._id }, {
                        $set: {
                            status: amount >= needPay ? '已付清' : '待支付',
                            pay: amount >= needPay ? payment.amount : (payment.pay || 0) + amount,
                        }
                    });
                    amount = amount - needPay;
                }
            }
        }
        return responseData;

    } catch (e) {
        return ctx.badRequest(e)
    }
}

async function getOrderInfo(ctx) {
    try {
        let { id, no } = ctx.request.query;
        let data, order, response;
        if (id) {
            order = await strapi.query('order').findOne({ id: id });
            response = await axios.get(`${strapi.config.server.bossSs.url}/api/order/get_detail?id=${id}`, {});

        } else if (no) {
            order = await strapi.query('order').findOne({ no: no });
            response = await axios.get(`${strapi.config.server.bossSs.url}/api/order/get_detail?no=${no}`, {});
        }
        data = response?.data || {};
        if (order) {
            order.goodsTarget = await Promise.all(order?.goodsTarget?.map(async (target) => {
                const goods = await strapi.query('sale-sku-product').findOne({ id: target?.goods?.id });
                target.goods = goods;
                return target;
            }));
            order.paymentInstallmentPlans = await Promise.all(order?.paymentInstallmentPlans?.map(async (plan) => {
                plan.targets = await Promise.all(plan?.targets?.map(async (target) => {
                    const goods = await strapi.query('sale-sku-product').findOne({ id: target?.goods?.id });
                    target.goods = goods;
                    return target;
                }));
                return plan;
            }))
        }
        data.wly = order;
        return data;
    } catch (e) {
        return ctx.badRequest(e)
    }
}

async function orderAppUsageUpdate(ctx) {
    try {
        let { orderId, targetIds } = ctx.request.body;
        if (!orderId || _.isEmpty(targetIds)) return ctx.badRequest('参数错误');

        let order = await strapi.query('order').findOne({ id: orderId });
        if (!order) return ctx.badRequest('订单不存在');

        let curTargetIds = _.flatten(order.paymentInstallmentPlans.map(e => e.targets.map(t => t.id)));
        if (_.intersection(curTargetIds, targetIds).length !== targetIds.length) return ctx.badRequest('开通订单和目标id不匹配');

        let needUpdateTargets = [];
        for (const plan of order.paymentInstallmentPlans) {
            let curNeedUpdateTargets = plan?.targets.filter(t => targetIds.some(id => t.id === id));
            if (_.isEmpty(curNeedUpdateTargets)) return;
            if (curNeedUpdateTargets.some(t => t.status === '已开通' || t.status === '开通中') || plan.status !== '已付清') return ctx.badRequest('不符合开通条件');
            needUpdateTargets = needUpdateTargets.concat(curNeedUpdateTargets);
        }

        if (_.isEmpty(needUpdateTargets)) return ctx.badRequest('无符合开通条件的对象');
        for (let target of needUpdateTargets) {
            await strapi.components['contract.target'].updateOne({ _id: ObjectId(target.id) }, { status: '开通中' });
            const productInfos = [];
            const versions = await strapi.query('customer-app-version').find({ id: { $in: target.goods.customerAppVersion } });

            for (let version of versions) {
                const productParam = target?.productParam?.find(e => e.id === version.id)
                const curParams = productParam?.usageParams.filter(e => !_.isNil(e.value)).map(e => { return { name: e.name, value: e.type === 'Number' && e.value ? +e.value : e.value } })
                productInfos.push({
                    'customerId': target?.school?.value?.id,
                    'usage': {
                        'type': version.type,
                        'beginTime': target.beginTime,
                        enabled: 1,
                        'endTime': target.endTime,
                        'params': curParams
                    },
                    enabled: 1,
                    remark: order.no,
                    handler: {
                        user_id: ctx.state.user.id,
                        name: ctx.state.user.username
                    },
                    source: {
                        type: 'order',
                        data: {
                            order_id: orderId,
                            order_no: order.no,
                        }
                    }
                })
            }

            let responseList = [];
            for (let product of productInfos) {
                let response = await axios.post(`${strapi.config.server.bossApi.url}/external/api/customer/app_usage/update`, product, {
                    headers: {
                        'apikey': strapi.config.server.bossApi.apikey,
                        'token': strapi.config.server.bossApi.token,
                    }
                });
                let responseData;
                if (response.status === 200) {
                    responseData = response?.data;
                }
                responseList.push(responseData);
            }
            if (responseList.some(item => item?.code === 0)) {
                await strapi.components['contract.target'].updateOne({ _id: ObjectId(target.id) }, { status: '部分开通失败', failReason: responseList });
            } else {
                await strapi.components['contract.target'].updateOne({ _id: ObjectId(target.id) }, { status: '已开通' });
                if (versions.find(e => e.productName === '阅卷')) {
                    axios.post(`${strapi.config.server.wlySpaceApi.url}/external/customer-services/changeSchoolTag`, {
                        customerId: target?.school?.value?.id,
                        yjTag: '付费校'
                    });
                    // strapi.query('boss-school').update({ id: target?.school?.value?.id }, { tag: '付费校' });
                }
            }
        }

        return ctx.wrapper.succ({});
    } catch (e) {
        return ctx.badRequest(e)
    }
}

async function orderProductParamUpdate(ctx) {
    try {
        const { id } = ctx.params;
        const { targets } = ctx.request.body;

        const order = await strapi.query('order').findOne({ id: id });
        if (!id || !order) return ctx.badRequest('参数错误');

        const targetIds = _.map(targets, 'id');
        const curTargetIds = _.flatten(order.paymentInstallmentPlans.map(e => e.targets.map(t => t.id)));
        if (_.intersection(curTargetIds, targetIds).length !== targetIds.length) return ctx.badRequest('订单和目标id不匹配');

        const response = await axios.get(`${strapi.config.server.bossSs.url}/api/order/get_detail?id=${id}`, {});
        if (response?.data.status >= 20) return ctx.badRequest('此阶段订单不允许修改产品参数');

        let bulkWriteArray = [];
        for (const target of targets) {
            let defaultFields = {};
            if (!_.isEmpty(target.productParam)) defaultFields.productParam = target.productParam;
            if (!_.isEmpty(target.beginTime) && !_.isEmpty(target.endTime)) {
                defaultFields.beginTime = target.beginTime;
                defaultFields.endTime = target.endTime;
            }
            bulkWriteArray.push({ updateOne: { filter: { _id: new ObjectId(target.id) }, update: { $set: defaultFields } } });
        }
        await strapi.components['contract.target'].bulkWrite(bulkWriteArray);

        return ctx.wrapper.succ({});
    } catch (e) {
        return ctx.badRequest(e)
    }
}

async function getOrderListByQuery(ctx) {
    try {
        let { schoolId, no, source = 'tiku', limit = 10, start = 0 } = ctx.request.query;
        let goodsIds = [], resObj = {
            total: 0,
            list: []
        };
        if (limit > 50) { limit = 50 }
        if (source && source === 'tiku') {
            let version = await strapi.query('customer-app-version').findOne({ type: 10002 });
            let products = await strapi.query('sale-sku-product').find({ customerAppVersion: { $in: version.id } });
            goodsIds = products.map(e => e._id);
        }

        let targetCond = {}, targetIds;
        if (schoolId) { targetCond['school.value.schoolId'] = schoolId }
        if (goodsIds && goodsIds.length > 0) { targetCond['goods'] = { $in: goodsIds } }
        if (!_.isEmpty(targetCond)) {
            let targets = await strapi.components['contract.target'].find(targetCond);
            targetIds = targets.map(e => e._id);
            if (targetIds.length === 0) return ctx.wrapper.succ(resObj);
        }

        let orderCond = {};
        if (no) { orderCond['no'] = no }
        if (targetIds && targetIds.length > 0) { orderCond['goodsTarget.ref'] = { $in: targetIds } }
        let total = await strapi.query('order').model.countDocuments(orderCond);
        let orders = await strapi.query('order').model.find(orderCond, { goodsTarget: 1, no: 1, createdAt: 1, updatedAt: 1 }).sort({ _id: -1 }).skip(+start).limit(+limit);
        resObj.total = total;
        resObj.list = orders;
        return ctx.wrapper.succ(resObj);
    } catch (e) {
        return ctx.badRequest(e)
    }
}

module.exports = {
    createOrder,
    orderPay,
    getOrderInfo,
    orderAppUsageUpdate,
    orderProductParamUpdate,
    getOrderListByQuery,
    // updateAndSyncBoss,
    ...curdRouter.createHandlers(),
}
