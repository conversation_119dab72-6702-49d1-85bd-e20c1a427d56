{"collectionName": "access-key", "info": {"name": "AccessKey", "label": "访问密钥", "description": "访问密钥"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {}, "attributes": {"name": {"label": "名称", "type": "string", "required": true}, "key": {"label": "密钥", "type": "string", "required": true, "format": "secret"}, "status": {"label": "启用状态", "type": "boolean", "default": true}}}