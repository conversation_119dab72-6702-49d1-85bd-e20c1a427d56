const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('workflow')
const { internalApiExecute } = require('./api');
const { ObjectId } = require('mongodb');
const moment = require('moment');
const _ = require('lodash')

async function webhookExecute(ctx) {
    const { id } = ctx.params;
    const params = _.assign({}, ctx.request.body, ctx.request.query);

    try {
        let data = await internalWebhookExecute(ctx, id, params);
        return ctx.wrapper.succ(data);
    } catch (e) {
        console.log(e)
        return ctx.wrapper.error('HANDLE_ERROR', e.message || e)
    }
}

async function timerExecute(ctx) {
    const { id } = ctx.params;
    let data = await internalTimerExecute(id);
    return ctx.wrapper.succ(data);
}

async function internalWebhookExecute(ctx, id, params) {
    const workflow = await strapi.query('workflow').findOne({ id: ObjectId(id) }, []);
    if (!workflow || !workflow.published_at || workflow.type !== 'webhook') {
        throw new Error('webhook 工作流不存在');
    }

    (workflow?.inputParam || []).forEach(element => {
        if (element.required && !params[element.key]) {
            throw new Error(`传参 ${element.key} 必传`);
        }
        if (!params[element.key] && element.default) {
            params[element.key] = element.default;
        }
        if (!params[element.key] && element.userValue) {
            params[element.key] = ctx.state.user[element.userValue];
        }
    });

    let modelData, queryData = {}, updateData = {};
    if (!_.isEmpty(workflow.queryParam)) {
        workflow.queryParam.forEach(item => {
            queryData[item.key] = _.get(params, item.reqKey);
        });
        modelData = await strapi.query(workflow.model).findOne(queryData);
    }
    if (!_.isEmpty(workflow.updateParam)) {
        workflow.updateParam.forEach(item => {
            if (item.type === 'fixed') {
                updateData[item.key] = _checkValueType(item.value, item.valueType);
            }
            if (item.type === 'pass') {
                updateData[item.key] = params[item.reqKey];
            }
            if (item.type === 'expression') {
                updateData[item.key] = eval(item.value);
            }
        });
    }

    if (workflow.api) {
        const api = await strapi.query('api').findOne({ id: workflow.api })
        if (api.type === 'bpm' && !_.isEmpty(modelData.processRecord) && modelData.processRecord.find(item => item.api === workflow.api && item.workflow === workflow.id)) {
            return modelData;
        }
        const apiRes = await internalApiExecute(workflow.api, params);
        if (api.type === 'bpm' && apiRes.id && modelData) {
            const processRecord = await strapi.query('process-record').create({
                "bossId": apiRes.id,
                "model": workflow.model,
                "modelId": modelData.id,
                "api": workflow.api,
                "workflow": id
            });
            await strapi.query(workflow.model).model.updateOne({ _id: ObjectId(modelData.id) }, { $push: { 'processRecord': processRecord._id } });
        }
    }

    if (!_.isEmpty(workflow.queryParam) && !_.isEmpty(workflow.updateParam) && workflow.model) {
        modelData = await strapi.query(workflow.model).update(queryData, updateData);
    } else if (!_.isEmpty(workflow.queryParam) && workflow.model) {
        modelData = await strapi.query(workflow.model).findOne(queryData);
    }

    return modelData;
}

async function internalTimerExecute(id) {
    const workflow = await strapi.query('workflow').findOne({ id: ObjectId(id) }, []);
    if (!workflow || !workflow.published_at || workflow.type !== 'time') {
        return;
    }
    let modelArr = await strapi.query(workflow.model).model.find({ [workflow.timerParam]: { $lt: Date.now() } });
    modelArr = modelArr.map(e => {
        return e.toObject()
    })

    if (!_.isEmpty(workflow.updateParam)) {
        let updateData = {};
        workflow.updateParam.forEach(item => {
            updateData[item.key] = _checkValueType(item.value, item.valueType);
        });
        let curArr = _.partition(modelArr, updateData);
        if (curArr && curArr[1].length > 0) {
            await strapi.query(workflow.model).model.updateMany({ _id: { $in: curArr[1].map(e => e._id) } }, { $set: updateData });
        }
    }

    return;
}

async function internalWorkflowExecute(params) {
    const { beforeData, afterData, model } = params;
    const modelId = afterData.id;
    const workflows = await strapi.query('workflow').find({ model: model, type: 'event', _publicationState: 'live' });
    if (!workflows) return;

    for (let workflow of workflows) {
        if (_.isEmpty(workflow.triggerParam)) break;
        let trigger = true;
        for (let triggerInfo of workflow.triggerParam) {
            if (!(beforeData[triggerInfo.key] !== afterData[triggerInfo.key] && _triggerCondition(triggerInfo.type, afterData[triggerInfo.key], triggerInfo.value))) {
                trigger = false;
                break;
            }
        }
        if (!trigger) break;

        if (!_.isEmpty(workflow.updateParam)) {
            let updateData = {};
            workflow.updateParam.forEach(item => {
                updateData[item.key] = item.value;
            });
            await strapi.query(workflow.model).update({ _id: ObjectId(modelId) }, updateData);
        }

        if (workflow.api) {
            const api = await strapi.query('api').findOne({ id: workflow.api })
            if (api.type === 'bpm' && afterData.processRecord.find(item => item.api === workflow.api && item.workflow === workflow.id)) {
                return afterData;
            }
            const apiRes = await internalApiExecute(workflow.api, afterData);
            if (api.type === 'bpm' && apiRes.id) {
                const processRecord = await strapi.query('process-record').create({
                    "bossId": apiRes.id,
                    "model": workflow.model,
                    "modelId": modelId,
                    "api": workflow.api,
                    "workflow": workflow.id,
                });
                await strapi.query(workflow.model).model.updateOne({ _id: ObjectId(modelId) }, { $push: { 'processRecord': processRecord._id } });
            }
        }
    }
    return afterData;
}

function _triggerCondition(cond, value, other) {
    switch (cond) {
        case '$gt':
        case '$gte':
        case '$lt':
        case '$lte':
        case '$eq': {
            return _[_.trim(cond, '$')](value, other);
        }
        case '$ne': {
            return !_.eq(value, other);
        }
        case '$in': {
            return _.includes(value, other);
        }
        case '&nin': {
            return !_.includes(value, other);
        }
        default:
            return false;
    }
}

function _checkValueType(value, valueType) {
    switch (valueType) {
        case 'string': {
            return '' + value;
        }
        case 'number': {
            return +value;
        }
        case 'boolean': {
            return value === 'false' ? false : Boolean(value);
        }
        default:
            return value;
    }
}

module.exports = {
    webhookExecute,
    timerExecute,
    internalWorkflowExecute,
    ...curdRouter.createHandlers(),
}