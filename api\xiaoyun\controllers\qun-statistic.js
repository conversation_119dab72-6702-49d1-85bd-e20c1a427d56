const { CurdRouter } = require('accel-utils')
const userRole = require('../utils/userRole')

const curdRouter = new CurdRouter('qun-statistic')

async function userStatistic(ctx) {
  const user = ctx.state.user

  const { type } = ctx.request.query

  let filter = {
    manager: user.id
  }
  if (type) {
    filter.crm_type_in = type.split(',')
  }
  let data = await strapi.query('qun-statistic').find(filter, [])
  const result = {}
  if (data && data.length) {
    data.forEach(e => {
      delete e._id
      delete e.manager
      delete e.crm_type
      delete e.team
      delete e.id
      Object.keys(e).forEach(key => {
        if (!result[key]) {
          result[key] = 0
        }
        result[key] += e[key]
      })
    })
    return result
  } else {
    return {}
  }
}

async function teamStatistic(ctx) {
  const user = ctx.state.user
  const { type } = ctx.request.query

  if (!userRole.isAdmin(user) && !userRole.isLeader(user) && !userRole.isMember(user)) {
    return ctx.wrapper.error('AUTH_ERROR', '没有权限')
  }
  let list = []
  let filter = {
    _sort: 'team',
    _limit: 1000
  }
  if (type) {
    filter.crm_type_in = type.split(',')
  }

  let groups = await strapi.query('manager-group').find({ type: 'service' }, ['members'])
  const members = []
  for (const group of groups) {
    members.push(...group?.members?.map(e => e.id) || [])
  }
  filter.manager_in = members
  list = await strapi.query('qun-statistic').find(filter)

  // if (userRole.isSalesAdmin(user) || userRole.isServiceAdmin(user)) {
  //   const teamType = userRole.isServiceAdmin(user) ? 'service' : 'sales'
  //   let groups = await strapi.query('manager-group').find({ type: teamType }, ['members'])
  //   const members = []
  //   for (const group of groups) {
  //     members.push(...group?.members?.map(e => e.id) || [])
  //   }
  //   filter.manager_in = members
  //   list = await strapi.query('qun-statistic').find(filter)
  // } else if (userRole.isAdmin(user)) {
  //   list = await strapi.query('qun-statistic').find(filter)
  // } else {
  //   const group = await strapi.query('manager-group').find({ leader: user.id })
  //   if (group) {
  //     let members = group.members?.map(e => e.id) || []
  //     if (!members.some(e => e === user.id)) {
  //       members = [user.id, ...members]
  //     }
  //     filter.manager_in = members
  //     list = await strapi.query('qun-statistic').find(filter) || []
  //   }
  // }

  const result = []
  const userMap = {}
  if (list && list.length) {
    list.forEach(e => {
      if (!userMap[e.manager.id]) {
        delete e._id
        delete e.crm_type
        delete e.id
        userMap[e.manager.id] = e
      } else {
        Object.keys(e).forEach(key => {
          if (key === 'manager' || key === 'crm_type' || key === 'id' || key === '_id' || key === 'team') return
          userMap[e.manager.id][key] += e[key]
        })
      }
    })
    for (const key in userMap) {
      result.push(userMap[key])
    }
  }

  return result
}

module.exports = {
  userStatistic,
  teamStatistic,
  ...curdRouter.createHandlers(),
}
