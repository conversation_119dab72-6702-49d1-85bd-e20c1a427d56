const { CurdRouter } = require('accel-utils')

function getUserQueryByRole(query, user) {
  if (user.role.type === 'admin'
    || user.role.type === 'SuperAdmin'
    || user.role.type === 'sales-admin'
    || user.role.type === 'service-admin'
    || user.role.type === 'sales-observer'
  ) {
    return
  }
  if (user.role.type === 'service-group-leader') {
    query.groupType = 'service'
  } else if (user.role.type === 'sales-group-leader'
    || user.role.type === 'sales-manager') {
    query.groupType = 'sales'
  } else {
    query.operator_eq = user.id
  }
}

const curdRouter = new (class extends CurdRouter {
  async count(ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)
    getUserQueryByRole(query, user)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    getUserQueryByRole(query, user)
    return super.find(ctx)
  }

  async export(ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    getUserQueryByRole(query, user)
    return super.export(ctx)
  }

})('telephone-record')

module.exports = {
  ...curdRouter.createHandlers(),
}
