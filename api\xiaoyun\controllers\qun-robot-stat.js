const { CurdRouter } = require('accel-utils')
const axios = require('axios')
const Jimp = require('jimp')
const QrCode = require('qrcode-reader')
const { sendRobotMsg } = require('../../../scripts/utils')

const curdRouter = new CurdRouter('qun-robot-stat')



async function getNoRobotQun(ctx) {
  const { robotId, inviteUid } = ctx.request.query
  if (!inviteUid || !robotId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let qunInfo = await getNoRobotQunInfo(inviteUid)
  if (!qunInfo) {
    return ctx.wrapper.error('NULL_ERROR', '无群数据')
  }
  let qid = qunInfo.qunId
  // 通过链接获取二维码信息。 解析二维码
  let response = await axios.get(`http://wly-space-api-lan.iyunxiao.com/qun/qr_code/${qid}`, { responseType: 'arraybuffer' })
  let imageBuffer = response.data
  let qrCode = await decodeImage(imageBuffer)
  if (!qrCode) {
    return ctx.wrapper.error('NULL_ERROR', '二维码解析失败')
  }
  let code = qrCode.split('/')[4]
  let workWxCodeLink = `https://work.weixin.qq.com/apph5/external_room/join/qrcode?wwwxroomcode=${code}`
  await sendRobotMsg(workWxCodeLink, robotId)
  return {
    qid,
    name: qunInfo.name,
    code
  }
}

async function getNoRobotQunInfo(inviteId) {
  let qunUser = await strapi.query('qun-user').findOne({ userId: inviteId })
  if (!qunUser) {
    return null
  }

  let qunInfo = await strapi.query('no-robot-qun').findOne({
    inviteRobot: inviteId,
    hasInvited_ne: true
  })

  if (qunInfo) {
    await strapi.query('no-robot-qun').update({ id: qunInfo.id }, { hasInvited: true})
  }

  return qunInfo
}

async function decodeImage(imageBuffer) {
  // 2. 使用 Jimp 读取图片
  const image = await Jimp.read(imageBuffer);
  const qr = new QrCode(); // 创建二维码解析器实例

  return new Promise((resolve, reject) => {
    qr.callback = function (err, result) {
      if (err) {
        reject('Error decoding QR code: ' + err);
      } else {
        resolve(result.result); // 返回解析到的二维码数据
      }
    };
    qr.decode(image.bitmap); // 解析二维码
  });
}

module.exports = {
  ...curdRouter.createHandlers(),
  getNoRobotQun
}
