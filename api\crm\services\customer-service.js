const _ = require('lodash')
const managerGroup = require('../models/manager-group')

function _getIntersection (arr1, arr2) {
  const set = new Set(arr1)
  return arr2.filter(item => set.has(item))
}

function getBaseQuery(query) {
    query.yxzxType_null = true
}

function getQueryFollowerChange (query, groups) {
  if (query.directServiceTeam_in || query.directServiceTeam_eq) {

    let teamsQuery = query.directServiceTeam_in || query.directServiceTeam_eq
    let teams = typeof teamsQuery === 'string' ? [teamsQuery] : teamsQuery
    const curGroups = groups.filter(e => teams.includes(e.id))
    const ids = _.flatten(curGroups.map(e => e.members?.map(member => member.id)))
    query.directServiceManager_in = query.directServiceManager_in
      ? query.directServiceManager_in.concat(ids)
      : ids
    delete query.directServiceTeam_in
    delete query.directServiceTeam_eq
  }

  if (query.directSalesTeam_in || query.directSalesTeam_eq) {
    let teamsQuery = query.directSalesTeam_in || query.directSalesTeam_eq
    let teams = typeof teamsQuery === 'string' ? [teamsQuery] : teamsQuery
    const curGroups = groups.filter(e => teams.includes(e.id))
    const ids = _.flatten(curGroups.map(e => e.members?.map(member => member.id)))
    query.directSalesManager_in = query.directSalesManager_in
      ? query.directSalesManager_in.concat(ids)
      : ids
    delete query.directSalesTeam_in
    delete query.directSalesTeam_eq
  }

}

function getUserQueryByRole (query, user, groups) {
  const curGroups = groups.filter(e => e.leader?.[0]?.id === user.mingdaoId)
  // if (user.role.type === 'service-group-member') {
  //   query.directServiceManager_in = query.directServiceManager_in
  //     ? _getIntersection(query.directServiceManager_in, [user.mingdaoId])
  //     : [user.mingdaoId]
  // } else if (user.role.type === 'sales-group-member') {
  //   query.directSalesManager_in = query.directSalesManager_in
  //     ? _getIntersection(query.directSalesManager_in, [user.mingdaoId])
  //     : [user.mingdaoId]
  // }
  if (user.role.type === 'service-group-leader') {
    const ids = _.flatten(curGroups.map(e => e.members?.map(member => member.id)))
    query.directServiceManager_in = query.directServiceManager_in
      ? _getIntersection(query.directServiceManager_in, ids)
      : ids
  } else if (user.role.type === 'service-group-member') {
    query.directServiceManager_in = query.directServiceManager_in
      ? _getIntersection(query.directServiceManager_in, [user.mingdaoId])
      : [user.mingdaoId]
  } else if (user.role.type === 'sales-group-leader') {
    const ids = _.flatten(curGroups.map(e => e.members?.map(member => member.id)))
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, ids)
      : ids
  } else if (user.role.type === 'sales-group-member') {
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, [user.mingdaoId])
      : [user.mingdaoId]
  } else if (user.role.type === 'sales-manager') {
    const managerGroups = groups.filter(e => e.manager.map(manager => manager.id).includes(user.mingdaoId))
    const ids = _.flattenDeep(managerGroups.map(e => e.members.map(member => member.id)))
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, ids)
      : ids
  }
}


function getFollowerNowGroup (customerService, groups) {
  // 填充服务数据
  if (customerService['directServiceManager']?.[0]) {
    const curGroups = groups.filter(e => e.members.map(member => member.id).includes(customerService['directServiceManager']?.[0].id))
    customerService['directServiceTeam'] = curGroups
  }
  if (customerService['directSalesManager']?.[0]) {
    const curGroups = groups.filter(e => e.members.map(member => member.id).includes(customerService['directSalesManager']?.[0].id))
    customerService['directSalesTeam'] = curGroups
  }

  return customerService
}

async function getFollowerRecord (customerService) {
  // 填充直营跟进记录
  let mongoList = await strapi.query('archive-sales-follow-record').model.find({
    'customerService.id': customerService.id,
    // _limit: -1,
  })
  mongoList = mongoList.map(e => e.id)
  customerService.customerSalesFollowRecords = customerService.customerSalesFollowRecords.concat(mongoList)
  return customerService
}


module.exports = {
  getBaseQuery,
  getQueryFollowerChange,
  getUserQueryByRole,
  getFollowerNowGroup,
  getFollowerRecord,
}
