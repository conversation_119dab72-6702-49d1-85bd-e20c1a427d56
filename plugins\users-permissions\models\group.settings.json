{"collectionName": "users-permissions_group", "info": {"name": "PermissionGroup", "label": "功能", "description": "功能由一组视图与接口权限组成"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {"content-manager": {"visible": false}}, "attributes": {"isPreset": {"label": "是否预置", "type": "boolean", "required": true, "configurable": false, "editable": false, "default": false}, "name": {"label": "功能名称", "type": "string", "required": true, "configurable": false}, "sId": {"label": "功能编号", "type": "string", "required": true, "configurable": false}, "cover": {"label": "功能封面", "model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}, "configurable": false}, "apiPermissions": {"label": "接口权限", "type": "json", "editable": true}, "pages": {"label": "页面权限", "collection": "page", "via": "groups", "plugin": "users-permissions", "dominant": true, "configurable": false}, "roles": {"label": "关联角色", "collection": "role", "via": "modules", "plugin": "users-permissions", "configurable": false}}}