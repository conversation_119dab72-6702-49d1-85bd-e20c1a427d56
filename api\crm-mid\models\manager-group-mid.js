module.exports = {
  collectionName: 'member-group',
  info: {
    name: 'ManagerGroupMid',
    label: '企信用户组',
    description: '企信用户组'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      required: true,
    },
    unit: {
      label: '单元',
      type: 'string',
    },
    type: {
      label: '类型',
      type: 'string',
      required: true,
      options: [
        { label: '直营小组', value: 'sales', },
        { label: '运营小组', value: 'service', },
      ],
    },
    leader: {
      label: '组长',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      meta: {
        query: {
          ['roles.type']: "{{ $formData.type ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-admin', 'service-group-leader', 'service-group-member'] :  ($formData.type === 'sales' ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member'] : ['service-admin', 'service-group-leader', 'service-group-member']) }}"
        }
      }
    },
    members: {
      label: '组员',
      collection: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      dominant: true,
      meta: {
        query: {
          ['roles.type']: "{{ $formData.type ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-admin', 'service-group-leader', 'service-group-member'] :  ($formData.type === 'sales' ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member'] : ['service-admin', 'service-group-leader', 'service-group-member']) }}"
        }
      }
    },
    manager: {
      label: '阿米巴',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      meta: {
        query: {
          ['roles.type']: ['sales-manager']
        }
      }
    },
  }
}
