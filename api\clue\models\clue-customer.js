const settings = require('../../crm/models/customer-service.js')
module.exports = {
    ...settings,
    duplicateCollection: true,
    attributes: Object.assign({}, settings.attributes, {
        name: {
            // label: '名称',
            "ref": "672d912b962c1f8ca4a20962",
            // "private": true,
        },
        customerId: {
            // label: '客户ID',
            "ref": "672d912b962c1f8ca4a20963",
            "private": true,
        },
        schoolId: {
            // label: '学校 ID',
            "ref": "675bd26fba60f67ec34d6e5e",
            // "private": true,
        },
        customerServiceFollowRecords: {
            // label: '客户跟进',
            "ref": "6731c6391637ee6db9afde36",
            "private": true,
        },
        customerSalesFollowRecords: {
            // label: '客户跟进记录',
            "ref": "6731c6571637ee6db9afde3d",
            "private": true,
        },
        customerContacts: {
            // label: '客户联系人',
            "ref": "672d912b962c1f8ca4a20977",
            "private": true,
        },
    })
}