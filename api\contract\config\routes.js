const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    {
      'method': 'PUT',
      'path': '/contracts/:id/process',
      'handler': 'contract.deleteProcessToBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/init-huoban-json',
      'handler': 'contract.initHuobanJson',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/login/by-boss-token',
      'handler': 'contract.loginByBossToken',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/:id/crm/import',
      'handler': 'contract.importCrmById',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/contracts',
      'handler': 'contract.find',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/contracts/count',
      'handler': 'contract.count',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts',
      'handler': 'contract.create',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/contracts/export',
      'handler': 'contract.export',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/import',
      'handler': 'contract.import',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/contracts/:id',
      'handler': 'contract.findOne',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/contracts/:id',
      'handler': 'contract.update',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/contracts/:id',
      'handler': 'contract.delete',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/check-field',
      'handler': 'contract.checkContractFieldByBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/contracts/seal-process',
      'handler': 'contract.contractSealProcessToBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/v1/yxhuoban/school/get-followup',
      'handler': 'follow.getFollowUp',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/v1/yxhuoban/school/update-followup',
      'handler': 'follow.updateFollowUp',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/orders/by-boss',
      'handler': 'order.createOrder',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/orders/pay',
      'handler': 'order.orderPay',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/orders/info',
      'handler': 'order.getOrderInfo',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/orders/app-usage',
      'handler': 'order.orderAppUsageUpdate',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/orders/:id/product-param',
      'handler': 'order.orderProductParamUpdate',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/orders/by-query',
      'handler': 'order.getOrderListByQuery',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/orders',
      controller: 'order'
    }),
    {
      'method': 'GET',
      'path': '/boss-schools/getSchoolTag',
      'handler': 'boss-school.getSchoolTag',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/boss-schools/updateSchoolTag',
      'handler': 'boss-school.updateSchoolTag',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/boss-schools/getSchoolAppUsage',
      'handler': 'boss-school.getSchoolAppUsage',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/boss-schools',
      controller: 'boss-school'
    }),
    ...createDefaultRoutes({
      basePath: '/boss-agents',
      controller: 'boss-agent'
    }),
    ...createDefaultRoutes({
      basePath: '/boss-app-usage-configs',
      controller: 'boss-app-usage-config'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-app-versions',
      controller: 'customer-app-version'
    }),
    ...createDefaultRoutes({
      basePath: '/sale-sku-products',
      controller: 'sale-sku-product'
    }),
    ...createDefaultRoutes({
      basePath: '/contract-templates',
      controller: 'contract-template'
    }),
    {
      'method': 'POST',
      'path': '/sale-sku-products/list',
      'handler': 'sale-sku-product.findMany',
      'config': {
        'policies': [], 'prefix': '',
      }
    }
  ]
}
