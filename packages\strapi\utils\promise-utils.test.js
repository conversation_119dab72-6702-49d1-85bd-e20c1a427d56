const { asyncConcurrency, cacheAsyncFunction } = require('./promise-utils')

test('Async Currency', async () => {
  const tasks = Array.from({ length: 100 }, (_, i) => () =>
    new Promise(resolve => setTimeout(() => {
      console.info(`resolve Task ${i + 1}`)
      resolve(`Task ${i + 1}`)
    }, Math.random() * 300))
  )
  const result = await asyncConcurrency(tasks, 10)
  expect(result.length).toBe(100)
}, 5000)

test('Cache Async Function', async () => {

  let fetchDataCounter = 0

  async function fetchData (param) {
    console.log('Fetching data for', param)
    fetchDataCounter++
    // 模拟异步操作
    return new Promise((resolve) => setTimeout(() => resolve(`Data for ${param}`), 500))
  }

  const cachedFetchData = cacheAsyncFunction(fetchData, 2000)

  // 测试函数
  async function test () {
    const promises = [
      cachedFetchData('param1'),
      cachedFetchData('param1'),
      cachedFetchData('param1')
    ]

    const results = await Promise.all(promises)
    console.log(results) // 确保只会有一次实际调用 fetchData，并且所有请求都得到相同的结果
    expect(fetchDataCounter).toBe(1)

    await new Promise(resolve => setTimeout(resolve, 2500))

    console.log(await cachedFetchData('param1')) // 缓存过期后，重新执行 fetchData
    expect(fetchDataCounter).toBe(2)
  }

  await test()
}, 10000)



