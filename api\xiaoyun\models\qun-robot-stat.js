module.exports = {
  collectionName: 'qun-robot-stat',
  info: {
    name: 'QunRobotStat',
    label: '群机器人检查',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    owner: {
      label: '群主',
      type: 'string',
    },
    ownerId: {
      label: '群主ID',
      type: 'string',
      required: true
    },
    qunCount: {
      label: '群数量',
      type: 'number',
      required: true
    },
    schoolQunCount: {
      label: '关联学校群数量',
      type: 'number',
      required: true
    },
    robotCount0: {
      label: '机器人成员数量0',
      type: 'number',
      required: true
    },
    robotCount1: {
      label: '机器人成员数量1',
      type: 'number',
      required: true
    },
    robotCount2: {
      label: '机器人成员数量2',
      type: 'number',
      required: true
    },
    inRobotQunCount1: {
      label: '含小云1(客户服务)群数量',
      type: 'number',
    },
    inRobotQunCount2: {
      label: '含小云2(服务总监)群数量',
      type: 'number',
    },
    inRobotQunCount3: {
      label: '含小云3(客户助手)群数量',
      type: 'number',
    },
    robotAdminCount0: {
      label: '机器人管理员数量0',
      type: 'number',
      required: true
    },
    robotAdminCount1: {
      label: '机器人管理员数量1',
      type: 'number',
      required: true
    },
    robotAdminCount2: {
      label: '机器人管理员数量2',
      type: 'number',
      required: true
    },
    inviteUserQunCount: {
      label: '需邀请用户群数量',
      type: 'number',
      required: true
    },
    kickUserQunCount: {
      label: '需踢出用户群数量',
      type: 'number',
      required: true
    },
    noServiceManagerCount: {
      label: '无直服经理数量',
      type: 'number',
      required: true
    },
    noSalesManagerCount: {
      label: '无直营经理数量',
      type: 'number',
      required: true
    },
    userCountGte40: {
      label: '用户数量40+',
      type: 'number',
      required: true
    }
  }
}
