const CurdRouter = require('../../../packages/accel-utils/prototypes/curdRouter')
const _ = require('lodash')

class AccessKeyCurdRouter extends CurdRouter {
  constructor(name, config = {}) {
    super(name, config)
    this.mode = config.mode
  }

  async publicFind(ctx) {
    let key = ctx.request.header['access-key']
    if (!key) {
      return ctx.badRequest('no access-key')
    }

    const accessKey = await strapi.services['access-key'].findOne({ key: key })
    if (!accessKey) {
      // 不存在或禁用。
      return ctx.badRequest('Invalid access-key')
    }

    if (!accessKey.status) {
      return ctx.badRequest('access-key 被禁用，请联系管理员')
    }

    let { query } = this._parseCtx(ctx)
    query.accessKeys = accessKey.id
    ctx.populate = []

    return super.find(ctx)
  }
}

module.exports = AccessKeyCurdRouter
