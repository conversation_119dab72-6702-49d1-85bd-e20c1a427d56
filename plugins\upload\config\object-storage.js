// 默认配置
const defaultObjectStorageConfig = {
  target: 'bos',
  baseDir: 'test/',

  // 默认上传根路径
  // 完整路径 'test/upload/'
  uploadPath: 'upload/',
  // 富文本上传根路径
  // 完整路径 'test/upload/rich-text/'
  richTextUploadPath: 'upload/rich-text/',

  config: {
    // Bos Config
    AK: 'ALTAK3fAE78I9f5oz2IHlhVU7q',
    SK: 'a726c9dc79834a8f8e0494bf26521119',
    Bucket: 'ayx-wly',
    Endpoint: 'https://ayx-wly.bj.bcebos.com'
  }
}

const objectStorageConfig = strapi.config.get('plugins.upload.objectStorage')

if (!objectStorageConfig) {
  console.warn('Plugin upload 未配置对象存储，默认使用未来云测试对象存储 Bucket，部署发布前务必注意替换。配置文件位置 [ProjectDir]/config/plugins.js，详情参考 [ProjectDir]/plugins/upload/README.md')
}

module.exports = {
  objectStorageConfig: {
    ...defaultObjectStorageConfig,
    ...(objectStorageConfig || {})
  },
}
