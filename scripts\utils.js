const FormData = require('form-data')
const fs = require('fs')
const axios = require('axios')
const moment = require('moment/moment')
const xlsx = require('xlsx')
const defaultRobotKey = 'a28d0eae-f751-487a-ab32-c37334ef40b8'

async function uploadFile(filePath, robotKey = defaultRobotKey) {
  try {
    const form = new FormData()
    // 读取文件并附加到 FormData
    form.append('media', fs.createReadStream(filePath))

    console.log('File uploading...')
    // 发送 POST 请求
    const response = await axios.post(`https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=${robotKey}&type=file`, form, {
      headers: {
        ...form.getHeaders(), // 设置正确的 Content-Type (multipart/form-data)
      },
    })

    console.log('File uploaded successfully:', response.data)

    await axios.post(`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${robotKey}`, {
        'msgtype': 'file',
        'file': {
          'media_id': response.data.media_id
        }
      }
    )
  } catch (error) {
    console.log('Error uploading file:', error)
  }
}

async function printExcel(name, data, upload = false, robotKey = defaultRobotKey) {
  let fileName = __dirname + '/' + name + '-' + moment().format('MMDDHHmm') + '.xlsx'
  // 写入文件
  // 创建一个工作簿（Workbook）
  const workbook = xlsx.utils.book_new()

// 将数据转换为工作表
  const worksheet = xlsx.utils.json_to_sheet(data)

// 将工作表添加到工作簿
  xlsx.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

// 生成 Excel 文件的二进制数据
  const excelBuffer = xlsx.write(workbook, { bookType: 'xlsx', type: 'buffer' })

// 同步写入 Excel 文件
  fs.writeFileSync(fileName, excelBuffer)
  logger('-----文件写入完成: ' + __dirname + '/')

  if (upload && process.env.NODE_ENV === 'production') {
    await uploadFile(fileName, robotKey)
    fs.unlinkSync(fileName)
  }
}

async function parseExcel(path) {
  // 读取 Excel 文件
  const workbook = xlsx.readFile(path)

// 获取工作表名称
  const sheetNames = workbook.SheetNames

// 选择一个工作表
  const sheet = workbook.Sheets[sheetNames[0]]

// 将工作表数据转换为 JSON 格式
  const data = xlsx.utils.sheet_to_json(sheet)

// 输出数据
//   console.log(data);
  return data
}

function logger(...msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sendRobotMsg(content, robotKey = defaultRobotKey) {
  if (process.env.NODE_ENV !== 'production') {
    console.log(content)
    return
  }
  let result = await axios.post(`https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${robotKey}`, {
    msgtype: 'text',
    text: {
      content: content
    }
  })
  return result.data
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms))
}

module.exports = {
  printExcel,
  parseExcel,
  uploadFile,
  sendRobotMsg,
  logger,
  sleep
}
