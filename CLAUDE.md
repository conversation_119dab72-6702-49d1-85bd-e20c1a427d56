# CLAUDE.md

## 项目概述

这是一个基于 Strapi v3.6.8 的业务管理系统（wly-boss-serv），提供企业级的多租户SaaS应用开发框架，包含CRM、合同管理、客户管理、权益管理等核心业务模块。

## 开发协作角色定位

### CLAUDE CODE（AI 助手）角色
作为专业的后端开发工程师，我的职责包括：
- **需求分析**：深入理解业务需求，提出技术实现方案
- **架构设计**：设计合理的系统架构和技术架构
- **技术方案**：制定详细的技术实现方案和最佳实践
- **工作拆解**：将复杂任务拆分为可执行的开发任务
- **代码实现**：编写高质量的代码实现
- **技术思考**：对需求实现提供专业的技术见解和优化建议

### 用户角色
作为产品负责人和项目管理者，您的职责包括：
- **需求提供**：明确业务需求和预期目标
- **工作检查**：审查开发进度和代码质量
- **成果验收**：验证功能是否满足需求
- **过程干预**：在开发过程中提供指导和调整
- **方向把控**：确保开发方向符合业务目标

### 协作原则
- 我会主动思考和提出技术建议，但最终决策权在您
- 开发过程中保持充分沟通，确保理解一致
- 遵循敏捷开发理念，快速迭代，及时反馈

## 常用开发命令

```bash
# 安装依赖（自动初始化子模块）
npm install

# 开发环境启动
npm run develop-local-dev    # 本地开发环境（DATABASE=local SERVER=local）
npm run develop-test-dev     # 测试开发环境（DATABASE=test SERVER=test）

# 生产环境启动
npm run develop-local-start  # 本地生产启动
npm run develop-test-start   # 测试生产启动
npm run start                # 正式生产启动
npm run start-test           # 测试环境启动

# 调试模式
npm run debug-dev            # 调试开发模式
```

## 架构概述

### 目录结构
- `/api/*` - 业务API模块，每个模块包含：
  - `config/routes.js` - 路由定义
  - `config/permission.js` - 模块权限配置
  - `controllers/*.js` - 请求处理器
  - `models/*.js` - 数据模型（Mongoose schemas）
  - `services/*.js` - 业务逻辑
  - `utils/*.js` - 工具函数

- `/plugins/*` - 插件系统（严禁改动）
  - `log` - 系统日志
  - `upload` - 文件上传（支持对象存储）
  - `users-permissions` - 用户权限管理（提供企业级用户管理、多租户支持、细粒度权限控制）
  - `swagger` - API 文档自动生成

- `/middlewares/*` - 中间件
  - `koaBody` - 请求体解析
  - `originFilter` - CORS处理
  - `responseWrapper` - 响应格式化

- `/packages/*` - 工具包
  - `accel-utils` - 加速开发工具集（CRUD原型类、验证工具、集合工具等）

- `/config/*` - 全局配置
  - `permission.js` - 项目整体权限配置
  - `database.js` - 数据库配置
  - `server.js` - 服务器配置
  - `swagger.js` - API文档配置

### 核心API模块

#### 业务管理模块
- **contract** - 合同与订单管理（合同、订单、商品、Boss数据同步）
- **crm** - 客户关系管理（客户档案、跟进记录、招投标项目）
- **crm-mid** - 客户中间件（客户服务、问题管理、精准教学）
- **clue** - 资源池管理（公共池、单元池、运营组池、直营组池）

#### 功能扩展模块
- **extend** - 扩展功能（数据缓存、消息中心、支付、变更历史）
- **xiaoyun** - 小云群管理（群列表、群成员、群统计、机器人）
- **negotiationTool** - 直营谈单工具（信息页面、信息模块）
- **brand** - 多品牌管理（伙伴平台菜单、用户菜单）

#### 系统管理模块
- **account** - 账户管理
- **app** - 应用管理（电话记录、应用更新）
- **key** - 访问密钥管理
- **project** - 项目管理（SaaS项目、需求、问题）
- **whiteNameList** - 白名单管理

### 核心插件说明

#### users-permissions 插件
- 提供企业级用户管理、多租户支持、细粒度权限控制以及多种认证方式
- CMS 核心支持，数据模型、页面视图配置等（基于 `config/permission.js` 配置文件）
- 提供基础模型：user、role、permission、page、group、app、branch
- 引用插件模型示例：
  ```javascript
  createdBy: { 
    label: "创建用户",  
    plugin: "users-permissions",  // 需要声明插件来源
    model: "user"
  }
  ```

#### accel-utils 工具包
为 Strapi 应用提供加速开发的工具集，包含了 CRUD 原型类、验证工具、集合工具和各种实用函数。

常用方法：
- `CurdRouter`、`BranchCurdRouter` - CRUD路由基类
- `createDefaultRoutes`、`createDefaultPermissions` - 快速生成路由和权限
- `parseCtxData`、`parseCtxUserAndBranch` - 上下文解析工具

### 数据库配置

项目使用多数据库连接，支持不同环境的自动切换：

#### 数据库连接
1. **default** - 主数据库（wly_boss/testwly-boss）
2. **wlyConfig** - 配置数据库（wly_config/testwly_config）
3. **yjAms** - AMS数据库（ams）
4. **mingdao** - 明道云连接

#### 环境配置
通过 `DATABASE` 环境变量控制：
- `local` - 本地环境（localhost:27017）
- `test` - 测试环境
- `prodDev` - 生产开发环境
- `prod` - 生产环境（默认）

配置文件：`/config/database.js`

### 开发规范

#### 文档与注释规范
- **所有开发文档名称内容、代码注释、提交信息等必须使用中文编写**
- 保持注释简洁明了，说明代码的意图而非实现细节
- 复杂业务逻辑必须添加详细注释
- API 接口文档使用中文描述参数和返回值

#### 命名规范（来自 .cursorrules）
- 变量：小写字母加下划线 (user_data)
- 函数：驼峰命名法 (getUserData)
- 目录/文件/表名：小写字母加横线 (user-data)
- 枚举：大写字母加下划线 (USER_STATUS)

#### API开发模式
1. 使用 `CurdRouter` 创建标准CRUD接口
2. 路由通过 `createDefaultRoutes` 自动生成
3. 复杂业务逻辑放在 services 层
4. 使用 JWT 进行身份认证

### 重要配置文件
- `/config/server.js` - 服务器配置（端口、集成服务）
- `/config/database.js` - 数据库连接
- `/config/swagger.js` - Swagger API 文档配置

### 开发原则
1. 先阅读 README.md 理解项目结构
2. 充分理解需求后再开始编码
3. 遵循 SOLID 原则
4. 保持代码简单可控
5. 添加必要的注释和监控

### API 响应规范 - ctx.wrapper 使用约束

#### 必须使用项目定义的错误类型
根据 `/middlewares/responseWrapper/index.js` 定义，**只能使用以下错误类型**：

| 错误类型 | 使用场景 |
|---------|---------|
| `PARAMETERS_ERROR` | 请求参数缺失或格式错误 |
| `HANDLE_ERROR` | 业务逻辑处理失败 |
| `NULL_ERROR` | 数据不存在或未找到 |
| `AUTH_ERROR` | 认证失败 |
| `EXCEED_FRQ_ERROR` | 访问频率超限 |
| `ILLEGAL_USER` | 用户被禁用 |
| `AUTH_VERSION_ERROR` | 认证版本不匹配 |

#### 使用示例
```javascript
// ✅ 正确 - 使用已定义的错误类型
if (!id) {
  return ctx.wrapper.error('PARAMETERS_ERROR', '缺少必要参数');
}
if (!data) {
  return ctx.wrapper.error('NULL_ERROR', '数据不存在');
}

// ❌ 错误 - 不要创建新的错误类型
return ctx.wrapper.error('NOT_FOUND', '...'); // NOT_FOUND 未定义
return ctx.wrapper.error('DATA_ERROR', '...'); // DATA_ERROR 未定义

// ✅ 成功响应
return ctx.wrapper.succ(data);
```

#### 开发约束
1. **禁止创建新的错误类型** - 只使用上表中列出的类型
2. **禁止直接操作 ctx.body** - 统一使用 ctx.wrapper
3. **提供清晰的错误描述** - 第二个参数应明确说明错误原因

### 默认端口
- 开发环境：3015
- 可通过 NODE_PORT 环境变量自定义

### 认证信息
- 默认管理员：<EMAIL> / wlyAdmin123!@#

## Swagger API 文档

项目已集成 Swagger 文档自动生成系统，访问地址：http://testwly-boss-api.iyunxiao.com/api-docs

### Swagger 配置

项目的 Swagger 配置统一管理在 `/config/swagger.js` 文件中，包括：
- API 文档信息（标题、版本、描述等）
- API 模块标签定义（defaultTags）
- 特殊路径标签（pathTags）
- Swagger UI 配置选项

### 自定义接口注释模板

对于非标准 CRUD 的自定义接口，在控制器方法上方添加 @swagger 注释：

```javascript
/**
 * @swagger
 * /api/custom-endpoint:
 *   get:
 *     tags:
 *       - 标签名称
 *     summary: 简短描述
 *     description: 详细描述
 *     parameters:
 *       - in: query
 *         name: param1
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数说明
 *     responses:
 *       200:
 *         description: 成功
 */
async function customEndpoint(ctx) {
  // 实现逻辑
}
```

### 注释要点

1. **标签使用**：查看 `/config/swagger.js` 中的 `defaultTags` 定义
2. **路径格式**：使用 `{id}` 而不是 `:id`
3. **认证方式**：
   - 默认使用 JWT（bearerAuth）
4. **数据类型**：string, integer, number, boolean, array, object
5. **格式化**：date, date-time, email, password, binary

详细模板参考：`plugins/swagger/docs/自定义接口注释指南.md`

## 最近更新

### 2025-09-18 新增功能
- 添加 `goods-sku-product` 售卖中台商品管理模块
- 新增 `syncSaleSkuToGoodsSku.js` 数据同步脚本
- 支持 `customerTypes` 字段交集计算
- 权益计划（equity-plan）与商品关联

## 功能模块开发指南

### 开发流程示例

以创建一个图书管理模块为例，展示完整的开发流程：

#### 第一步：配置数据模型
`api/book/models/book.js`
```javascript
module.exports = {
  kind: "collectionType",
  collectionName: "book",
  info: {
    name: "Book",
    description: "一本书",
    label: "图书"
  },
  options: {
    increments: true,
    timestamps: true,
    defaultMainField: "name",
    // 自动生成配置项（可选）
    // autoGenerate: true  // 自动生成Controller、Route、Page、Function、Role Permission
  },
  attributes: {
    name: {
      label: "名称",
      type: "string",
      required: true,
    },
    description: {
      label: "描述",
      type: "string"
    },
    createdBy: {
      label: "创建用户",
      plugin: "users-permissions",
      model: "user"
    },
  }
}
```

#### 第二步：编写控制器
`api/book/controllers/book.js`
```javascript
'use strict'
const { CurdRouter } = require('accel-utils')

const bookCurdRouter = new (class extends CurdRouter {
  constructor (modelName) {
    super(modelName)
  }
  // 自定义方法
  async updateDescription (ctx){
    // api/ 目录模型查询可缺省命名空间
    return strapi.query('book').update(
      { id: ctx.params.id },
      { description: ctx.request.body.description }
    )
  }
})('book')

module.exports = {
  ...bookCurdRouter.createHandlers()
}
```

#### 第三步：配置路由
`api/book/config/routes.js`
```javascript
const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    // 基础 CRUD 路由 - 使用工具方法创建
    ...createDefaultRoutes({
      basePath: '/books',
      controller: 'book'
    }),
    // 自定义路由
    {
      'method': 'POST',
      'path': '/books/:id/description',
      'handler': 'book.updateDescription',
      'config': {
        'policies': [], 'prefix': '',
      }
    }
  ]
}
```

#### 第四步：配置权限
`config/permission.js` 或 `api/book/config/permission.js`
```javascript
const { createDefaultPermissions } = require('accel-utils')

// 页面配置 - 生成目录与视图页面
const pageGroups = [
  {
    sId: 'LibraryManagement',
    name: '图书馆管理',
    pages: [
      {
        sId: 'BookManagement', 
        name: '图书管理', 
        icon: 'book',
        meta: {
          modelId: 'book',
          modelPath: 'books',
        }
      },
    ],
  },
]

// 功能配置 - 包含接口与页面权限
const functions = [
  {
    name: '图书管理',
    sId: 'LibraryManagementFunction',
    pages: ['BookManagement'],
    apiPermissions: [
      // 基础 CRUD 权限 - 使用工具方法创建
      ...createDefaultPermissions({
        type: 'application',
        controller: 'book',
      }),
      // 自定义权限
      { 'type': 'application', 'controller': 'book', 'action': 'updateDescription' }
    ]
  },
]

// 角色配置 - 为指定角色分配功能
const roles = [
  {
    name: '图书管理员',
    type: 'Librarian',
    description: '图书管理员可以管理所有图书信息',
    modules: [
      'AuthenticatedFunction',
      'LibraryManagementFunction',
    ]
  },
]
```

### 权限配置文件结构

权限配置采用分层结构：
- **项目整体配置**：`config/permission.js`
- **模块配置**：`api/*/config/permission.js`
- **插件配置**：`plugins/*/config/permission.js`

配置文件主要包含以下部分：

1. **pageGroups** - 页面分组配置，定义系统菜单结构
2. **functions** - 功能模块配置，定义权限组合
3. **roles** - 角色配置，定义用户角色和权限
4. **apps** - 应用配置，多应用支持
5. **branches** - 租户配置，多租户支持
6. **queryFilters** - 查询过滤器，数据访问控制
7. **settings** - 系统设置，全局配置项

### 自动生成模式

通过在模型配置中设置 `autoGenerate: true`，系统会自动生成：
- 默认的 Controller
- 标准的 CRUD 路由
- 管理页面配置
- 功能权限配置
- 角色权限分配

这种模式适用于标准的 CRUD 操作场景，可以大大加快开发速度。

## 数据同步脚本

### syncSaleSkuToGoodsSku.js
同步 saleSkuProduct 表数据到 goods-sku-product 表


## 重要说明
- 除非绝对必要，否则不要创建新文件
- 始终优先编辑现有文件而不是创建新文件
- 除非用户明确要求，否则不要主动创建文档文件（*.md）或 README 文件
- 数据同步脚本位于 `/scripts` 目录下
