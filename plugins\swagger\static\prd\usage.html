<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Strapi Swagger Plugin 进阶使用，包含基础使用、API端点、模块过滤、扩展配置等详细说明">
    <meta name="keywords" content="Strapi, Swagger, 进阶使用, API文档, 配置选项">
    <title>进阶使用 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>

    <div class="layout">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1>📚 Swagger Plugin</h1>
                <p>产品文档中心</p>
            </div>

            <div class="nav-back">
                <a href="index.html">← 返回首页</a>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">概览</div>
                    <a href="index.html" class="nav-item">
                        <i>🏠</i>产品概览
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">功能特性</div>
                    <a href="features.html" class="nav-item">
                        <i>✨</i>功能特性
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">文档指南</div>
                    <a href="installation.html" class="nav-item">
                        <i>🛠️</i>安装配置
                    </a>
                    <a href="usage.html" class="nav-item active">
                        <i>📖</i>进阶使用
                    </a>
                    <a href="api-reference.html" class="nav-item">
                        <i>🔧</i>API 参考
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">支持</div>
                    <a href="troubleshooting.html" class="nav-item">
                        <i>🔍</i>故障排除
                    </a>
                    <a href="release-notes.html" class="nav-item">
                        <i>📋</i>发版记录
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">外部链接</div>
                    <a href="http://localhost:8108/api-docs" class="nav-item" target="_blank">
                        <i>🌐</i>实时文档
                    </a>
                    <a href="https://swagger.io/specification/" class="nav-item" target="_blank">
                        <i>📚</i>OpenAPI 规范
                    </a>
                </div>
            </div>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="content">

            <div class="content-body">
                <div class="toc">
                    <h3>📋 目录</h3>
                    <ul>
                        <li><a href="#basic-usage">基础使用</a></li>
                        <li><a href="#config-setup">扩展配置</a></li>
                        <li><a href="#api-endpoints">文档访问</a></li>
                        <li><a href="#authentication">认证说明</a></li>
                    </ul>
                </div>

                <section class="section" id="basic-usage">
                    <h2>🚀 基础使用</h2>

                    <h3>快速访问</h3>
                    <p>启动项目后，即可访问自动生成的 API 文档：</p>

                    <div class="example-box">
                        <h4>🌐 文档地址</h4>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
# 完整文档界面
http://localhost:8108/api-docs

# 获取 OpenAPI JSON
http://localhost:8108/api-docs/swagger.json
                            </pre>
                        </div>
                    </div>

                    <h3>标准 CRUD 接口（自动生成）</h3>
                    <p>使用 <code>createDefaultRoutes</code> 创建的标准接口会自动生成文档：</p>

                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
// api/product/config/routes.js
const { createDefaultRoutes } = require('accel-utils');

module.exports = {
  routes: [
    ...createDefaultRoutes({
      basePath: '/products',
      controller: 'product'
    })
  ]
};
                        </pre>
                    </div>

                    <div class="tip-box">
                        <h4>💡 自动生成的接口</h4>
                        <p>插件会自动为标准 CRUD 路由生成完整的 OpenAPI 文档，包括请求参数、响应格式、错误代码等。</p>
                    </div>

                    <h3>自定义接口</h3>
                    <p>对于自定义接口，插件支持多种文档处理方式：</p>

                    <ul class="feature-list">
                        <li><strong>🔍 自动识别</strong> - 插件自动扫描控制器方法，提取基本信息生成文档</li>
                        <li><strong>📝 注释增强</strong> - 使用 @swagger 注释为自定义接口添加详细的参数说明</li>
                        <li><strong>🎨 标签分类</strong> - 通过配置文件为不同控制器设置中文标签和描述</li>
                    </ul>

                    <h4>基础自动识别</h4>
                    <p>插件会自动识别控制器中的方法并生成基础文档：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
// api/product/controllers/product.js
module.exports = {
  // 自定义搜索接口
  async search(ctx) {
    const { query } = ctx.request;
    const results = await strapi.services.product.search(query);
    ctx.body = results;
  },

  // 批量操作接口
  async batchUpdate(ctx) {
    const { ids, data } = ctx.request.body;
    const results = await strapi.services.product.batchUpdate(ids, data);
    ctx.body = results;
  }
};
                        </pre>
                    </div>

                    <h4>使用 @swagger 注释</h4>
                    <p>使用 @swagger 注释来详细说明接口的定义，为自定义接口添加完整的文档：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /api/products/search:
 *   get:
 *     summary: 搜索产品
 *     tags: [产品管理]
 *     parameters:
 *       - name: keyword
 *         in: query
 *         description: 搜索关键词
 *         required: true
 *         schema:
 *           type: string
 *       - name: category
 *         in: query
 *         description: 产品分类
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 搜索结果
 */
async search(ctx) {
  // 实现代码
}
                        </pre>
                    </div>

                    <div class="tip-box">
                        <h4>📝 详细注释指南</h4>
                        <p>更多关于 @swagger 注释的详细用法，请参考 <a href="index.html#custom-annotations">自定义注释文档</a>。</p>
                    </div>
                </section>

                <section class="section" id="config-setup">
                    <h2>📋 扩展配置</h2>

                    <h3>扩展模块配置</h3>
                    <p>为无法修改的插件创建扩展配置 <code>config/swagger-extensions.yaml</code>：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 用户认证插件扩展
users-permissions:
  tags:
    - name: 用户认证
      description: 用户认证相关接口
  paths:
    /auth/local:
      post:
        tags: [用户认证]
        summary: 用户登录
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  identifier:
                    type: string
                    description: 用户名或邮箱
                  password:
                    type: string
                    description: 密码
                required: [identifier, password]
        responses:
          200:
            description: 登录成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    jwt:
                      type: string
                    user:
                      type: object
                        </pre>
                    </div>

                    <h3>环境变量配置</h3>
                    <p>在 <code>.env</code> 文件中添加相关配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 生产环境下强制启用 API 文档（可选）
ENABLE_API_DOCS=true

# API 文档访问端口
PORT=8108

# 数据库配置（影响文档生成）
DATABASE_URL=your_database_url

# 应用环境（影响文档可见性）
NODE_ENV=development
                        </pre>
                    </div>

                    <div class="tip-box">
                        <h4>💡 环境变量说明</h4>
                        <p>
                            <strong>ENABLE_API_DOCS</strong>：在生产环境中强制启用文档访问<br>
                            <strong>PORT</strong>：API 文档访问端口<br>
                            <strong>NODE_ENV</strong>：非生产环境下文档默认可访问
                        </p>
                    </div>
                </section>

                <section class="section" id="api-endpoints">
                    <h2>🔧 文档访问</h2>

                    <h3>📖 Swagger UI 端点</h3>
                    <p>访问 Swagger UI 文档界面的基本地址：</p>

                    <div class="card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api-docs</span>
                        </div>
                        <p><strong>描述：</strong>访问完整的 Swagger UI 文档界面</p>
                        
                        <h4>基本访问</h4>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
# 访问完整文档
GET /api-docs
                            </pre>
                        </div>
                    </div>

                    <h3>📄 OpenAPI JSON 端点</h3>
                    <p>获取 OpenAPI 3.0 规范的 JSON 数据：</p>

                    <div class="card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api-docs/swagger.json</span>
                        </div>
                        <p><strong>描述：</strong>获取 OpenAPI 3.0 JSON 规范</p>
                        
                        <h4>基本访问</h4>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
# 获取完整 JSON
GET /api-docs/swagger.json
                            </pre>
                        </div>
                    </div>

                    <h3>🔍 过滤访问</h3>
                    <p>插件支持多种过滤方式，让你快速定位所需接口：</p>

                    <h4>查询参数过滤</h4>
                    <p>支持通过查询参数进行过滤，参数格式说明：</p>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>参数名</th>
                                <th>类型</th>
                                <th>必填</th>
                                <th>描述</th>
                                <th>示例</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>module</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>过滤指定模块</td>
                                <td>base</td>
                            </tr>
                            <tr>
                                <td><code>controller</code></td>
                                <td>string</td>
                                <td>否</td>
                                <td>过滤指定控制器</td>
                                <td>user</td>
                            </tr>
                        </tbody>
                    </table>

                    <h4>形参格式</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# Swagger UI 过滤格式
GET /api-docs/{module}
GET /api-docs/{module}/{controller}

# JSON 过滤格式
GET /api-docs/swagger.json/{module}
GET /api-docs/swagger.json/{module}/{controller}
                        </pre>
                    </div>

                    <h4>具体示例</h4>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# Swagger UI 过滤示例
GET /api-docs/base
GET /api-docs/base/user
GET /api-docs/growth/activity

# JSON 过滤示例
GET /api-docs/swagger.json/base
GET /api-docs/swagger.json/base/user
GET /api-docs/swagger.json/growth/activity
                        </pre>
                    </div>


                    <h4>界面过滤功能</h4>
                    <p>文档页面提供便捷的下拉选择器：</p>
                    <ul>
                        <li><strong>模块选择器</strong>：快速切换不同模块的文档</li>
                        <li><strong>控制器选择器</strong>：进一步过滤指定控制器的接口</li>
                        <li><strong>JSON 选择器</strong>：直接获取不同模块的 OpenAPI JSON 数据</li>
                    </ul>

                    <div class="warning-box">
                        <h4>⚠️ 模块分离</h4>
                        <p>业务模块和扩展模块严格分离，确保文档的清晰性和准确性。</p>
                    </div>

                    <h3>📚 文档资源端点</h3>
                    <p>访问 Markdown 文档和静态资源：</p>

                    <div class="card">
                        <div class="endpoint-header">
                            <span class="method-badge method-get">GET</span>
                            <span class="endpoint-url">/api-doc/markdown/{filename}</span>
                        </div>
                        <p><strong>描述：</strong>访问 Markdown 格式的文档</p>
                        
                        <h4>可用文档</h4>
                        <ul>
                            <li>README.md - 使用指南</li>
                            <li>前端AI接口开发约定.md - 前端开发约定</li>
                            <li>技术规范与实现.md - 技术规范</li>
                            <li>自定义接口注释指南.md - 注释指南</li>
                        </ul>

                        <h4>请求示例</h4>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
# 访问使用指南
GET /api-doc/markdown/README.md

# 访问前端开发约定
GET /api-doc/markdown/前端AI接口开发约定.md
                            </pre>
                        </div>
                    </div>
                </section>

                <section class="section" id="authentication">
                    <h2>🔐 认证说明</h2>

                    <h3>🛡️ 环境限制</h3>
                    <p>默认情况下，API 文档仅在开发环境 (NODE_ENV !== 'production') 下可用。</p>

                    <h3>生产环境启用</h3>
                    <p>如需在生产环境中启用文档，设置环境变量：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
ENABLE_API_DOCS=true
                        </pre>
                    </div>

                    <h3>访问控制</h3>
                    <p>插件本身不提供身份认证功能，建议在反向代理或中间件层面添加访问控制：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# Nginx 配置示例
location /api-docs {
    auth_basic "API Documentation";
    auth_basic_user_file /etc/nginx/.htpasswd;
    proxy_pass http://localhost:8108;
}
                        </pre>
                    </div>
                </section>


                <section class="section" id="config-setup">
                    <h2>📋 扩展配置</h2>


                    <h3>扩展模块配置</h3>
                    <p>为无法修改的插件创建扩展配置 <code>config/swagger-extensions.yaml</code>：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 用户认证插件扩展
users-permissions:
  tags:
    - name: 用户认证
      description: 用户认证相关接口
  paths:
    /auth/local:
      post:
        tags: [用户认证]
        summary: 用户登录
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  identifier:
                    type: string
                    description: 用户名或邮箱
                  password:
                    type: string
                    description: 密码
                required: [identifier, password]
        responses:
          200:
            description: 登录成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    jwt:
                      type: string
                    user:
                      type: object
    </pre>
</div>

                    <h3>环境变量配置</h3>
                    <p>在 <code>.env</code> 文件中添加相关配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 生产环境下强制启用 API 文档（可选）
ENABLE_API_DOCS=true

# API 文档访问端口
PORT=8108

# 数据库配置（影响文档生成）
DATABASE_URL=your_database_url

# 应用环境（影响文档可见性）
NODE_ENV=development
                        </pre>
                    </div>

                    <div class="tip-box">
                        <h4>💡 环境变量说明</h4>
                        <p>
                            <strong>ENABLE_API_DOCS</strong>：在生产环境中强制启用文档访问<br>
                            <strong>PORT</strong>：API 文档访问端口<br>
                            <strong>NODE_ENV</strong>：非生产环境下文档默认可访问
                        </p>
                    </div>
                </section>

            </div>
        </main>
    </div>

    <script src="scripts.js"></script>
</body>
</html>
