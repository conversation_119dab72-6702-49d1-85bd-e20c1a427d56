module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.customerInactivePoolId,
  "connection": "mingdao",
  "info": {
    name: 'CustomerInactivePool',
    label: '不活跃学校',
    description: ''
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    name: {
      // label: '名称',
      "ref": "672d912b962c1f8ca4a20962"
    },
    customerId: {
      // label: '客户ID',
      "ref": "672d912b962c1f8ca4a20963"
    },
    schoolId: {
      // label: '学校 ID',
      "ref": "675bd26fba60f67ec34d6e5e"
      // "ref": "672d912b962c1f8ca4a20964"
    },
    qunId: {
      // label: '群ID',
      "ref": "6735aa579b0246655126adea"
    },
    hideQunQrCode: {
      // label: '是否隐藏群二维码',
      "ref": "672d912b962c1f8ca4a20967"
    },
    yjShowSalesManager: {
      // label: '显示直营经理二维码',
      "ref": "67469c83ba60f67ec34b3b32"
    },
    schoolCategory: {
      // label: '学校类别',
      "ref": "672d912b962c1f8ca4a2096c"
    },
    // isVIP: {
    //   // label: '是否付费',
    //   "ref": "672d912b962c1f8ca4a2096d"
    // },
    yjTag: {
      // label: '阅卷签约标签',
      "ref": "672d912b962c1f8ca4a2097a"
    },
    customerTags: {
      // label: '客户标签',
      "ref": "6731cdc91637ee6db9afdec2"
    },
    isTest: {
      // label: '是否测试学校',
      "ref": "672d912b962c1f8ca4a2098d"
    },
    obsolete: {
      // label: '是否废弃学校',
      "ref": "672d912b962c1f8ca4a2098e"
    },
    lastExamTime: {
      // label: '最后考试时间',
      "ref": "672d912b962c1f8ca4a209ad"
    },
    province: {
      // label: '省份',
      "ref": "672d912b962c1f8ca4a209b1"
    },
    city: {
      // label: '城市',
      "ref": "672d912b962c1f8ca4a209b2"
    },
    district: {
      // label: '区县',
      "ref": "672d912b962c1f8ca4a209b3"
    },
    edu_system: {
      // label: '学制',
      "ref": "672d912b962c1f8ca4a209b4"
    },
    system: {
      // label: '体制',
      "ref": "672d912b962c1f8ca4a209b5"
    },
    mingyou_school_type: {
      // label: '名优校类型',
      "ref": "672d912b962c1f8ca4a209b6"
    },
    total_number: {
      // label: '学校总人数',
      "ref": "672d912b962c1f8ca4a209b7"
    },
    school_size: {
      // label: '学校规模',
      "ref": "672d912b962c1f8ca4a209b8"
    },
    crm_type: {
      // label: '类型',
      "ref": "672d912b962c1f8ca4a209b9"
    },
    yjVersion: {
      // label: '阅卷版本',
      "ref": "672d912b962c1f8ca4a209bd"
    },
    yjStartTime: {
      // label: '阅卷开始时间',
      "ref": "6731c8c51637ee6db9afde65"
    },
    yjEndTime: {
      // label: '阅卷截止时间',
      "ref": "672d912b962c1f8ca4a209bf"
    },
    fxVersion: {
      // label: '分析版本',
      "ref": "672d912b962c1f8ca4a209c0"
    },
    fxStartTime: {
      // label: '分析开始时间',
      "ref": "672d912b962c1f8ca4a209c1"
    },
    fxEndTime: {
      // label: '分析截止时间',
      "ref": "672d912b962c1f8ca4a209c2"
    },
    tkVersion: {
      // label: '题库版本',
      "ref": "672d912b962c1f8ca4a209c3"
    },
    tkStartTime: {
      // label: '题库开始时间',
      "ref": "672dc6621637ee6db9afccc4"
    },
    tkEndTime: {
      // label: '题库截止时间',
      "ref": "672dc6621637ee6db9afccc5"
    },

    // 立项信息
    salesArchiveStatus: {
      // label: '直营建档状态',
      "ref": "6743fd03ba60f67ec349968e"
    },
    projectNo: {
      // label: '直营立项项目编号',
      "ref": "673bf3a4ba60f67ec3495ffd"
    },
    projectApproval: {
      // label: '立项审批',
      "ref": "67400984ba60f67ec34989d5"
    },
    projectStatus: {
      // label: '项目状态',
      "ref": "67400984ba60f67ec34989d6"
    },
    projectManger: {
      // label: '项目经理',
      "ref": "67400984ba60f67ec34989d7"
    },
    projectTag: {
      // label: '项目标签',
      "ref": "67400984ba60f67ec34989d9"
    },
    projectObjective: {
      // label: '沟通目的',
      "ref": "67400984ba60f67ec34989da"
    },
    projectApprovalRemark: {
      // label: '立项审批备注',
      "ref": "67400984ba60f67ec34989db"
    },

    // 阅卷授权
    authAgent: {
      // label: '授权经销商（new）',
      "ref": "673bf0c2ba60f67ec3495fe5"
    },
    authAgentId: {
      // label: '授权经销商id',
      "ref": "67444a8cba60f67ec3499a49"
    },
    authAgentNo: {
      // label: '授权经销商no',
      "ref": "67444aa3ba60f67ec3499a54"
    },
    authAgentName: {
      // label: '授权经销商名称',
      "ref": "67444a8cba60f67ec3499a4a"
    },
    authType: {
      // label: '授权类型（new）',
      "ref": "673bf0c2ba60f67ec3495fe7"
    },
    authEndTime: {
      // label: '授权到期日期（new）',
      "ref": "673bf0c2ba60f67ec3495fe8"
    },
    authRemark: {
      // label: '备注（new）',
      "ref": "673bf3a4ba60f67ec3495ffe"
    },

    operator: {
      // label: '修改操作人',
      "ref": "67564f54ba60f67ec34c67d8"
    },
    activated: {
      // label: '是否激活',
      "ref": "677f3a7b9e13a09bfff91f3b"
    },
  }
}
