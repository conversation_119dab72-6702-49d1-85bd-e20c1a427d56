module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.customerServiceId,
  "connection": "mingdao",
  "info": {
    name: 'CustomerService',
    label: '学校档案',
    description: '学校档案'
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    name: {
      // label: '名称',
      "ref": "672d912b962c1f8ca4a20962"
    },
    customerId: {
      // label: '客户ID',
      "ref": "672d912b962c1f8ca4a20963"
    },
    schoolId: {
      // label: '学校 ID',
      "ref": "675bd26fba60f67ec34d6e5e"
      // "ref": "672d912b962c1f8ca4a20964"
    },
    qunId: {
      // label: '群ID',
      "ref": "6735aa579b0246655126adea"
    },
    hideQunQrCode: {
      // label: '是否隐藏群二维码',
      "ref": "672d912b962c1f8ca4a20967"
    },
    yjShowSalesManager: {
      // label: '显示直营经理二维码',
      "ref": "67469c83ba60f67ec34b3b32"
    },
    directServiceTeam: {
      // label: '运营小组',
      mainField: 'name',
      "ref": "6735ce649b0246655126b9cb"
    },
    directServiceManager: {
      // label: '直服经理',
      mainField: 'username',
      "ref": "672d912b962c1f8ca4a20969"
    },
    directSalesTeam: {
      // label: '直营小组',
      mainField: 'name',
      "ref": "6735ce649b0246655126b9cd"
    },
    directSalesManager: {
      // label: '直营经理',
      mainField: 'username',
      "ref": "672d912b962c1f8ca4a2096b"
    },
    // yjShowSalesManager: {
    //   // label: '阅卷显示直营经理',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },
    schoolCategory: {
      // label: '学校类别',
      "ref": "672d912b962c1f8ca4a2096c"
    },
    // isVIP: {
    //   // label: '是否付费',
    //   "ref": "672d912b962c1f8ca4a2096d"
    // },
    yjTag: {
      // label: '阅卷签约标签',
      "ref": "672d912b962c1f8ca4a2097a"
    },
    // fxTag: {
    //   // label: '分析标签',
    //   "ref": "673adb3cba60f67ec3494f94"
    // },
    // lastServiceTrackTime: {
    //   // label: '最近一次服务记录',
    //   "ref": "672d912b962c1f8ca4a2096e"
    // },
    // servicePlanTrackTime: {
    //   // label: '直服计划沟通时间',
    //   "ref": "672d912b962c1f8ca4a2096f"
    // },
    // lastSalesTrackTime: {
    //   // label: '最近一次直营跟进',
    //   "ref": "672d912b962c1f8ca4a20970"
    // },
    // lastServiceAssignTime: {
    //   // label: '最近直服分配时间',
    //   "ref": "672d912b962c1f8ca4a20971"
    // },
    // lastSalesAssignTime: {
    //   // label: '最近直营分配时间',
    //   "ref": "672d912b962c1f8ca4a20972"
    // },
    // customerProblems: {
    //   // label: '客户问题',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },

    // customerAssignRecords: {
    //   // label: '客户分配记录',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },
    customerContacts: {
      // label: '客户联系人',
      "ref": "672d912b962c1f8ca4a20977"
    },
    // interactContacts: {
    //   // label: '已沟通联系人',
    //   "ref": "672d912b962c1f8ca4a20978"
    // },
    // customerServiceManagers: {
    //   // label: '客户服务经理',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },

    customerTags: {
      // label: '客户标签',
      "ref": "6731cdc91637ee6db9afdec2"
    },
    customerServiceFollowRecords: {
      // label: '运营经理跟进记录	',
      "ref": "6731c6391637ee6db9afde36"
    },
    customerSalesFollowRecords: {
      // label: '直营经理跟进记录	',
      "ref": "6731c6571637ee6db9afde3d"
    },
    customerServiceFlowRecords: {
      // label: '运营经理流转记录	',
      "ref": "675188d4ba60f67ec34c379c"
    },
    customerSalesFlowRecords: {
      // label: '直营经理流转记录	',
      "ref": "675188d4ba60f67ec34c379e"
    },
    // customerBargainChecks: {
    //   // label: '客户跟进记录',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },
    // serviceIsAddWx: {
    //   // label: '直服是否已加微信',
    //   "ref": "672d912b962c1f8ca4a2097e"
    // },
    // salesIsAddWx: {
    //   // label: '直营是否已加微信',
    //   "ref": "672d912b962c1f8ca4a2097f"
    // },
    // isFollowValid: {
    //   // label: '沟通记录是否有效',
    //   "ref": "672d912b962c1f8ca4a20980"
    // },
    // customerIsValid: {
    //   // label: '有效沟通学校',
    //   "ref": "6736c489ba60f67ec349411a"
    // },
    // customerIsValidMark: {
    //   // label: '【有效沟通学校】标记无效',
    //   "ref": "6736c489ba60f67ec349411b"
    // },
    // customerValidTime: {
    //   // label: '标记有效沟通学校时间',
    //   "ref": "672d912b962c1f8ca4a20983"
    // },
    // customerIsValidLink: {
    //   // label: '有效链接学校',
    //   "ref": "6736c489ba60f67ec349411c"
    // },
    // customerIsValidLinkTime: {
    //   // label: '标记有效链接学校时间',
    //   "ref": "672d912b962c1f8ca4a20985"
    // },
    // customerIsValidLinkMark: {
    //   // label: '【有效链接学校】标记无效',
    //   "ref": "6736c489ba60f67ec349411d"
    // },
    // proxyName: {
    //   // label: '独家代理商名称',
    //   "ref": "672d912b962c1f8ca4a20987"
    // },
    // exclusiveArea: {
    //   // label: '独家授权区域',
    //   "ref": "672d912b962c1f8ca4a20988"
    // },
    // exclusiveSchool: {
    //   // label: '独家授权学校',
    //   "ref": "672d912b962c1f8ca4a20989"
    // },
    // specialArea: {
    //   // label: '特殊区域',
    //   "ref": "672d912b962c1f8ca4a2098a"
    // },
    // specialAgent: {
    //   // label: '特殊商',
    //   "ref": "672d912b962c1f8ca4a2098b"
    // },
    // noTouching: {
    //   // label: '不建议沟通',
    //   "ref": "672d912b962c1f8ca4a2098c"
    // },
    isTest: {
      // label: '是否测试学校',
      "ref": "672d912b962c1f8ca4a2098d"
    },
    obsolete: {
      // label: '是否废弃学校',
      "ref": "672d912b962c1f8ca4a2098e"
    },
    // intendedSalesMonth: {
    //   // label: '意向成交月份',
    //   "ref": "672d912b962c1f8ca4a2098f"
    // },
    // clueSource: {
    //   // label: '线索来源',
    //   "ref": "672d912b962c1f8ca4a20990"
    // },
    // directServiceClueIntention: {
    //   // label: '直服线索意向度',
    //   "ref": "672d912b962c1f8ca4a20991"
    // },
    // dealObstacle: {
    //   // label: '未谈单原因',
    //   "ref": "672d912b962c1f8ca4a20992"
    // },
    // dealObstacleReason: {
    //   // label: '未谈单其他说明',
    //   "ref": "672d912b962c1f8ca4a20993"
    // },
    // dealStage: {
    //   // label: '当前阶段',
    //   "ref": "672d912b962c1f8ca4a20994"
    // },
    // clueStage: {
    //   // label: '线索阶段环节',
    //   "ref": "672d912b962c1f8ca4a20995"
    // },
    // incubationStage: {
    //   // label: '培育阶段环节',
    //   "ref": "672d912b962c1f8ca4a20996"
    // },
    // initialCooperationStage: {
    //   // label: '初次合作阶段环节',
    //   "ref": "672d912b962c1f8ca4a20997"
    // },
    // lightOperationStage: {
    //   // label: '轻运营阶段环节',
    //   "ref": "672d912b962c1f8ca4a20998"
    // },
    // expansionStage: {
    //   // label: '深耕阶段环节',
    //   "ref": "672d912b962c1f8ca4a20999"
    // },
    // followStatus: {
    //   // label: '跟进状态',
    //   "ref": "672d912b962c1f8ca4a2099a"
    // },
    // abandonReason: {
    //   // label: '放弃原因',
    //   "ref": "672d912b962c1f8ca4a2099b"
    // },
    // onSiteVisit: {
    //   // label: '是否需要面访',
    //   "ref": "672d912b962c1f8ca4a2099c"
    // },
    // onSiteVisitors: {
    //   // label: '面访指派人员',
    //   "ref": "672d912b962c1f8ca4a2099d"
    // },
    totalNumber: {
      // label: '学校人数（直营）',
      "ref": "672d912b962c1f8ca4a2099e"
    },
    eduSystem: {
      // label: '学制（直营）',
      "ref": "672d912b962c1f8ca4a2099f"
    },
    schoolExamFrequency: {
      // label: '学校考试频次',
      "ref": "672d912b962c1f8ca4a209a0"
    },
    currentYjMethod: {
      // label: '当前阅卷方式',
      "ref": "672d912b962c1f8ca4a209a1"
    },
    currentYjProduct: {
      // label: '当前使用阅卷品牌',
      "ref": "672d912b962c1f8ca4a209a2"
    },
    // competitorYjExpiration: {
    //   // label: '竞品阅卷到期日期',
    //   "ref": "672d912b962c1f8ca4a209a3"
    // },
    yjHistoryPayStandard: {
      // label: '阅卷历史付费标准',
      "ref": "672d912b962c1f8ca4a209a4"
    },
    currentTkProduct: {
      // label: '当前题库产品',
      "ref": "672d912b962c1f8ca4a209a5"
    },
    tkHistoryPayStandard: {
      // label: '题库历史付费标准',
      "ref": "672d912b962c1f8ca4a209a6"
    },
    // hfsPayStatus: {
    //   // label: '好分数付费状态',
    //   "ref": "672d912b962c1f8ca4a209a7"
    // },
    requiredProducts: {
      // label: '产品需求',
      "ref": "672d912b962c1f8ca4a209a8"
    },
    demandIntention: {
      // label: '需求意向度',
      "ref": "672d912b962c1f8ca4a209a9"
    },
    // directSalesForecast: {
    //   // label: '直营成交预判',
    //   "ref": "672d912b962c1f8ca4a209aa"
    // },
    schoolBudget: {
      // label: '学校预算情况',
      "ref": "672d912b962c1f8ca4a209ab"
    },
    // currentFollowResult: {
    //   // label: '当前跟进结论',
    //   "ref": "672d912b962c1f8ca4a209ac"
    // },
    lastExamTime: {
      // label: '最后考试时间',
      "ref": "672d912b962c1f8ca4a209ad"
    },
    // t_count: {
    //   // label: '老师数',
    //   "ref": "672d912b962c1f8ca4a209ae"
    // },
    // s_count: {
    //   // label: '学生数',
    //   "ref": "672d912b962c1f8ca4a209af"
    // },
    // deleted: {
    //   // label: '是否删除',
    //   "ref": "672d8ee8962c1f8ca4a20859"
    // },
    province: {
      // label: '省份',
      "ref": "672d912b962c1f8ca4a209b1"
    },
    city: {
      // label: '城市',
      "ref": "672d912b962c1f8ca4a209b2"
    },
    district: {
      // label: '区县',
      "ref": "672d912b962c1f8ca4a209b3"
    },
    edu_system: {
      // label: '学制',
      "ref": "672d912b962c1f8ca4a209b4"
    },
    system: {
      // label: '体制',
      "ref": "672d912b962c1f8ca4a209b5"
    },
    mingyou_school_type: {
      // label: '名优校类型',
      "ref": "672d912b962c1f8ca4a209b6"
    },
    total_number: {
      // label: '学校总人数',
      "ref": "672d912b962c1f8ca4a209b7"
    },
    school_size: {
      // label: '学校规模',
      "ref": "672d912b962c1f8ca4a209b8"
    },
    crm_type: {
      // label: '类型',
      "ref": "672d912b962c1f8ca4a209b9"
    },
    // '[sales_manager]': {
    //   // label: '业务经理',
    //   "ref": "672d912b962c1f8ca4a209ba"
    // },
    // '[proxy]': {
    //   // label: '代理商',
    //   "ref": "672d912b962c1f8ca4a209bb"
    // },
    // usages: {
    //   // label: '开通应用',
    //   "ref": "672d912b962c1f8ca4a209bc"
    // },
    yjVersion: {
      // label: '阅卷版本',
      "ref": "672d912b962c1f8ca4a209bd"
    },
    yjStartTime: {
      // label: '阅卷开始时间',
      "ref": "6731c8c51637ee6db9afde65"
    },
    yjEndTime: {
      // label: '阅卷截止时间',
      "ref": "672d912b962c1f8ca4a209bf"
    },
    fxVersion: {
      // label: '分析版本',
      "ref": "672d912b962c1f8ca4a209c0"
    },
    fxStartTime: {
      // label: '分析开始时间',
      "ref": "672d912b962c1f8ca4a209c1"
    },
    fxEndTime: {
      // label: '分析截止时间',
      "ref": "672d912b962c1f8ca4a209c2"
    },
    tkVersion: {
      // label: '题库版本',
      "ref": "672d912b962c1f8ca4a209c3"
    },
    tkStartTime: {
      // label: '题库开始时间',
      "ref": "672dc6621637ee6db9afccc4"
    },
    tkEndTime: {
      // label: '题库截止时间',
      "ref": "672dc6621637ee6db9afccc5"
    },
    // salesId: {
    //   // label: '直营经理Id',
    //   "ref": "67356e259b0246655126ab4d"
    // },
    // serviceId: {
    //   // label: '直服经理Id',
    //   "ref": "67356e259b0246655126ab4e"
    // },

    // 立项信息
    salesArchiveStatus: {
      // label: '直营建档状态',
      "ref": "6743fd03ba60f67ec349968e"
    },
    projectNo: {
      // label: '直营立项项目编号',
      "ref": "673bf3a4ba60f67ec3495ffd"
    },
    projectApproval: {
      // label: '立项审批',
      "ref": "67400984ba60f67ec34989d5"
    },
    projectStatus: {
      // label: '项目状态',
      "ref": "67400984ba60f67ec34989d6"
    },
    projectManger: {
      // label: '项目经理',
      "ref": "67400984ba60f67ec34989d7"
    },
    projectTag: {
      // label: '项目标签',
      "ref": "67400984ba60f67ec34989d9"
    },
    projectObjective: {
      // label: '沟通目的',
      "ref": "67400984ba60f67ec34989da"
    },
    projectApprovalRemark: {
      // label: '立项审批备注',
      "ref": "67400984ba60f67ec34989db"
    },

    // 阅卷授权
    authAgent: {
      // label: '授权经销商（new）',
      "ref": "673bf0c2ba60f67ec3495fe5"
    },
    authAgentId: {
      // label: '授权经销商id',
      "ref": "67444a8cba60f67ec3499a49"
    },
    authAgentNo: {
      // label: '授权经销商no',
      "ref": "67444aa3ba60f67ec3499a54"
    },
    authAgentName: {
      // label: '授权经销商名称',
      "ref": "67444a8cba60f67ec3499a4a"
    },
    authType: {
      // label: '授权类型（new）',
      "ref": "673bf0c2ba60f67ec3495fe7"
    },
    authEndTime: {
      // label: '授权到期日期（new）',
      "ref": "673bf0c2ba60f67ec3495fe8"
    },
    authRemark: {
      // label: '备注（new）',
      "ref": "673bf3a4ba60f67ec3495ffe"
    },

    unit: {
      // label: '所属单元',
      "ref": "6751185eba60f67ec34c238d"
    },
    // serviceTeam: {
    //   // label: '运营组',
    //   "ref": "6751185eba60f67ec34c238f"
    // },
    // salesTeam: {
    //   // label: '直营组',
    //   "ref": "6751185eba60f67ec34c2391"
    // },
    huobanProjectInfo: {
      'ref': '67526603ba60f67ec34c3ffd'
    },

    remoteProjectTag: {
      // label: '项目标签（new）',
      "ref": "67345c461637ee6db9affcc7"
    },
    remoteProjectObjective: {
      // label: '沟通目的（new）',
      "ref": "67345c461637ee6db9affcc8"
    },

    operator: {
      // label: '修改操作人',
      "ref": "67564f54ba60f67ec34c67d8"
    },
    distributor: {
      // label: '分配操作人',
      "ref": "67564f54ba60f67ec34c67da"
    },
    distributionRemark : {
      // label: '分配备注',
      ref: "67603a8b9e13a09bfff16f85"
    },
    facilitator: {
      // label: '直营谈单协助人',
      "ref": "67593ffbba60f67ec34ce733"
    },
    yxzxType: {
      // label: '云校智学学校类型',
      "ref": "67f774059e13a09bff28d833"
    },
  }
}
