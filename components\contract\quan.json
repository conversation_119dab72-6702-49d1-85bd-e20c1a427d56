{"collectionName": "components_contract_quan", "info": {"name": "<PERSON>uan", "label": "券", "icon": "comment", "description": ""}, "options": {}, "attributes": {"quanGoods": {"label": "商品", "type": "string", "options": [{"value": "101", "label": "360会员券"}, {"value": "200", "label": "错题本券（初中）"}, {"value": "201", "label": "错题本券（高中）"}, {"value": "202", "label": "错题本券（学段通用）"}, {"value": "210", "label": "简装版错题本券（初中）"}, {"value": "211", "label": "简装版错题本券（高中）"}, {"value": "212", "label": "简装版错题本券（学段通用）"}, {"value": "300", "label": "错题本科次券（初中）"}, {"value": "301", "label": "错题本科次券（高中）"}, {"value": "310", "label": "简装版错题本科次券（初中）"}, {"value": "311", "label": "简装版错题本科次券（高中）"}]}, "limitType": {"label": "定向类型", "type": "enumeration", "enum": ["通用", "定向至区域", "定向至学校"]}, "limits": {"label": "定向范围", "type": "string"}, "expTime": {"label": "过期日期", "type": "date"}, "calculatedUnitPrice": {"label": "原单价", "editable": false, "format": "rmb", "type": "number"}, "unitPrice": {"label": "实际单价", "format": "rmb", "type": "number"}, "quantity": {"label": "数量", "type": "number"}, "totalPrice": {"label": "总金额", "format": "rmb", "type": "number"}, "schools": {"label": "经营授权学校列表", "size": 12, "type": "json"}, "remark": {"label": "备注", "type": "string"}}}