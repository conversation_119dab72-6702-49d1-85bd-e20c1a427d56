'use strict';

module.exports = {
    "collectionName": "order",
    "info": {
        "name": "Order",
        "label": "订单",
        "description": "订单"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true,
        "workflows": [
            {
                "id": "65e52a3886ebc43fc8ada3e3",
                "label": "订单审核",
                "params": {
                    "id": "id",
                }
            }
        ]
    },
    "pluginOptions": {},
    "attributes": {
        "no": {
            "label": "订单编号",
            "size": 12,
            "type": "string"
        },
        "type": {
            "label": "类型",
            "editable": false,
            "type": "number",
            "size": 3,
            "options": [
                {
                    "value": 1,
                    "label": "合同发起"
                },
                {
                    "value": 2,
                    "label": "经销商发起"
                }
            ]
        },
        "goodsTarget": {
            "label": "商品列表",
            "type": "component",
            "repeatable": true,
            "displayMode": "table",
            "component": "contract.target",
            "viewTableConfig": {
                "columns": ["school", "goods", "calculatedUnitPrice", "unitPrice", "quantity", "beginTime", "endTime", "totalPrice", "remark"]
            }
        },
        // "goodsItem": {
        //     "label": "商品列表",
        //     "type": "component",
        //     "repeatable": true,
        //     "displayMode": "table",
        //     "component": "contract.goods-item"
        // },
        "approveStatus": {
            "label": "审批状态",
            "type": "number",
            "size": 3,
            "editable": false
        },
        "approveProcessId": {
            "label": "审批流程Id",
            "type": "string",
            "size": 3,
            "editable": false
        },
        "approveProcessRemark": {
            "label": "审批批注",
            "type": "string",
            "size": 3,
            "editable": false
        },
        "approveBeginTime": {
            "label": "审批开始时间",
            "type": "date",
            "size": 3,
            "format": "YYYY-MM-DD",
            "editable": false
        },
        "approveFinishTime": {
            "label": "审批完成时间",
            "type": "date",
            "size": 3,
            "format": "YYYY-MM-DD",
            "editable": false
        },
        "paymentStatus": {
            "label": "付款状态",
            "type": "number",
            "size": 3,
            "editable": false
        },
        "paymentInstallmentPlans": {
            "label": "支付计划列表",
            "type": "component",
            "repeatable": true,
            "displayMode": "table",
            "component": "contract.payment-installment-plan"
        },
        "processRecord": {
            "label": "关联流程记录",
            "editable": false,
            "collection": "process-record"
        },
        "contract": {
            "label": "关联合同",
            "editable": false,
            "dominant": true,
            "mainField": "no",
            "model": "contract"
        },
    }
}