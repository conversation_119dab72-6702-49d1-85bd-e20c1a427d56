'use strict'
const axios = require('axios')
const JSEncrypt = require('./jsencrypt')
const CryptoJS = require('crypto-js')
const { createOpenApiConnection } = require('./mingdao-open-api')
const { asyncCache, limitConcurrency } = require('./promise-utils')

function createWebApiConnection (webApiConfig) {
  const defaultProjectId = webApiConfig.projectId
  const publicKey = webApiConfig.publicKey

  // 使用时间戳+随机数组合，既保留时序又避免冲突
  const connectionId = `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`

  // 明道云 Web API
  const mingdaoWebApi = async function () {

    let mingdaoWebApiCounter = 0
    const authData = {
      token: '',
      expiresAt: '',
      syncPromise: null
    }

    async function syncAuthData () {
      authData.token = await getAuthorization(webApiConfig.account, webApiConfig.password)
      authData.expiresAt = new Date(+new Date() + 1000 * 60 * 60 * 2)
    }

    // Web 接口数据解密
    const _interfaceDataDecryption = source => {
      const { data, key, encrypted } = source || {}
      if (encrypted) {
        const decrypted = CryptoJS.AES.decrypt(data, CryptoJS.enc.Utf8.parse(key), {
          iv: CryptoJS.enc.Utf8.parse(publicKey.replace(/[\r\n]/, '').slice(26, 42)),
        })
        // 返回解密后的数据
        return {
          data: JSON.parse(decrypted.toString(CryptoJS.enc.Utf8)),
        }
      }
      return source
    }

    // 每小时定时更新
    setInterval(syncAuthData, 1000 * 60 * 60)

    return {
      /**
       * 请求明道云 Web API
       * @param {AxiosRequestConfig<D>} config
       * @param {boolean} isRetry
       * @returns {Promise<AxiosResponse<any>> | *}
       */
      async axios (config, isRetry = false) {
        // BaseURL
        config.baseURL = webApiConfig.host
        // Method
        config.method = config.method || 'GET'
        // Authorization
        if (authData.syncPromise && (!authData.token || authData.expiresAt && new Date() > authData.expiresAt)) {
          await authData.syncPromise
        }
        if (!authData.token || (authData.expiresAt && new Date() > authData.expiresAt)) {
          authData.syncPromise = syncAuthData()
          await authData.syncPromise
          authData.syncPromise = null
        }
        config.headers = {
          Authorization: authData.token
        }
        // Axios Request
        let requestTag = `[WebApi:${connectionId}] [${mingdaoWebApiCounter++}] ${config.method.toUpperCase()} ${config.url}`
        if (config.params) {
          requestTag += ' ' + JSON.stringify(config.params)
        }
        if (config.data) {
          requestTag += ' ' + JSON.stringify(config.data)
        }
        console.time(requestTag)
        let res
        try {
          res = await axios(config)
        } catch (e) {
          if (e.response?.status === 401) {
            authData.token = ''
            authData.expiresAt = ''
            authData.syncPromise = syncAuthData()
          } else throw e
        } finally {
          console.timeEnd(requestTag)
          // if (config.data) {
          //   console.info('Request Body',config.data)
          // }
        }
        let data = res.data

        // 应用无权限则自动添加管理员权限
        if (data.state === 7 && config.data['appId'] && !isRetry
          && !config.url.includes('AddRoleMemberForAppAdmin')) {
          await addRoleMemberForAppAdmin(config.data['appId'])
          return await this.axios(config, true)
        }
        if (data.state !== 1) {
          throw Error(JSON.stringify(data))
        }
        data = {
          sourceData: data.data,
          ..._interfaceDataDecryption(data)
        }
        return data.data
      }
    }
  }()

  async function getAuthorization (account, password) {
    // RSAKey 加密
    const _encrypt = text => {
      const encrypt = new JSEncrypt()
      encrypt.setPublicKey(publicKey)
      return encrypt.encrypt(encodeURIComponent(text))
    }
    const data = {
      account: _encrypt(account),
      password: _encrypt(password),
      captchaType: 1,
      isCookie: false
    }
    const res = await axios({
      method: 'POST',
      baseURL: webApiConfig.host,
      url: '/wwwapi/Login/MDAccountLogin',
      data: data
    }).catch(e => {
      console.error(`Request Error: [${e.request._currentUrl || e.config.url}]`, e.toString())
    })

    const parseSetCookie = (setCookieHeaders) => {
      return setCookieHeaders.map(setCookieHeader => {
        const cookieParts = setCookieHeader.split(';').map(part => part.trim())
        const cookie = {
          name: cookieParts[0].split('=')[0],
          value: cookieParts[0].split('=')[1],
          expires: null,
          path: null,
          domain: null,
          secure: false,
          httpOnly: false
        }
        for (let i = 1; i < cookieParts.length; i++) {
          const part = cookieParts[i]
          if (part.startsWith('expires=')) {
            cookie.expires = part.substring(8)
          } else if (part.startsWith('path=')) {
            cookie.path = part.substring(5)
          } else if (part.startsWith('domain=')) {
            cookie.domain = part.substring(7)
          } else if (part === 'secure') {
            cookie.secure = true
          } else if (part === 'httpOnly') {
            cookie.httpOnly = true
          }
        }
        return cookie
      })
    }

    const setCookieHeaders = res.headers['set-cookie']
    if (!setCookieHeaders) {
      console.log('No set-cookie header found')
    }
    const cookies = parseSetCookie(setCookieHeaders)
    const authKey = 'md_pss_id'
    const authValue = cookies.find(e => e.name === authKey && e.value)?.value
    return `${authKey} ${authValue}`
  }

  // 获取应用授权密钥
  async function getAuthorizes (appId) {
    return await (await mingdaoWebApi).axios({
      method: 'POST',
      url: '/wwwapi/AppManagement/GetAuthorizes',
      data: {
        appId: appId,
      }
    })
  }

  // 获取应用列表
  async function getAppsForProject (projectId) {
    return await (await mingdaoWebApi).axios({
      method: 'POST',
      url: '/wwwapi/AppManagement/GetAppsForProject',
      data: {
        projectId: projectId || defaultProjectId,
        status: '',
        order: 3,
        pageIndex: 1,
        pageSize: 10000,
        keyword: '',
        containsLink: true,
        dbInstanceId: 'all',
        filterDBType: 0
      }
    })
  }

  // 检查应用是否有管理员权限
  async function checkAppAdminForUser (appId) {
    return await (await mingdaoWebApi).axios({
      method: 'POST',
      url: '/wwwapi/AppManagement/CheckAppAdminForUser',
      data: {
        appId: appId,
      }
    })
  }

  // 指定应用赋予管理员权限
  async function addRoleMemberForAppAdmin (appId) {
    return await (await mingdaoWebApi).axios({
      method: 'POST',
      url: '/wwwapi/AppManagement/AddRoleMemberForAppAdmin',
      data: {
        appId: appId,
      }
    })
  }

  /**
   * 获取工作表信息
   * @param worksheetId
   * @param options
   * @returns {Promise<axios.AxiosResponse<MingdaoWorksheetData>|*>}
   */
  async function getWorksheetInfo (worksheetId, options = {}) {
    return await (await mingdaoWebApi).axios({
      method: 'POST',
      url: '/wwwapi/Worksheet/GetWorksheetInfo',
      data: {
        worksheetId: worksheetId,
        getViews: true,
        getTemplate: true,
        getRules: true,
        getSwitchPermit: true,
        ...options
      }
    })
  }

  // 获取模块版本信息
  // modelType: 3 - WorksheetInfo
  async function getVersion (moduleType, sourceId) {
    return await (await mingdaoWebApi).axios({
      method: 'GET',
      url: '/wwwapi/Version/GetVersion',
      params: {
        moduleType: moduleType,
        sourceId: sourceId,
      }
    })
  }

  const addWorksheetRowData = {
    'silent': true,
    'receiveControls': [{
      'controlId': '6715b9f4ae4fd9d1ba4f1b12',
      'type': 2,
      'value': 'QQQAAA',
      'controlName': '名称',
      'dot': 0
    }, {
      'controlId': '6715ba1dae4fd9d1ba4f1b1c',
      'type': 41,
      'value': '<p>前言</p><p>第一章：…</p><p>第二章：…</p><p>第三章：…</p><p>&nbsp;</p>',
      'controlName': '目录',
      'dot': 0
    }, {
      'controlId': '6715c5f3ae4fd9d1ba4f1c10',
      'type': 37,
      'value': 0,
      'controlName': '评价总数',
      'dot': 0
    }, {
      'controlId': '6715cd40ae4fd9d1ba4f1c5f',
      'type': 45,
      'value': 'https://a.iyunxiao.com/book:66124928000007aa699415ee',
      'controlName': '嵌入',
      'dot': 0
    }, {
      'controlId': '6715cc9aae4fd9d1ba4f1c42',
      'type': 6,
      'value': '0',
      'controlName': '展示',
      'dot': 0
    }, {
      'controlId': '6715cc9aae4fd9d1ba4f1c43',
      'type': 6,
      'value': '0',
      'controlName': '点击',
      'dot': 0
    }, {
      'controlId': '6715ba1dae4fd9d1ba4f1b1d',
      'type': 6,
      'value': '0',
      'controlName': '点赞',
      'dot': 0
    }, {
      'controlId': '6715ba1dae4fd9d1ba4f1b1e',
      'type': 6,
      'value': '0',
      'controlName': '收藏',
      'dot': 0
    }, {
      'controlId': '6718c21eae4fd9d1ba4f2646',
      'type': 34,
      'value': '[[{"controlId":"tempRowId","value":"temp-ff94ae80-cf31-4afa-91ff-683f3eff81ac"},{"controlId":"6718c21eae4fd9d1ba4f264c","value":"[\\"52f3c7d3-100c-4f05-bd92-4c7579740e4e\\"]"},{"controlId":"6718c23eae4fd9d1ba4f2658","value":"111"},{"controlId":"6718c26cae4fd9d1ba4f2675","value":"223"},{"controlId":"6719ae4fae4fd9d1ba4f285a","value":"[{\\"name\\":\\"大货架\\",\\"sid\\":\\"43c4eab9-e079-4301-83e9-e269faa03f6d\\"}]"}]]',
      'controlName': '出入库-子表-无实体',
      'dot': 0
    }],
    'appId': 'f3808f22-82d5-439d-8ebd-b9ee58a34908',
    'projectId': '9922dfeb-4eba-4dea-a566-486ac53dd6fc',
    'viewId': '6715b9f4ae4fd9d1ba4f1b15',
    'worksheetId': '6715b9f4ae4fd9d1ba4f1b11',
    'pushUniqueId': '1730279714347',
    'rowStatus': 1
  }

  // 创建工作表行
  async function addWorksheetRow (params) {
    return await (await mingdaoWebApi).axios({
      method: 'post',
      url: '/wwwapi/Worksheet/AddWorksheetRow',
      data: {
        projectId: params.projectId || defaultProjectId,
        appId: params.appId,
        worksheetId: params.worksheetId,
        // viewId: params.viewId,
        receiveControls: params.controls,
        silent: true,
        pushUniqueId: +new Date() + '',
        rowStatus: 1
      }
    })
  }

  const updateWorksheetRowData = {
    'appId': 'f3808f22-82d5-439d-8ebd-b9ee58a34908',
    'viewId': '6715b9f4ae4fd9d1ba4f1b15',
    'getType': 1,
    'worksheetId': '6715b9f4ae4fd9d1ba4f1b11',
    'rowId': '0f476bb3-ddd0-4812-bb63-4b4076db71bf',
    'newOldControl': [
      {
        'controlId': '6718c21eae4fd9d1ba4f2646',
        'type': 34,
        'value': '[{"editType":0,"newOldControl":[{"controlId":"tempRowId","value":"temp-1e2af583-5424-43fd-9447-48db520e8cce"},{"controlId":"6718c21eae4fd9d1ba4f264c","value":"[\\"52f3c7d3-100c-4f05-bd92-4c7579740e4e\\"]"},{"controlId":"6718c23eae4fd9d1ba4f2658","value":"1"},{"controlId":"6718c26cae4fd9d1ba4f2675","value":"ddd"},{"controlId":"6719ae4fae4fd9d1ba4f285a","value":"[{\\"name\\":\\"大货架\\",\\"sid\\":\\"43c4eab9-e079-4301-83e9-e269faa03f6d\\"}]"}]}]',
        'controlName': '出入库-子表-无实体',
        'dot': 0,
        'editType': 9
      }
    ],
    'projectID': '9922dfeb-4eba-4dea-a566-486ac53dd6fc',
    'pushUniqueId': '1730081679799'
  }

  // 更新工作表行
  async function updateWorksheetRow (params) {
    return await (await mingdaoWebApi).axios({
      method: 'post',
      url: '/wwwapi/Worksheet/UpdateWorksheetRow',
      data: {
        projectID: params.projectId || defaultProjectId,
        appId: params.appId,
        worksheetId: params.worksheetId,
        // viewId: params.viewId,
        newOldControl: params.controls,
        rowId: params.rowId,
        getType: 1,
        pushUniqueId: +new Date() + '',
      }
    })
  }

  /**
   * 批量更新工作表行
   * @param {object} params
   * @param {string} params.appId
   * @param {string} params.worksheetId
   * @param {number} params.controlId
   * @param {number} params.controlType
   * @param {any} params.value
   * @param {string[]} params.rowIds
   * @returns {Promise<axios.AxiosResponse<*>|*>}
   */
  async function updateWorksheetRows (params) {
    const cells = {
      controlId: params.controlId,
      type: params.controlType,
      dot: 0
    }
    // 关联记录 级联选择 Value 处理为 relationValues
    if ([29, 35].includes(params.controlType)) {
      cells.relationValues = JSON.parse(params.value).map(e => ({ sid: e }))
    } else {
      cells.value = params.value
    }
    return await (await mingdaoWebApi).axios({
      method: 'post',
      url: '/wwwapi/Worksheet/UpdateWorksheetRows',
      data: {
        appId: params.appId,
        worksheetId: params.worksheetId,
        cells: cells,
        rowIds: params.rowIds,
      }
    })
  }

  // 获取 APP Open API
  async function getAppOpenApi (appId) {
    const authorizes = await getAuthorizes(appId)
    const firstAuthorize = authorizes[0]
    return createOpenApiConnection({
      host: webApiConfig.host,
      appKey: firstAuthorize.appKey,
      sign: firstAuthorize.sign,
    })
  }

  return {
    mingdaoWebApi,
    getAuthorizes,
    getAppsForProject,
    getAppOpenApi: asyncCache(getAppOpenApi, 1000 * 600),
    checkAppAdminForUser,
    getWorksheetInfo: asyncCache(getWorksheetInfo, 1000 * 60),
    getVersion,
    addWorksheetRow: limitConcurrency(addWorksheetRow, 3),
    updateWorksheetRow: limitConcurrency(updateWorksheetRow, 3),
    updateWorksheetRows,
  }
}

module.exports = {
  createWebApiConnection
}

