module.exports = {
  collectionName: 'xkwServiceApply',
  info: {
    name: 'xkwServiceApply',
    label: '学科网组卷服务申请',
    description: '学科网组卷服务接入申请记录'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    schoolId: {
      label: '学校ID',
      type: 'integer',
      required: true,
      editable: false,
      unique: true,
      description: '申请学校的ID'
    },
    schoolName: {
      label: '学校名称',
      type: 'string',
      editable: false,
      required: true,
      description: '申请学校的名称'
    },
    userId: {
      label: '提交人ID',
      type: 'string',
      editable: false,
      required: true,
      description: '申请提交人的用户ID'
    },
    phone: {
      label: '提交人手机号',
      type: 'string',
      editable: false
    },
    userName: {
      label: '提交人',
      type: 'string',
      editable: false,
      required: true,
      description: '申请提交人姓名或账号'
    },
    xkwSchoolName: {
      label: '学科网学校名称',
      type: 'string',
      editable: false,
      required: true,
      description: '在学科网平台注册的学校名称'
    },
    applyStatus: {
      label: '申请状态',
      type: 'string',
      default: 'pending',
      editable: false,
      options: [
        { label: '待审核', value: 'pending' },
        { label: '已通过', value: 'approved' },
        { label: '已拒绝', value: 'rejected' },
        { label: '处理中', value: 'processing' }
      ],
      description: '申请状态：pending-待审核, approved-已通过, rejected-已拒绝, processing-处理中'
    },
    reviewer: {
      label: '审核人',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      editable: false,
      description: '审核该申请的用户'
    },
    remark: {
      label: '备注',
      type: 'text',
      description: '审核备注信息'
    },
    from: {
      label: '来源',
      type: 'string',
      editable: false,
      required: true,
      options: [
        { label: '好分数精准教学', value: 'hfsjzjx' },
        { label: '云校智学', value: 'yxzhixue' }
      ]
    }
  }
}
