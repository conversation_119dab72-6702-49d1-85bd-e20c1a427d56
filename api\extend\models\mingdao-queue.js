'use strict';

module.exports = {
    collectionName: 'mingdao-queue',
    info: {
        name: 'MingdaoQueue',
        label: '明道更新队列',
        description: '明道更新队列'
    },
    options: {
        draftAndPublish: false,
        timestamps: true,
        // indexes: [
        //     { keys: { key: 1 }, options: { unique: true } },
        // ],
    },
    pluginOptions: {},
    attributes: {
        type: {
            label: '类型',
            type: 'string',
            editable: false,
            required: true,
            options: [
                {
                    value: 'school',
                    label: '学校档案更新'
                },
            ]
        },
        // key: {
        //     label: '唯一键',
        //     editable: false,
        //     "unique": true,
        //     type: 'string',
        // },
        customerId: {
            label: '客户id',
            type: 'string',
            editable: false,
            required: true,
        },
        "customer": {
            "label": "学校",
            "editable": false,
            "mainField": "name",
            "model": "customer-service-mid"
        },
        relatedId: {
            label: '关联id',
            type: 'string',
            editable: false,
        },
        status: {
            label: '状态',
            type: 'string',
            editable: false,
            required: true,
            default: 'prepared',
            options: [
                {
                    value: 'prepared',
                    label: '待更新'
                },
                // {
                //     value: 'doing',
                //     label: '进行中'
                // },
                {
                    value: 'success',
                    label: '成功'
                },
                {
                    value: 'failed',
                    label: '失败'
                },
                {
                    value: 'cancelled',
                    label: '已取消'
                }
            ]
        },
    }
}