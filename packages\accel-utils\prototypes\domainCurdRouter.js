const CurdRouter = require('./curdRouter')
const { dataFieldProcess, parseCtxDomainAndBranch } = require('../collection-utils')
const _ = require('lodash')

class DomainCurdRouter extends CurdRouter {
  /**
   * @param name
   * @param {object} config
   * @param {string} [config.modelName=]
   * @param {string} [config.pluginName=]
   * @param {string} [config.sort=]
   */
  constructor (name, config = {}) {
    super(name, config)
  }

  async _parseDomainCtx (ctx) {
    const {
      model,
      params,
      query,
      data,
      files,
      populate,
      branchId
    } = await parseCtxDomainAndBranch(ctx, this.modelName, this.pluginName)
    if (!query?._sort && this.defaultSort) {
      query._sort = this.defaultSort
    }
    return {
      model, params, query, data, files, populate, branchId,
      user: ctx.state.user
    }
  }

  async domainFind (ctx) {
    const { query } = await this._parseDomainCtx(ctx)
    ctx.query = query
    return super.find(ctx)
  }

  async domainCount (ctx) {
    const { query } = await this._parseDomainCtx(ctx)
    ctx.query = query
    return super.count(ctx)
  }

  async domainFindOne (ctx) {
    const { params } = await this._parseDomainCtx(ctx)
    ctx.params = params
    return super.findOne(ctx)
  }

  async domainCreate (ctx) {
    const { branchId, data, files, model } = await this._parseDomainCtx(ctx)
    const user = ctx.state.user
    const strapiModel = this._getModel(ctx)
    if (branchId) {
      data.pBranch = branchId
      // 针对多租户数据
      if (strapiModel.attributes.pBranches) {
        data.pBranches = [branchId]
      }
    }
    if (strapiModel.attributes.user && user) {
      data.user = user.id
    }
    try {
      await dataFieldProcess(data, model)
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    let entity = await this._getService(ctx).create({ data, files })
    return this._sanitizeEntity(ctx, entity)
  }

  async domainUpdate (ctx) {
    const { query, data, files, model } = await parseCtxDomainAndBranch(ctx, this.modelName, this.pluginName)
    const safeParams = query
    if (!safeParams.id) {
      return ctx.badRequest('Invalid target id.')
    }
    try {
      await dataFieldProcess(data, model)
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const entity = await this._getService(ctx).update({
      params: safeParams,
      data, files
    })
    return this._sanitizeEntity(ctx, entity)
  }

  async domainUpdateMany (ctx) {
    const { query } = await parseCtxDomainAndBranch(ctx, this.modelName, this.pluginName)
    const safeParams = query
    const body = ctx.request.body
    if (!_.isObject(body.filter) || !_.isObject(body.data)) {
      return ctx.badRequest('filter is invalid')
    }
    body.filter = {
      ...body.filter,
      ...safeParams
    }
    return super.updateMany(ctx)
  }

  async domainDelete (ctx) {
    const { query } = await this._parseDomainCtx(ctx)
    const safeParams = query
    if (!safeParams.id) {
      return ctx.badRequest('Invalid target id.')
    }
    const entity = await this._getService(ctx).delete({ params: safeParams })
    return this._sanitizeEntity(ctx, entity)
  }

  async domainDeleteMany (ctx) {
    const { data, query } = await this._parseDomainCtx(ctx)
    if (!_.isArray(data.filter.id_in)) {
      return ctx.badRequest('filter.id_in is invalid')
    }
    const entity = await this._getService(ctx).deleteMany(
      {
        params: {
          ...data.filter,
          ...query,
        }
      }
    )
    return this._sanitizeEntity(ctx, entity)
  }

  async domainExport (ctx) {
    const { query } = await this._parseDomainCtx(ctx)
    ctx.query = query
    return super.export(ctx)
  }

  async domainImport (ctx) {
    const { branchId, user } = await this._parseDomainCtx(ctx)
    ctx._constFields = {
      pBranch: branchId,
      user: user.id
    }
    return super.import(ctx)
  }

  async domainSelfFind (ctx) {
    const { query, user } = await this._parseDomainCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.find(ctx)
  }

  async domainSelfCount (ctx) {
    const { query, user } = await this._parseDomainCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.count(ctx)
  }

  async domainSelfFindOne (ctx) {
    const { params, user } = await this._parseDomainCtx(ctx)
    ctx.params = {
      ...params,
      user: user.id
    }
    return super.findOne(ctx)
  }

  async domainSelfExport (ctx) {
    const { query, user } = await this._parseDomainCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.export(ctx)
  }

  async domainSelfCreate (ctx) {
    return this.domainCreate(ctx)
  }

  async domainSelfUpdate (ctx) {
    return this.domainUpdate(ctx)
  }

  async domainSelfUpdateMany (ctx) {
    return this.domainUpdateMany(ctx)
  }

  async domainSelfDelete (ctx) {
    return this.domainDelete(ctx)
  }

  async domainSelfDeleteMany (ctx) {
    return this.domainDelete(ctx)
  }

  async domainSelfImport (ctx) {
    return this.domainImport(ctx)
  }

  createHandlers (names) {
    if (names) return super.createHandlers(names)
    const defaultNames = [
      'domainFind', 'domainCount', 'domainFindOne',
      'domainCreate', 'domainUpdate', 'domainUpdateMany', 'domainDelete',
      'domainExport', 'domainImport', 'domainDeleteMany',
      'domainSelfFind', 'domainSelfCount', 'domainSelfFindOne',
      'domainSelfCreate', 'domainSelfUpdate', 'domainSelfUpdateMany', 'domainSelfDelete',
      'domainSelfExport', 'domainSelfImport', 'domainSelfDeleteMany',
    ]
    const extendNames = this._getExtendMethodNames()
    return super.createHandlers([
      ...defaultNames,
      ...extendNames,
    ])
  }
}

module.exports = DomainCurdRouter
