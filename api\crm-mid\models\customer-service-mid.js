const { customerTypes } = require('../utils/crmTypes')
module.exports = {
  collectionName: 'customer-service',
  info: {
    name: 'CustomerService',
    label: '客户服务',
    description: '客户服务'
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
    indexes: [
      { keys: { createdAt: -1 } },
      { keys: { updatedAt: -1 } },
      { keys: { schoolId: -1 } },
    ],
    'groups': [
      {
        'title': '',
        'fields': [
          'name',
          'schoolId',
          'province',
          'city',
          'district',
          '\n',
          'crm_type',
          'edu_system',
          'system',
          'mingyou_school_type',
          'school_size',
          'total_number',
          '\n',
          'intendedSalesMonth',
          'isVIP',
          't_count',
          's_count',
          'totalNumber',
          'eduSystem',
          'schoolCategory',
        ]
      },
      {
        'title': '直营信息',
        'fields': [
          'directSalesTeam',
          'directSalesManager',
          'directServiceTeam',
          'directServiceManager',
          'lastServiceAssignTime',
          'lastSalesAssignTime',
          '\n',
          'clueSource',
          'directServiceClueIntention',
          'dealObstacle',
          'dealObstacleReason',
          '\n',
          'dealStage',
          'clueStage',
          'incubationStage',
          'initialCooperationStage',
          'lightOperationStage',
          'expansionStage',
          'followStatus',
          'onSiteVisit',
          'onSiteVisitors',
          'abandonReason',
        ]
      },
      {
        'title': '学校概况',
        'fields': [
          'yjVersion',
          'yjStartTime',
          'yjEndTime',
          'fxVersion',
          'fxStartTime',
          'fxEndTime',
          'tkVersion',
          'tkStartTime',
          'tkEndTime',
          '\n',
          'lastExamTime',
        ]
      },
      {
        'title': '产品使用情况',
        'fields': [
          'schoolExamFrequency',
          'demandIntention',
          'directSalesForecast',
          'schoolBudget',
          'hfsPayStatus',
          '\n',
          'currentYjMethod',
          'currentYjProduct',
          'competitorYjExpiration',
          'yjHistoryPayStandard',
          'currentTkProduct',
          'tkHistoryPayStandard',
          '\n',
          'requiredProducts',
        ]
      },
      {
        'title': '群信息',
        'fields': [
          'qun',
          'qunId',
          'hideQunQrCode',
          'yjShowSalesManager'
        ]
      },
      {
        'title': '跟进记录',
        'fields': [
          'currentFollowResult',
          'customerContacts',
          'interactContacts',
          'lastSalesTrackTime',
          'lastServiceTrackTime',
          'servicePlanTrackTime'
        ]
      },
      {
        'title': '客户有效信息标记',
        'fields': [
          'serviceIsAddWx',
          'salesIsAddWx',
          'isFollowValid',
          '\n',
          'customerValidTime',
          'customerIsValid',
          '\n',
          'customerIsValidLinkTime',
          'customerIsValidLink',
        ]
      },
      {
        'title': '特殊标记信息',
        'fields': [
          'exclusiveArea',
          'exclusiveSchool',
          'proxyName',
          '\n',
          'specialArea',
          'specialAgent',
          'noTouching',
          'isTest',
          'isObsolete'
        ]
      }
    ],
    'allOf': [
      {//当前阶段
        'if': {
          'properties': {
            'dealStage': {
              'const': '线索' //label": "线索"
              // "const": "培育" //label": "培育"
              // "const": "初次合作" //label": "初次合作"
              // "const": "轻运营" //label": "轻运营"
              // "const": "深耕" //label": "深耕"
            }
          }
        },
        'then': {
          'properties': {
            'clueStage': { 'visible': true },
            'incubationStage': { 'visible': false },
            'initialCooperationStage': { 'visible': false },
            'lightOperationStage': { 'visible': false },
            'expansionStage': { 'visible': false },
          },
        },
        'else': {}
      },
      {//当前阶段
        'if': {
          'properties': {
            'dealStage': {
              // "const": "线索" //label": "线索"
              'const': '培育' //label": "培育"
              // "const": "初次合作" //label": "初次合作"
              // "const": "轻运营" //label": "轻运营"
              // "const": "深耕" //label": "深耕"
            }
          }
        },
        'then': {
          'properties': {
            'clueStage': { 'visible': false },
            'incubationStage': { 'visible': true },
            'initialCooperationStage': { 'visible': false },
            'lightOperationStage': { 'visible': false },
            'expansionStage': { 'visible': false },
          },
        },
        'else': {}
      },
      {//当前阶段
        'if': {
          'properties': {
            'dealStage': {
              // "const": "线索" //label": "线索"
              // "const": "培育" //label": "培育"
              'const': '初次合作' //label": "初次合作"
              // "const": "轻运营" //label": "轻运营"
              // "const": "深耕" //label": "深耕"
            }
          }
        },
        'then': {
          'properties': {
            'clueStage': { 'visible': false },
            'incubationStage': { 'visible': false },
            'initialCooperationStage': { 'visible': true },
            'lightOperationStage': { 'visible': false },
            'expansionStage': { 'visible': false },
          },
        },
        'else': {}
      },
      {//当前阶段
        'if': {
          'properties': {
            'dealStage': {
              // "const": "线索" //label": "线索"
              // "const": "培育" //label": "培育"
              // "const": "初次合作" //label": "初次合作"
              'const': '轻运营' //label": "轻运营"
              // "const": "深耕" //label": "深耕"
            }
          }
        },
        'then': {
          'properties': {
            'clueStage': { 'visible': false },
            'incubationStage': { 'visible': false },
            'initialCooperationStage': { 'visible': false },
            'lightOperationStage': { 'visible': true },
            'expansionStage': { 'visible': false },
          },
        },
        'else': {}
      },
      {//当前阶段
        'if': {
          'properties': {
            'dealStage': {
              // "const": "线索" //label": "线索"
              // "const": "培育" //label": "培育"
              // "const": "初次合作" //label": "初次合作"
              // "const": "轻运营" //label": "轻运营"
              'const': '深耕' //label": "深耕"
            }
          }
        },
        'then': {
          'properties': {
            'clueStage': { 'visible': false },
            'incubationStage': { 'visible': false },
            'initialCooperationStage': { 'visible': false },
            'lightOperationStage': { 'visible': false },
            'expansionStage': { 'visible': true },
          },
        },
        'else': {}
      },
    ],
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      editable: false,
      type: 'string',
      size: 3,
    },
    customerId: {
      label: '客户ID',
      editable: false,
      visible: false,
      unique: true,
      type: 'string'
    },
    schoolId: {
      label: '学校 ID',
      editable: false,
      type: 'integer',
      size: 3,
    },

    // 建群相关
    qun: {
      label: '群信息',
      model: 'xiaoyun-qun',
      size: 3,
      via: 'customer',
      mainField: 'name',
      editable: false,
      meta: {
        query: {
          'customer_null': true,
          'isDeleted_ne': true,
        }
      }
    },
    qunId: {
      label: '群ID',
      editable: false,
      type: 'string',
      size: 3,
    },
    hideQunQrCode: {
      label: '是否隐藏群二维码',
      type: 'boolean',
      default: false,
      size: 3,
    },
    yjShowSalesManager: {
      label: '阅卷显示直营经理',
      type: 'boolean',
      editable: false,
      size: 2,
    },
    schoolCategory: {
      label: '学校类别',
      type: 'string',
      editable: false,
      options: [
        { label: '体验校', value: '体验校', },
        { label: '付费校', value: '付费校', },
        { label: '流失校', value: '流失校', },
        { label: '临期半年', value: '临期半年', },
        { label: '临期3个月', value: '临期3个月', },
        { label: '临期2个月', value: '临期2个月', },
        { label: '临期1个月', value: '临期1个月', },
        { label: '应续2个月内', value: '应续2个月内', },
        { label: '应续3个月内', value: '应续3个月内', },
        { label: '应续半年', value: '应续半年', },
        { label: '应续超过半年', value: '应续超过半年', },
      ],
      size: 2
    },
    isVIP: {
      label: '是否付费',
      editable: false,
      type: 'boolean',
      default: false,
      size: 2,
    },
    directServiceManager: {
      label: '直服经理',
      editable: false,
      plugin: "users-permissions",
      model: "user",
      mainField: 'username',
      meta: {
        query: {
          provider: 'yxWeCom'
        }
      },
      size: 2,
    },
    directSalesManager: {
      label: '直营经理',
      editable: false,
      plugin: "users-permissions",
      model: "user",
      mainField: 'username',
      meta: {
        query: {
          provider: 'yxWeCom'
        }
      },
      size: 2,
    },
    lastServiceTrackTime: {
      label: '最近一次服务记录',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      editable: false,
      size: 2,
    },
    servicePlanTrackTime: {
      label: '直服计划沟通时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 2,
    },
    lastSalesTrackTime: {
      label: '最近一次直营跟进',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    lastServiceAssignTime: {
      label: '最近直服分配时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      editable: false,
      size: 2,
    },
    lastSalesAssignTime: {
      label: '最近直营分配时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },

    // 关联表
    customerProblems: {
      label: '客户问题',
      collection: 'customer-problem',
      via: 'customerService',
      visible: false,
    },
    yjTag: {
      label: '阅卷签约标签',
      type: 'string',
      editable: false,
      size: 3,
      options: [
        {
          "label": "体验校",
          "value": "体验校"
        },
        {
          "label": "付费校",
          "value": "付费校"
        },
        {
          "label": "应续校",
          "value": "应续校"
        },
        {
          "label": "流失校",
          "value": "流失校"
        }
      ]
    },
    fxTag: {
      label: '分析标签',
      type: 'string',
      editable: false,
      size: 3,
    },
    serviceIsAddWx: {
      label: '直服是否已加微信',
      type: 'boolean',
      size: 3,
    },
    salesIsAddWx: {
      label: '直营是否已加微信',
      type: 'boolean',
      size: 3,
    },
    isFollowValid: {
      label: '沟通记录是否有效',
      type: 'boolean',
      size: 3,
      editable: false
    },
    customerIsValid: {
      label: '有效沟通学校',
      type: 'boolean',
      size: 3,
    },
    customerIsValidMark: {
      label: '【有效沟通学校】标记无效',
      type: 'boolean',
      size: 3,
      editable: false,
      visible: false
    },
    customerValidTime: {
      label: '标记有效沟通学校时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 3,
      editable: false
    },
    // 客户是否有效链接
    customerIsValidLink: {
      label: '有效链接学校',
      type: 'boolean',
      size: 3
    },
    customerIsValidLinkTime: {
      label: '标记有效链接学校时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 3,
      editable: false
    },
    customerIsValidLinkMark: {
      label: '【有效链接学校】标记无效',
      type: 'boolean',
      size: 3,
      editable: false,
      visible: false
    },
    // crm同步
    proxyName: {
      type: 'string',
      label: '独家代理商名称',
      editable: false,
      size: 4,
    },
    exclusiveArea: {
      label: '独家授权区域',
      type: 'boolean',
      // visible: false,
      editable: false,
      default: false,
      size: 2,
    },
    exclusiveSchool: {
      label: '独家授权学校',
      type: 'boolean',
      // visible: false,
      editable: false,
      default: false,
      size: 2,
    },
    // 直服特殊标记
    specialArea: {
      label: '特殊区域',
      type: 'boolean',
      // visible: false,
      editable: false,
      default: false,
      size: 2,
    },
    specialAgent: {
      label: '特殊商',
      type: 'boolean',
      // visible: false,
      editable: false,
      default: false,
      size: 2,
    },
    noTouching: {
      label: '不建议沟通',
      type: 'boolean',
      editable: false,
      default: false,
      size: 2,
    },
    isTest: {
      label: '是否测试学校',
      type: 'boolean',
      default: false,
      size: 2,
    },
    obsolete: {
      label: '是否废弃学校',
      type: 'boolean',
      default: false,
      size: 2,
    },
    // 直营数据
    intendedSalesMonth: {
      label: '意向成交月份',
      type: 'string',
      size: 2,
    },
    clueSource: {
      label: '线索来源',
      type: 'string',
      options: [
        { label: '商机电话', value: '商机电话' },
        { label: '官网主动咨询', value: '官网主动咨询' },
        { label: '阅卷端咨询', value: '阅卷端咨询' },
        { label: '体验版挖掘', value: '体验版挖掘' },
        { label: '渠道转交', value: '渠道转交' },
        { label: '直服转交', value: '直服转交' },
        { label: '历史付费校挖掘', value: '历史付费校挖掘' },
        { label: '教师运营转交', value: '教师运营转交' },
      ],
      size: 2,
    },
    directServiceClueIntention: {
      label: '直服线索意向度',
      type: 'string',
      options: [
        { label: '无意向', value: '无意向' },
        { label: '有意向', value: '有意向' },
        { label: '意向不明', value: '意向不明' },
        { label: '失联', value: '失联' },
      ],
      size: 2
    },
    dealObstacle: {
      label: '未谈单原因',
      type: 'string',
      options: [
        { label: '当前无采购计划', value: '当前无采购计划' },
        { label: '价格不合适', value: '价格不合适' },
        { label: '无经费预算', value: '无经费预算' },
        { label: '未找到决策人', value: '未找到决策人' },
        { label: '当前无考试不着急', value: '当前无考试不着急' },
        { label: '其他', value: '其他' },
      ],
      size: 2
    },
    dealObstacleReason: {
      label: '未谈单其他说明',
      type: 'string',
      size: 6
    },
    dealStage: {
      label: '当前阶段',
      type: 'string',
      options: [
        // 初次联系、挖掘需求、明确意向、谈单、签约
        { label: '初次联系', value: '初次联系' },
        { label: '挖掘需求', value: '挖掘需求' },
        { label: '明确意向', value: '明确意向' },
        { label: '谈单', value: '谈单' },
        { label: '签约', value: '签约' },
      ],
      size: 2
    },
    clueStage: {
      label: '线索阶段环节',
      type: 'string',
      options: [
        { label: '线索获取', value: '线索获取' },
        { label: '首次联系', value: '首次联系' },
        { label: '获取关键人联系方式', value: '获取关键人联系方式' },
        { label: '加关键人微信', value: '加关键人微信' },
      ],
      visible: false,
      size: 2
    },
    incubationStage: {
      label: '培育阶段环节',
      type: 'string',
      options: [
        { label: '产品介绍', value: '产品介绍' },
        { label: '需求挖掘', value: '需求挖掘' },
        { label: '明确意向', value: '明确意向' },
      ],
      visible: false,
      size: 2
    },
    initialCooperationStage: {
      label: '初次合作阶段环节',
      type: 'string',
      options: [
        { label: '基础合作方案设计', value: '基础合作方案设计' },
        { label: '沟通采购方案意向', value: '沟通采购方案意向' },
        { label: '明确是否入校拜访', value: '明确是否入校拜访' },
        { label: '入校拜访公关', value: '入校拜访公关' },
        { label: '确认学校基础合作方案', value: '确认学校基础合作方案' },
        { label: 'SaaS签约', value: 'SaaS签约' },
        { label: 'SaaS收款', value: 'SaaS收款' },
      ],
      visible: false,
      size: 2
    },
    lightOperationStage: {
      label: '轻运营阶段环节',
      type: 'string',
      options: [
        { label: '沟通学校会员推广意向', value: '沟通学校会员推广意向' },
        { label: '明确和学校的会员合作方案', value: '明确和学校的会员合作方案' },
        { label: '学校发资料推注册', value: '学校发资料推注册' },
        { label: '加家长微信并建群', value: '加家长微信并建群' },
        { label: '会员持续运营', value: '会员持续运营' },
        { label: '寻找确认年级分销人', value: '寻找确认年级分销人' },
        { label: '年级分销人赋能试运营', value: '年级分销人赋能试运营' },
      ],
      visible: false,
      size: 2
    },
    expansionStage: {
      label: '深耕阶段环节',
      type: 'string',
      options: [
        { label: '二次培育学校的深耕需求', value: '二次培育学校的深耕需求' },
        { label: '进一步挖掘B/C深耕需求', value: '进一步挖掘B/C深耕需求' },
        { label: '明确深耕合作方案', value: '明确深耕合作方案' },
        { label: '深耕合同签约', value: '深耕合同签约' },
        { label: '启动收费', value: '启动收费' },
        { label: '服务运营', value: '服务运营' },
      ],
      visible: false,
      size: 2
    },
    followStatus: {
      label: '跟进状态',
      type: 'string',
      options: [
        { label: '待跟进', value: '待跟进' },
        { label: '首次联系', value: '首次联系' },
        { label: '继续跟进', value: '继续跟进' },
        { label: '已放弃', value: '已放弃' },
      ],
      size: 2
    },
    abandonReason: {
      label: '放弃原因',
      type: 'string',
      options: [
        { label: '学校拒绝沟通', value: '学校拒绝沟通' },
        { label: '无学校联系方式', value: '无学校联系方式' },
        { label: '已签约竞品多年', value: '已签约竞品多年' },
        { label: '独家区域', value: '独家区域' },
        { label: '区域统付竞品', value: '区域统付竞品' },
      ],
      size: 2
    },
    onSiteVisit: {
      label: '是否需要面访',
      type: 'string',
      options: [
        { label: '需要面访', value: '需要面访' },
        { label: '暂不需要面访', value: '暂不需要面访' },
      ],
      size: 2
    },
    onSiteVisitors: {
      label: '面访指派人员',
      type: 'string',
      size: 2
    },
    totalNumber: {
      label: '学校人数（直营）',
      type: 'string',
      size: 2
    },
    eduSystem: {
      label: '学制（直营）',
      type: 'string',
      options: [
        { label: '小学', value: '小学' },
        { label: '初中', value: '初中' },
        { label: '高中', value: '高中' },
        { label: '九年一贯制', value: '九年一贯制' },
        { label: '十二年一贯制', value: '十二年一贯制' },
        { label: '完全中学', value: '完全中学' },
      ],
      size: 2
    },
    schoolExamFrequency: {
      label: '学校考试频次',
      type: 'string',
      size: 2,
    },
    currentYjMethod: {
      label: '当前阅卷方式',
      type: 'string',
      size: 2,
    },
    currentYjProduct: {
      label: '当前使用阅卷品牌',
      type: 'string',
      size: 2,
    },
    competitorYjExpiration: {
      label: '竞品阅卷到期日期',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 2,
    },
    yjHistoryPayStandard: {
      label: '阅卷历史付费标准',
      type: 'string',
      size: 2,
    },
    currentTkProduct: {
      label: '当前题库产品',
      type: 'string',
      size: 2,
    },
    tkHistoryPayStandard: {
      label: '题库历史付费标准',
      type: 'string',
      size: 2,
    },
    hfsPayStatus: {
      label: '好分数付费状态',
      type: 'string',
      options: [
        { label: '历史付费（超3个月）', value: '历史付费（超3个月）' },
        { label: '历史付费（1~3个月）', value: '历史付费（1~3个月）' },
        { label: '存续中（1个月内到期）', value: '存续中（1个月内到期）' },
        { label: '存续中（1~3个月到期）', value: '存续中（1~3个月到期）' },
        { label: '存续中（3个月后超期）', value: '存续中（3个月后超期）' },
        { label: '未付费', value: '未付费' },
      ],
      size: 2
    },
    requiredProducts: {
      label: '产品需求',
      type: 'json',
      jsonSchema: {
        type: 'array',
        title: '产品需求',
        items: {
          type: 'string',
          options: [
            { label: '阅卷', value: '阅卷' },
            { label: '分析', value: '分析' },
            { label: '题库', value: '题库' },
            { label: '精准教学方案', value: '精准教学方案' },
            { label: '精准练', value: '精准练' },
            { label: '校本教辅', value: '校本教辅' },
            { label: '会员', value: '会员' },
            { label: '排课', value: '排课' },
          ],
        }
      },
      size: 3,
    },
    demandIntention: {
      label: '需求意向度',
      type: 'string',
      options: [
        { label: '强需求', value: '强需求' },
        { label: '需求一般', value: '需求一般' },
        { label: '需求弱', value: '需求弱' },
        { label: '无需求', value: '无需求' },
      ],
      size: 3
    },
    directSalesForecast: {
      label: '直营成交预判',
      type: 'string',
      options: [
        { label: '当月可成交', value: '当月可成交' },
        { label: '1-3月内可成交', value: '1-3月内可成交' },
        { label: '3-6个月内可成交', value: '3-6个月内可成交' },
        { label: '超6个月以上可成交', value: '超6个月以上可成交' },
      ],
      size: 3
    },
    schoolBudget: {
      label: '学校预算情况',
      type: 'string',
      size: 2,
    },
    currentFollowResult: {
      label: '当前跟进结论',
      type: 'string',
      options: [
        { label: '持续运营', value: '持续运营' },
        { label: '价格不接受', value: '价格不接受' },
        { label: '已切竞品', value: '已切竞品' },
        { label: '渠道成交', value: '渠道成交' },
        { label: '直营成交', value: '直营成交' },
      ],
      size: 4
    },

    // 同步字段
    // 阅卷数据
    lastExamTime: {
      label: '最后考试时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 3
    },
    t_count: {
      label: '老师数',
      type: 'number',
      editable: false,
      size: 2
    },
    s_count: {
      label: '学生数',
      type: 'number',
      editable: false,
      size: 2
    },
    // @Customer 字段
    deleted: {
      label: '是否删除',
      type: 'boolean',
      editable: false,
      visible: false,
    },
    province: {
      label: '省份',
      type: 'string',
      editable: false,
      size: 2,
    },
    city: {
      label: '城市',
      type: 'string',
      editable: false,
      size: 2,
    },
    district: {
      label: '区县',
      type: 'string',
      editable: false,
      size: 2,
    },
    edu_system: {
      label: '学制',
      type: 'string',
      editable: false,
      size: 2,
    },
    system: {
      label: '体制',
      type: 'string',
      editable: false,
      size: 2,
    },
    mingyou_school_type: {
      label: '名优校类型',
      type: 'string',
      editable: false,
      size: 2,
    },
    total_number: {
      label: '学校总人数',
      type: 'number',
      editable: false,
      size: 2,
    },
    school_size: {
      label: '学校规模',
      type: 'string',
      editable: false,
      size: 2,
    },
    crm_type: {
      label: '类型',
      'type': 'string',
      editable: false,
      options: customerTypes,
      size: 2,
    },
    '[sales_manager]': {
      label: '业务经理',
      type: 'json',
      editable: false,
      visible: false,
      jsonSchema: {
        type: 'array',
        title: '业务经理列表',
        items: {
          type: 'object',
          properties: {
            id: {
              title: 'ID',
              type: 'string',
            },
            user_id: {
              title: '客户经理ID',
              type: 'string',
            },
            name: {
              title: '客户经理姓名',
              type: 'string'
            },
            authorisation_type: {
              title: '授权类型',
              type: 'string'
            },
            authorisation_time: {
              title: '授权时间',
              type: 'datetime'
            },
            time: {
              title: '设置时间',
              type: 'datetime'
            }
          }
        }
      }
    },
    '[proxy]': {
      label: '代理商',
      type: 'json',
      editable: false,
      visible: false,
      jsonSchema: {
        type: 'array',
        title: '代理商列表',
        items: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              title: '代理商ID',
            },
            user_id: {
              type: 'string',
              title: '代理商UserId',
            },
            name: {
              type: 'string',
              title: '代理商名称'
            },
            authorisation_type: {
              type: 'string',
              title: '代理商授权类型'
            },
            time: {
              title: '设置时间',
              type: 'datetime'
            },
            begin_time: {
              title: '开始时间',
              type: 'datetime'
            },
            end_time: {
              title: '截至时间',
              type: 'datetime'
            }
          }
        }
      }
    },
    // @CustomerAppsUsage 字段
    usages: {
      label: '开通应用',
      type: 'json',
      editable: false,
      visible: false,
      jsonSchema: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            type: { title: '类型', type: 'integer' },
            status: { title: '状态', type: 'integer' },
            begin_time: { title: '开始时间', type: 'date' },
            end_time: { title: '结束时间', type: 'date' },
            enabled: { title: '是否可用', type: 'integer' },
            is_trial: { title: '是否试用', type: 'integer' },
            appVersion: {
              title: '版本',
              type: 'object',
              properties: {
                type: { title: '类型', type: 'integer' },
                version_name: { title: '版本名', type: 'string' },
                app_category: { title: '产品组', type: 'string' },
                app_category_name: { title: '产品组名', type: 'string' },
                product_category: { title: '产品名', type: 'string' },
                product_category_name: { title: '产品名名称', type: 'string' },
              }
            }
          }
        }
      }
    },
    yjVersion: {
      label: '阅卷版本',
      type: 'string',
      editable: false,
      size: 2,
    },
    yjStartTime: {
      label: '阅卷开始时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    yjEndTime: {
      label: '阅卷截止时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      editable: false,
      size: 2,
    },
    fxVersion: {
      label: '分析版本',
      type: 'string',
      editable: false,
      size: 2,
    },
    fxStartTime: {
      label: '分析开始时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    fxEndTime: {
      label: '分析截止时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      editable: false,
      size: 2,
    },
    tkVersion: {
      label: '题库版本',
      type: 'string',
      editable: false,
      size: 2,
    },
    tkStartTime: {
      label: '题库开始时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    tkEndTime: {
      label: '题库截止时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },

    // 阅卷授权
    authAgent: {
      label: '授权经销商',
      model: 'boss-agent',
      mainField: 'company',
      size: 2,
    },
    authAgentId: {
      label: '授权经销商id',
      type: 'string',
      size: 2,
    },
    authAgentNo: {
      label: '授权经销商no',
      type: 'string',
      size: 2,
    },
    authAgentName: {
      label: '授权经销商名称',
      type: 'string',
      size: 2,
    },

    authType: {
      label: '授权类型',
      type: 'string',
      editable: false,
      size: 2,
    },
    authEndTime: {
      label: '授权到期日期',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    authRemark: {
      label: '授权备注',
      type: 'string',
      editable: false,
      size: 2,
    },
  }
}
