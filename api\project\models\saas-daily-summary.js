module.exports = {
  collectionName: strapi.config.server.mingdaoConfig.saasDailySummaryId,
  connection: 'mingdao',
  info: {
    name: 'DailySummary',
    label: '日报',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    name: {
      ref: '67b5861a9e13a09bff0a09e7'
    },
    date: {
      ref: '67b585f89e13a09bff0a09b9'
    },
    user: {
      ref: '67b54c729e13a09bff09f255'
    },
    taskType: {
      ref: '67b54c729e13a09bff09f257'
    },
    requirements: {
      ref: '67b54c729e13a09bff09f258'
    },
    questions: {
      ref: '67b5a38f9e13a09bff0a223b'
    },
    todayDesc: {
      ref: '67b54a229e13a09bff09f181'
    },
    tomorrowPlan: {
      ref: '67b54d1e9e13a09bff09f2fb'
    },
    //风险及需要的支持
    other: {
      ref: '67b586aa9e13a09bff0a0a68'
    },
  }
}
