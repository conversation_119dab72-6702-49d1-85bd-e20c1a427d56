'use strict'
const fs = require('fs')
const { writeFile } = require('fs/promises')
const { getSecurityToken, uploadFileToBucket } = require('../services/object-storage')
const { objectStorageConfig } = require('../config/object-storage')

/**
 * 将 Stream 转为 Buffer
 * @param stream
 * @returns {Promise<Buffer>}
 */
function stream2buffer (stream) {
  return new Promise((resolve, reject) => {
    const _buf = []
    stream.on('data', (chunk) => _buf.push(chunk))
    stream.on('end', () => resolve(Buffer.concat(_buf)))
    stream.on('error', (err) => reject(err))
  })
}

// 浏览器直接对接对象存储上传获取临时Token
async function getSts () {
  return await getSecurityToken()
}

// 对接对象存储的普通文件上传接口
async function upload (ctx) {
  const { files } = ctx.request
  const file = files.file
  const { name } = file
  const dir = [objectStorageConfig.baseDir , objectStorageConfig.uploadPath].filter(e => e).join('')
  let uploadInfo = await uploadFileToBucket(name, file.path, dir)
  return ctx.wrapper({ url: uploadInfo.url })
}

// 对接 WangEditor 的富文本文件上传接口
async function richTextUpload (ctx) {
  const { files } = ctx.request
  const file = files['wangeditor-uploaded-image']
  const { name } = file
  try {
    const dir = [objectStorageConfig.baseDir, objectStorageConfig.richTextUploadPath].filter(e => e).join('')
    let uploadInfo = await uploadFileToBucket(name, file.path, dir)
    return {
      errno: 0,
      data: {
        src: uploadInfo.url,
        url: uploadInfo.url,
      }
    }
  } catch (e) {
    console.error(e)
    return {
      errno: 1,
      message: e.toString()
    }
  }
}

// 上传到本地
async function uploadToLocal (ctx) {
  const { files } = ctx.request
  const file = files.file
  const fileHashName = file.path.match(/[^\\\/]+$/)[0]
  const fileName = `${fileHashName}_${file.name}`
  const buffer = await stream2buffer(fs.createReadStream(file.path))
  await writeFile(`./public/uploads/${fileName}`, buffer)
  const baseUrl = strapi.config.server.serverUrl
  const url = `${baseUrl}/uploads/${fileName}`
  return ctx.wrapper({ url })
}

// 富文本文件上传到本地
async function richTextUploadToLocal (ctx) {
  const { files } = ctx.request
  const file = files['wangeditor-uploaded-image']
  try {
    const fileHashName = file.path.match(/[^\\\/]+$/)[0]
    const fileName = `${fileHashName}_${file.name}`
    const buffer = await stream2buffer(fs.createReadStream(file.path))
    await writeFile(`./public/uploads/rich-text/${fileName}`, buffer)
    const baseUrl = strapi.config.server.serverUrl
    const url = `${baseUrl}/uploads/rich-text/${fileName}`
    return {
      errno: 0,
      data: {
        src: url,
        url: url,
      }
    }
  } catch (e) {
    return {
      errno: 1,
      message: e.toString()
    }
  }
}

module.exports = {
  getSts,
  upload,
  richTextUpload,
  uploadToLocal,
  richTextUploadToLocal,
}


