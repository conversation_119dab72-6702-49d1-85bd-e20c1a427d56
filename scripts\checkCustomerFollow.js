const { MongoClient, ObjectId } = require('mongodb')
const moment = require('moment')
const _ = require('lodash')
const axios = require('axios')

const DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://localhost:27017/testwly-boss'
  // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
  : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

let db, dbClient


(async function () {
  dbClient = await MongoClient.connect(DB_URL)
  db = dbClient.db()
  try {
    logger('gen start')
    await main()
    logger('gen end')
  } catch (e) {
    console.log(e.stack || 'err')
  } finally {
    await dbClient.close()
    setTimeout(() => {
      process.exit(1)
    }, 5000)
  }
})()

function logger (msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg}`)
}

async function main () {
  // eslint-disable-next-line no-constant-condition
  let lastSyncObjectId
  const limit = 5000
  let curCount = 0

  let allUsers = await db.collection('users-permissions_user').find({
  }).toArray()
  let salesRoles = await db.collection('users-permissions_role').find({
    type: { $in: ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member'] }
  }).toArray()
  let serviceRoles = await db.collection('users-permissions_role').find({
    type: { $in: ['service-admin', 'service-group-leader', 'service-group-member'] }
  }).toArray()
  let salesUsers = await db.collection('users-permissions_user').find({
    roles: { $in: salesRoles.map(e => e._id) },
    blocked: { $ne: true },
  }).toArray()
  let serviceUsers = await db.collection('users-permissions_user').find({
    roles: { $in: serviceRoles.map(e => e._id) },
    blocked: { $ne: true },
  }).toArray()
  let salesUserMap = {}, serviceUserMap = {}
  for (const user of salesUsers) {
    salesUserMap[user._id] = user
  }
  for (const user of serviceUsers) {
    serviceUserMap[user._id] = user
  }

  let msgs = [], abnormalUserIds = []
  while (true) {
    const conditions = {
      // school_id_20: { $gt: 0 }
      // school_id_20: 28379
      directSalesManager: { $exists: true },
      directServiceManager: { $exists: true },
    }
    if (lastSyncObjectId) {
      conditions._id = { '$lt': ObjectId(lastSyncObjectId) }
    }

    let customers = await db.collection('customer-service').find(conditions).sort({ _id: -1 }).limit(limit).toArray()
    // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //   db: 'wly_boss',
    //   coll: 'customer-service',
    //   filter: conditions,
    //   sort: { _id: -1 },
    //   limit: limit
    // }).then(({ data }) => { return data });
    for (const customer of customers) {
      if (customer.directSalesManager && !salesUserMap[customer.directSalesManager.toString()]) {
        msgs.push(`${customer.name} ${customer.schoolId} 直营经理异常`)
        abnormalUserIds.push(customer.directSalesManager?.toString())
      }
      if (customer.directServiceManager && !serviceUserMap[customer.directServiceManager.toString()]) {
        msgs.push(`${customer.name} ${customer.schoolId} 运营经理异常`)
        abnormalUserIds.push(customer.directServiceManager?.toString())
      }
    }

    if (customers.length === 0) { break }
    lastSyncObjectId = customers[customers.length - 1]._id.toString()
    if (customers.length < limit) { break }
    curCount = curCount + limit
    // console.log(customerServices.length)
  }

  if (!_.isEmpty(msgs)) {
    // msgs = _.concat([`异常数目: ${msgs?.length || 0}`], msgs)
    msgs =[`异常数目: ${msgs?.length || 0}`]
    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=05f30d67-fec2-427e-88ad-cfd8615c7c83', {
      msgtype: 'text',
      text: {
        content: msgs.join('\n'),
      }
    })
  }

  if (!_.isEmpty(abnormalUserIds)) {
    abnormalUserIds = _.uniq(abnormalUserIds)
    const abnormalUsers = allUsers.filter(e => {
      return abnormalUserIds.includes(e._id.toString())
    })
    const blockedUsers = abnormalUsers.filter(e => e.blocked)
    const roleAbnormalUsers = abnormalUsers.filter(e => !e.blocked)

    let content = `异常跟进人数: ${abnormalUserIds?.length || 0} 角色异常数: ${roleAbnormalUsers?.length || 0} 离职人数: ${blockedUsers?.length || 0}
角色异常: ${roleAbnormalUsers.map(e => e.username).join(',')}
离职: ${blockedUsers.map(e => e.username).join(',')}`
    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=05f30d67-fec2-427e-88ad-cfd8615c7c83', {
      msgtype: 'text',
      text: {
        content: content,
      }
    })
  }
}