const qunModel = require('./xiaoyun-qun')
const customerModel = require('../../crm-mid/models/customer-service-mid')
const customerAttrs = {}
for (const attr in customerModel.attributes) {
  customerAttrs['customer_' + attr] = {
    ...customerModel.attributes[attr],
    default: null
  }
  delete customerAttrs['customer_' + attr]['unique']
}

const qunAttrs = {}

for (const attr in qunModel.attributes) {
  qunAttrs['qun_' + attr] = {
    ...qunModel.attributes[attr],
    default: null
  }
  delete qunAttrs['qun_' + attr]['unique']
}
module.exports = {
  collectionName: 'qun-check',
  info: {
    name: 'Qun<PERSON><PERSON><PERSON>',
    label: '群检查信息',
    description: '群检查信息'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    isStand: {
      label: '是否规范',
      type: 'boolean',
      editable: false
    },
    syncTime: {
      label: '同步时间',
      type: 'datetime',
      editable: false
    },
    ...customerAttrs,
    ...qunAttrs
  }
}
