'use strict';

module.exports = {
    "collectionName": "bossSchool",
    "info": {
        "name": "BossSchool",
        "label": "boss学校",
        "description": "boss学校"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true
    },
    "pluginOptions": {},
    "attributes": {
        "schoolId": {
            "label": "学校ID",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "oldSchoolId": {
            "label": "学校ID（1.5）",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "showName": {
            "label": "学校展示名称",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "name": {
            "label": "学校名称",
            "editable": false,
            "type": "string"
        },
        "type": {
            "label": "类型",
            "type": "string",
            "editable": false,
            "options": [
                {
                    "label": "学校",
                    "value": "普通"
                },
                {
                    "label": "统考平台",
                    "value": "教研室"
                },
                {
                    "label": "联盟平台",
                    "value": "联盟"
                },
                {
                    "label": "集团校平台",
                    "value": "集团校"
                },
                {
                    "label": "单次联考",
                    "value": "联考"
                },
                {
                    "label": "教育局",
                    "value": "教育局"
                },
                {
                    "label": "培训机构",
                    "value": "培训机构"
                },
                {
                    label: '中心校平台',
                    value: '中心校'
                },
            ]
        },
        // "tag": {
        //     "label": "标签",
        //     "type": "string",
        //     "size": 3,
        //     "options": [
        //         {
        //             "label": "未付费校",
        //             "value": "未付费校"
        //         },
        //         {
        //             "label": "流失校",
        //             "value": "流失校"
        //         },
        //         {
        //             "label": "付费校",
        //             "value": "付费校"
        //         },
        //         {
        //             "label": "断续存档",
        //             "value": "断续存档"
        //         },
        //         {
        //             "label": "断续新校",
        //             "value": "断续新校"
        //         }
        //     ]
        // },
        // "fxTag": {
        //     "label": "分析标签",
        //     "type": "string",
        //     "size": 3,
        // },
        "eduSystem": {
            "label": "学制",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "system": {
            "label": "体制",
            "editable": false,
            "size": 3,
            "type": "string"
        }
    }
}
