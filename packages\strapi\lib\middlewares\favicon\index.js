'use strict';

/**
 * Module dependencies
 */

// Node.js core.
const { resolve } = require('path');
const fs = require('fs'); // 引入 fs 模块用于文件系统操作
const favicon = require('koa-favicon');

/**
 * Favicon hook
 */

module.exports = strapi => {
  return {
    /**
     * Initialize the hook
     */

    initialize() {
      const { dir } = strapi;
      const {
        maxAge,
        path: faviconPath,
      } = strapi.config.middleware.settings.favicon;

      // 构造完整的 favicon 路径
      const resolvedFaviconPath = resolve(dir, faviconPath);

      // 检查指定路径的 favicon 文件是否存在
      fs.access(resolvedFaviconPath, fs.constants.F_OK, err => {
        let finalFaviconPath = resolvedFaviconPath;

        // 如果文件不存在，则回退到当前代码文件目录下的 favicon.ico
        if (err) {
          console.warn(`Favicon not found at ${resolvedFaviconPath}, using default favicon.ico`);
          finalFaviconPath = resolve(__dirname, 'favicon.ico');
        }

        // 使用最终的 favicon 路径
        strapi.app.use(
          favicon(finalFaviconPath, {
            maxAge,
          })
        );
      });
    },
  };
};
