const { getToken } = require('../../xiaoyun/utils/goToken')
const userRole = require('../../xiaoyun/utils/userRole')
const axios = require('axios')
const { getUserGroup } = require('../../crm/controllers/manager-group')


async function getUserByAppCode(code) {
  const token = await getToken('org')
  if (!token) {
    return null
  }
  const res = await axios.get('http://org.yunxiao.io/emp/check_user_info_fd?__go_token=' + token + '&code=' + code)
  console.log('---check_user_info_fd----', res.data)
  if (!res.data || !res.data.UserId) {
    return null
  }

  return await strapi.query('user', 'users-permissions').findOne({ customId: res.data.UserId })
}


function getUserInfo(user) {
  const userPlugin = strapi.plugins['users-permissions']
  const userData = userPlugin.services['user'].getFullAuthData(user)
  return {
    jwt: userData.jwt,
    id: user.id,
    qxId: user.customId,
    name: user.username
  }
}

async function loginByQxCode(ctx) {
  const { code } = ctx.request.body

  if (!code) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const user = await getUserByAppCode(code)
  if (!user) {
    return ctx.wrapper.error('AUTH_ERROR', '用户不存在')
  }
  return ctx.wrapper.succ(getUserInfo(user))
}

async function loginByQxId(ctx) {
  const { qxId } = ctx.request.body
  if (!qxId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  const user = await strapi.query('user', 'users-permissions').findOne({ customId: qxId })
  if (!user) {
    return ctx.wrapper.error('AUTH_ERROR', '用户不存在')
  }
  return ctx.wrapper.succ(getUserInfo(user))
}

async function logout(ctx) {
  return ctx.wrapper.succ({})
}

async function upload(ctx) {
  const { startTime, phone, isIncoming, status, duration, missedCallDuration, fileUrl, fileSize } = ctx.request.body
  console.log(ctx.request.body)
  if (!ctx.request.body.hasOwnProperty('duration') || !ctx.request.body.hasOwnProperty('missedCallDuration')) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  if (!startTime || !phone || !status) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  if (isIncoming !== true && isIncoming !== false) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let userGroups = await getUserGroup()
  let group = userGroups.find(item => item.members.map(member => member.id).includes(ctx.state.user.id))

  let data = await strapi.query('telephone-record').create({
    startTime: new Date(startTime).toISOString(),
    phone,
    status,
    duration,
    missedCallDuration,
    fileUrl,
    fileSize,
    isIncoming: isIncoming ? '1' : '2',
    operator: ctx.state.user.id,
    groupType: group?.type === '运营小组' ? 'service' : (group?.type === '直营小组' ? 'sales' : null)
  })

  if (!data) {
    return ctx.wrapper.error('HANDLE_ERROR', '添加失败')
  }

  return ctx.wrapper.succ(data.id)
}

async function getRecords(ctx) {
  const { start = 0, limit = 20 } = ctx.query
  const user = ctx.state.user

  const total = await strapi.query('telephone-record').count({ operator: user.id })

  const list = await strapi.query('telephone-record').find({ operator: user.id, _limit: limit, _start: start }, [])

  let result = (list || []).map(item => {
    return {
      ...item,
      fileUrl: item.fileUrl || '',
      fileSize: item.fileSize || 0
    }
  })
  return ctx.wrapper.succ({
    total: total || 0,
    list: result
  })
}

module.exports = {
  loginByQxId,
  loginByQxCode,
  logout,
  upload,
  getRecords
}
