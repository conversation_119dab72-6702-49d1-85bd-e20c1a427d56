const jwt = require('jsonwebtoken')

async function getYjSupportToken (ctx) {
  let yxWeComId = ctx.state.user.customId
  // 校端超管跳转阅卷默认使用 wangdi 的身份
  if (!yxWeComId && ctx.state.user.role.type === 'SuperAdmin') {
    yxWeComId = 'wangdi_34739'
  }
  return jwt.sign(
    {
      'yxWeComId': yxWeComId
    },
    Buffer.from('TtKNLeHdrNbB8WL4YGmfGpQv', 'base64'),
    {
      expiresIn: 60 * 60 * 24 * 7,
      jwtid: yxWeComId,
      algorithm: 'HS256'
    }
  )
}

module.exports = {
  getYjSupportToken
}
