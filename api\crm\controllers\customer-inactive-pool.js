const { CurdRouter } = require('accel-utils')
const curdRouter = new (class extends CurdRouter {

  _getQueryByUser(query, user) {
    // 1. 销售：仅限已分配的省份；运营：不限
    // 2. 学校类型 =/= 单次联考
    query.crm_type_ne = '13cc0c2a-c4a7-4f35-ad4e-08c1ecc430b7' // 单次联考
    if (['sales-manager', 'sales-group-leader', 'sales-group-member',].includes(user.role.type)) {
      query._where = {
        province_in: user.provinces || []
      };
    }

    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    return super.find(ctx)
  }
})('customer-inactive-pool')

module.exports = {
  ...curdRouter.createHandlers(),
}
