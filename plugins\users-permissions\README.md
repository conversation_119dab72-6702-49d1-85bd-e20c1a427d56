# Users Permissions Plugin
用户权限插件

### 配置说明
```typescript
interface UsersPermissionsConfig {
  
  // 超级管理员账号密码
  adminUser?: {
    email: string,
    password: string,
  },
  
  // 默认模型配置
  modelConfig?: {
    viewSettings: {
      // 默认视图
      defaultViewType?: 'table',
      // 默认视图配置
      defaultViewConfig?: {
        // 操作列按钮模式
        // '1': 操作列按钮模式
        // '2': 操作列菜单模式
        optStyle: '1' | '2',
        // 个人视图开启
        userViewEnabled: boolean,
      }
    }
  }
  
}

```
