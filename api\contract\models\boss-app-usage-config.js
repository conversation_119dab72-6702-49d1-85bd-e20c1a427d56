'use strict';

module.exports = {
    "collectionName": "bossAppUsageConfig",
    "info": {
        "name": "BossAppUsageConfig",
        "label": "boss权益开通记录",
        "description": "boss权益开通记录"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": false
    },
    "pluginOptions": {},
    "attributes": {
        "orderId": {
            "label": "关联订单id",
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "contractId": {
            "label": "关联合同id",
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "crmUrl": {
            "label": "crm跳转链接",
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "no": {
            "label": "关联订单/合同编号",
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "customer": {
            "label": "用户",
            "editable": false,
            "mainField": "showName",
            "model": "boss-school"
        },
        "agent": {
            "label": "经销商",
            "editable": false,
            "mainField": "company",
            "model": "boss-agent"
        },
        "customerType": {
            "label": "类型",
            "type": "string",
            "editable": false,
            "options": [
                {
                    "label": "学校",
                    "value": "普通"
                },
                {
                    "label": "统考平台",
                    "value": "教研室"
                },
                {
                    "label": "联盟平台",
                    "value": "联盟"
                },
                {
                    "label": "集团校平台",
                    "value": "集团校"
                },
                {
                    "label": "单次联考",
                    "value": "联考"
                },
                {
                    "label": "教育局",
                    "value": "教育局"
                },
                {
                    "label": "培训机构",
                    "value": "培训机构"
                },
                {
                    label: '中心校平台',
                    value: '中心校'
                },
            ]
        },
        "customerAppVersion": {
            "label": "产品",
            "editable": false,
            "mainField": "name",
            "model": "customer-app-version"
        },
        "enabled": {
            "label": "是否启用",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "isTrial": {
            "label": "是否试用",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "isBase": {
            "label": "是否初始数据",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "duration": {
            "label": "周期",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "status": {
            "label": "配置状态",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "beginTime": {
            "label": "起始日期",
            "editable": false,
            "type": "date",
            "size": 3
        },
        "endTime": {
            "label": "结束日期",
            "editable": false,
            "size": 3,
            "type": "date"
        },
        "createTime": {
            "label": "创建日期",
            "editable": false,
            "size": 3,
            "type": "date"
        },
        "updateTime": {
            "label": "更新日期",
            "editable": false,
            "size": 3,
            "type": "date"
        },
        "sourceType": {
            "label": "来源",
            "editable": false,
            "size": 3,
            "type": "string",
            "options": [
                {
                    "value": "applySaasQuan",
                    "label": "saas券"
                },
                {
                    "value": "createCustomer",
                    "label": "初始体验版"
                },
                {
                    "value": "trial",
                    "label": "试用"
                },
                {
                    "value": "cancelTrial",
                    "label": "取消试用"
                },
                {
                    "value": "add",
                    "label": "新增"
                },
                {
                    "value": "order",
                    "label": "订单开通"
                }
            ]
        },
        "submitter": {
            "label": "提交人",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "durationParams": {
            "label": "访问周期",
            "editable": false,
            "size": 3,
            "type": "json"
        },
        "params": {
            "label": "版本参数",
            "editable": false,
            "size": 3,
            "type": "json"
        },
        "remark": {
            "label": "备注",
            "editable": false,
            "size": 3,
            "type": "string"
        }
    }
}
