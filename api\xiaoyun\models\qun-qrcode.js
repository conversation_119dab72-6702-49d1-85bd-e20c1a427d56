module.exports = {
  collectionName: 'qun-qrcode',
  info: {
    name: 'QunQrcode',
    label: '群二维码',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
    indexes: [
      { keys: { qunId: -1 } }
    ],
  },
  pluginOptions: {},
  attributes: {
    qunId: {
      label: '群ID',
      type: 'string',
      unique: true,
      editable: false
    },
    qrCode: {
      label: '二维码',
      type: 'string',
      editable: false
    },
    // 过期时间
    expireTime: {
      label: '过期时间',
      type: 'datetime',
      editable: false
    },
    // 手动二维码
    customQrCode: {
      label: '上传二维码',
      model: 'file',
      via: 'related',
      allowedTypes: [
        'images'
      ],
      plugin: 'upload',
      required: false,
      pluginOptions: {},
    },
    remark: {
      label: '备注',
      type: 'string'
    },
  }
}
