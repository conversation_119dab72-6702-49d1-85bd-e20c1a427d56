/**
 * Promise-based 并发控制
 * 执行 Promise 函数数组, 限制并发数, 确保不会同时执行超过指定数量的 Promise
 *
 * @param {Array<Function>} asyncFunctions - 返回 Promises 的数组
 * @param {number} [limit=1] - 最大并发数量
 * @returns {Promise<Array>} - 返回结果数组
 * @example
 * const tasks = Array.from({ length: 100 }, (_, i) => () =>
 *   new Promise(resolve => setTimeout(() => {
 *     console.info(`resolve Task ${i + 1}`)
 *     resolve(`Task ${i + 1}`)
 *   }, Math.random() * 300))
 * )
 * const result = await asyncConcurrency(tasks, 10)
 * expect(result.length).toBe(100)
 */
function asyncConcurrency (asyncFunctions, limit = 1) {
  const results = []
  const executing = []
  let index = 0

  return new Promise((resolve) => {
    const enqueue = () => {
      if (index >= asyncFunctions.length) {
        if (executing.length === 0) {
          resolve(results)
        }
        return
      }
      const promiseIndex = index++
      const p = asyncFunctions[promiseIndex]()
        .then(result => {
          results[promiseIndex] = result
        }).finally(() => {
          executing.splice(executing.indexOf(p), 1)
          enqueue()
        })
      executing.push(p)
      if (executing.length < limit) {
        enqueue()
      }
    }
    for (let i = 0; i < limit; i++) {
      enqueue()
    }
  })
}

/**
 * 包装异步函数支持缓存功能
 * 包装一个异步函数，支持缓存功能，确保在指定时间内多次调用具有相同参数的函数时，返回缓存的结果而不是重复执行异步函数。
 *
 * @template T
 * @param {(...args: any[]) => Promise<T>} asyncFunction - 异步函数
 * @param {number} [cacheDuration=5000] - 缓存持续时间（milliseconds）
 * @param {number} [cleanupInterval=1800] - 定期清理时间（seconds）
 * @returns {(...args: any[]) => Promise<T>} - 包装后的函数
 *
 * @example
 * async function fetchData(param) {
 *     return new Promise((resolve) => setTimeout(() => resolve(`Data for ${param}`), 1000));
 * }
 * const cachedFetchData = cacheAsyncFunction(fetchData, 5000);
 * cachedFetchData('param1').then(console.log); // Fetches new data
 * cachedFetchData('param1').then(console.log); // Returns cached data
 */
function asyncCache (asyncFunction, cacheDuration = 5000, cleanupInterval = 1800) {
  // Map 用于存储缓存值和正在进行中的 Promise
  const cache = new Map()
  const inProgress = new Map() // 存储正在执行的任务

  // 定期清理过期的缓存数据
  setInterval(() => {
    const now = Date.now()
    for (const [key, cachedValue] of cache.entries()) {
      if (now - cachedValue.timestamp >= cacheDuration) {
        cache.delete(key)
      }
    }
  }, cleanupInterval * 1000)

  return async function (...args) {
    const key = JSON.stringify(args) // 将参数序列化作为缓存的 key

    // 如果缓存存在且有效，直接返回缓存的结果
    if (cache.has(key)) {
      const cachedValue = cache.get(key)
      if (Date.now() - cachedValue.timestamp < cacheDuration) {
        return cachedValue.value
      }
      // 否则，缓存已过期，删除缓存
      cache.delete(key)
    }

    // 检查是否已经有相同参数的任务在执行
    if (inProgress.has(key)) {
      // 如果是，则等待正在进行的任务完成，并返回其结果
      return inProgress.get(key)
    }

    // 否则，开始一个新的任务，执行原始 async 函数
    const taskPromise = asyncFunction(...args)
      .then(result => {
        // 在任务完成后，缓存结果并清除进行中的任务记录
        cache.set(key, { value: result, timestamp: Date.now() })
        inProgress.delete(key)
        return result
      })
      .catch(error => {
        // 如果任务失败，仍然需要清除进行中的任务记录
        inProgress.delete(key)
        throw error
      })

    // 将正在进行的任务记录下来
    inProgress.set(key, taskPromise)

    return taskPromise
  }
}

/**
 * 包装一个异步函数，并限制其并发执行数。
 *
 * 返回一个新函数，该函数在调用传入的异步函数时，
 * 保证同时最多只有 `concurrencyLimit` 个异步任务在执行，
 * 多余的调用将进入等待队列，等到前面的任务执行完毕后再依次执行。
 *
 * @template T
 * @param {(...args: any[]) => Promise<T>} asyncFunction - 需要包装的异步函数。
 * @param {number} concurrencyLimit - 最大并发执行数，必须为大于 0 的整数。
 * @returns {(...args: any[]) => Promise<T>} 一个新函数，包装了原异步函数并控制并发量。
 * @throws {TypeError} 如果 asyncFunction 不是函数，或者 concurrencyLimit 不是大于 0 的整数。
 *
 * @example
 * // 假设 fetchData 是一个返回 Promise 的异步函数
 * const limitedFetchData = limitConcurrency(fetchData, 3);
 *
 * // 同时最多只有 3 个 fetchData 正在执行，其他调用将被排队等待
 * Promise.all([
 *   limitedFetchData(url1),
 *   limitedFetchData(url2),
 *   limitedFetchData(url3),
 *   limitedFetchData(url4) // 这个调用会等待前面的任务完成后再执行
 * ]).then(results => console.log(results));
 */
function limitConcurrency(asyncFunction, concurrencyLimit) {
  // 参数校验
  if (typeof asyncFunction !== 'function') {
    throw new TypeError('asyncFunction 必须是一个函数');
  }
  if (
    typeof concurrencyLimit !== 'number' ||
    !Number.isInteger(concurrencyLimit) ||
    concurrencyLimit < 1
  ) {
    throw new TypeError('concurrencyLimit 必须是大于 0 的整数');
  }

  // 当前正在执行的任务数量
  let activeCount = 0;
  // 队列保存等待执行的任务，每个任务保存调用参数以及 Promise 的 resolve/reject
  const queue = [];

  /**
   * 处理队列中的下一个任务（如果存在）。
   */
  function next() {
    // 当队列中有任务且当前执行数未达到上限时，取出下一个任务执行
    if (queue.length > 0 && activeCount < concurrencyLimit) {
      const { args, resolve, reject } = queue.shift();
      runTask(args, resolve, reject);
    }
  }

  /**
   * 执行异步任务并处理其结果或异常。
   *
   * @param {any[]} args - 传递给 asyncFunction 的参数数组。
   * @param {(value: T | PromiseLike<T>) => void} resolve - 外部 Promise 的 resolve 方法。
   * @param {(reason?: any) => void} reject - 外部 Promise 的 reject 方法。
   */
  function runTask(args, resolve, reject) {
    activeCount++;
    // 用 Promise.resolve() 包裹调用，确保即使 asyncFunction 不是严格的 async 函数也能正确处理
    Promise.resolve()
      .then(() => asyncFunction(...args))
      .then(result => {
        resolve(result);
      })
      .catch(error => {
        reject(error);
      })
      .finally(() => {
        activeCount--;
        // 任务结束后尝试启动队列中下一个任务
        next();
      });
  }

  /**
   * 返回包装后的函数，每次调用都会返回一个 Promise，
   * 当达到并发上限时，调用将被放入队列等待执行。
   *
   * @param  {...any} args - 传递给 asyncFunction 的参数。
   * @returns {Promise<T>} 包装后的异步函数的执行结果。
   */
  return function (...args) {
    return new Promise((resolve, reject) => {
      // 若当前任务数未达到上限，则立即执行
      if (activeCount < concurrencyLimit) {
        runTask(args, resolve, reject);
      } else {
        // 否则将任务添加到队列中等待
        queue.push({ args, resolve, reject });
      }
    });
  };
}


module.exports = {
  asyncConcurrency,
  asyncCache,
  limitConcurrency
}
