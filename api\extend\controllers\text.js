const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('text')
const path = require('path');
const axios = require('axios');
const fs = require('fs');
const { ObjectId } = require('mongodb');
const _ = require('lodash')

async function updateFile(ctx) {
    const { id } = ctx.params;
    const { text } = ctx.request.body;

    const updateResult = await strapi.query('text').update({ id: ObjectId(id) }, { text })
    // console.log(text);
    fs.writeFileSync(path.resolve(__dirname, '../../contract/models/contract.js'), text);
    let exec = require('child_process').exec, child;

    exec('pm2 reload wly-boss-serv --force',
        function (error, stdout, stderr) {
            console.log('stdout: ' + stdout);
            console.log('stderr: ' + stderr);
            if (error !== null) {
                console.log('exec error: ' + error);
            }
        });
    return ctx.wrapper.succ(updateResult);
}

module.exports = {
    updateFile,
    ...curdRouter.createHandlers(),
}
