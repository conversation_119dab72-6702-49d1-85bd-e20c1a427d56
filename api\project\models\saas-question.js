module.exports = {
  collectionName: strapi.config.server.mingdaoConfig.saasQuestionId,
  connection: 'mingdao',
  info: {
    name: 'SaasProjectQuestion',
    label: '问题跟踪',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    title: {
      ref: '67b549fc9e13a09bff09f175'
    },
    desc: {
      ref: '67b54b179e13a09bff09f1ca'
    },
    requirement: {
      ref: '67b5543b9e13a09bff09f70f'
    },
    // 严重程度
    problemLevel: {
      ref: '67b54b179e13a09bff09f1cb'
    },
    status: {
      ref: '67b54b179e13a09bff09f1cc'
    },
    resolveUser: {
      ref: '67b54b179e13a09bff09f1cd'
    },
    remark: {
      ref: '67b54b179e13a09bff09f1cf'
    },
    resolveTime: {
      ref: '67b54b179e13a09bff09f1d0'
    },
    submitUser: {
      ref: '67b54b179e13a09bff09f1d1'
    }
  }
}
