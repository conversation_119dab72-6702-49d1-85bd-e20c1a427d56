const { <PERSON>urd<PERSON>outer } = require('accel-utils')
const { ObjectId } = require('mongodb')
const _ = require('lodash')
const {
  getBaseQuery,
  getQueryFollowerChange,
  getUserQueryByRole,
  getFollowerNowGroup,
  getFollowerRecord,
} = require('../../crm/services/customer-service')
const axios = require('axios')
// const { getYjAppUsageStatisticByLocal } = require('../../crm-mid/services/customer')
// let accessKeys = []
// let lastCacheTime = 0
// const configServUrl = strapi.config.server.configServUrl
const { customerTypes } = require('../../crm-mid/utils/crmTypes')
const huoban = require('../../contract/utils/huoban')
const huobanConfig = strapi.config.server.huoban

const curdRouter = new (class extends CurdRouter {
  // ...
  _getIntersection (arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }

  async findOne (ctx) {
    const query = ctx.request.query
    const customerId = query.customerId
    const schoolId = query.schoolId
    let customer, customerService
    if (customerId) {
      customerService = await strapi.query('customer-service').find({ customerId })
      customerService = customerService?.[0]
      // if (!customerService && customerId) {
      //   customer = await strapi.query('customer').findOne({ id: customerId })
      //   if (customer) {
      //     customerService = await strapi.query('customer-service').create({
      //       name: customer.name,
      //       customerId: customerId,
      //       schoolId: customer.school_id_20
      //     })
      //   }
      // }
    } else if (schoolId) {
      customerService = await strapi.query('customer-service').find({ schoolId: +schoolId })
      customerService = customerService?.[0]
      // if (!customerService && schoolId) {
      //   customer = await strapi.query('customer').findOne({ school_id_20: +schoolId })
      //   if (customer) {
      //     customerService = await strapi.query('customer-service').create({
      //       name: customer.name,
      //       customerId: customer.id,
      //       schoolId: customer.school_id_20
      //     })
      //   }
      // }
    } else {
      customerService = await super.findOne(ctx)
    }

    return customerService
    // const groups = await strapi.query('manager-group').find({})
    // return getFollowerNowGroup(customerService, groups)
  }

  async count (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    // getQueryKeyChange(query)
    // getQueryFollowerChange(query, groups)
    getUserQueryByRole(query, user, groups)
    return super.count(ctx)
  }

  async find (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    // getQueryKeyChange(query)
    // getQueryFollowerChange(query, groups)
    getUserQueryByRole(query, user, groups)

    const customerServices = await super.find(ctx)
    // 填充服务数据
    for (let i = 0; i < customerServices.length; i++) {
      customerServices[i] = await getFollowerRecord(customerServices[i])
    }
    return customerServices
  }

  async update (ctx) {
    const { data } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    // 明道云 mongo 跟进记录联合查询, 更新子表过滤 mongo 数据
    if (data.customerSalesFollowRecords) {
      data.customerSalesFollowRecords = data.customerSalesFollowRecords.filter(e => e.id?.length !== 24)
    }
    const result = await super.update(ctx)
    return result
  }

  async updateMany (ctx) {
    const { data: { filter, data } } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    const result = await super.updateMany(ctx)
    return result
  }
})('customer-service')

async function updateCustomFields (ctx) {
  let { customerId } = ctx.request.body
  if (!customerId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const customers = await strapi.query('customer-service').find({ customerId: customerId })

  if (customers.length !== 1) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const customer = customers[0]

  const oldCustomers = await strapi.query('customer-service-mid').find({ customerId: customerId }, [])

  if (oldCustomers.length !== 1) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const customerServiceMid = oldCustomers[0]

  // 更新中间表跟进人信息
  const mingdaoIds = _.compact([customer?.directSalesManager?.[0]?.rowid, customer?.directServiceManager?.[0]?.rowid])
  const users = await strapi.query('user', 'users-permissions').find({ mingdaoId_in: mingdaoIds }, [])
  const directSalesManager = users.find(e => e.mingdaoId && e.mingdaoId === customer?.directSalesManager?.[0]?.rowid)
  const directServiceManager = users.find(e => e.mingdaoId && e.mingdaoId === customer?.directServiceManager?.[0]?.rowid)
  let customerMidUpdate = {
    $set: {
      hideQunQrCode: customer?.hideQunQrCode || false,
      yjShowSalesManager: customer?.yjShowSalesManager || false,
    },
    $unset: {},
  }
  if (directSalesManager) {
    if (customerServiceMid.directSalesManager !== directSalesManager?.id) {
      customerMidUpdate.$set.directSalesManager = directSalesManager?.id
      // 如果已立项，更新直营经理 。更新伙伴云立项信息。
      if (customer.projectNo) {
        console.log('------- 需要修改远程直营经理', directSalesManager.username, customer.name)

        const now = new Date()
        await strapi.query('huoban-queue').create({
          type: 'salesFollow',
          key: `${now.getTime()}-salesFollow`,
          customerId: customerServiceMid.customerId,
          customer: customerServiceMid.customerId,
          // relatedId: log._id.toString(),
          status: 'prepared',
          createdAt: now,
          updatedAt: now,
        })
      }
    }
  } else {
    customerMidUpdate.$unset.directSalesManager = ''
  }
  if (directServiceManager) {
    if (customerServiceMid.directServiceManager !== directServiceManager?.id) {
      customerMidUpdate.$set.directServiceManager = directServiceManager?.id
    }
  } else {
    customerMidUpdate.$unset.directServiceManager = ''
  }
  await strapi.query('customer-service-mid').model.updateOne({
    // id: customerServiceMid.id,
    customerId: customer.customerId
  }, customerMidUpdate)


  return ctx.wrapper.succ({})
}

async function syncHuobanProjectManager (customer, curUser) {
  if (!customer.projectNo) return
  console.log('------- 开始 修改远程直营经理')
  const schoolItem = await huoban.getSchoolById(customer.schoolId)
  const salesInfo = await huoban.getUserByQxId(curUser.customId)

  if (!salesInfo) {
    console.log('------- 伙伴云未找到直营经理')

    throw new Error('伙伴云未找到直营经理')
  }

  let fields = {}
  fields[huobanConfig.school_table.fieldsMap['远程项目编号']] = customer.projectNo
  fields[huobanConfig.school_table.fieldsMap['远程直营经理']] = { id: salesInfo.item_id }
  const result = await huoban.updateSchoolItem(schoolItem.item_id, fields)
  console.log('------- 修改完成', result)

  if (result.code !== 0) {
    throw new Error(result.msg)
  }
}

async function updateMingdaoSchool (ctx) {
  let { customerIds } = ctx.request.body
  const crmTypeMap = {
    '普通': '学校',
    '教研室': '统考平台',
    '联盟': '联盟平台',
    '集团校': '集团校平台',
    '中心校': '中心校平台',
    '联考': '单次联考',
    '教育局': '教育局',
    '培训机构': '培训机构',
  }
  try {
    const customerServices = await strapi.query('customer-service').find({ customerId: customerIds, _limit: 100 }, [])
    const customerInactivePools = await strapi.query('customer-inactive-pool').find({ customerId: customerIds, _limit: 100 }, [])
    const customerServiceMap = {}
    const customerInactivePoolMap = {}
    for (const customerService of customerServices) {
      customerServiceMap[customerService.customerId] = customerService
    }
    for (const customerInactivePool of customerInactivePools) {
      customerInactivePoolMap[customerInactivePool.customerId] = customerInactivePool
    }
    const midCustomers = await strapi.query('customer-service-mid').find({
      customerId_in: customerIds,
      schoolId_gt: 0,
      _limit: 100
    }, [])
    // const midCustomers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //   db: 'wly_boss',
    //   coll: 'customer-service',
    //   filter: {
    //     customerId: { $in: customerIds },
    //     schoolId: { $gt: 0 },
    //   },
    //   limit: 100
    // }).then(({ data }) => { return data })
    for (const midCustomer of midCustomers) {
      // 若【学校档案】或【学校档案（不活跃）】存在，更新数据。若不存在，在【学校档案】新增数据
      const customerService = customerServiceMap[midCustomer.customerId]
      const customerInactivePool = customerInactivePoolMap[midCustomer.customerId]
      let modelName = customerService ? 'customer-service' : (customerInactivePool ? 'customer-inactive-pool' : 'customer-service')
      let customerServiceId = customerService?.id || customerInactivePool?.id

      // if (customerServiceId
      //   && midCustomer?.name == customerService?.name
      //   && midCustomer?.schoolCategory == customerService?.schoolCategory
      //   && midCustomer?.yjTag == customerService?.yjTag
      //   // && midCustomer?.lastExamTime?.getTime() == customerService?.lastExamTime?.getTime()
      //   && midCustomer?.province == customerService?.province
      //   && midCustomer?.city == customerService?.city
      //   && midCustomer?.district == customerService?.district
      //   && midCustomer?.edu_system == customerService?.edu_system
      //   && midCustomer?.system == customerService?.system
      //   && midCustomer?.mingyou_school_type == customerService?.mingyou_school_type
      //   && midCustomer?.total_number == customerService?.total_number
      //   && midCustomer?.school_size == customerService?.school_size
      //   && midCustomer?.crm_type == customerService?.crm_type
      //   // && midCustomer?.usages == customerService?.usages
      //   && midCustomer?.yjVersion == customerService?.yjVersion
      //   // && midCustomer?.yjStartTime?.getTime() == customerService?.yjStartTime?.getTime()
      //   // && midCustomer?.yjEndTime?.getTime() == customerService?.yjEndTime?.getTime()
      //   // && midCustomer?.fxVersion == customerService?.fxVersion
      //   // && midCustomer?.fxStartTime?.getTime() == customerService?.fxStartTime?.getTime()
      //   // && midCustomer?.fxEndTime?.getTime() == customerService?.fxEndTime?.getTime()
      //   // && midCustomer?.tkVersion == customerService?.tkVersion
      //   // && midCustomer?.tkStartTime?.getTime() == customerService?.tkStartTime?.getTime()
      //   // && midCustomer?.tkEndTime?.getTime() == customerService?.tkEndTime?.getTime()
      // ) {
      //   continue;
      // } else {
      //   console.log(`name: + ${midCustomer?.name == customerService?.name}`)
      //   console.log(`schoolCategory: + ${midCustomer?.schoolCategory == customerService?.schoolCategory}`)
      //   console.log(`yjTag: + ${midCustomer?.yjTag == customerService?.yjTag}`)
      //   // console.log(`lastExamTime: + ${midCustomer?.lastExamTime?.getTime() == customerService?.lastExamTime?.getTime()}`)
      //   console.log(`province: + ${midCustomer?.province == customerService?.province}`)
      //   console.log(`city: + ${midCustomer?.city == customerService?.city}`)
      //   console.log(`district: + ${midCustomer?.district == customerService?.district}`)
      //   console.log(`edu_system: + ${midCustomer?.edu_system == customerService?.edu_system}`)
      //   console.log(`system: + ${midCustomer?.system == customerService?.system}`)
      //   console.log(`mingyou_school_type: + ${midCustomer?.mingyou_school_type == customerService?.mingyou_school_type}`)
      //   console.log(`total_number: + ${midCustomer?.total_number == customerService?.total_number}`)
      //   console.log(`school_size: + ${midCustomer?.school_size == customerService?.school_size}`)
      //   console.log(`crm_type: + ${midCustomer?.crm_type == customerService?.crm_type}`)
      //   // console.log(`usages: + ${midCustomer?.usages == customerService?.usages}`)
      //   console.log(`yjVersion: + ${midCustomer?.yjVersion == customerService?.yjVersion}`)
      //   console.log(`yjStartTime: + ${midCustomer?.yjStartTime?.getTime() == customerService?.yjStartTime?.getTime()}`)
      //   console.log(`yjEndTime: + ${midCustomer?.yjEndTime?.getTime() == customerService?.yjEndTime?.getTime()}`)
      //   console.log(`fxVersion: + ${midCustomer?.fxVersion == customerService?.fxVersion}`)
      //   console.log(`fxStartTime: + ${midCustomer?.fxStartTime?.getTime() == customerService?.fxStartTime?.getTime()}`)
      //   console.log(`fxEndTime: + ${midCustomer?.fxEndTime?.getTime() == customerService?.fxEndTime?.getTime()}`)
      //   console.log(`tkVersion: + ${midCustomer?.tkVersion == customerService?.tkVersion}`)
      //   console.log(`tkStartTime: + ${midCustomer?.tkStartTime?.getTime() == customerService?.tkStartTime?.getTime()}`)
      //   console.log(`tkEndTime: + ${midCustomer?.tkEndTime?.getTime() == customerService?.tkEndTime?.getTime()}`)
      // }

      let defaultFields = {
        name: midCustomer.name,
        customerId: midCustomer.customerId,
        schoolId: midCustomer.schoolId.toString(),
        schoolCategory: midCustomer.schoolCategory,
        yjTag: midCustomer.yjTag,
        lastExamTime: midCustomer.lastExamTime,
        province: midCustomer.province,
        city: midCustomer.city,
        district: midCustomer.district,
        edu_system: midCustomer.edu_system,
        system: midCustomer.system,
        mingyou_school_type: midCustomer.mingyou_school_type,
        total_number: midCustomer.total_number,
        school_size: midCustomer.school_size,
        crm_type: midCustomer?.crm_type ? crmTypeMap[midCustomer?.crm_type] : undefined,
        // s_count: midCustomer.s_count,
        // t_count: midCustomer.t_count,
        // usages: midCustomer.usages,
        yjVersion: midCustomer.yjVersion,
        yjStartTime: midCustomer.yjStartTime,
        yjEndTime: midCustomer.yjEndTime,
        fxVersion: midCustomer.fxVersion,
        fxStartTime: midCustomer.fxStartTime,
        fxEndTime: midCustomer.fxEndTime,
        tkVersion: midCustomer.tkVersion,
        tkStartTime: midCustomer.tkStartTime,
        tkEndTime: midCustomer.tkEndTime,

        authAgentId: midCustomer.authAgentId,
        // authAgentNo: midCustomer.authAgentNo,
        authAgentName: midCustomer.authAgentName,
        authType: midCustomer.authType,
        authEndTime: midCustomer.authEndTime,
        authRemark: midCustomer.authRemark,
        // 群关联信息
        qunId: midCustomer.qunId
      }
      const attributes = strapi.query(modelName).model.attributes
      for (const key of Object.keys(defaultFields)) {
        const curAttribute = attributes[key]
        if (curAttribute?.type === 'string' && curAttribute?.options?.length > 0) {
          const curOptions = curAttribute.options
          const curValue = curOptions.find(item => item.label === defaultFields[key])?.value
          defaultFields[key] = curValue
        }
      }
      if (customerServiceId) {
        // let result = await strapi.query('customer-service').update({
        //   id: customerServiceId
        // }, defaultFields)
        await strapi.entityService.update({
          params: {
            id: customerServiceId
          },
          data: defaultFields
        }, { model: modelName })
      } else {
        let result = await strapi.query(modelName).create(defaultFields)
      }
    }

    return ctx.wrapper.succ({})
  } catch (e) {
    console.log(e)
    console.error(e)
    throw e
  }
}

async function getSalesFollowCustomers (ctx) {
  let { qxId } = ctx.request.query
  if (!qxId) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  const user = await strapi.query('user', 'users-permissions').findOne({
    customId: qxId,
    _projection: { customId: 1, id: 1, mingdaoId: 1 }
  }, [])
  if (_.isEmpty(user)) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')

  const customers = await strapi.query('customer-service-mid').find({
    directSalesManager: user.id,
    _limit: 10000,
    _projection: { id: 1, name: 1, schoolId: 1, crm_type: 1, province: 1, city: 1, district: 1 }
  }, [])

  const crmTypeMap = {}
  for (const customerType of customerTypes) {
    crmTypeMap[customerType.value] = customerType.label
  }
  return ctx.wrapper.succ(customers.map(item => {
    return {
      schoolId: item.schoolId,
      name: item.name,
      location: {
        province: item.province,
        city: item.city,
        district: item.district
      },
      type: crmTypeMap[item.crm_type],
      linkStatus: 1,
    }
  }))
}

async function distributeClueCustomer (ctx) {
  let { filter, data } = ctx.request.body
  let { directSalesManager, directServiceManager, directSalesTeam, directServiceTeam, unit } = data
  // 公共资源池认领限制：
  // 1. 认领-本人【直营1000，运营2000】：本人资源达上限后不能认领（运营经理/直营经理=本人）
  // 2. 认领-本组【直营组100，运营组100】：本组未分配资源达上限后不能认领（（所属运营小组=本组 && 运营经理为空）or（所属直营小组=本组 && 直营经理为空））
  // 3. 认领-本单元【单元100】：本单元未分配资源达上限后不能认领（所属单元=本单元 && 运营经理为空 && 直营经理为空）
  const countLimits = {
    unitCount: 100,
    salesTeamCount: 100,
    serviceTeamCount: 100,
    salesManagerCount: 1000,
    serviceManagerCount: 2000,
  }
  const distributeCount = filter.id_in.length

  if (unit && !directSalesTeam && !directServiceTeam && !directSalesManager && !directServiceManager) {
    const unitCount = await strapi.query('customer-service').count({ unit_null: true, directSalesManager_null: true, directServiceManager_null: true, })
    if (countLimits.unitCount < unitCount + distributeCount) return ctx.wrapper.error('HANDLE_ERROR', `单元资源数目不可超过${countLimits.unitCount}，当前为${unitCount}`)
  }
  if (directSalesTeam && !directSalesManager) {
    const teamCount = await strapi.query('customer-service').count({ directSalesTeam_eq: directSalesTeam, directSalesManager_null: true, })
    if (countLimits.salesTeamCount < teamCount + distributeCount) return ctx.wrapper.error('HANDLE_ERROR', `小组资源数目不可超过${countLimits.salesTeamCount}，当前为${teamCount}`)
  }
  if (directServiceTeam && !directServiceManager) {
    const teamCount = await strapi.query('customer-service').count({ directServiceTeam_eq: directServiceTeam, directServiceManager_null: true, })
    if (countLimits.serviceTeamCount < teamCount + distributeCount) return ctx.wrapper.error('HANDLE_ERROR', `小组资源数目不可超过${countLimits.serviceTeamCount}，当前为${teamCount}`)
  }
  if (directSalesManager) {
    const managerCount = await strapi.query('customer-service').count({ directSalesManager_eq: directSalesManager, })
    if (countLimits.salesManagerCount < managerCount + distributeCount) return ctx.wrapper.error('HANDLE_ERROR', `个人资源数目不可超过${countLimits.salesManagerCount}，当前为${managerCount}`)
  }
  if (directServiceManager) {
    const managerCount = await strapi.query('customer-service').count({ directServiceManager_eq: directServiceManager, })
    if (countLimits.serviceManagerCount < managerCount + distributeCount) return ctx.wrapper.error('HANDLE_ERROR', `个人资源数目不可超过${countLimits.serviceManagerCount}，当前为${managerCount}`)
  }

  try {
    await curdRouter.updateMany(ctx)
    return ctx.wrapper.succ({})
  } catch (e) {
    throw e
  }
}

async function searchCustomerByKey (ctx) {
  let { key } = ctx.request.query
  if (!key) return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')

  let query = { _limit: 10 }
  let num = Number(key)
  if (_.isFinite(num)) {
    query.schoolId_eq = +key
  } else {
    query.name_contains = key
  }
  const customers = await strapi.query('customer-service').find(query)
  const inactivePools = await strapi.query('customer-inactive-pool').find(query)
  const customerMids = await strapi.query('customer-service-mid').find(query)

  return ctx.wrapper.succ({
    customers,
    inactivePools,
    customerMids,
  })
}

module.exports = {
  updateCustomFields,
  updateMingdaoSchool,
  getSalesFollowCustomers,
  distributeClueCustomer,
  searchCustomerByKey,
  ...curdRouter.createHandlers(),
}
