module.exports = {
  collectionName: 'customer-follow-record',
  info: {
    name: 'CustomerFollowRecord',
    label: '客户跟进记录',
    description: '客户跟进记录'
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
    defaultMainField: 'title',
  },
  pluginOptions: {},
  attributes: {
    customerService: {
      label: '客户',
      model: 'customer-service-mid',
      mainField: 'name',
      required: true,
      visible: false
    },
    follower: {
      label: '跟进人',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      required: true,
      size: 6,
      meta: {
        query: {
          ['role.type']: ['sales-admin', 'sales-manager', 'sales-group-leader', 'sales-group-member']
        }
      }
    },
    operator: {
      label: '操作人',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      visible: false
    },
    time: {
      label: '跟进时间',
      type: 'datetime',
      'format': 'YYYY-MM-DD',
      required: true,
      size: 4
    },
    content: {
      label: '沟通内容',
      type: 'richtext',
    },
    nextContent: {
      label: '下次跟进方向',
      type: 'text',
    },
    nextTime: {
      label: '下次跟进时间',
      type: 'datetime',
      'format': 'YYYY-MM-DD',
      size: 4
    },
    recordInvalid: {
      label: '记录无效',
      type: 'boolean',
      editable: false,
      visible: false
    },
    status: {
      label: '状态',
      type: 'string',
      options: [
        { label: '提醒', value: 'remind' },
        { label: '已完成', value: 'finished' },
      ],
      visible: false,
      size: 4
    },
    prevFollow: {
      label: '上次服务',
      model: 'customer-follow-record',
      via: 'nextFollow',
      editable: false
    },
    nextFollow: {
      label: '下次服务',
      model: 'customer-follow-record',
      via: 'prevFollow',
      editable: false,
      visible: false
    },

    stage: {
      label: '阶段',
      type: 'string',
      options: [
        { label: '初次联系', value: 'firstContact', },
        { label: '挖掘需求', value: 'exploreDemand', },
        { label: '明确意向', value: 'confirmIntention', },
        { label: '谈单', value: 'negotiations', },
        { label: '签约', value: 'signing', },
      ],
      editable: false,
      size: 4
    },
    action: {
      label: '动作',
      type: 'string',
      options: [
        { label: '有效沟通', value: 'call', },
        { label: '有效链接', value: 'introduce', },
        { label: '关键人加V', value: 'keyman', },
        { label: '明确产品需求', value: 'confirmDemand', },
        { label: '明确采购计划', value: 'confirmPlan', },
        { label: '组织规模服务', value: 'trial', },
        { label: '明确预算', value: 'confirmBudget', },
        { label: '明确决策链和流程', value: 'decisionChain', },
        { label: '明确采购需求', value: 'confirmVersion', },
        { label: '设计报价方案并解读', value: 'quotation', },
        { label: '明确采购决议', value: 'purchase', },
        { label: '确认合同', value: 'confirmTerms', },
        { label: '签订合同并回款', value: 'contract', },
      ],
      editable: false,
      size: 4
    }
  }
}
