module.exports = {
  'kind': 'collectionType',
  'collectionName': strapi.config.server.mingdaoConfig.brandMenuId,
  'connection': 'mingdao',
  'info': {
    name: 'BrandMenu',
    label: '阅卷家菜单',
    description: ''
  },
  'options': {},
  'pluginOptions': {},
  'attributes': {
    name: {
      // label: '名称',
      ref: '67510d79ba60f67ec34c2081'
    },
    icon: {
      ref: '67512aeaba60f67ec34c2751',
    },
    no: {
      // label: '编号',
      ref: '6751238fba60f67ec34c2616'
    },
    role: {
      ref: '6751135dba60f67ec34c2248'
    },
    // 优先级
    priority: {
      // label
      ref: '67515eb6ba60f67ec34c2c39'
    },
    isHttp: {
      // label: '是否外链',
      ref: '67514c3fba60f67ec34c28d7'
    },
    router: {
      // label: '路径',
      ref: '67510dd0ba60f67ec34c20a7'
    },
    component: {
      // label: '组件',
      ref: '67514bc4ba60f67ec34c28b1'
    },
    params: {
      // label: '参数',
      ref: '67514bc4ba60f67ec34c28b2'
    },
    // 是否停用 67514bc4ba60f67ec34c28b3
    disabled: {
      // label: '是否停用',
      ref: '67514bc4ba60f67ec34c28b3'
    },
    // 是否缓存 67514bc4ba60f67ec34c28b4
    cache: {
      // label: '是否缓存',
      ref: '67514bc4ba60f67ec34c28b4'
    },
    parent: {
      ref: '67510dddba60f67ec34c20b0'
    },
    _children: {
      ref: '67510dddba60f67ec34c20b1'
    },
    brandName: {
      ref: '67a586d39e13a09bff02c934'
    },
    brandApp: {
      ref: '67a586d39e13a09bff02c935'
    }
  }
}
