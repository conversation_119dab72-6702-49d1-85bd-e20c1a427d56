variables:
  SSH_TEST: "ssh work@**************"
  SSH_PROD: "ssh work@**************"

stages:
  - deploy
  - notify

deploy-prod:
  stage: deploy
  only:
    - master
  script:
    - echo "Success"
    # SSH 先踩一下点，确保不会出现需要确认
    #- $SSH_PROD -o StrictHostKeyChecking=no -o GSSAPIAuthentication=no -p 22 "hostname"
    #- $SSH_PROD "cd ~/project/template-serv/ && git fetch"
    #- $SSH_PROD "cd ~/project/template-serv/ && git reset --hard origin/master"
    #- $SSH_PROD "cd ~/project/template-serv/ && chmod 777 packages/strapi/bin/strapi.js"
    #- $SSH_PROD "cd ~/project/template-serv/ && yarn && yarn build && pm2 reload template-serv"
  tags:
    - local

# 构建通知
.notify-rule: &notify-rule
  stage: notify
  tags:
    - local
  only:
    - master

# 构建成功时的通知消息
notify-success:
  <<: *notify-rule
  only:
    - master
  script:
    - GIT_LOG=$(git log --pretty=format:'提交人:%an Hash:%h %s' -1)
#    - >-
#      curl -X POST -H "Content-Type: application/json" \
#        -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"✅ 【正式】(模板)服务构建成功 \r\n ${GIT_LOG}\"}}" \
#        https://open.feishu.cn/open-apis/bot/v2/hook/f501dc75-8a53-4016-9810-151366ce96a3
  when: on_success

# 构建失败时的通知消息
notify-fail:
 <<: *notify-rule
 script:
   - GIT_LOG=$(git log --pretty=format:'提交人:%an Hash:%h %s' -1)
#   - >-
#     curl -X POST -H "Content-Type: application/json" \
#       -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"💥 【正式】(模板)服务构建失败 \r\n ${GIT_LOG}\"}}" \
#       https://open.feishu.cn/open-apis/bot/v2/hook/f501dc75-8a53-4016-9810-151366ce96a3
 when: on_failure

# 测试环境构建
deploy-test:
  stage: deploy
  only:
    - test
  script:
    - echo "Success"
    # SSH 先踩一下点，确保不会出现需要确认
    #- $SSH_TEST -o StrictHostKeyChecking=no -o GSSAPIAuthentication=no -p 22 "hostname"
    #- $SSH_TEST "cd ~/test/template-serv/ && git fetch"
    #- $SSH_TEST "cd ~/test/template-serv/ && git reset --hard origin/test"
    #- $SSH_TEST "cd ~/test/template-serv/ && chmod 777 packages/strapi/bin/strapi.js"
    #- $SSH_TEST "cd ~/test/template-serv/ && yarn && yarn build-test && pm2 reload test-template-serv"
  tags:
    - local

# 构建通知
.test-notify-rule: &test-notify-rule
  stage: notify
  tags:
    - local
  only:
    - test

# 构建成功时的通知消息
test-notify-success:
  <<: *test-notify-rule
  only:
    - test
  script:
    - GIT_LOG=$(git log --pretty=format:'提交人:%an Hash:%h %s' -1)
    - >-
      curl -X POST -H "Content-Type: application/json" \
        -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"✅ 【测试】(模板)服务构建成功 \r\n ${GIT_LOG}\"}}" \
        https://open.feishu.cn/open-apis/bot/v2/hook/f501dc75-8a53-4016-9810-151366ce96a3
  when: on_success

# 构建失败时的通知消息
test-notify-fail:
  <<: *notify-rule
  script:
    - GIT_LOG=$(git log --pretty=format:'提交人:%an Hash:%h %s' -1)
    - >-
      curl -X POST -H "Content-Type: application/json" \
        -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"💥 【测试】(模板)服务构建失败 \r\n ${GIT_LOG}\"}}" \
        https://open.feishu.cn/open-apis/bot/v2/hook/f501dc75-8a53-4016-9810-151366ce96a3
  when: on_failure
