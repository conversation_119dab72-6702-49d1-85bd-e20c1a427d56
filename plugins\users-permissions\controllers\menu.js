'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */

const defaultConfig = require('../config/permission')
const { CurdRouter } = require('accel-utils')
const _ = require('lodash')

const curdRouter = new CurdRouter('menu', { pluginName: 'users-permissions' })

module.exports = {
  ...curdRouter.createHandlers(),
  async getBaseMenu () {
    const pluginStore = strapi.store({ type: 'plugin', name: 'users-permissions' })
    return await pluginStore.get({ key: 'menus' })
  },
  async setBaseMenu (ctx) {
    const pluginStore = strapi.store({
      type: 'plugin',
      name: 'users-permissions',
    })
    const body = ctx.request.body
    const pageGroups = [
      ...defaultConfig.usersPermissionsConfig.pageGroups,
      ...(strapi.config.permission.pageGroups || [])
    ]
    await pluginStore.set({ key: 'menus', value: body.menus })
    return {
      menus: body,
      pageGroups,
    }
  },
}
