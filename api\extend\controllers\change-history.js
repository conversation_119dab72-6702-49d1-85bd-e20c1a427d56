const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('change-history')

async function _addChangeHistory(data) {
    const { model,beforeData ,afterData} = data;
    const info = 
    await strapi.query('change-history').create({
        model: model,
        modelId: beforeData.id,
        beforeData: beforeData,
        afterData: afterData,
    });
    return info;
}

module.exports = {
    _addChangeHistory,
    ...curdRouter.createHandlers(),
}
