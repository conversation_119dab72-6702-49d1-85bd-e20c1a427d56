const { customerTypes } = require('../../crm-mid/utils/crmTypes')
module.exports = {
  collectionName: 'qun-statistic',
  info: {
    name: 'QunStatistic',
    label: '群统计',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    manager: {
      label: '用户',
      model: 'user',
      plugin: 'users-permissions',
      mainField: 'username',
      editable: false
    },
    team: {
      label: '小组',
      collection: 'manager-group-mid',
      editable: false,
    },
    crm_type: {
      label: '类型',
      type: 'string',
      editable: false,
      options: customerTypes,
      size: 2,
    },
    schoolCount: {
      label: '学校数量',
      type: 'number',
      editable: false
    },
    qunCount: {
      label: '群数量',
      type: 'number',
      editable: false
    },
    noUserQunCount: {
      label: '无用户群数量',
      type: 'number',
      editable: false
    },
    vipSchoolCount: {
      label: '付费校数量',
      type: 'number',
      editable: false
    },
    vipQunCount: {
      label: '付费校群数量',
      type: 'number',
      editable: false
    },
    vipNoUserQunCount: {
      label: '付费校无用户群数量',
      type: 'number',
      editable: false
    },
    noVipSchoolCount: {
      label: '未付费校数量',
      type: 'number',
      editable: false
    },
    noVipQunCount: {
      label: '未付费校群数量',
      type: 'number',
      editable: false
    },
    noVipNoUserQunCount: {
      label: '未付费校无用户群数量',
      type: 'number',
      editable: false
    },
    todayVipNeedQunCount: {
      label: '今日活跃付费校需建群数量',
      type: 'number',
      editable: false
    },
    todayNoVipNeedQunCount: {
      label: '今日活跃未付费校需建群数量',
      type: 'number',
      editable: false
    },
    future3DayVipNeedQunCount: {
      label: '未来3天活跃付费校需建群数量',
      type: 'number',
      editable: false
    },
    future3DayNoVipNeedQunCount: {
      label: '未来3天活跃未付费校需建群数量',
      type: 'number',
      editable: false
    },
    future7DayVipNeedQunCount: {
      label: '未来7天活跃付费校需建群数量',
      type: 'number',
      editable: false
    },
    future7DayNoVipNeedQunCount: {
      label: '未来7天活跃未付费校需建群数量',
      type: 'number',
      editable: false
    },
    month1NoVipSchoolCount: {
      label: '近1个月活跃未付费校数量',
      type: 'number',
      editable: false
    },
    month1NoVipQunCount: {
      label: '近1个月活跃未付费校群数量',
      type: 'number',
      editable: false
    },
    month1NoVipNoUserQunCount: {
      label: '近1个月活跃未付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month1VipSchoolCount: {
      label: '近1个月活跃付费校数量',
      type: 'number',
      editable: false
    },
    month1VipQunCount: {
      label: '近1个月活跃付费校群数量',
      type: 'number',
      editable: false
    },
    month1VipNoUserQunCount: {
      label: '近1个月活跃付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month3NoVipSchoolCount: {
      label: '近3个月活跃未付费校数量',
      type: 'number',
      editable: false
    },
    month3NoVipQunCount: {
      label: '近3个月活跃未付费校群数量',
      type: 'number',
      editable: false
    },
    month3NoVipNoUserQunCount: {
      label: '近3个月活跃未付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month3VipSchoolCount: {
      label: '近3个月活跃付费校数量',
      type: 'number',
      editable: false
    },
    month3VipQunCount: {
      label: '近3个月活跃付费校群数量',
      type: 'number',
      editable: false
    },
    month3VipNoUserQunCount: {
      label: '近3个月活跃付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month6NoVipSchoolCount: {
      label: '近6个月活跃未付费校数量',
      type: 'number',
      editable: false
    },
    month6NoVipQunCount: {
      label: '近6个月活跃未付费校群数量',
      type: 'number',
      editable: false
    },
    month6NoVipNoUserQunCount: {
      label: '近6个月活跃未付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month6VipSchoolCount: {
      label: '近6个月活跃付费校数量',
      type: 'number',
      editable: false
    },
    month6VipQunCount: {
      label: '近6个月活跃付费校群数量',
      type: 'number',
      editable: false
    },
    month6VipNoUserQunCount: {
      label: '近6个月活跃付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month12NoVipSchoolCount: {
      label: '近12个月活跃未付费校数量',
      type: 'number',
      editable: false
    },
    month12NoVipQunCount: {
      label: '近12个月活跃未付费校群数量',
      type: 'number',
      editable: false
    },
    month12NoVipNoUserQunCount: {
      label: '近12个月活跃未付费校无用户群数量',
      type: 'number',
      editable: false
    },
    month12VipSchoolCount: {
      label: '近12个月活跃付费校数量',
      type: 'number',
      editable: false
    },
    month12VipQunCount: {
      label: '近12个月活跃付费校群数量',
      type: 'number',
      editable: false
    },
    month12VipNoUserQunCount: {
      label: '近12个月活跃付费校无用户群数量',
      type: 'number',
      editable: false
    },
  }
}
