const _ = require("lodash")
async function handlingPermission(newPermissions, role, roleId) {
  await Promise.all(
    Object.keys(newPermissions || {}).reduce((acc, type) => {
      Object.keys(newPermissions[type].controllers).forEach(controller => {
        Object.keys(newPermissions[type].controllers[controller]).forEach(action => {
          const bodyAction = newPermissions[type].controllers[controller][action]
          const currentAction = _.get(
            role.permissions,
            `${type}.controllers.${controller}.${action}`,
            {}
          )

          if (!_.isEqual(bodyAction, currentAction)) {
            acc.push(
              strapi.query('permission', 'users-permissions').update(
                {
                  role: roleId,
                  type,
                  controller,
                  action: action.toLowerCase(),
                },
                bodyAction
              )
            )
          }
        })
      })

      return acc
    }, [])
  )
}

async function checkAndDeleteGroups(role, newModules, roleId) {
  try {
    // 这里的newModules 做一下两种数据类型的兼容，将可能的ObjectId数组，转为string
    newModules = newModules.map(module => module.toString())
    let diffModules = _.differenceWith(role.modules.map(module => module.toString()), newModules, _.isEqual)
    let diffGroups = await strapi.query('group', 'users-permissions').find({
      id_in: diffModules
    })

    let disableApiPermissions = []
    for (let diffGroup of diffGroups) {
      const permissions = diffGroup.apiPermissions.reduce((acc, apiPermission) => {
        if (apiPermission.enabled) {
          acc.push({
            role: roleId,
            type: apiPermission.type,
            controller: apiPermission.controller,
            action: apiPermission.action.toLowerCase()
          });
        }
        return acc;
      }, []);
      disableApiPermissions = [...disableApiPermissions, ...permissions]
    }

    // 添加错误处理和超时控制
    const updatePromises = disableApiPermissions.map(permission => {
      return new Promise(async (resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error('Update timeout'))
        }, 10000)

        try {
          const result = await strapi.query('permission', 'users-permissions').update(
            {
              role: permission.role,
              type: permission.type,
              controller: permission.controller,
              action: permission.action
            },
            {
              enabled: false
            }
          )
          clearTimeout(timeoutId)
          resolve(result)
        } catch (err) {
          clearTimeout(timeoutId)
          console.error('Permission update failed:', err)
          resolve(null) // 返回null而不是中断整个流程
        }
      })
    })

    const results = await Promise.all(updatePromises)
    return results.filter(Boolean) // 过滤掉失败的更新
  } catch (error) {
    console.error('Check and delete groups error:', error)
    throw error
  }
}

async function handlingGroup(roleId, newRoleData,role) {
  // 获取permissions表中的该角色所有权限
  const dbPermissions = await strapi.query('permission', 'users-permissions').find({
    _limit: -1,
    role: roleId
  }, []);
  const newModules = newRoleData.modules
  if (newModules && newModules.length > 0) {
    const groups = await strapi.query('group', 'users-permissions').find({
      id_in: newModules
    })

    // 删除功能操作时，去掉对应的权限
    await checkAndDeleteGroups(role, newModules, roleId);

    for (let group of groups) {
      if (!group.apiPermissions) continue
      const newEnableApiPermission = []
      const deletedApiPermissions = []
      for (let apiPermission of group.apiPermissions) {
        if (!apiPermission.enabled) continue
        const dbPermission = dbPermissions.find(e => {
          return e.role === roleId
            && e.type === apiPermission.type
            && e.controller === apiPermission.controller
            && e.action === apiPermission.action.toLowerCase()
        })
        if (!dbPermission) {
          deletedApiPermissions.push(apiPermission)
          continue
        }

        // noRequired 控制从group Restful的model处来的，目的是在不破坏项目
        // 老逻辑的情况下，修复
        if (dbPermission.enabled !== true ) {
          dbPermission.enabled = true
          newEnableApiPermission.push(dbPermission)
        }
      }
      // 更新新增接口权限
      if (newEnableApiPermission.length > 0) {
        await Promise.all(newEnableApiPermission.map(record => {
          return strapi.query('permission', 'users-permissions').update(
            {
              role: record.role,
              type: record.type,
              controller: record.controller,
              action: record.action,
            },
            {
              enabled: true
            }
          )
        }))
      }
      // 删除 Group 中已失效的 ApiPermission
      if (deletedApiPermissions.length > 0) {
        console.log('Delete Invalid ApiPermissions : ')
        for (let apiPermission of deletedApiPermissions) {
          console.log(`ApiPermission roleID:${roleId} type:${apiPermission.type} controller:${apiPermission.controller} action:${apiPermission.action}`)
        }
        try {
          await strapi.query('group', 'users-permissions').update({
            id: group.id
          }, {
            apiPermissions: group.apiPermissions.filter(e => !deletedApiPermissions.includes(e))
          })
        } catch (e) {
          console.info('Update Group ApiPermissions Error: ', e)
        }
      }
    }
  }
}


module.exports = {
    handlingPermission,
    handlingGroup
};