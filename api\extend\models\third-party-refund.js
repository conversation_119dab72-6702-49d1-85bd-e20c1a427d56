'use strict';

module.exports = {
    "collectionName": "thirdPartyRefund",
    "info": {
        "name": "ThirdPartyRefund",
        "label": "三方支付记录",
        "description": "三方支付记录"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true
    },
    "pluginOptions": {},
    "attributes": {
        "amount": {
            "label": "金额",
            "editable": false,
            "format": "rmb",
            "type": "number"
        },
        "status": {
            "label": "状态",
            "type": "string",
            "editable": false,
            "default": "发起支付",
            "options": [
                {
                    "value": "发起退款",
                    "label": "发起退款"
                },
                {
                    "value": "退款成功",
                    "label": "退款成功"
                },
                {
                    "value": "退款失败",
                    "label": "退款失败"
                },
                {
                    "value": "退款关闭",
                    "label": "退款关闭"
                },
                {
                    "value": "取消退款",
                    "label": "取消退款"
                }
            ]
        },
        "tradeNo": {
            "label": "交易商户订单号",
            "editable": false,
            "visible": false,
            "type": "string"
        },
        "refundNo": {
            "label": "退款商户订单号",
            "editable": false,
            "visible": false,
            "type": "string"
        },
        "reason": {
            "label": "原因（显示给用户）",
            "editable": false,
            "type": "string"
        },
        "merchantId": {
            "label": "商户ID",
            "editable": false,
            "visible": false,
            "type": "string"
        },
        "refundTime": {
            "label": "退款日期",
            "editable": false,
            "type": "datetime"
        },
        "callbackUrl": {
            "label": "回调执行Url",
            "visible": false,
            "type": "string"
        },
        "callbackResult": {
            "label": "回调执行结构",
            "visible": false,
            "type": "json"
        },
        "attach": {
            "label": "附加信息",
            "editable": false,
            "type": "json"
        },
        "remark": {
            "label": "备注",
            "editable": false,
            "type": "string",
        },
        "paymentRecord": {
            "label": "关联支付记录",
            "mainField": "payTime",
            "model": "third-party-payment",
            "editable": false
        }
    }
}