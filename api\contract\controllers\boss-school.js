const { CurdRouter } = require('accel-utils')
const axios = require('axios')
const { MongoClient, ObjectId } = require('mongodb')

const curdRouter = new (class extends CurdRouter {
    // ...
    async find(ctx) {
        const schools = await super.find(ctx)

        const ids = schools.map(s => s.id).join(',')
        let schoolTagList = await _getSchoolTag({ customerIds: ids });
        // const ids = schools.map(s => s.schoolId).join(',')
        // let schoolTagList = await _getSchoolTag({ schoolIds: ids });

        for (const school of schools) {
            school.tag = schoolTagList.find(t => t.customerId === school.id)?.yjTag || school.tag
            school.fxTag = schoolTagList.find(t => t.customerId === school.id)?.fxTag || school.fxTag
        }
        return schools
    }

    async findOne(ctx) {
        const school = await super.findOne(ctx)
        // let schoolTagList = await _getSchoolTag({ schoolIds: school.schoolId });
        let schoolTagList = await _getSchoolTag({ customerIds: school.id });

        school.tag = schoolTagList.find(t => t.customerId === school.id)?.yjTag || school.tag
        school.fxTag = schoolTagList.find(t => t.customerId === school.id)?.fxTag || school.fxTag
        return school
    }

    async update(ctx) {
        const { params, data } = this._parseCtx(ctx)
        let changeInfo = {}
        if (data.tag) {
            changeInfo.yjTag = data.tag
            delete data.tag
        }
        if (data.fxTag) {
            changeInfo.fxTag = data.fxTag
            delete data.fxTag
        }

        const school = await super.update(ctx)
        if (changeInfo.yjTag || changeInfo.fxTag) {
            changeInfo.customerId = data.customerId
            await axios.post(`${strapi.config.server.wlySpaceApi.url}/external/customer-services/changeSchoolTag`, changeInfo);
            school.tag = changeInfo.yjTag || school.tag
            school.fxTag = changeInfo.fxTag || school.fxTag
        }
        return school
    }
})('boss-school')


async function _getSchoolTag(data) {
    let { customerIds, schoolIds } = data;
    let params = {};
    if (customerIds) {
        params.customerIds = customerIds;
    }
    if (schoolIds) {
        params.schoolIds = schoolIds;
    }
    let schools = await axios.get(`${strapi.config.server.wlySpaceApi.url}/external/customer-services/getSchoolTag`, {
        params: params
    });

    return schools?.data || [];
}

async function updateSchoolTag(ctx) {
    let { customerId, yjTag, fxTag } = ctx.request.body;
    let changeInfo = {}
    if (yjTag) {
        changeInfo.yjTag = yjTag
    }
    if (fxTag) {
        changeInfo.fxTag = fxTag
    }

    if (changeInfo.yjTag || changeInfo.fxTag) {
        changeInfo.customerId = customerId
        await axios.post(`${strapi.config.server.wlySpaceApi.url}/external/customer-services/changeSchoolTag`, changeInfo);
    }

    return ctx.wrapper.succ({});
}

async function getSchoolTag(ctx) {
    let { ids } = ctx.request.query;
    if (!ids) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    let schoolTagList = await _getSchoolTag({ customerIds: ids });
    let schoolTagMap = {};
    if (schoolTagList && schoolTagList.length) {
        schoolTagList.forEach(e => {
            schoolTagMap[e.customerId] = e.yjTag || '';
        })
    }

    return schoolTagMap;
}

async function getSchoolAppUsage(ctx) {
    let { id } = ctx.request.query;
    let bossDbClient;
    try {
        const dataSource = await strapi.query('data-source').findOne({ name: 'boss-io', });
        bossDbClient = await MongoClient.connect(dataSource?.url);
        const bossDb = bossDbClient.db();

        let school = await strapi.query('boss-school').findOne({ id: id, }); 
        let appVersions = await bossDb.collection('@CustomerAppVersion').find({ deleted: { $ne: 1 }, }).toArray()
        let customerAppsUsage = await bossDb.collection('@CustomerAppsUsage').findOne({ customer_id: id })
        let productSettings = await bossDb.collection('@CustomerAppSettings').find({ type: 'productCategory' }).toArray()
        let appSettings = await bossDb.collection('@CustomerAppSettings').find({ type: 'appCategory' }).toArray()

        let usages = []
        for (const usage of (customerAppsUsage?.usages || [])) {
            const appVersion = appVersions.find(appVersion => appVersion.type === usage.type)

            if (!appVersion) {
                usages.push({
                    type: usage.type,
                    status: _getUsageStatus(usage),
                    beginTime: new Date(usage.begin_time),
                    endTime: new Date(usage.end_time),
                    enabled: usage.enabled,
                    isTrial: usage.is_trial,
                })
            } else {
                usages.push({
                    type: usage.type,
                    status: _getUsageStatus(usage),
                    beginTime: new Date(usage.begin_time),
                    endTime: new Date(usage.end_time),
                    enabled: usage.enabled,
                    isTrial: usage.is_trial,
                    // appVersion: {
                    //     version_name: appVersion.name,
                    //     type: appVersion.type,
                    //     app_category: appVersion.app_category,
                    //     app_category_name: appSettings.find(e => e.sign === appVersion.app_category)?.name || appVersion.app_category,
                    //     product_category: appVersion.product_category,
                    //     product_category_name: productSettings.find(e => e.sign === appVersion.product_category)?.name || appVersion.product_category,
                    // },
                    name: appVersion.name,
                    appCategoryInfo: {
                        sign: appVersion.app_category,
                        name: appSettings.find(e => e.sign === appVersion.app_category)?.name || appVersion.app_category,
                    },
                    productCategoryInfo: {
                        sign: appVersion.product_category,
                        name: productSettings.find(e => e.sign === appVersion.product_category)?.name || appVersion.product_category,
                    }
                })

            }
        }
        return ctx.wrapper.succ({ 
            id: school.id,
            name: school.name,
            type: school.type,
            schoolId: school.schoolId,
            eduSystem: school.eduSystem,
            system: school.system,
            appUsages: usages 
        });
    } catch (e) {
        console.log(e);
        console.error(e);
        throw e
    } finally {
        await bossDbClient.close();
    }
}

function _getUsageStatus(usageInfo, currentTimeTicks) {
    if (!currentTimeTicks) {
        currentTimeTicks = Date.now();
    }

    // 是否可用
    const enabled = _getUsageEnabled(usageInfo);
    if (enabled === 0) {
        return usageStatus.disabled;
    }

    const beginTime = usageInfo.beginTime || usageInfo.begin_time;
    // 没有开始时间或开始时间大于指定时间
    if (!beginTime || beginTime.getTime() > currentTimeTicks) {
        return usageStatus.unuse;
    }

    const endTime = usageInfo.endTime ? new Date(usageInfo.endTime) : new Date(usageInfo.end_time);
    return (currentTimeTicks >= endTime.getTime()) ? usageStatus.haveExpired : usageStatus.inUse
}

function _getUsageEnabled(usageInfo) {
    // !=0，默认1-可用
    if (usageInfo.enabled !== 0) {
        return 1;
    }
    return 0;
}

// 应用状态
const usageStatus = {
    unuse: 0,       // 未开始使用
    inUse: 1,           // 应用中
    haveExpired: 2,      // 已过期
    disabled: 9      // 已禁用
};
module.exports = {
    updateSchoolTag,
    getSchoolTag,
    getSchoolAppUsage,
    ...curdRouter.createHandlers(),
}
