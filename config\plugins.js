module.exports = ({ env }) => {
  const bosConfig = {
    prod: {
      AK: 'ALTAKOVxs9pURgBgK3dBp8LAhG',
      SK: '46f036aa332447f7801008cec77fc107',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'prod/boss/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    test: {
      AK: 'ALTAK3fAE78I9f5oz2IHlhVU7q',
      SK: 'a726c9dc79834a8f8e0494bf26521119',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'test/boss/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
    local: {
      AK: 'ALTAK3fAE78I9f5oz2IHlhVU7q',
      SK: 'a726c9dc79834a8f8e0494bf26521119',
      Bucket: 'ayx-wly',
      Endpoint: 'https://ayx-wly.bj.bcebos.com',
      customHost: 'https://wly-oss.yunxiao.com',
      baseDir: 'test/boss/',
      uploadPath: 'upload/',
      richTextUploadPath: 'rich-upload/',
    },
  }[env('SERVER', 'prod')]

  const userConfig = {
    prod: {
      email: '<EMAIL>',
      password: 'wlyAdmin123!@#'
    },
    prodDev: {
      email: '<EMAIL>',
      password: 'wlyAdmin123!@#'
    },
    test: {
      email: '<EMAIL>',
      password: '123456'
    },
    local: {
      email: '<EMAIL>',
      password: '123456'
    },
  }[env('SERVER', 'prod')]

  return {
    upload: {
      objectStorage: {
        target: 'bos',
        baseDir: bosConfig.baseDir,
        uploadPath: bosConfig.uploadPath,
        richTextUploadPath: bosConfig.richTextUploadPath,
        customHost: bosConfig.customHost,
        config: {
          // Bos Config
          AK: bosConfig.AK,
          SK: bosConfig.SK,
          Bucket: bosConfig.Bucket,
          Endpoint: bosConfig.Endpoint
        }
      }
    },
    usersPermissions: {
      adminUser: {
        email: userConfig.email,
        password: userConfig.password
      }
    },
    log: {
      ignoreRules: [
        ['*', /\/external\/proxy\/download/],
        ['GET', '*'],
        ['*', /\/track-event/],
        ['*', /\/system-notices\/actions\/exposure/],
        ['*', /\/external\/customer-services\/changeSchoolTag/],
        ['*', /\/external\/customer-services\/updateCustomFields/],
        ['*', /\/users-permissions\/relations/],
        ['*', /\/record-app\/upload/],
        ['*', /\/bidding-project/],
      ],
      notIgnoreRules: [
        ['GET', /\/qun\/list/],
        ['GET', /\/qun\/extra_info_list/],
        ['GET', /\/qun\/user\/info/],
      ],
     
    },
  }
}
