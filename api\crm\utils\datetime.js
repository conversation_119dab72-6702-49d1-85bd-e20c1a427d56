function changeDateToBeijingTime(date){
  const beijingTime = date.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false // 使用24小时制
  });

// 格式化输出为 YYYY-MM-DD HH:MM:SS
  const formattedTime = beijingTime
    .replace(/(\d{4})\/(\d{2})\/(\d{2})/, '$1-$2-$3') // 替换日期格式
    .replace(/\//g, '-');
  return formattedTime;
}
module.exports = {
  changeDateToBeijingTime,
}
