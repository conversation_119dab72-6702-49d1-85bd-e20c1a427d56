<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Strapi Swagger Plugin 详细安装指南，包含系统要求、安装步骤、配置说明和常见问题解决方案">
    <meta name="keywords" content="Strapi, Swagger, 安装, 配置, 插件, API文档">
    <title>安装配置 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>

    <div class="layout">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1>📚 Swagger Plugin</h1>
                <p>产品文档中心</p>
            </div>

            <div class="nav-back">
                <a href="index.html">← 返回首页</a>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">概览</div>
                    <a href="index.html" class="nav-item">
                        <i>🏠</i>产品概览
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">功能特性</div>
                    <a href="features.html" class="nav-item">
                        <i>✨</i>功能特性
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">文档指南</div>
                    <a href="installation.html" class="nav-item active">
                        <i>🛠️</i>安装配置
                    </a>
                    <a href="usage.html" class="nav-item">
                        <i>📖</i>使用指南
                    </a>
                    <a href="api-reference.html" class="nav-item">
                        <i>🔧</i>API 参考
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">支持</div>
                    <a href="troubleshooting.html" class="nav-item">
                        <i>🔍</i>故障排除
                    </a>
                    <a href="release-notes.html" class="nav-item">
                        <i>📋</i>发版记录
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">外部链接</div>
                    <a href="http://localhost:8108/api-docs" class="nav-item" target="_blank">
                        <i>🌐</i>实时文档
                    </a>
                    <a href="https://swagger.io/specification/" class="nav-item" target="_blank">
                        <i>📚</i>OpenAPI 规范
                    </a>
                </div>
            </div>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="content">

            <div class="content-body">
                <div class="toc">
                    <h3>📋 目录</h3>
                    <ul>
                        <li><a href="#requirements">系统要求</a></li>
                        <li><a href="#installation">安装步骤</a></li>
                        <li><a href="#configuration">基础配置</a></li>
                        <li><a href="#verification">安装验证</a></li>
                        <li><a href="#troubleshooting">常见问题</a></li>
                    </ul>
                </div>

                <section class="section" id="requirements">
                    <h2>📋 系统要求</h2>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>依赖项</th>
                                <th>最低版本</th>
                                <th>推荐版本</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Node.js</td>
                                <td>16.20.0</td>
                                <td>18.x+</td>
                                <td>JavaScript 运行环境</td>
                            </tr>
                            <tr>
                                <td>npm</td>
                                <td>6.0.0</td>
                                <td>8.x+</td>
                                <td>包管理工具</td>
                            </tr>
                            <tr>
                                <td>Strapi</td>
                                <td>4.0.0</td>
                                <td>4.x</td>
                                <td>主框架</td>
                            </tr>
                            <tr>
                                <td>Git</td>
                                <td>2.0+</td>
                                <td>最新版</td>
                                <td>版本控制（子模块支持）</td>
                            </tr>
                        </tbody>
                    </table>
                </section>

                <section class="section" id="installation">
                    <h2>⚡ 安装步骤</h2>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">1</div>
                            <h3>添加 Git 子模块</h3>
                        </div>
                        <p>在 Strapi 项目根目录下执行：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
git <NAME_EMAIL>:wly/strapi-swagger.git plugins/swagger
                            </pre>
                        </div>
                        <p>这将把插件克隆到 <code>plugins/swagger</code> 目录。</p>
                    </div>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">2</div>
                            <h3>初始化子模块（首次克隆项目时）</h3>
                        </div>
                        <p>如果是克隆包含子模块的项目：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
git submodule update --init --recursive
                            </pre>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">3</div>
                            <h3>安装插件依赖</h3>
                        </div>
                        <p>进入插件目录并安装依赖：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
cd plugins/swagger
npm install
cd ../..
                            </pre>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">4</div>
                            <h3>启用插件</h3>
                        </div>
                        <p>在 <code>config/middleware.js</code> 中确保插件启用：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
module.exports = {
  settings: {
    // 其他中间件配置...
    swagger: {
      enabled: true
    }
  }
};
                            </pre>
                        </div>
                    </div>
                </section>

                <section class="section" id="configuration">
                    <h2>⚙️ 基础配置</h2>

                    <h3>创建 Swagger 配置文件</h3>
                    <p>在项目根目录的 <code>config/swagger.js</code> 创建配置文件：</p>

                    <div class="example-box">
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
module.exports = {
  // Swagger 文档定义
  definition: {
    info: {
      title: '我的 API 文档',
      version: '1.0.0',
      description: '基于 Strapi 的 API 服务'
    },
    servers: [
      {
        url: 'http://localhost:3015',
        description: '开发环境'
      },
      {
        url: 'https://api.myapp.com',
        description: '生产环境'
      }
    ]
  },

  // API 模块标签映射（控制器名称 -> 中文名称）
  defaultTags: {
    'user': {
      name: '用户管理',
      description: '用户相关接口'
    },
    'product': {
      name: '产品管理',
      description: '产品相关接口'
    }
  },

  // 接口文档生成过滤配置
  ignore: {
    // 忽略的控制器列表（支持字符串精确匹配和正则表达式）
    controllers: [
      // 忽略部门相关的控制器
      "idsp-department",
      "db-cache",
      // 示例：忽略所有test开头的控制器
      // /^growth-.*/,

      // 示例：忽略特定控制器
      // 'internal-api',
      // 'debug-controller'
    ],

    // 忽略的路径列表（支持字符串精确匹配和正则表达式）
    paths: [
      // 示例：忽略所有内部接口
      // /^\/internal\/.*/,
      // 示例：忽略调试接口
      // /^\/debug\/.*/,
      // 示例：忽略特定路径
      // '/health-check',
      // '/metrics'
    ],
    // 忽略的模型列表（用于Schema生成，支持字符串精确匹配和正则表达式）
    models: [
      // 示例：忽略系统内部模型
      // 'core_store',
      // 'strapi_permission',
      // 'strapi_role',
      // "tags"

      // 示例：忽略测试模型
      // /^test-.*/,

      // 示例：忽略临时模型
      // /temp$/
    ]
  },
  // 特殊路径标签映射
  pathTags: {
    '/api/upload': {
      name: '文件上传',
      description: '文件上传相关接口'
    }
  },

  // Swagger UI 配置
  ui: {
    title: 'API 文档',
    options: {
      docExpansion: 'none',           // 默认折叠: 'list', 'full', 'none'
      defaultModelsExpandDepth: -1,   // 隐藏模型定义
      persistAuthorization: true,     // 记住认证信息
      tryItOutEnabled: true,          // 启用在线测试
      filter: true,                   // 启用搜索过滤
      tagsSorter: 'alpha',           // 标签排序方式
      operationsSorter: 'alpha'      // 接口排序方式
    }
  }
}                     
                            </pre>
                        </div>
                    </div>

                    <div class="tip-box">
                        <h4>🚀 下一步</h4>
                        <p>完成基础配置后，如需了解接口过滤、扩展模块、环境变量等高级配置选项，请参考 <a href="#usage">进阶使用</a> 页面。</p>
                    </div>
                </section>


                <section class="section" id="verification">
                    <h2>✅ 安装验证</h2>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">1</div>
                            <h3>启动开发服务器</h3>
                        </div>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
npm run dev
                            </pre>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">2</div>
                            <h3>访问文档界面</h3>
                        </div>
                        <p>在浏览器中打开：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
http://localhost:8108/api-docs
                            </pre>
                        </div>
                    </div>

                    <div class="step">
                        <div class="step-header">
                            <div class="step-number">3</div>
                            <h3>测试 JSON API</h3>
                        </div>
                        <p>验证 OpenAPI JSON 端点：</p>
                        <div class="code-block">
                            <button class="copy-button" onclick="copyCode(this)">复制</button>
                            <pre>
http://localhost:8108/api-docs/swagger.json
                            </pre>
                        </div>
                    </div>

                    <div class="tip-box">
                        <h4>✅ 安装成功标志</h4>
                        <p>如果能够正常访问文档界面并看到自动生成的接口文档，说明插件安装成功！</p>
                    </div>
                </section>

                <section class="section" id="troubleshooting">
                    <h2>🔍 常见问题</h2>

                    <div class="warning-box">
                        <h4>⚠️ 插件未启用</h4>
                        <p>确保在 <code>config/middleware.js</code> 中正确启用了 swagger 插件，并重启服务器。</p>
                    </div>

                    <div class="warning-box">
                        <h4>⚠️ 无法访问文档</h4>
                        <p>检查端口配置是否正确，确保没有其他服务占用相同端口。</p>
                    </div>

                    <div class="warning-box">
                        <h4>⚠️ 子模块无法更新</h4>
                        <p>运行 <code>git submodule update --remote</code> 来更新子模块到最新版本。</p>
                    </div>

                    <h3>获取帮助</h3>
                    <p>如果遇到问题，可以：</p>
                    <ul>
                        <li>查看服务器控制台输出的错误信息</li>
                        <li>检查浏览器开发者工具的网络请求</li>
                        <li>参考项目 README.md 文档</li>
                        <li>联系技术支持团队</li>
                    </ul>
                </section>
            </div>
        </main>
    </div>

    <script src="scripts.js"></script>
</body>
</html>
