'use strict';

const _ = require('lodash');
const { getConfigUrls, contentTypes: contentTypesUtils, createDefaultRoutes, createDefaultPermissions } = require('accel-utils');

const { createCoreApi } = require('../core-api');
const pluralize = require('pluralize')

module.exports = function(strapi) {
  // Set connections.
  strapi.connections = {};

  const defaultConnection = strapi.config.get('database.defaultConnection');

  // Set current connections.
  strapi.config.connections = strapi.config.get('database.connections', {});

  strapi.contentTypes = {};

  // Set models.
  strapi.models = Object.keys(strapi.api || []).reduce((acc, apiName) => {
    const api = strapi.api[apiName];

    for (let modelName in api.models) {
      let model = strapi.api[apiName].models[modelName];

      // mutate model
      contentTypesUtils.createContentType(model, { modelName, defaultConnection }, { apiName });

      strapi.contentTypes[model.uid] = model;

      const { service, controller } = createCoreApi({ model, api, strapi });

      _.set(strapi.api[apiName], ['services', modelName], service);
      _.set(strapi.api[apiName], ['controllers', modelName], controller);

      acc[modelName] = model;
    }
    return acc;
  }, {});

  // Set components
  Object.keys(strapi.components).forEach(componentName => {
    const component = strapi.components[componentName];
    component.connection = component.connection || defaultConnection;
  });

  // Model function generations
  const permission = strapi.config.permission
  Object.keys(strapi.api || []).forEach(apiName => {
    const api = strapi.api[apiName];
    Object.keys(api.models || {}).forEach(modelName => {
      const model = api.models[modelName];
      if (!model.options.autoGenerate) return

      const pluralName = pluralize.plural(modelName)
      const label = model.info.label
      // 1. controller -> default
      // console.log(model)
      // 2. routes
      api.config.routes = [
        ...createDefaultRoutes({
          controller: modelName,
          basePath: `/${pluralName}`,
        }),
        ...api.config.routes
      ]
      // 3. permission.pageGroups
      permission.pageGroups.push({
        name: label,
        sId: `${modelName}Group`,
        pages: [
          {
            sId: `${modelName}Management`, name: label, icon: 'table_chart',
            meta: {
              modelId: modelName,
              modelPath: pluralName,
            }
          },
        ]
      })
      // 4. permission.functions
      permission.functions.push({
        name: label,
        sId: `${modelName}Function`,
        pages: [`${modelName}Management`],
        apiPermissions: createDefaultPermissions({
          type: 'application',
          controller: modelName
        })
      })
      // 5. permission.roles -> grant authenticated
      permission.roles.push({
        type: 'authenticated',
        name: '普通用户',
        modules: [`${modelName}Function`],
      })
    })
  })

  // Set controllers.
  strapi.controllers = Object.keys(strapi.api || []).reduce((acc, key) => {
    for (let index in strapi.api[key].controllers) {
      let controller = strapi.api[key].controllers[index];
      controller.identity = controller.identity || _.upperFirst(index);
      acc[index] = controller;
    }

    return acc;
  }, {});

  // Set services.
  strapi.services = Object.keys(strapi.api || []).reduce((acc, key) => {
    for (let index in strapi.api[key].services) {
      acc[index] = strapi.api[key].services[index];
    }

    return acc;
  }, {});

  // Set routes.
  strapi.config.routes = Object.keys(strapi.api || []).reduce((acc, key) => {
    return acc.concat(_.get(strapi.api[key], 'config.routes') || {});
  }, []);

  Object.keys(strapi.plugins).forEach(pluginName => {
    let plugin = strapi.plugins[pluginName];
    Object.assign(plugin, {
      controllers: plugin.controllers || [],
      services: plugin.services || [],
      models: plugin.models || [],
    });

    Object.keys(plugin.controllers).forEach(key => {
      let controller = plugin.controllers[key];

      Object.assign(controller, {
        identity: controller.identity || key,
      });
    });

    Object.keys(plugin.models || []).forEach(modelName => {
      let model = plugin.models[modelName];

      // mutate model
      contentTypesUtils.createContentType(model, { modelName, defaultConnection }, { pluginName });

      strapi.contentTypes[model.uid] = model;
    });
  });

  // Preset config in alphabetical order.
  strapi.config.middleware.settings = Object.keys(strapi.middleware).reduce((acc, current) => {
    // Try to find the settings in the current environment, then in the main configurations.
    const currentSettings = _.merge(
      _.cloneDeep(_.get(strapi.middleware[current], ['defaults', current], {})),
      strapi.config.get(['middleware', 'settings', current], {})
    );

    acc[current] = !_.isObject(currentSettings) ? {} : currentSettings;

    // Ensure that enabled key exist by forcing to false.
    _.defaults(acc[current], { enabled: false });

    return acc;
  }, {});

  strapi.config.hook.settings = Object.keys(strapi.hook).reduce((acc, current) => {
    // Try to find the settings in the current environment, then in the main configurations.
    const currentSettings = _.merge(
      _.cloneDeep(_.get(strapi.hook[current], ['defaults', current], {})),
      strapi.config.get(['hook', 'settings', current], {})
    );

    acc[current] = !_.isObject(currentSettings) ? {} : currentSettings;

    // Ensure that enabled key exist by forcing to false.
    _.defaults(acc[current], { enabled: false });

    return acc;
  }, {});

  // default settings
  strapi.config.port = strapi.config.get('server.port') || strapi.config.port;
  strapi.config.host = strapi.config.get('server.host') || strapi.config.host;

  const { serverUrl } = getConfigUrls(strapi.config.get('server'));

  strapi.config.server = strapi.config.server || {};
  strapi.config.server.url = serverUrl;
};
