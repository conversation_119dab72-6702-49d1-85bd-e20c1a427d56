'use strict';

module.exports = {
    "collectionName": "contractTemplate",
    "info": {
        "name": "ContractTemplate",
        "label": "合同模版",
        "description": "合同模版"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true,
        indexes: [
            { keys: { name: 1 }, options: { unique: true } },
        ],
    },
    "pluginOptions": {},
    "attributes": {
        "name": {
            "label": "合同模版名称",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "100",
                    "label": "好分数使用协议"
                },
                {
                    "value": "101",
                    "label": "经销商合同"
                },
                {
                    "value": "102",
                    "label": "区县购买合同"
                },
                {
                    "value": "103",
                    "label": "考试服务协议"
                },
                {
                    "value": "104",
                    "label": "SaaS平台合作协议"
                },
                // {
                //     "value": "105",
                //     "label": "错题本合同"
                // },
                {
                    "value": "106",
                    "label": "保密协议"
                },
                {
                    "value": "107",
                    "label": "战略合作框架协议"
                },
                {
                    "value": "108",
                    "label": "设备采购合同"
                },
                {
                    "value": "109",
                    "label": "主体变更三方协议"
                },
                // {
                //     "value": "110",
                //     "label": "终止协议"
                // },
                // {
                //     "value": "111",
                //     "label": "区县授权合同"
                // },
                // {
                //     "value": "112",
                //     "label": "好分数代理合同"
                // },
                // {
                //     "value": "113",
                //     "label": "好分数产品代理合同"
                // },
                // {
                //     "value": "114",
                //     "label": "SaaS平台服务协议"
                // },
                {
                    "value": "115",
                    "label": "推广运营合同"
                },
                {
                    "value": "116",
                    "label": "好分数产品独家经销合同"
                },
                {
                    "value": "117",
                    "label": "好分数产品经销合同"
                },
                {
                    "value": "118",
                    "label": "错题本单品经销合同"
                },
                {
                    "value": "119",
                    "label": "360会员单品经销合同"
                },
                // {
                //     "value": "120",
                //     "label": "学情套餐"
                // },
                {
                    "value": "200",
                    "label": "产品采购协议"
                },
                {
                    "value": "201",
                    "label": "项目合作协议"
                },
                // {
                //     "value": "202",
                //     "label": "框架战略合作协议"
                // },
                // {
                //     "value": "203",
                //     "label": "授权经销商协议"
                // },
                // {
                //     "value": "204",
                //     "label": "采购合同"
                // },
                {
                    "value": "11012",
                    "label": "经营权膨胀金合同"
                },
                {
                    "value": "11013",
                    "label": "精准练联合运营"
                },
                {
                    "value": "11014",
                    "label": "渠道合作协议v2024"
                },
                {
                    "value": "999",
                    "label": "其他"
                }
            ]
        },
        "attachment": {
            "label": "模版文件",
            "model": "file",
            "via": "related",
            "allowedTypes": [
                "files"
            ],
            "plugin": "upload",
            "pluginOptions": {},
        },
    }
}
