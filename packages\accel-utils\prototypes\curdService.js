const CurdModel = require('./curdModel')
const { propOr } = require('lodash/fp')
const _ = require('lodash')
const { getNonWritableAttributes } = require('../lib/content-types')

const {
  hasDraftAndPublish,
  constants: { PUBLISHED_AT_ATTRIBUTE },
} = require('../lib').contentTypes

const setPublishedAt = data => {
  data[PUBLISHED_AT_ATTRIBUTE] = propOr(new Date(), PUBLISHED_AT_ATTRIBUTE,
    data)
}

const DP_PUB_STATE_LIVE = 'live'
const getLimitParam = params => {
  const { defaultLimit, maxLimit } = {
    defaultLimit: 100,
    maxLimit: null,
  };
  if (params._limit === undefined) {
    return defaultLimit;
  }
  const limit = _.toNumber(params._limit);
  // if there is max limit set and params._limit exceeds this number, return configured max limit
  if (maxLimit && (limit === -1 || limit > maxLimit)) {
    return maxLimit;
  }
  return limit;
};
const getFetchParams = (params = {}) => {
  return {
    _publicationState: DP_PUB_STATE_LIVE,
    ...params,
    _limit: getLimitParam(params),
  };
};

class CurdService extends CurdModel {
  constructor (name, config = {}) {
    super(name, config)
  }

  // make sure to keep the call to getNonWritableAttributes dynamic
  sanitizeInput (data) {
    return _.omit(data, getNonWritableAttributes(this._getModel()))
  }

  /**
   * Promise to fetch all records
   * @return {Promise}
   */
  find (params, populate) {
    return strapi.entityService.find(
      { params: getFetchParams(params), populate },
      { model: this.modelName },
    )
  }

  /**
   * Promise to fetch record
   * @return {Promise}
   */
  findOne (params, populate) {
    return strapi.entityService.findOne(
      { params: getFetchParams(params), populate },
      { model: this.modelName },
    )
  }

  /**
   * Promise to count record
   * @return {Promise}
   */
  count (params) {
    return strapi.entityService.count({ params: getFetchParams(params) },
      { model: this.modelName })
  }

  /**
   * Promise to add record
   * @return {Promise}
   */
  create (data, { files } = {}) {
    const sanitizedData = this.sanitizeInput(data)
    if (hasDraftAndPublish(this._getModel())) {
      setPublishedAt(sanitizedData)
    }
    return strapi.entityService.create({ data: sanitizedData, files },
      { model: this.modelName })
  }

  /**
   * Promise to edit record
   * @return {Promise}
   */
  update (params, data, { files } = {}) {
    const sanitizedData = this.sanitizeInput(data)
    return strapi.entityService.update(
      { params, data: sanitizedData, files },
      { model: this.modelName },
    )
  }

  /**
   * Promise to delete a record
   * @return {Promise}
   */
  delete (params) {
    return strapi.entityService.delete({ params }, { model: this.modelName })
  }

  /**
   * Promise to search records
   * @return {Promise}
   */
  search (params) {
    return strapi.entityService.search({ params: getFetchParams(params) },
      { model: this.modelName })
  }

  /**
   * Promise to count searched records
   * @return {Promise}
   */
  countSearch (params) {
    return strapi.entityService.countSearch(
      { params: getFetchParams(params) },
      { model: this.modelName },
    )
  }

  getBindMethods () {
    const methods = {}
    Object.getOwnPropertyNames(this.constructor.prototype).forEach(key => {
      if (
        key !== 'constructor'
        && !/^_/.test(key)
        && this[key].bind) {
        methods[key] = this[key].bind(this)
      }
    })
    return methods
  }
}

module.exports = CurdService
