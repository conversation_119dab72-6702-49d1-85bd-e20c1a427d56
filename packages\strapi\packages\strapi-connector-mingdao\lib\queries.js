function queries ({ model, strapi }) {
  void strapi // unused

  async function find (params) {
    return await model.find(params)
  }

  async function search (params) {
    return await model.search(params)
  }

  async function count (params) {
    return await model.count(params)
  }

  async function countSearch (params) {
    return await model.countSearch(params)
  }

  async function create (values) {
    return await model.create(values)
  }

  async function update (params, values) {
    return await model.update(params, values)
  }

  async function updateMany (params, values) {
    return await model.updateMany(params, values)
  }

  async function findOne (params) {
    return await model.findOne(params)
  }

  async function deleteMany (params) {
    return await model.deleteMany(params)
  }

  return {
    find,
    search,
    count,
    countSearch,
    create,
    update,
    updateMany,
    findOne,
    delete: deleteMany
  }
}

module.exports = queries
