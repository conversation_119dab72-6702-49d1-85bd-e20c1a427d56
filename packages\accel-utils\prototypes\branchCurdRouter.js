const CurdRouter = require('./curdRouter')
const { parseCtxUserAndBranch, dataFieldProcess } = require('../collection-utils')
const _ = require('lodash')

class BranchCurdRouter extends CurdRouter {
  constructor (name, config = {}) {
    super(name, config)
  }

  _parseBranchCtx (ctx) {
    const { model, params, query, data, files, populate, user, branchId } = parseCtxUserAndBranch(ctx, this.modelName)
    if (!query?._sort && this.defaultSort) {
      query._sort = this.defaultSort
    }
    return {
      model, params, query, data, files, populate,
      user, branchId
    }
  }

  async branchFind (ctx) {
    const { query } = this._parseBranchCtx(ctx)
    ctx.query = query
    return super.find(ctx)
  }

  async branchCount (ctx) {
    const { query } = this._parseBranchCtx(ctx)
    ctx.query = query
    return super.count(ctx)
  }

  async branchFindOne (ctx) {
    const { params } = this._parseBranchCtx(ctx)
    ctx.params = params
    return super.findOne(ctx)
  }

  async branchCreate (ctx) {
    const { branchId, data, files, model } = this._parseBranchCtx(ctx)
    const user = ctx.state.user
    const strapiModel = this._getModel(ctx)
    if (branchId) {
      data.pBranch = branchId
      // 针对多租户数据
      if (strapiModel.attributes.pBranches) {
        data.pBranches = [branchId]
      }
    }
    if (strapiModel.attributes.user && user) {
      data.user = user.id
    }
    try {
      await dataFieldProcess(data, model)
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    let entity = await this._getService(ctx).create({ data, files })
    return this._sanitizeEntity(ctx, entity)
  }

  async branchUpdate (ctx) {
    const { query, data, files, model } = parseCtxUserAndBranch(ctx, this.modelName)
    const safeParams = query
    if (!safeParams.id) {
      return ctx.badRequest('Invalid target id.')
    }
    try {
      await dataFieldProcess(data, model)
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const entity = await this._getService(ctx).update({
      params: safeParams,
      data, files
    })
    return this._sanitizeEntity(ctx, entity)
  }

  async branchUpdateMany (ctx) {
    const { query } = parseCtxUserAndBranch(ctx, this.modelName)
    const safeParams = query
    const body = ctx.request.body
    if (!_.isObject(body.filter) || !_.isObject(body.data)) {
      return ctx.badRequest('filter is invalid')
    }
    body.filter = {
      ...body.filter,
      ...safeParams
    }
    return super.updateMany(ctx)
  }

  async branchDelete (ctx) {
    const { query } = this._parseBranchCtx(ctx)
    const safeParams = query
    if (!safeParams.id) {
      return ctx.badRequest('Invalid target id.')
    }
    const entity = await this._getService(ctx).delete({ params: safeParams })
    return this._sanitizeEntity(ctx, entity)
  }

  async branchDeleteMany (ctx) {
    const { data, query } = this._parseBranchCtx(ctx)
    if (!_.isArray(data.filter.id_in)) {
      return ctx.badRequest('filter.id_in is invalid')
    }
    const entity = await this._getService(ctx).deleteMany(
      {
        params: {
          ...data.filter,
          ...query,
        }
      }
    )
    return this._sanitizeEntity(ctx, entity)
  }

  async branchExport (ctx) {
    const { query } = this._parseBranchCtx(ctx)
    ctx.query = query
    return super.export(ctx)
  }

  async branchImport (ctx) {
    const { branchId, user } = this._parseBranchCtx(ctx)
    ctx._constFields = {
      pBranch: branchId,
      user: user.id
    }
    return super.import(ctx)
  }

  async branchSelfFind (ctx) {
    const { query, user } = this._parseBranchCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.find(ctx)
  }

  async branchSelfCount (ctx) {
    const { query, user } = this._parseBranchCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.count(ctx)
  }

  async branchSelfFindOne (ctx) {
    const { params, user } = this._parseBranchCtx(ctx)
    ctx.params = {
      ...params,
      user: user.id
    }
    return super.findOne(ctx)
  }

  async branchSelfExport (ctx) {
    const { query, user } = this._parseBranchCtx(ctx)
    ctx.query = {
      ...query,
      user: user.id
    }
    return super.export(ctx)
  }

  async branchSelfCreate (ctx) {
    return this.branchCreate(ctx)
  }

  async branchSelfUpdate (ctx) {
    return this.branchUpdate(ctx)
  }

  async branchSelfUpdateMany (ctx) {
    return this.branchUpdateMany(ctx)
  }

  async branchSelfDelete (ctx) {
    return this.branchDelete(ctx)
  }

  async branchSelfDeleteMany (ctx) {
    return this.branchDelete(ctx)
  }

  async branchSelfImport (ctx) {
    return this.branchImport(ctx)
  }

  createHandlers (names) {
    if (names) return super.createHandlers(names)
    const defaultNames = [
      'branchFind', 'branchCount', 'branchFindOne',
      'branchCreate', 'branchUpdate', 'branchUpdateMany', 'branchDelete',
      'branchExport', 'branchImport', 'branchDeleteMany',
      'branchSelfFind', 'branchSelfCount', 'branchSelfFindOne',
      'branchSelfCreate', 'branchSelfUpdate', 'branchSelfUpdateMany', 'branchSelfDelete',
      'branchSelfExport', 'branchSelfImport', 'branchSelfDeleteMany',
    ]
    const extendNames = this._getExtendMethodNames()
    return super.createHandlers([
      ...defaultNames,
      ...extendNames,
    ])
  }
}

module.exports = BranchCurdRouter
