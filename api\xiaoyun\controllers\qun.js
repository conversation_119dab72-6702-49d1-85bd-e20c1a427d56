const { getToken, getQxUserInfo } = require('../utils/goToken')
const userRole = require('../utils/userRole')
const _ = require('lodash')
const axios = require('axios')
const { getAllQunList } = require('../utils/qunListUtil')
const { me } = require('strapi-plugin-users-permissions/controllers/User')

async function userInfo(ctx) {
  const { as } = ctx.request.query
  if (as) {
    let user = await strapi.query('user', 'users-permissions').findOne({ customId: as })
    if (!user) {
      return ctx.wrapper.error('AUTH_ERROR', '用户不存在')
    }
    ctx.state.user = user
  }
  let user = ctx.state.user
  if (!user) {
    return ctx.wrapper.error('AUTH_ERROR', '用户不存在')
  }
  let res = await axios.get(`http://ids.yunxiao.io/user/info?id=${user.customId}&__go_token=${await getToken('ids')}`)
  let data = await me(ctx)
  return {
    ...data,
    xiaoyunId: res.data[0] || null
  }
}

let allQunList = null

async function qunList(ctx) {
  let customers = await strapi.query('customer-service-mid').model.find({ qun: { $ne: null } }, ['qun', 'schoolId', 'name', 'qunId', 'directServiceManager', 'directSalesManager'])
  allQunList = await getAllQunList(customers)
  return allQunList
}

async function userQunList(ctx) {
  let user = ctx.state.user

  if (userRole.isAdmin(user) || userRole.isServiceWorker(user) || userRole.isSalesManager(user) || userRole.isSalesObserver(user)) {
    return {
      role: 'admin',
      list: '*'
    }
  } else if (userRole.isLeader(user) || userRole.isMember(user)) {
    const queryField = userRole.isService(user) ? 'directServiceManager' : 'directSalesManager'
    const quns = await strapi.query('customer-service-mid').find({
      [queryField]: user.id,
      qun: { $ne: null },
      _limit: 5000
    }, ['qun'])
    return {
      role: userRole.isLeader(user) ? 'leader' : 'member',
      list: quns.map(item => item.qun.qunId)
    }
  } else if (userRole.isAgentAdmin(user)) {
    const quns = await strapi.query('xiaoyun-qun').find({
      qunGroup: 2
    }, [])
    return {
      role: 'member',
      list: quns.map(item => item.qunId)
    }
  } else {
    return {
      role: null,
      list: []
    }
  }
}

async function qrCode(ctx) {
  // 从path 中取出id
  const qid = ctx.params.id
  if (!qid) {
    return ctx.wrapper.error('HANDLE_ERROR', '参数错误')
  }
  let data = null

  // 读取数据库缓存
  let qunInfo = await strapi.query('qun-qrcode').findOne({ qunId: qid })
  if (qunInfo) {
    let expireTime = qunInfo.expireTime
    if (expireTime && new Date(expireTime).getTime() > Date.now()) {
      data = qunInfo.qrCode
    }
  }

  let token = await getToken('ask')
  if (!data && token) {
    try {

      //
      const qunInfo = await strapi.query('xiaoyun-qun').findOne({ qunId: qid })
      if (!qunInfo) {
        return ctx.wrapper.error('HANDLE_ERROR', '群不存在')
      }

      // const schoolCreatorMap = {
      //   '1688855828587333': '客户服务助手',
      //   '1688854305554904': '好分数服务总监',
      //   '1688857577566284': '客户服务🛎︎',
      // }
      console.log('生成新二维码', qid, qunInfo.qunCreatorId, qunInfo.name)

      if (!['1688855828587333', '1688854305554904'].includes(qunInfo.qunCreatorId)) {
        return ctx.wrapper.error('HANDLE_ERROR', '获取二维码错误')
      }

      const res = await axios.get(`http://ask.yunxiao.io/qun/qr_code?qid=${qid}&__go_token=${token}`)
      data = res.data[0]
      if (qunInfo) {
        await strapi.query('qun-qrcode').update({ qunId: qid }, { qrCode: data, expireTime: res.data[1] })
      } else {
        await strapi.query('qun-qrcode').create({ qunId: qid, qrCode: data, expireTime: res.data[1] })
      }
    } catch (error) {
      console.log(error.message || error)
    }
  }

  if (data) {
    const imageBuffer = Buffer.from(data, 'base64')
    // 设置响应头并发送图片数据流
    ctx.set('Content-Type', 'image/png')
    return ctx.body = imageBuffer
  } else if (qunInfo && qunInfo.customQrCode) {
    const response = await axios.get(qunInfo.customQrCode.url, { responseType: 'stream' })
    ctx.set('Content-Type', response.headers['content-type'])
    return ctx.body = response.data
  } else if (qunInfo && qunInfo.qrCode) {
    // 非必要，返回过期图片。
    const imageBuffer = Buffer.from(qunInfo.qrCode, 'base64')
    // 设置响应头并发送图片数据流
    ctx.set('Content-Type', 'image/png')
    return ctx.body = imageBuffer
  }
  return ctx.wrapper.error('HANDLE_ERROR', '获取二维码失败')
}

async function qunInfo(ctx) {
  const { qid, force = false } = ctx.request.query
  if (!qid) {
    return ctx.wrapper.error('HANDLE_ERROR', '参数错误')
  }

  return await getQunInfo(qid, force)
}

async function getQunInfo(qid, force) {
  let token = await getToken('ids')
  let askToken = await getToken('ask')
  const isForce = force === true || force === 'true'
  const resInfo = await axios.get(`http://ask.yunxiao.io/qun/info?qid=${qid}${isForce ? '&force' : ''}&__go_token=${askToken}`)
  let result = resInfo.data
  const resNewInfo = await axios.get(`http://ask.yunxiao.io/qun/get?qid=${qid}${isForce ? '&force' : ''}&__go_token=${askToken}`)
  if (result && result.length > 1 && resNewInfo.data) {
    if (resNewInfo.data.name !== result[1]) {
      result[1] = resNewInfo.data.name || result[1]
    }
    if (result[2][0] !== resNewInfo.data.owner) {
      const userInfoRes = await axios.get(`http://ids.yunxiao.io/user/info?id=${resNewInfo.data.owner}&__go_token=${token}`)
      result[2] = userInfoRes.data || result[2]
    }
    result[5] = Object.keys(resNewInfo.data.members).length || result[5]
    if (resNewInfo.data.admins && resNewInfo.data.admins.length) {
      let internal = result[7].internal || []
      for (const admin of resNewInfo.data.admins) {
        let user = internal.find(item => item[0] === admin)
        if (user) {
          user[3] = true
        }
      }
    }

    if (isForce){
      await strapi.query('xiaoyun-qun').update({ qunId: qid }, {
        forceTime: new Date(),
      })
    }

  }
  return result
}

async function getQunTags(ctx) {
  const { qid, force = false } = ctx.request.query
  if (!qid) {
    return ctx.wrapper.error('HANDLE_ERROR', '参数错误')
  }
  let qunInfo = await strapi.query('xiaoyun-qun').findOne({ qunId: qid }, ['qunTags'])

  if (!qunInfo) {
    return ctx.wrapper.error('NULL_ERROR', '未查询到群信息')
  }
  return {
    qid,
    qunGroup: qunInfo.qunGroup || 0,
    qunTags: (qunInfo.qunTags || []).map(e => e.name),
  }
}

async function getAllQunTags(ctx) {
  let tags = await strapi.query('qun-tag').find({})
  const tagMap = {}
  for (const tag of tags) {
    tagMap[tag.id] = tag.name
  }
  let qunList = await strapi.query('xiaoyun-qun').model.find({ isDeleted: { $ne: true } }, ['qunId', 'qunTags', 'qunGroup'])

  return qunList.map(e => {
    return {
      qid: e.qunId,
      qunGroup: e.qunGroup || 0,
      qunTags: (e.qunTags || []).map(e => tagMap[e] || e),
    }
  })

}

async function getCustomerByQid(ctx) {
  const { qid } = ctx.request.query

  if (!qid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let qunInfo = await strapi.query('xiaoyun-qun').findOne({ qunId: qid }, [])
  if (qunInfo) {
    const customer = await strapi.query('customer-service-mid').findOne({ qun: qunInfo._id }, ['directServiceManager', 'directSalesManager'])

    if (customer) {
      return ctx.wrapper.succ(customer)
    }
  }

  return ctx.wrapper.error('NULL_ERROR', '未匹配到学校')
}

async function getWxUsersByRole(ctx) {
  const { role } = ctx.request.query

  let query = {}

  if (role === 1 || role === '1') {
    query = {
      role: '内部人员',
      userId: { $not: /.*16888.*/i }
    }
  } else if (role) {
    query = {
      role: { $in: role.split(',') },
      userId: { $not: /.*16888.*/i }
    }
  } else {
    query = {
      role: { $in: ['内部人员'] },
      userId: { $not: /.*16888.*/i }
    }
  }
  let users = await strapi.query('qun-user').model.find(query)
  return users.map(e => e.userId)
}

async function getQunExtraInfo(ctx) {
  const { qid } = ctx.request.query

  if (!qid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let qunInfo = await strapi.query('xiaoyun-qun').model.findOne({ qunId: qid })
  if (qunInfo) {
    let users = await strapi.query('user', 'users-permissions').model.find({ provider: 'yxWeCom' }).select({
      id: 1,
      customId: 1,
    })
    const userMap = {}
    for (const user of users) {
      userMap[user.id] = user.customId
    }

    let data = {
      qid,
      qunGroup: qunInfo.qunGroup || 0
    }
    if (qunInfo.qunTags && qunInfo.qunTags.length){
      data.qunTags = qunInfo.qunTags.map(e => e.name)
    }
    if (+qunInfo.schoolId){
      data.schoolId = +qunInfo.schoolId
    }
    if (qunInfo.serviceManager) {
      data.serviceManager = userMap[qunInfo.serviceManager] || qunInfo.serviceManager
    }
    if (qunInfo.salesManager) {
      data.salesManager = userMap[qunInfo.salesManager] || qunInfo.salesManager
    }
    return data
  }

  return ctx.wrapper.error('NULL_ERROR', '未匹配到群信息')
}

async function getQunExtraInfoList(ctx) {
  let users = await strapi.query('user', 'users-permissions').model.find({ provider: 'yxWeCom' }).select({
    id: 1,
    customId: 1,
  })
  const userMap = {}
  for (const user of users) {
    userMap[user.id] = user.customId
  }
  let qunList = await strapi.query('xiaoyun-qun').model.find({ isDeleted: { $ne: true } }).select({
    qunId: 1,
    qunGroup: 1,
    qunTags: 1,
    schoolId: 1,
    serviceManager: 1,
    salesManager: 1,
  })
  let qunTagList = await strapi.query('qun-tag').model.find({ isDeleted: { $ne: true } }).select({
    id: 1,
    name: 1
  })
  let qunTagMap = {}
  for (const tag of qunTagList) {
    qunTagMap[tag.id] = tag.name
  }
  return qunList.map(e => {
    let data = {
      qid: e.qunId,
      qunGroup: e.qunGroup || 0,
    }
    if (e.qunTags && e.qunTags.length) {
      data.qunTags = e.qunTags.map(e => qunTagMap[e] || e)
    }

    if (+e.schoolId) {
      data.schoolId = +e.schoolId
    }
    if (e.serviceManager) {
      data.serviceManager = userMap[e.serviceManager] || e.serviceManager
    }
    if (e.salesManager) {
      data.salesManager = userMap[e.salesManager] || e.salesManager
    }
    return data
  })
}

function getAvatar100(url) {
  if (url && url.indexOf('wework.qpic.cn') !== -1
    && url.endsWith('/0')){
    // 最后两位换成/100
    return url.substring(0, url.length - 2) + '/100'
  }

  return url
}

async function getQxUserAvatar (ctx) {
  const { id } = ctx.params
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let qxInfo = await getQxUserInfo(id)
  if (!qxInfo || !qxInfo.avatar) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户头像获取失败')
  }

  const response = await axios.get(qxInfo.avatar, { responseType: 'stream' })
  ctx.set('Content-Type', response.headers['content-type'])
  return ctx.body = response.data
}

async function getQxUserAvatarThumb(ctx) {
  const { id } = ctx.params
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let qxInfo = await getQxUserInfo(id)
  if (!qxInfo || !qxInfo.avatar) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '用户头像获取失败')
  }

  let avatar = getAvatar100(qxInfo.avatar)

  const response = await axios.get(avatar, { responseType: 'stream' })
  ctx.set('Content-Type', response.headers['content-type'])
  return ctx.body = response.data
}

module.exports = {
  userInfo,
  userQunList,
  qunInfo,
  getCustomerByQid,
  getQunInfo,
  // public for xiaoyun
  qunList,
  qrCode,
  getQunTags,
  getAllQunTags,
  getWxUsersByRole,
  getQunExtraInfo,
  getQunExtraInfoList,
  getQxUserAvatar,
  getQxUserAvatarThumb
}
