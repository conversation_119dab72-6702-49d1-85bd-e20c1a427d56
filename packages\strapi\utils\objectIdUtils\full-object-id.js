// 规则是接收一个字符串，然后返回一个objectId，如果接收的是一个数字，转为字符串，并用前置的0补齐24位
const {isNull,isUndefined} = require("lodash")

const {PeterObjectId} = require("./peter-objectid");

var checkForHexRegExp = new RegExp("^[0-9a-fA-F]+$");
const fullObjectId = (id) => {
  // 如果id为空，不存在 ，或者空字符串，则直接返回
  if(isNull(id) || isUndefined(id) || id === ''){
    throw new Error('id不能为空');
  }
  if(typeof id === 'number'){
    id = id.toString();
  }

  if(id.length <= 24 && checkForHexRegExp.test(id)){
    // 如果长度小于24，则需要补齐
    id = id.padStart(24, '0');
    return new PeterObjectId(id);
  }

  id  = hashStringToObjectId(id)

  return new PeterObjectId(id);
}


// 将一个字符串，每一个字符都要参与，映射为唯一的一个24位的16进制字符串
const hashStringToObjectId = (str) => {
  // 如果str为空，不存在 ，或者空字符串，则直接返回
  if(isNull(str) || isUndefined(str) || str === ''){
    throw new Error('字符串不能为空');
  }

  // 使用简单的字符串hash算法
  let hash = 0;
  for(let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }

  // 转换为正数并补齐24位16进制字符串
  const hexStr = Math.abs(hash).toString(16).padStart(24, '0');

  // 确保只返回24位
  return hexStr.substring(0, 24);

}


module.exports = {
  fullObjectId
}

