---
description: 生成简洁的 Git 提交信息
allowed-tools: ["Bash", "Read"]
---

根据代码变更生成简洁的 Git 提交信息。

使用方式：
- `git-commit` - 默认方式：暂存区有变更时基于暂存区，否则基于所有变更
- `git-commit staged` - 仅基于暂存区的变更生成提交信息
- `git-commit all` - 基于所有变更（包括未暂存）生成提交信息

1. 检查暂存区状态并分析修改内容
{{#if (not args)}}
!git diff --cached --stat > /tmp/staged.txt && if [ -s /tmp/staged.txt ]; then echo "基于暂存区变更生成提交信息：" && cat /tmp/staged.txt; else echo "暂存区无变更，基于所有变更生成提交信息：" && git diff --stat; fi && rm -f /tmp/staged.txt
{{else if (eq args "staged")}}
!git diff --cached --stat
{{else if (eq args "all")}}
!git diff --stat
{{else}}
错误：无效的参数 '{{args}}'。请使用 'staged' 或 'all'，或不带参数。
{{/if}}

2. 根据以下规则生成提交信息：
   - 使用中文
   - 格式：<类型>(模块): <简短描述>
   - 类型包括：
     * feat: 新功能
     * fix: 修复问题
     * docs: 文档更新
     * style: 代码格式调整
     * refactor: 代码重构
     * test: 测试相关
     * chore: 构建或辅助工具变动
   - 模块：主要涉及的模块，不超过3个，用逗号分隔
   - 描述：简洁明了，不超过 50 个字符
   - 如有必要，可以添加简短的说明（空一行后）

3. 示例格式：
   ```
   feat(auth,user): 添加用户认证功能
   
   - 实现登录/注册接口
   - 添加 JWT 验证中间件
   ```

   ```
   fix(document,test): 修复文档服务测试失败问题
   
   - 修正 mock 初始化顺序
   - 更新测试断言
   ```

   ```
   chore(build,config): 更新构建配置和依赖
   ```

请分析暂存区的变更，识别涉及的主要模块，生成合适的提交信息。