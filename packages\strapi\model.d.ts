// global.d.ts
export {}
declare global {
  // 业务模型
  interface Model {
    /**
     * 模型全局 ID
     * @description 具有前缀的完整ID，可用于 findRelation 等功能
     * @example plugins::users-permissions.user - 可用于 findRelation
     */
    uid: string,
    /**
     * 模型 ID
     * @example user
     * @description 即模型名称，可能会重复的模型ID，例如不同插件中存在同命的模型名称
     */
    apiID: string,
    /**
     * 是否在内容管理器中展示
     */
    isDisplayed: boolean,
    /**
     * 模型类型 - 集合或单例
     */
    kind: 'collectionType' | 'singleType',
    /**
     * 模型信息 用来存储模型的展示型的信息字段
     */
    info: {
      // 模型名称
      name: string,
      // 模型描述
      description: string,
      // 模型展示名称
      label: string,
      // 模型展示图标 Component Model - 可使用 MaterialDesign 的图标
      icon?: string,
      // 模型版本
      version?: string,
    },
    /**
     * 模型基础配置信息 如自增、时间戳、发布控制等
     */
    options: {
      // 是否使用自增ID
      increments?: boolean,
      // 是否启用时间戳 @default false
      // 设置 ture 等同于 ["createdAt", "updatedAt"]
      timestamps?: boolean | [string, string],
      // 是否启用草稿系统 @default false
      draftAndPublish?: false,
      // 设置一组私有属性
      privateAttributes?: string[],
      // 配置API响应是否包含 created_by 和 updated_by 字段 @default false
      populateCreatorFields?: boolean,
      // 配置主字段 - 用于关联选择、默认排序等
      defaultMainField?: string,
      // 条件子模式 - 暂时实现根据一个字段的值做字段隐藏控制
      allOf?: {
        if: {
          properties: {
            [key: string]: {
              const: any,
            }
          }
        },
        then: {
          properties: {
            [key: string]: {
              visible: boolean,
            }
          }
        },
        else: {
          properties: {
            [key: string]: {
              visible: boolean,
            }
          }
        }
      }[],
      // 字段分组
      groups: {
        // 分组名称 - 无分组名称则只用来控制位置
        title?: string,
        // 分组字段列表
        fields: (string | Partial<FormField & {
          innerHTML?: string,
          size?: number,
          lineBreak?: 'after' | 'before' | 'all'
        }>)[],
        // 分组默认展开
        defaultCollapsed?: boolean,
        // 分组聚合选项卡
        tabPanel: string
      }[]
    },
    /**
     * 模型属性字段 最重要的部分
     */
    attributes: {
      [name: string]: ModelAttribute
    },
    /**
     * 模型的分类 - 针对组件模型
     * @example 'block.audio'
     */
    category?: string,
    /**
     * 关联模型列表
     */
    associations: {
      alias: string,
      type: string,
      targetUid: string,
      associationModel: Model
    }[],
    // 模型外部数据 - 未标准化 仅用于系统信息对照查看
    modelExt?: {
      appName: string,
      views: object[],
      rules: object[]
    },
    // 判断模型是否为关联关系模式加载
    isAssociation?: boolean
  }

  // 模型字段基础信息
  interface ModelAttributeBase {
    /**
     * 标签 - 可配置属性的中文名称
     */
    label?: string,
    /**
     * 默认值
     */
    default?: string,
    // 数据校验
    /**
     * 为 true 的情况下会增加一个校验器来检查此属性
     * @default false
     */
    required?: boolean,
    /**
     * 字段是否允许重复
     */
    unique?: boolean,
    // 安全配置
    /**
     * 是否可在 Strapi Content-type Builder plugin 中配置调整
     * @default true
     */
    configurable?: boolean,
    /**
     * 为 true 的情况下，通过 service 的查询会移除此属性
     * @default false
     */
    private?: boolean,
    /**
     * 为 false 的情况下，默认的 REST API 会不在查询此字段关联数据
     * @default true
     */
    autoPopulate?: boolean,
    // 表单默认配置
    /**
     * 是否可编辑
     * @default true
     */
    editable?: boolean,
    /**
     * 表单默认 placeholder
     */
    placeholder?: string,
    description?: string,
    /**
     * 表单默认可见性
     * @default true
     */
    visible?: boolean,
    visibleOnCreate?: boolean,
    // 其他配置
    /**
     * 是否自动创建单字段索引 - 仅在 MongoDB 数据库支持
     * @default false
     */
    index?: boolean,
    /** 枚举选项配置 - 用于提供预制的选项组 */
    options?: ModelAttributeOption[] | string
    /**
     * 数据格式
     * @example { type: 'string', format: 'color'}  即代表当前字段是颜色字符串 "#FF00FF"
     */
    format?: 'color' | 'url' | 'secret' | string
    // 旧版本颜色字符串为 { type: 'string', color: true }
    /**
     * 组件扩展字段
     */
    meta?: {
      query?: Record<string, string | number>
    },
    /**
     * 可选数据来源接口
     */
    apiSearch?: string,
    content?: object,
    /**
     * 列表与编辑模式配置
     * 若存在则在对应场景相对外部字段有更高的优先级
     */
    list?: {
      // 列表模式配置
      label: string,              // 展示名称
      visible?: boolean,          // 是否可见
      searchable?: boolean,       // 是否可搜索
      sortable?: boolean,         // 是否可排序
      mainField?: string,         // 主字段
    },
    edit?: {
      // 创建/编辑模式配置
      label?: string,             // 展示名称
      visible?: boolean,          // 是否可见
      visibleOnCreate?: boolean,  // 新增是否可见
      description?: string,       // 字段描述
      placeholder?: string,       // 表单占位内容
      editable?: boolean,         // 是否可编辑
      mainField?: string          // 主字段
    },
    /**
     * 控件配置
     */
    controlType?: number,           // 控件类型
    controlStyle?: {                // 控件样式
      // 名称颜色 eg. '#ff0000ff'
      // 格式 RGBA
      titleColor?: string,
      // 名称样式 eg. '0110'
      // 按位标识 第一位:加粗? 第二位:斜体 第三位:下划线 第四位:删除线
      titleStyle?: string,
      // 名称字号 eg. '1'
      // '1':sm  '2':md  '3':lg  '4':xl
      titleSize?: '1' | '2' | '3' | '4',
    },
    size?: number,  // 尺寸 - 基于12栅格的尺寸 eg. 1 3 6 12
    row?: number,   // 位置 - 行 eg. 0
    col?: number,   // 位置 - 列 eg. 0
    sectionId?: string, // 标签页模式分组 ID - 标识字段归属的标签页
    /**
     * 关联关系配置
     */
    relationOptions: {
      source: string,                 // 关联表或字段ID
      sourceType: 'model' | 'field',  // 关联模式
      sourceField?: string,           // 关联控件ID [关联表] [双向关联:对应表字段情况下]
      sourceControlType?: string,     // 关联他表字段控件类型
      sourceTitleControlId?: string,  // 关联表标题控件ID
      sourceEntityName?: string,      // 关联表实体名称 eg.'记录'
    },
    /**
     * 默认值配置
     */
    defaultValueOptions: {
      source: string,       // 关联字段ID
      sourceType: 'field',  // 来源模式
      sourceField: string,  // 来源字段ID
      staticValue?: string, // 静态默认值
    }
  }

  // 模型字段选项配置
  interface ModelAttributeOption {
    label: string,    // 选项名
    value: string,    // 选项值
    color?: string,   // 颜色值 eg. '#2196F3'
    hide?: boolean,   // 是否隐藏 - 已删除标签场景
    index?: number,   // 排序
  }

  // 文本类型字段
  interface ModelAttributeTypeText extends ModelAttributeBase {
    type: 'string' | 'text' | 'richtext',
    /**
     * 最大长度 - 针对字符串类型
     */
    maxLength?: number,
    /**
     * 最小长度 - 针对字符串类型
     */
    minLength?: number,
  }

  // 媒体字段类型
  interface ModelAttributeTypeMedia extends ModelAttributeBase {
    type: 'media',
    model?: 'file',       // multiple = false
    collection?: 'file',  // multiple = true
    /**
     * 是否支支持多选
     */
    multiple?: boolean,
    allowedTypes?: ('images' | 'files' | 'videos')[],
    pluginOptions?: object,
  }

  // 数字字段类型
  interface ModelAttributeTypeNumber extends ModelAttributeBase {
    type: 'integer' | 'biginteger' | 'float' | 'decimal' | 'number',
    /**
     * 最大值 - 针对数字类型
     */
    max?: number,
    /**
     * 最小值 - 针对数字类型
     */
    min?: number,
  }

  // 枚举字段类型
  interface ModelAttributeTypeEnumeration extends ModelAttributeBase {
    type: 'enumeration',
    /**
     * 可用的枚举值 - 针对枚举类型
     */
    enum: string[],
  }

  // Uid 字段类型
  // [ext] 目标模型的 uid - 非模型本身字段 model 接口根据其他关系配置字段生成
  interface ModelAttributeTypeUid extends ModelAttributeBase {
    // uid 类型字段配置
    // 生成规则参数：https://github.com/sindresorhus/slugify
    type: 'uid',
    /**
     * 生成 uid 使用的字段
     */
    targetField?: string,
    /**
     * 配置 用户 uid 生成配置规则
     * @description uid生成配置 结果uid必须遵守以下 RegEx /^[A-Za-z0-9-_.~]*$/
     */
    options?: string,
  }

  // 关系字段类型
  // [ext] 关系类型描述 - 非模型本身字段 model 接口根据其他关系配置字段生成
  interface ModelAttributeTypeRelation extends ModelAttributeBase {
    type: 'relation',
    relationType: 'oneWay'
      | 'oneToOne' | 'oneToMany'
      | 'manyToOne' | 'manyToMany' | 'manyWay',
    // 关系类型
    model?: string,      // 关联单个的关系模型名称 与collection互斥
    collection?: string, // 关联多个的关系模型名称 与model互斥
    via?: string,        // via 为建立关联关系的模型字段
    /**
     * [ext] 目标模型的 uid - 非模型本身字段 model 接口根据其他关系配置字段生成
     * @example 'application::author.author'
     */
    targetModel?: string,
    /**
     * 最大元素数量限制
     */
    maxItems?: number,
    /**
     * 多个关联模型的字段表单是否需要排序
     * @default false
     */
    multipleSort?: boolean,
    /**
     * 关联模型的主字段 - 用于列表、表单等展示
     */
    mainField?: string,
    /**
     * 关键数据查询筛选条件
     */
    query?: string,
  }

  interface JSONSchemaBasicOptions {
    // 基础注解
    type: string,         // 类型
    title?: string,       // 用户界面标题
    label?: string,       // title alias - deprecated - 仅为兼容旧版本
    description?: string, // 用户界面描述
    default?: any,        // 默认值
    examples?: any[],     // 示例值
    enum?: any[],         // 可用的枚举值
    const?: any,          // 固定值 - 功能类似单个枚举值
    format?: string,      // 格式 - e.g. 'date-time'
    // 可用 format
    // date-time : 日期时间 [type=string]
    // agent: 客户端信息 [type=object]
    // 其他字段
    // "allOf", "anyOf", "oneOf", "not", "if", "then", "else", "items", "additionalItems", "contains", "propertyNames", "properties", "patternProperties", "additionalProperties"

    // 控件扩展字段
    'x-options': ModelAttributeOption[],
  }

  type JSONSchemaBasic = JSONSchemaBasicOptions &
    ({ // 字符串类型
      type: 'string',
      maxLength?: number,
      minLength?: number,
      pattern?: string,
      options?: { key: string, value: any }[],
    } | { // 数字类型
      type: 'number',
      maximum?: number,     // 最大值
      minimum?: number,     // 最小值
      multipleOf?: number,  // 倍数
      exclusiveMaximum?: number, // 排除最大值 - 配置任意数字即代表值 < maximum
      exclusiveMinimum?: number, // 排除最小值 - 配置任意数字即代表值 > minimum
      options?: { key: string, value: any }[],
    } | { // 布尔类型
      type: 'boolean',
    } | { // 其他类型
      type: 'null',
    })

  // 对象类型
  type JSONSchemaObject = JSONSchemaBasicOptions & {
    type: 'object',
    properties: {
      [key: string]: JSONSchema
    },

    // 校验字段
    required?: string[],  // 必须字段
    // 依赖必须字段 - 即一个字段存在需依赖其他字段存在
    // 例如：firstname 存在时，lastname 必须存在
    // dependentRequired: { "firstname": ["lastname"] }
    dependentRequired?: {
      [key: string]: string[] | {
        required: string[],
      }
    },
  }

  // 数组类型
  type JSONSchemaArray = JSONSchemaBasicOptions & {
    type: 'array',
    items?: JSONSchema,          // 数组元素类型
    contains?: JSONSchema,       // 数据必须包含
    prefixItems?: JSONSchema[],  // 预设数组结构

    // 校验字段
    uniqueItems?: boolean,  // 是否允许重复
    maxItems?: number,      // 最大元素数量
    minItems?: number,      // 最小元素数量
    maxContains?: number,   // 包含 contains 元素的最大数量
    minContains?: number,   // 包含 contains 元素的最小数量
  }

  type JSONSchema = JSONSchemaBasic | JSONSchemaObject | JSONSchemaArray

  // JSON类型字段
  interface ModelAttributeTypeJson extends ModelAttributeBase {
    type: 'json',
    jsonSchema?: JSONSchema,

    // 扩展字段
    modelId?: string,       // 模型ID
    modelFields?: string[], // 模型应用字段
    // ApiSearch 组件控制字段
    multiple?: boolean,     // 是否多选
  }

  // 动态区域类型字段
  interface ModelAttributeTypeDynamicZone extends ModelAttributeBase {
    type: 'dynamiczone',
    /**
     * 动态区域内支持的组件列表 - 例如 [ "user.address", "user.profile" ]
     */
    components?: string[],
    /**
     * 最大item数量， 默认999
     */
    maxItems?: number,
    /**
     * 最小item数量，默认 0
     */
    minItems?: number,
    /**
     * 组件是否可重复 - 此时值会是一个数组
     */
    repeatable?: boolean
    /**
     * 组件展示形式 。 默认值： "default" , 表格形式为：" "table", table 仅在动态区域为 components 为单模型时有效。
     */
    displayMode?: string,
    /**
     * 子表视图配置，仅在 displayMode 为 table 时有效
     */
    viewTableConfig: {
      /**
       * 默认值：取前五个基础类型数据列
       */
      columns?: string[],
      /**
       * [ create, batchEdit、batchDelete、edit、delete、show、copy、json ] ，
       * 默认值： [create, batchEdit、batchDelete、edit、delete、show]
       */
      functions?: string[]
    }
  }

  // 组件类型字段
  interface ModelAttributeTypeComponent extends ModelAttributeBase {
    type: 'component',
    /**
     * 组件类型对应的组件名称
     * @example "user.profile"
     */
    component: string,
    /**
     * 最大item数量， 默认无限
     */
    maxItems?: number,
    /**
     * 最小item数量，默认 0
     */
    minItems?: number,
    /**
     * 组件是否可重复 - 此时值会是一个数组
     */
    repeatable?: boolean,
    /**
     * 组件展示形式 。 默认值： "default" , 表格形式为：" "table", table 仅在repeatable为 true 时有效。
     */
    displayMode?: string,
    /**
     * 子表视图配置，仅在 displayMode 为 table 时有效
     */
    viewTableConfig: {
      /**
       * 需要展示的列名称，如：[ 'id', 'name', 'label']
       * 默认值：取前五个基础类型数据列
       */
      columns?: string[],
      /**
       * [ create, batchEdit、batchDelete、edit、delete、show、copy、json ] ，
       * 默认值： [create, batchEdit、batchDelete、edit、delete、show]
       */
      functions?: string[]
    },
    /**
     * 关联模型的主字段 - 用于列表、表单等展示
     */
    mainField?: string,
  }

  // tabs 类型字段// 文本类型字段
  interface ModelAttributeTypeTabs extends ModelAttributeBase {
    type: 'tabs';
    name?: string;
  }

  // 其他类型字段
  interface ModelAttributeTypeOther extends ModelAttributeBase {
    type: 'email'        // [ ]
      | 'password'       // [ ]
      | 'date' | 'time'  // [ ]
      | 'datetime'       // [✅]
      | 'boolean'        // [✅]
  }

  // 模型属性
  type ModelAttribute =
    ModelAttributeTypeOther
    | ModelAttributeTypeText
    | ModelAttributeTypeMedia
    | ModelAttributeTypeNumber
    | ModelAttributeTypeEnumeration
    | ModelAttributeTypeUid
    | ModelAttributeTypeRelation
    | ModelAttributeTypeJson
    | ModelAttributeTypeDynamicZone
    | ModelAttributeTypeComponent
    | ModelAttributeTypeTabs;

  // 列表筛选项
  interface TableFieldFilter {
    field: string,
    operator: string,
    value: TableQueryValue | TableQueryValue[]
    popupShow: boolean
  }

  // 视图配置
  interface FormViewConfig {
    label: string,
    id: string,
    functions: string[],
    columns: TableColumn[],
    filters: TableFieldFilter[],
    hideQuery: boolean,
    // 新增字段
    pageSize?: number,
    sortField?: string,
    sortOrder?: string
  }

  // 角色可用视图
  interface FormRoleConfigs {
    [key: string]: {
      views?: string[]
    }
  }

  // 表格视图配置
  interface ViewTableConfig {
    page?: ModelPage,

    functions?: string[],
    columns?: TableColumn[],
    filters?: TableFieldFilter[],
    hideQuery?: boolean,
    pageSize?: number,
    sortField?: string,
    sortOrder?: 'ASC' | 'DESC'

    configFunctions?: string[],
    configColumns?: TableColumn[],
    viewConfigs?: FormViewConfig[],
    roleConfigs?: {
      [key: string]: {
        functions?: string[],
        columns?: TableColumn[],
        views?: string[],
      }
    }
  }

  // 页面配置
  interface ModelPage {
    id: string,
    // 视图编号
    sId: string,
    // 是否预置
    isPreset: boolean,
    // 视图名称
    name: string,
    // 父级视图
    parent: string,
    // 基础元数据
    meta: {
      modelId: string,
      modelPath: string,
      tableColumns: (string | TableColumn)[],
      tableFunctions: (AutoTableFunction | string[] | 'simple' | 'full'),
      formFields: (string | { name: string, size: number })[],
    },
    // 扩展配置数据
    configs: ViewTableConfig,
    // 视图图标
    icon: string,
    // 封面
    cover: { url: string },
  }

  // 表格功能配置
  interface AutoTableFunction {
    // 表格数据筛选过滤
    searchFilter?: boolean,
    fieldFilter?: boolean,
    optionFilter?: boolean,
    dateRangeFilter?: boolean,
    // 表格头部操作
    create?: boolean,
    batchEdit?: boolean,
    refresh?: boolean,
    export?: boolean | string[],
    // 表格行内操作
    show?: boolean,
    edit?: boolean,
    delete?: boolean,
    json?: boolean,
    copy?: boolean,
    // 表格分页
    pagination?: boolean,
    // 表格行独立自定义功能
    filterRow?: (row: Record<string, any>) => {
      show?: boolean,
      edit?: boolean,
      delete?: boolean,
      json?: boolean,
      copy?: boolean,
    }
    multiple?: boolean
  }

  // AutoForm 字段配置
  type FormField = ModelAttribute & {
    name: string,
    mainField?: string,
    visible?: boolean,
    innerHTML?: string,  // 嵌入自定义 HTML 内容
    lineBreak?: 'after' | 'before' | 'all' // 三种模式 分别是前方、末尾、前后均插入换行符
  }

  // 文件上传
  interface UploadFile {
    id: string,
    name: string,
    url: string,
    mime: string
  }

  // Quasar 表格列配置
  interface QTableColumn {
    /**
     * Unique id, identifies column, (used by pagination.sortBy, 'body-cell-[name]' slot, ...)
     */
    name: string;
    /**
     * Label for header
     */
    label: string;
    /**
     * Row Object property to determine value for this column or function which maps to the required property
     * @param row The current row being processed
     * @returns Value for this column
     */
    field: string | ((row: any) => any);
    /**
     * If we use visible-columns, this col will always be visible
     */
    required?: boolean;
    /**
     * Horizontal alignment of cells in this column
     * Default value: right
     */
    align?: 'left' | 'right' | 'center';
    /**
     * Tell QTable you want this column sortable
     */
    sortable?: boolean;
    /**
     * Compare function if you have some custom data or want a specific way to compare two rows
     * @param a Value of the first comparison term
     * @param b Value of the second comparison term
     * @param rowA Full Row object in which is contained the first term
     * @param rowB Full Row object in which is contained the second term
     * @returns Comparison result of term 'a' with term 'b'. Less than 0 when 'a' should come first; greater than 0 if 'b' should come first; equal to 0 if their position must not be changed with respect to each other
     */
    sort?: (a: any, b: any, rowA: any, rowB: any) => number;
    /**
     * Set column sort order: 'ad' (ascending-descending) or 'da' (descending-ascending); Overrides the 'column-sort-order' prop
     * Default value: ad
     */
    sortOrder?: 'ad' | 'da';
    /**
     * Function you can apply to format your data
     * @param val Value of the cell
     * @param row Full Row object in which the cell is contained
     * @returns The resulting formatted value
     */
    format?: (val: any, row: any) => any;
    /**
     * Style to apply on normal cells of the column
     * @param row The current row being processed
     */
    style?: string | ((row: any) => string);
    /**
     * Classes to add on normal cells of the column
     * @param row The current row being processed
     */
    classes?: string | ((row: any) => string);
    /**
     * Style to apply on header cells of the column
     */
    headerStyle?: string;
    /**
     * Classes to add on header cells of the column
     */
    headerClasses?: string;
  }

  // 表格列配置
  type TableColumn = QTableColumn & {
    name: string,
    type?: string,
    attribute?: ModelAttribute,
    // 是否在表格显示
    tableDisplay?: boolean,
    // 表格列操作功能配置
    optActions?: {
      type: 'button',
      label: string,
      icon?: string,
      url: string,
      method: 'get' | 'post' | 'put' | 'delete',
    }[],
    // 表格列操作功能配置
    label?: string,
    searchable?: boolean,
    sortable?: boolean,
    mainField?: string
  }

  // 筛选查询值
  type TableQueryValue = string | number | boolean | null | {
    id?: string,
    name?: string,
    from?: string,
    to?: string
  }

  // 筛选结构
  type TableQuery = Record<string, TableQueryValue | TableQueryValue[]>;

  interface FilterField {
    label?: string,
    value: string,
    attribute: ModelAttribute,
    filterOperators?: {
      label: string, value: string
    }[],
    filterOptions?: { label: string, value: string }[],
    formField?: FormField,
    icon: string
  }

  interface ControlContext {
    parentModel?: Model,
    parentEntity?: object
  }
}




