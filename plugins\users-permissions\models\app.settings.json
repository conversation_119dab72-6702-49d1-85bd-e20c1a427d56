{"collectionName": "apps", "info": {"name": "Apps", "label": "应用", "description": "应用包括本地应用与三方应用（微信小程序、公众号、企业微信等）"}, "options": {"increments": true, "timestamps": true, "draftAndPublish": false, "allOf": [{"if": {"properties": {"type": {"const": "wechatMiniProgram"}}}, "then": {"properties": {"verifyData": {"visible": true}, "wechatPayConfig": {"visible": true}, "hostname": {"visible": true}, "wechatPayApiClientCert": {"visible": true}, "wechatPayApiClientKey": {"visible": true}}}}, {"if": {"properties": {"type": {"const": "wechatOfficialAccount"}}}, "then": {"properties": {"verifyData": {"visible": true}}}}, {"if": {"properties": {"type": {"const": "yxWeCom"}}}, "then": {"properties": {"verifyData": {"visible": true}}}}]}, "pluginOptions": {"content-manager": {"visible": true}}, "attributes": {"isPreset": {"label": "是否预置", "type": "boolean", "required": true, "editable": false, "default": false, "configurable": false}, "name": {"label": "名称", "type": "string", "required": true, "configurable": false}, "sId": {"label": "应用ID", "type": "uid", "required": false, "configurable": false}, "thirdLevelDomain": {"label": "三级子域名", "type": "string", "required": false, "configurable": false}, "modules": {"label": "应用功能", "collection": "group", "via": "roles", "dominant": true, "plugin": "users-permissions", "configurable": false}, "type": {"label": "类型", "type": "enumeration", "enum": ["local", "wechatMiniProgram", "wechatOfficialAccount", "yxWeCom"], "options": [{"value": "local", "label": "本地应用"}, {"value": "wechatMiniProgram", "label": "微信小程序"}, {"value": "wechatOfficialAccount", "label": "微信公众号"}, {"value": "yxWeCom", "label": "云校企业微信"}], "required": true, "default": "local"}, "tokenSecret": {"label": "TokenSecret", "type": "string", "required": false, "format": "secret", "private": true}, "verifyData": {"label": "应用认证数据", "type": "json", "visible": false, "private": true}, "wechatPayConfig": {"label": "微信支付配置", "type": "json", "jsonSchema": {"title": "微信支付配置", "type": "object", "properties": {"APP_ID": {"title": "APP_ID", "type": "string"}, "MCH_ID": {"title": "MCH_ID", "type": "string"}, "APIv3SecretKey": {"title": "APIv3SecretKey", "type": "string"}}}, "visible": false, "private": true}, "redirectUri": {"label": "应用URL", "type": "string"}, "icon": {"label": "图标", "model": "file", "via": "related", "allowedTypes": ["images"], "plugin": "upload", "required": false, "pluginOptions": {}}, "hostname": {"label": "微信支付回调域名", "type": "string", "visible": false}, "wechatPayApiClientCert": {"label": "微信支付证书（apiclient_cert.pem）", "model": "file", "via": "related", "allowedTypes": ["files"], "plugin": "upload", "required": false, "pluginOptions": {}, "visible": false, "private": true}, "wechatPayApiClientKey": {"label": "微信支付证书密钥（apiclient_key.pem）", "model": "file", "via": "related", "allowedTypes": ["files"], "plugin": "upload", "required": false, "pluginOptions": {}, "visible": false, "private": true}, "pBranch": {"label": "当前租户", "plugin": "users-permissions", "model": "branch", "configurable": false}}}