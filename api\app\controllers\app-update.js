const { CurdRouter } = require('accel-utils')

const curdRouter = new (class extends CurdRouter {

})('app-update')

async function getLatest (ctx) {
  const { appId, platform } = ctx.request.query
  if (!appId || !platform) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const app = await strapi.query('app').findOne({
    sId: appId
  })
  if (!app) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '应用不存在')
  }

  const list = await strapi.query('app-update').find({
    app: app.id,
    platform,
    status: 'published',
    _sort: 'releaseDate:desc',
    _limit: 1
  })
  if (list && list.length > 0) {
    const data = list[0]
    return data
  }

  return {}
}

async function getDownloadUrl(ctx) {
  const { appId, platform } = ctx.request.query
  if (!appId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const app = await strapi.query('app').findOne({
    sId: appId
  })
  if (!app) {
    return { }
  }


  const platforms = platform ? platform.split(',') : ['mac', 'mac-arm64', 'win']
  const params = {
    app: app.id,
    status: 'published',
    _sort: 'releaseDate:desc'
  }

  const result = {}
  for (const item of platforms) {
    const data = await strapi.query('app-update').findOne({
      ...params,
      platform: item
    })
    if (data) {
      result[item] = data.downloadUrl
    }
  }

  return result
}

module.exports = {
  ...curdRouter.createHandlers(),
  getLatest,
  getDownloadUrl
}
