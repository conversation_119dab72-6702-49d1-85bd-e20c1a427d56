const { <PERSON>urdRouter } = require('accel-utils')
const { ObjectId } = require('mongodb')
const _ = require('lodash')
const {
  getBaseQuery,
  getQueryFollowerChange,
  getQueryKeyChange,
  getUserQueryByRole,
  getFollowerNowGroup,
  getManagerBySchoolId, getCustomersByUser
} = require('../services/customer-service')
const axios = require('axios')
const { getYjAppUsageStatisticByLocal } = require('../services/customer')
const { getModelService } = require('accel-utils/collection-utils')
const { todoTagId } = require('../../xiaoyun/utils/qunGetUtil')
let accessKeys = []
let lastCacheTime = 0
const configServUrl = strapi.config.server.wlyConfig.url

const curdRouter = new (class extends CurdRouter {
  // ...
  _getIntersection (arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }

  async statistic () {
    const data = await getYjAppUsageStatisticByLocal()
    return {
      ...data,
    }
  }

  async findOne (ctx) {
    const query = ctx.request.query
    const customerId = query.customerId
    const schoolId = query.schoolId
    let customer, customerService
    if (customerId) {
      customerService = await strapi.query('customer-service-mid').findOne({ customerId })
      if (!customerService && customerId) {
        customer = await strapi.query('customer').findOne({ id: customerId })
        if (customer) {
          customerService = await strapi.query('customer-service-mid').create({
            name: customer.name,
            customerId: customerId,
            schoolId: customer.school_id_20
          })
        }
      }
    } else if (schoolId) {
      customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId })
      if (!customerService && schoolId) {
        customer = await strapi.query('customer').findOne({ school_id_20: +schoolId })
        if (customer) {
          customerService = await strapi.query('customer-service-mid').create({
            name: customer.name,
            customerId: customer.id,
            schoolId: customer.school_id_20
          })
        }
      }
    } else {
      customerService = await super.findOne(ctx)
    }

    const groups = await strapi.query('manager-group').find({})
    return getFollowerNowGroup(customerService, groups)
  }

  async count (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    getQueryKeyChange(query)
    getQueryFollowerChange(query, groups)
    getUserQueryByRole(query, user, groups)
    return super.count(ctx)
  }

  async find (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    getQueryKeyChange(query)
    getQueryFollowerChange(query, groups)
    getUserQueryByRole(query, user, groups)

    const customerServices = await super.find(ctx)
    // 填充服务数据
    for (let i = 0; i < customerServices.length; i++) {
      customerServices[i] = getFollowerNowGroup(customerServices[i], groups)
    }
    return customerServices
  }

  async update (ctx) {
    const { data, params } = this._parseCtx(ctx)
    if (data.qun) {
      const qun = await strapi.query('xiaoyun-qun').findOne({ id: data.qun })
      data.qunId = qun?.qunId || null
    } else if (data.hasOwnProperty('qun')) {
      data.qunId = null
    }

    const result = await super.update(ctx)
    return result
  }

})('customer-service-mid')

async function getCustomerManager (ctx) {
  const { schoolId, accessKey } = ctx.request.query
  if (!schoolId || +schoolId <= 0 || !accessKey) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  if (lastCacheTime + 60 * 60 * 1000 < Date.now()) {
    const res = await axios.get(`${configServUrl}/access-key/list`)
    accessKeys = res.data
    lastCacheTime = Date.now()
  }

  const accessKeyObj = accessKeys.find(e => e.key === ctx.request.query.accessKey)
  if (!accessKeyObj) {
    // 不存在或禁用。
    return ctx.badRequest('Invalid access-key')
  }

  if (!accessKeyObj.status) {
    return ctx.badRequest('access-key 被禁用，请联系管理员')
  }

  const customerService = await getManagerBySchoolId(schoolId)
  if (customerService) {
    return ctx.wrapper.succ(customerService)
  }
  return ctx.wrapper.error('PARAMETERS_ERROR', '未查询到学校信息')
}

async function getCustomerListByWxId (ctx) {
  const { userId, accessKey } = ctx.request.query
  if (!userId || !accessKey) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  if (lastCacheTime + 60 * 60 * 1000 < Date.now()) {
    const res = await axios.get(`${configServUrl}/access-key/list`)
    accessKeys = res.data
    lastCacheTime = Date.now()
  }

  const accessKeyObj = accessKeys.find(e => e.key === ctx.request.query.accessKey)
  if (!accessKeyObj) {
    // 不存在或禁用。
    return ctx.badRequest('Invalid access-key')
  }

  if (!accessKeyObj.status) {
    return ctx.badRequest('access-key 被禁用，请联系管理员')
  }

  const user = await strapi.query('user', 'users-permissions').findOne({ customId: userId })
  if (!user) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '查无此人')
  }

  const customerServices = await getCustomersByUser(user)
  return ctx.wrapper.succ(customerServices)
}

async function getSchoolTag (ctx) {
  let { schoolIds, customerIds } = ctx.request.query
  if (!customerIds && !schoolIds) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let schools
  if (customerIds) {
    schools = await strapi.query('customer-service-mid').find({ customerId_in: customerIds.split(',') })
  } else {
    schoolIds = schoolIds.split(',').map(item => +item)
    schools = await strapi.query('customer-service-mid').find({ schoolId_in: schoolIds })
  }
  let schoolTags = []
  if (schools && schools.length) {
    schoolTags = schools.map(item => {
      return {
        schoolId: item.schoolId,
        customerId: item.customerId,
        yjTag: item.yjTag || null,
        fxTag: item.fxTag || null,
      }
    })
  }

  return schoolTags
}

async function changeSchoolTag (ctx) {
  let { schoolId, customerId, yjTag, fxTag } = ctx.request.body
  if (!schoolId && !customerId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  if (!yjTag && !fxTag) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let school = null
  if (schoolId) {
    school = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId })
  } else {
    school = await strapi.query('customer-service-mid').findOne({ customerId: customerId })
  }

  if (!school) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '查无此学校')
  }

  let updateData = {}
  if (yjTag) {
    let oldYjTag = school.yjTag
    if (oldYjTag !== yjTag) {
      // 标签变更
      await strapi.query('customer-op-log').create({
        customerService: school.id,
        type: 'yjTag',
        content: {
          oldYjTag: oldYjTag,
          newYjTag: yjTag
        }
      })
    }
    updateData.yjTag = yjTag
  }
  if (fxTag) {
    updateData.fxTag = fxTag
  }
  await strapi.query('customer-service-mid').update({ schoolId: school.schoolId }, updateData)
  return ctx.wrapper.succ({})
}

// 绑定/取消群绑定
async function bindQunInfo (ctx) {
  const { binding, schoolId, customerId, qunId, mingdaoId } = ctx.request.body
  if (!schoolId && !customerId && !mingdaoId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  if (!schoolId || +schoolId <= 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let params = {}
  if (customerId) {
    params.customerId = customerId
  } else {
    params.schoolId = +schoolId
  }

  let school = await strapi.query('customer-service-mid').findOne(params)

  if (!school) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，查无此学校')
  } else if (school.deleted) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，学校已被删除')
  }

  if (binding === '0' || binding === 0) {
    if (!school.qun) {
      return ctx.wrapper.succ('解绑成功')
    }
    // 取消绑定
    await getModelService('customer-service-mid').update({
      params: {
        id: school.id
      },
      data: {
        qun: null,
        qunId: null,
      }
    })
    console.log('解绑群', school.qun)
    // 将群标为待回收
    await strapi.query('xiaoyun-qun').update({ id: school.qun.id }, {
      customer: null,
      qunTags: [todoTagId]
    })

    // 修改明道云
    await strapi.query('customer-service').update({ id: mingdaoId }, { qunId: null })
    return ctx.wrapper.succ('解绑成功')
  } else if (binding === '1' || binding === 1) {
    // 绑定
    if (!qunId) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误, 缺少 qunId')
    }

    const qun = await strapi.query('xiaoyun-qun').findOne({ qunId: qunId })
    if (!qun) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误, 不存在该群')
    }
    if (qun.isDeleted) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误, 该群已被解散')
    }

    if (school.qun && school.qun.qunId === qunId) {
      return ctx.wrapper.succ('绑定成功')
    }

    await getModelService('customer-service-mid').update({
      params: {
        id: school.id
      },
      data: {
        qun: qun.id,
        qunId: qun.qunId,
      }
    })

    // 修改明道云
    await strapi.query('customer-service').update({ id: mingdaoId }, { qunId })

    return ctx.wrapper.succ('绑定成功')
  } else {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
}

module.exports = {
  getCustomerManager,
  getCustomerListByWxId,
  getSchoolTag,
  changeSchoolTag,
  bindQunInfo,
  ...curdRouter.createHandlers(),
}
