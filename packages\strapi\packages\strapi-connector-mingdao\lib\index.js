const _ = require('lodash')
const { createWebApiConnection } = require('strapi/utils/mingdao-web-api')
const queries = require('./queries')
const { mountMingdaoModel } = require('../../../utils/mingdao-utils')
const { contentTypes: contentTypesUtils } = require('accel-utils');

const isMingdaoConnection = ({ connector }) => connector === 'mingdao'

module.exports = function (strapi) {
  const { connections } = strapi.config
  const connectionNames = Object.keys(connections).filter(key =>
    isMingdaoConnection(connections[key])
  )

  async function createMingdaoInstance (connectionName) {
    const connection = connections[connectionName]
    const instance = createWebApiConnection(connection.settings)
    const ctx = {
      instance,
      connection
    }
    _.set(strapi, `connections.${connectionName}`, instance)

    await mountApis(connectionName)
    return ctx
  }

  async function mountApis (connectionName, ctx) {
    const options = {
      models: _.pickBy(strapi.models, ({ connection }) => connection === connectionName),
      target: strapi.models,
    }
    return mountModels(options, ctx)
  }

  async function mountModels (options, ctx) {
    const mingdaoModels = Object.keys(options.models).map(key => options.models[key])
    for (let key of Object.keys(options.models)) {
      delete strapi.contentTypes[strapi.models[key].uid]
      delete strapi.models[key]
    }
    strapi.mingdaoModels = mingdaoModels
    console.info('Mingdao Model Mount...')
    const logLabel = `Mingdao Model Mounted [${mingdaoModels.length}]`
    console.time(logLabel)
    const modelInstances = await Promise.all(mingdaoModels.map(model => {
      return mountMingdaoModel(model.collectionName, model)
    }))
    // 支持同一个表创建多个models 冗余明道云models
    for (let key of Object.keys(options.models)) {
      const matchModel = modelInstances?.find(e => e.modelName === options.models[key].modelName)
      // Mingdao ID & Alias
      if (matchModel) {
        strapi.models[key] = matchModel
        // 支持过滤私有字段
        strapi.models[key].privateAttributes = contentTypesUtils.getPrivateAttributes(strapi.models[key]);
      }
    }
    console.timeEnd(logLabel)
    return modelInstances
  }

  return {
    async initialize () {
      console.info('MingDao initialize...')
      // 初始化 database 配置中的明道云应用连接
      await Promise.all(connectionNames.map(connectionName => {
        return createMingdaoInstance(connectionName)
      }))
    },
    destroy () {
      console.info('MingDao destroy...')
      for (let connectionName of connectionNames) {
        _.unset(strapi, `connections.${connectionName}`)
      }
    },
    buildQuery () {
      console.info('MingDao buildQuery...')
    },
    queries,
    get defaultTimestamps () {
      return ['ctime', 'utime']
    }
  }
}
