{"fieldsMap": {"立项编号": "2200000201633949", "登记日期": "2200000483871903", "项目属性": "2200000486645967", "项目类型": "2200000486321902", "项目标签": "2200000488286726", "沟通目的": "2200000488848489", "项目名称": "2200000174099142", "阿米巴单元": "2200000457505363", "项目经理": "2200000174558464", "1129是否已过会": "2200000487941370", "分组": "2200000483893238", "省份": "2200000443799881", "地市": "2200000174558466", "合作区域（精确到区县）": "2200000462245438", "教育局/学校名称": "2200000180426108", "学校/教育局": "2200000439341577", "学校快查（仅单校）": "2200000487106002", "经销商": "2200000174713831", "意向商": "2200000483871906", "现有商": "2200000483871908", "新商": "2200000484597379", "资源方": "2200000483898323", "联系人": "2200000483871910", "电话": "2200000483871917", "联系地址": "2200000483871911", "合作产品": "2200000483871912", "是否续约": "2200000483871913", "现合作到期日期": "2200000483871915", "项目概况": "2200000447149927", "项目参考附件": "2200000465255656", "备注": "2200000483894670", "CRM项目编号": "2200000447079813", "项目性质": "2200000459029395", "AMB项目类型": "2200000458897953", "🛎产品成本": "2200000459029393", "🛎智慧校园/直营错题本去年业绩额": "2200000464109547", "错题本计提-每次生产本数": "2200000464399699", "错题本计提-每次审题科目数": "2200000464399679", "错题本计提-一年生产次数": "2200000464432960", "🛎24财年累计项目业绩（扣除收入前支出）": "2200000458898616", "销管备注": "2200000297051533", "项目收入": "2200000459205481", "销售提成": "2200000463667072", "计算项目收入": "2200000464145794", "项目收入（触发器计算）": "2200000464145928", "是否人工调整": "2200000459202263", "调整后项目收入-正常": "2200000464429651", "核准状态": "2200000459202264", "核准记录": "2200000459202267", "是否作为线索分配": "2200000460659998", "线索提供方": "2200000455608510", "线索分配记录": "2200000467548163", "延期申请原因【存档】": "2200000460523366", "过期前项目信息【存档】": "2200000460523367", "项目状态（存档，原过程管理使用）": "2200000280563226", "项目属性【存档】": "2200000483871905", "累计回款金额（业务自行登记）": "2200000320342555", "本月实际回款（业务自行登记）": "2200000321981028", "预计最近回款日期": "2200000177261463", "预计最近回款金额（万）": "2200000177261462", "预计消耗老款（万）": "2200000181219328", "本月回款把握": "2200000304913746", "团队成员": "2200000458957520", "合作方业绩分配约定": "2200000458957548", "项目开始时间": "2200000180426102", "预计招投标时间": "2200000280563225", "项目管理标签": "2200000466723629", "具体产品（废弃字段）": "2200000177261460", "产品线（废弃字段）": "2200000297082317", "潜在合作伙伴（废弃字段）": "2200000181151853", "立项冲突记录数": "2200000464733398", "项目收入（按存量-触发器计算）": "2200000464169312", "调整后项目收入-按存量": "2200000459202265", "上会申请方案": "2200000447121293", "参考附件": "2200000447141147", "决策委员会审批意见": "2200000447121291", "合作方式（存档）": "2200000180426986", "产品": "2200000439335982", "产品补充说明": "2200000200101695", "项目标签【存档】": "2200000174558469", "项目跟进记录": "2200000176001491", "项目所需支持": "2200000180788422", "风险": "2200000323499787", "计划上会日期（已迁移）": "2200000447150287", "决策状态（已迁移）": "2200000447121292", "2023业绩归属": "2200000323453735", "是否包含外采": "2200000297047997", "外采成本（元）": "2200000297047998", "外采内容备注": "2200000447167573", "合同开始日期": "2200000280563229", "合同到期日期": "2200000280563230", "方案": "2200000180569508", "启动实施阶段": "2200000280563234", "排课服务情况": "2200000280563235", "日常实施服务": "2200000280563236", "授权学校（隐藏字段）": "2200000485377705", "授权产品（隐藏字段）": "2200000485617813", "1017补充信息": "2200000486064242", "计算临期学校数量": "2200000486240472", "新数据": "2200000483894072", "mark": "2200000487045935", "跟进记录数": "2200000487045918", "mark2": "2200000489350867", "立项审核": "2200000197759297", "项目状态": "2200000447079812", "审批备注": "2200000197797191", "立项日期": "2200000439349084", "立项过期日期": "2200000460114505", "下次跟进日期": "2200000483871922", "下次跟进事项": "2200000483871923", "项目阶段": "2200000486321903", "拜访阶段结束标识": "2200000483898059", "线索阶段结束标识": "2200000483898060", "机会阶段结束标识": "2200000483898061", "意向阶段结束标识": "2200000483898062", "合同阶段结束标识": "2200000483898063", "决策方式": "2200000486483896", "预计回款金额（元）": "2200000483871919", "预计回款日期": "2200000483871920", "关键决策人姓名": "2200000174558470", "关键决策人电话": "2200000174558471", "关键决策人职务": "2200000174558472", "报价&方案说明": "2200000198532851", "客户终端价格（元）": "2200000431721998", "公司应收（元）": "2200000177261461", "申请制作合同": "2200000439346766", "合同管理快速链接": "2200000460239060", "到达合同阶段日期": "2200000491390593", "合同阶段打回": "2200000490682252", "达成率": "2200000486540449", "关键人加V": "2200000486540442", "去年的合同价": "2200000486540919", "学校档案竞品信息填写": "2200000486540443", "报价单（审议后）": "2200000486540446", "决议结果": "2200000486540445", "双章合同": "2200000486540444", "验收单": "2200000486540447", "发票": "2200000486540448", "产出达成率": "2200000491350536", "产出线索": "2200000491350537", "明确预算": "2200000491350538", "找到项目关键人": "2200000491350539", "明确项目参数/具体需求": "2200000491350540", "初步方案设计": "2200000491350541", "达成共识（关键条款、方案）": "2200000491350542", "盖双章合同": "2200000491350543", "发票/回款单/验收单": "2200000491350544", "流程发起人": "2200000449874851", "流程状态": "2200000449874852", "流程进展": "2200000449874853", "流程启动时间": "2200000449874854", "流程结束时间": "2200000449874855", "项目类型-大类": "1329001111000000", "阿米巴单元-阿米巴单元": "1237001101000000", "阿米巴单元-类型": "1237001120000000", "阿米巴单元-阿米巴负责人": "1237001114000000", "阿米巴单元-归属联盟": "1237001153000000", "阿米巴单元-联盟（临时）": "1237001190000000", "项目经理-员工姓名": "1102001101000000", "项目经理-企信ID": "1102001115000000", "项目经理-系统账号": "1102001102000000", "项目经理-外部用户": "1102001121000000", "项目经理-昵称": "1102001121001102", "项目经理-手机号": "1102001121001103", "项目经理-所属阿米巴单元": "1102001116000000", "项目经理-阿米巴负责人": "1102001117000000", "项目经理-部门负责人": "1102001114000000", "项目经理-直营小组": "1102001118000000", "项目经理-直营小组长": "1102001120000000", "项目经理-外部用户-组长": "1102001122000000", "项目经理-状态": "1102001107000000", "省份-省名": "1199001101000000", "地市-省": "1104001102000000", "地市-省名": "1104001102001101", "经销商-经销商编号": "1112001112000000", "现有商-经销商编号": "1294001112000000", "AMB项目类型-AMB项目类型": "1240001114000000", "AMB项目类型-业务AMB团队的收入计算规则": "1240001126000000", "AMB项目类型-是否有成本": "1240001124000000", "AMB项目类型-成本计算说明": "1240001125000000", "AMB项目类型-收入计算类型": "1240001129000000", "AMB项目类型-存量项目-成本部分比例": "1240001120000000", "AMB项目类型-存量项目-超出成本部分比例": "1240001121000000", "AMB项目类型-增量项目-成本部分比例": "1240001122000000", "AMB项目类型-增量项目-超出成本部分比例）": "1240001123000000", "AMB项目类型-B类-成本比例": "1240001127000000", "项目阶段-阶段名称": "1330001112000000", "项目阶段-阶段提示": "1330001113000000", "项目阶段-序号": "1330001115000000"}, "table_id": "2100000018480321", "name": "项目管理", "alias": "", "space_id": "4000000003570865", "created_on": "2021-11-08 08:31:35", "fields": [{"field_id": "2200000201633949", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000483871903", "name": "登记日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486645967", "name": "项目属性", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059740536", "space_id": "4000000003570865"}}, {"field_id": "2200000486321902", "name": "项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692209", "space_id": "4000000003570865"}}, {"field_id": "2200000488286726", "name": "项目标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059982481", "space_id": "4000000003570865"}}, {"field_id": "2200000488848489", "name": "沟通目的", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060066489", "space_id": "4000000003570865"}}, {"field_id": "2200000174099142", "name": "项目名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000457505363", "name": "阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000174558464", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000487941370", "name": "1129是否已过会", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}, {"id": "3", "name": "多对一"}, {"id": "4", "name": "待回收"}]}}, {"field_id": "2200000483893238", "name": "分组", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "21", "name": "暂不跟进 -12月"}, {"id": "22", "name": "暂不跟进 -25年"}, {"id": "13", "name": "A组"}, {"id": "12", "name": "B组"}, {"id": "14", "name": "C组"}, {"id": "19", "name": "专家组"}, {"id": "17", "name": "1113-1114申请上会转直营/中止"}]}}, {"field_id": "2200000443799881", "name": "省份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000174558466", "name": "地市", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524705", "space_id": "4000000003570865"}}, {"field_id": "2200000462245438", "name": "合作区域（精确到区县）", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "如果是区域合作，则填写本字段，可多选，精确到区县", "config": {"is_multi": 1, "table_id": "2100000018524706", "space_id": "4000000003570865"}}, {"field_id": "2200000180426108", "name": "教育局/学校名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000439341577", "name": "学校/教育局", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "必填，每个学校/教育局单独立项，若无账号请先处理账号问题", "config": {"is_multi": 1, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000487106002", "name": "学校快查（仅单校）", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000174713831", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "如果搜不到经销商，请联系@朱文娟添加", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000483871906", "name": "意向商", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "存量商"}, {"id": "2", "name": "新商"}]}}, {"field_id": "2200000483871908", "name": "现有商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000484597379", "name": "新商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "请先创建新商，每人仅能查看自己录入的数据", "config": {"is_multi": 0, "table_id": "2100000059427326", "space_id": "4000000003570865"}}, {"field_id": "2200000483898323", "name": "资源方", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000483871910", "name": "联系人", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000483871917", "name": "电话", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000483871911", "name": "联系地址", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000483871912", "name": "合作产品", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "2", "name": "阅卷"}, {"id": "12", "name": "分析"}, {"id": "13", "name": "云平台"}, {"id": "4", "name": "360会员"}, {"id": "5", "name": "错题本"}, {"id": "6", "name": "统考"}, {"id": "8", "name": "联考"}, {"id": "9", "name": "排课"}, {"id": "10", "name": "作业系统"}, {"id": "11", "name": "其他"}]}}, {"field_id": "2200000483871913", "name": "是否续约", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000483871915", "name": "现合作到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000447149927", "name": "项目概况", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000465255656", "name": "项目参考附件", "alias": "", "field_type": "file", "data_type": "file", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_watermark": 0}}, {"field_id": "2200000483894670", "name": "备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000447079813", "name": "CRM项目编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000459029395", "name": "项目性质", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "老校/续签"}, {"id": "2", "name": "新校/新签"}, {"id": "3", "name": "其他"}]}}, {"field_id": "2200000458897953", "name": "AMB项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636965", "space_id": "4000000003570865"}}, {"field_id": "2200000459029393", "name": "🛎产品成本", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000464109547", "name": "🛎智慧校园/直营错题本去年业绩额", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000464399699", "name": "错题本计提-每次生产本数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464399679", "name": "错题本计提-每次审题科目数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464432960", "name": "错题本计提-一年生产次数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000458898616", "name": "🛎24财年累计项目业绩（扣除收入前支出）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000297051533", "name": "销管备注", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000459205481", "name": "项目收入", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000463667072", "name": "销售提成", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464145794", "name": "计算项目收入", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "计算"}, {"id": "2", "name": "不计算"}]}}, {"field_id": "2200000464145928", "name": "项目收入（触发器计算）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000459202263", "name": "是否人工调整", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "否"}, {"id": "2", "name": "是"}]}}, {"field_id": "2200000464429651", "name": "调整后项目收入-正常", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000459202264", "name": "核准状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "待审核"}, {"id": "2", "name": "确认审核"}, {"id": "3", "name": "存疑待沟通"}]}}, {"field_id": "2200000459202267", "name": "核准记录", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000460659998", "name": "是否作为线索分配", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "2", "name": "否，自行跟进"}, {"id": "1", "name": "是一条线索"}]}}, {"field_id": "2200000455608510", "name": "线索提供方", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000467548163", "name": "线索分配记录", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000460523366", "name": "延期申请原因【存档】", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000460523367", "name": "过期前项目信息【存档】", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000280563226", "name": "项目状态（存档，原过程管理使用）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "开拓中"}, {"id": "2", "name": "履约中"}, {"id": "3", "name": "正常到期"}, {"id": "4", "name": "中止"}, {"id": "6", "name": "续约洽谈中"}, {"id": "7", "name": "关闭"}]}}, {"field_id": "2200000483871905", "name": "项目属性【存档】", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "SaaS经销合作"}, {"id": "2", "name": "直营项目"}, {"id": "3", "name": "集成商合作"}, {"id": "4", "name": "仅授权"}, {"id": "5", "name": "直营SaaS"}]}}, {"field_id": "2200000320342555", "name": "累计回款金额（业务自行登记）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000321981028", "name": "本月实际回款（业务自行登记）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000177261463", "name": "预计最近回款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000177261462", "name": "预计最近回款金额（万）", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000181219328", "name": "预计消耗老款（万）", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000304913746", "name": "本月回款把握", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "较大把握"}, {"id": "2", "name": "50%把握"}, {"id": "3", "name": "把握不大"}]}}, {"field_id": "2200000458957520", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000458957548", "name": "合作方业绩分配约定", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000180426102", "name": "项目开始时间", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000280563225", "name": "预计招投标时间", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000466723629", "name": "项目管理标签", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "存量属性"}, {"id": "2", "name": "增量属性"}]}}, {"field_id": "2200000177261460", "name": "具体产品（废弃字段）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 0, "options": [{"id": "7", "name": "阅卷SaaS-单校"}, {"id": "8", "name": "阅卷SaaS-教育局"}, {"id": "9", "name": "阅卷SaaS-联盟校"}, {"id": "26", "name": "阅卷SaaS-单次联考"}, {"id": "16", "name": "阅卷SaaS打包"}, {"id": "17", "name": "阅卷SaaS集采"}, {"id": "10", "name": "考试服务费"}, {"id": "2", "name": "错题本"}, {"id": "18", "name": "错题本打包"}, {"id": "4", "name": "360会员"}, {"id": "22", "name": "云平台"}, {"id": "21", "name": "排课服务"}, {"id": "15", "name": "校本题库"}, {"id": "28", "name": "智慧校园平台"}, {"id": "29", "name": "新高考解决方案"}, {"id": "14", "name": "排课排考"}, {"id": "13", "name": "教师培训"}, {"id": "5", "name": "学情套餐"}, {"id": "3", "name": "升学宝消耗"}, {"id": "19", "name": "区域基础授权"}, {"id": "11", "name": "区域独家招商"}, {"id": "20", "name": "区域独家续约"}, {"id": "12", "name": "异业合作"}, {"id": "30", "name": "定制化开发"}]}}, {"field_id": "2200000297082317", "name": "产品线（废弃字段）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "3", "name": "智慧校园-云平台"}, {"id": "12", "name": "智慧校园-阅卷"}, {"id": "7", "name": "阅卷集采"}, {"id": "2", "name": "SaaS打包"}, {"id": "8", "name": "联考"}, {"id": "9", "name": "区域独家"}, {"id": "4", "name": "360会员/考试套餐"}, {"id": "13", "name": "走班排课系统&服务"}, {"id": "14", "name": "排课会员"}, {"id": "1", "name": "错题本"}, {"id": "6", "name": "渠道项目"}, {"id": "15", "name": "精准练项目"}, {"id": "5", "name": "其他"}]}}, {"field_id": "2200000181151853", "name": "潜在合作伙伴（废弃字段）", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "如果代理商未建立正式合作，填写此项。注：务必填写公司全称", "config": {}}, {"field_id": "2200000464733398", "name": "立项冲突记录数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464169312", "name": "项目收入（按存量-触发器计算）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000459202265", "name": "调整后项目收入-按存量", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000447121293", "name": "上会申请方案", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000447141147", "name": "参考附件", "alias": "", "field_type": "file", "data_type": "file", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_watermark": 0}}, {"field_id": "2200000447121291", "name": "决策委员会审批意见", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000180426986", "name": "合作方式（存档）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "渠道合作"}, {"id": "5", "name": "单次项目"}, {"id": "2", "name": "直营"}]}}, {"field_id": "2200000439335982", "name": "产品", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "12", "name": "阅卷"}, {"id": "21", "name": "分析"}, {"id": "22", "name": "题库"}, {"id": "8", "name": "联考"}, {"id": "23", "name": "统考"}, {"id": "20", "name": "中心校片区"}, {"id": "3", "name": "云平台"}, {"id": "14", "name": "排课系统"}, {"id": "13", "name": "排课服务"}, {"id": "4", "name": "360会员/考试套餐"}, {"id": "1", "name": "错题本"}, {"id": "17", "name": "定制化开发"}, {"id": "9", "name": "区域独家"}, {"id": "19", "name": "膨胀金"}, {"id": "25", "name": "扫描仪设备"}, {"id": "5", "name": "其它"}, {"id": "18", "name": "阅卷集采"}, {"id": "16", "name": "精准练新模式"}, {"id": "26", "name": "作业系统"}]}}, {"field_id": "2200000200101695", "name": "产品补充说明", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "1", "name": "阅卷定制"}, {"id": "2", "name": "云平台定制"}, {"id": "3", "name": "C端定制"}, {"id": "4", "name": "打包"}, {"id": "5", "name": "阅卷集采"}, {"id": "6", "name": "独家"}, {"id": "7", "name": "膨胀"}, {"id": "8", "name": "联合运营"}, {"id": "9", "name": "违规处理"}, {"id": "10", "name": "历史款追回"}]}}, {"field_id": "2200000174558469", "name": "项目标签【存档】", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "16", "name": "初步接触"}, {"id": "8", "name": "合作洽谈"}, {"id": "21", "name": "立项评估"}, {"id": "11", "name": "客户意向明确"}, {"id": "17", "name": "客户资金到位"}, {"id": "22", "name": "招投标采购"}, {"id": "15", "name": "合同审议盖章"}, {"id": "23", "name": "发票提交"}, {"id": "20", "name": "财务打款"}, {"id": "7", "name": "实施交付"}, {"id": "19", "name": "C端收费运营"}, {"id": "13", "name": "项目暂缓"}, {"id": "14", "name": "终止未合作"}, {"id": "25", "name": "非正常中止"}, {"id": "26", "name": "流失风险"}, {"id": "28", "name": "归属流转"}, {"id": "29", "name": "正常到期"}]}}, {"field_id": "2200000176001491", "name": "项目跟进记录", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "【日期】详情（倒序）\n例如，\n【0315】与商确定合同主要内容\n【0312】与商初步沟通合作方案", "config": {}}, {"field_id": "2200000180788422", "name": "项目所需支持", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000323499787", "name": "风险", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000447150287", "name": "计划上会日期（已迁移）", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000447121292", "name": "决策状态（已迁移）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "待评审"}, {"id": "2", "name": "方案打回"}, {"id": "3", "name": "决议通过"}, {"id": "4", "name": "修改完善"}, {"id": "5", "name": "变更重审"}, {"id": "6", "name": "已出合同"}, {"id": "7", "name": "项目取消"}]}}, {"field_id": "2200000323453735", "name": "2023业绩归属", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "1#渠道"}, {"id": "2", "name": "2#项目"}, {"id": "3", "name": "3#精准练"}, {"id": "4", "name": "4#会员流量"}, {"id": "6", "name": "5#自营"}, {"id": "7", "name": "集团项目池"}, {"id": "5", "name": "其它"}]}}, {"field_id": "2200000297047997", "name": "是否包含外采", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "否"}, {"id": "2", "name": "是"}]}}, {"field_id": "2200000297047998", "name": "外采成本（元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000447167573", "name": "外采内容备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000280563229", "name": "合同开始日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000280563230", "name": "合同到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000180569508", "name": "方案", "alias": "", "field_type": "file", "data_type": "file", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_watermark": 0}}, {"field_id": "2200000280563234", "name": "启动实施阶段", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000280563235", "name": "排课服务情况", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000280563236", "name": "日常实施服务", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000485377705", "name": "授权学校（隐藏字段）", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000485617813", "name": "授权产品（隐藏字段）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "阅卷"}, {"id": "2", "name": "360会员"}, {"id": "3", "name": "错题本"}]}}, {"field_id": "2200000486064242", "name": "1017补充信息", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000486240472", "name": "计算临期学校数量", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000483894072", "name": "新数据", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000487045935", "name": "mark", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000487045918", "name": "跟进记录数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000489350867", "name": "mark2", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000197759297", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "其他待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "2200000447079812", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "2200000197797191", "name": "审批备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000439349084", "name": "立项日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000460114505", "name": "立项过期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000483871922", "name": "下次跟进日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000483871923", "name": "下次跟进事项", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000486321903", "name": "项目阶段", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692208", "space_id": "4000000003570865"}}, {"field_id": "2200000483898059", "name": "拜访阶段结束标识", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已产出线索"}]}}, {"field_id": "2200000483898060", "name": "线索阶段结束标识", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已明确预算"}]}}, {"field_id": "2200000483898061", "name": "机会阶段结束标识", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已取得信息：关键人、项目参数/具体需求、初步方案设计"}]}}, {"field_id": "2200000483898062", "name": "意向阶段结束标识", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已达成共识：①标书/关键商务条款②利益分享方案"}]}}, {"field_id": "2200000483898063", "name": "合同阶段结束标识", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "合同双方盖章"}]}}, {"field_id": "2200000486483896", "name": "决策方式", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "校长/董事长决策"}, {"id": "5", "name": "教育局决策"}, {"id": "2", "name": "校务会决策"}, {"id": "4", "name": "暂不明确"}]}}, {"field_id": "2200000483871919", "name": "预计回款金额（元）", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000483871920", "name": "预计回款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000174558470", "name": "关键决策人姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000174558471", "name": "关键决策人电话", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000174558472", "name": "关键决策人职务", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000198532851", "name": "报价&方案说明", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": true, "description": "如，\n阅卷SaaS，1套，1年，1.5万\n分析系统，1套，1年，2万\n走班排课系统，1套，1年，1万", "config": {}}, {"field_id": "2200000431721998", "name": "客户终端价格（元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000177261461", "name": "公司应收（元）", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000439346766", "name": "申请制作合同", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "2", "name": "否"}, {"id": "3", "name": "非标方案需上会"}, {"id": "5", "name": "标准方案"}]}}, {"field_id": "2200000460239060", "name": "合同管理快速链接", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000491390593", "name": "到达合同阶段日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000490682252", "name": "合同阶段打回", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}]}}, {"field_id": "2200000486540449", "name": "达成率", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "2200000486540442", "name": "关键人加V", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540919", "name": "去年的合同价", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540443", "name": "学校档案竞品信息填写", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540446", "name": "报价单（审议后）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540445", "name": "决议结果", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540444", "name": "双章合同", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540447", "name": "验收单", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000486540448", "name": "发票", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "3", "name": "否"}]}}, {"field_id": "2200000491350536", "name": "产出达成率", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "2200000491350537", "name": "产出线索", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350538", "name": "明确预算", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350539", "name": "找到项目关键人", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350540", "name": "明确项目参数/具体需求", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350541", "name": "初步方案设计", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350542", "name": "达成共识（关键条款、方案）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350543", "name": "盖双章合同", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000491350544", "name": "发票/回款单/验收单", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000449874851", "name": "流程发起人", "alias": "process_executor_id", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000449874852", "name": "流程状态", "alias": "process_status", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "执行中"}, {"id": "2", "name": "已完成（同意）"}, {"id": "3", "name": "已完成（不同意）"}, {"id": "4", "name": "已撤销"}, {"id": "5", "name": "已关闭"}, {"id": "6", "name": "异常中"}]}}, {"field_id": "2200000449874853", "name": "流程进展", "alias": "process_current_log", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000449874854", "name": "流程启动时间", "alias": "process_created_on", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "datetime"}}, {"field_id": "2200000449874855", "name": "流程结束时间", "alias": "process_completed_on", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "datetime"}}, {"field_id": "1329001111000000", "name": "大类", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486321902}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "项目"}, {"id": "2", "name": "直营"}]}}, {"field_id": "1237001101000000", "name": "阿米巴单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000457505363}, "required": false, "description": "", "config": {}}, {"field_id": "1237001120000000", "name": "类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000457505363}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "渠道业务"}, {"id": "4", "name": "会员业务"}, {"id": "3", "name": "直营业务"}, {"id": "6", "name": "直服业务"}, {"id": "2", "name": "非业务"}, {"id": "5", "name": "调整"}]}}, {"field_id": "1237001114000000", "name": "阿米巴负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000457505363}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1237001153000000", "name": "归属联盟", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000457505363}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056608623", "space_id": "4000000003570865"}}, {"field_id": "1237001190000000", "name": "联盟（临时）", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000457505363}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059885507", "space_id": "4000000003570865"}}, {"field_id": "1102001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174558464}, "required": true, "description": "", "config": {}}, {"field_id": "1102001115000000", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174558464}, "required": true, "description": "", "config": {}}, {"field_id": "1102001102000000", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1102001121000000", "name": "外部用户", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1102001121001102", "name": "昵称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {}}, {"field_id": "1102001121001103", "name": "手机号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {}}, {"field_id": "1102001116000000", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1102001117000000", "name": "阿米巴负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1102001114000000", "name": "部门负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1102001118000000", "name": "直营小组", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059956977", "space_id": "4000000003570865"}}, {"field_id": "1102001120000000", "name": "直营小组长", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1102001122000000", "name": "外部用户-组长", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1102001107000000", "name": "状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000174558464}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "在职"}, {"id": "2", "name": "离职"}]}}, {"field_id": "1199001101000000", "name": "省名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000443799881}, "required": true, "description": "", "config": {}}, {"field_id": "1104001102000000", "name": "省", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000174558466}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "1104001102001101", "name": "省名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174558466}, "required": true, "description": "", "config": {}}, {"field_id": "1112001112000000", "name": "经销商编号", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {"field_id": 2200000174713831}, "required": true, "description": "", "config": {}}, {"field_id": "1294001112000000", "name": "经销商编号", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {"field_id": 2200000483871908}, "required": true, "description": "", "config": {}}, {"field_id": "1240001114000000", "name": "AMB项目类型", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {}}, {"field_id": "1240001126000000", "name": "业务AMB团队的收入计算规则", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {}}, {"field_id": "1240001124000000", "name": "是否有成本", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "1240001125000000", "name": "成本计算说明", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {}}, {"field_id": "1240001129000000", "name": "收入计算类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "A"}, {"id": "5", "name": "A（判断成本）"}, {"id": "2", "name": "B"}, {"id": "3", "name": "C"}, {"id": "4", "name": "D（不计算）"}]}}, {"field_id": "1240001120000000", "name": "存量项目-成本部分比例", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1240001121000000", "name": "存量项目-超出成本部分比例", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1240001122000000", "name": "增量项目-成本部分比例", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1240001123000000", "name": "增量项目-超出成本部分比例）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1240001127000000", "name": "B类-成本比例", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000458897953}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1330001112000000", "name": "阶段名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486321903}, "required": false, "description": "", "config": {}}, {"field_id": "1330001113000000", "name": "阶段提示", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {"field_id": 2200000486321903}, "required": false, "description": "", "config": {}}, {"field_id": "1330001115000000", "name": "序号", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000486321903}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}]}