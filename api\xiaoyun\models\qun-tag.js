module.exports = {
  collectionName: 'qun-tag',
  info: {
    name: 'QunTag',
    label: '群标签',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      type: 'string',
      unique: true
    },
    // type: {
    //   label: '标签类型',
    //   type: 'string',
    //   options: [
    //     {
    //       label: '群',
    //       value: 'qun'
    //     },
    //     {
    //       label: '成员',
    //       value: 'member'
    //     },
    //     {
    //       label: '客户',
    //       value: 'customer'
    //     }
    //   ],
    // },
    roles: {
      label: '适用角色',
      plugin: 'users-permissions',
      collection: 'role',
      configurable: false
    }
  }
}
