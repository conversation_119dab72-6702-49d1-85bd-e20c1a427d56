{"info": {"name": "permission", "label": "接口权限", "description": "基于Controller和Action来控制接口的访问权限"}, "pluginOptions": {"content-manager": {"visible": false}}, "attributes": {"type": {"type": "string", "required": true, "configurable": false}, "controller": {"type": "string", "required": true, "configurable": false}, "action": {"type": "string", "required": true, "configurable": false}, "enabled": {"type": "boolean", "required": true, "configurable": false}, "policy": {"type": "string", "configurable": false}, "role": {"model": "role", "via": "permissions", "plugin": "users-permissions", "configurable": false}}}