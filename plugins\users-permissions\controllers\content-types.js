'use strict'

const { validateKind } = require('./validation')
const {
  isNil, upperFirst,
  pick, has, prop, getOr, concat
} = require('lodash/fp')
const { keys, isUndefined, omit, uniqBy, isRegExp } = require('lodash')
const pluralize = require('pluralize')
const { contentTypes: contentTypesUtils } = require('accel-utils')
const { mountMingdaoModel } = require('strapi/utils/mingdao-utils')
const { sanitizeEntity } = require('accel-utils/lib')
const { getTimestampsAttributes, isMediaAttribute, isSingleType, } = require('accel-utils').contentTypes
const { PUBLISHED_AT_ATTRIBUTE } = require('accel-utils').contentTypes.constants
const modelConfig = strapi.config.get('plugins.usersPermissions.modelConfig')

const formatContentTypeLabel = contentType => {
  const name = prop('info.name', contentType) || contentType.modelName
  if (contentType.info.label) return contentType.info.label
  try {
    return isSingleType(contentType)
      ? upperFirst(name)
      : upperFirst(pluralize(name))
  } catch (error) {
    // in case pluralize throws cyrillic characters
    return upperFirst(name)
  }
}

const formatAttributes = model => {
  return keys(model.attributes).reduce((acc, key) => {
    acc[key] = formatAttribute(key, model.attributes[key], { model })
    return acc
  }, {})
}

const formatAttribute = (key, attribute, { model }) => {
  // Normal Type
  if (has('type', attribute)
    && attribute.type !== 'relation'
    && attribute.type !== 'media'
    && attribute.type !== 'component'
  ) return attribute

  const shortTypes = ['string', 'number', 'integer', 'biginteger', 'float', 'decimal', 'datetime', 'uid']
  const _getModelDefaultMainField = (relationModel) => {
    if (!relationModel) return 'id'
    const defaultMainField = relationModel?.options.defaultMainField
    let firstNameKey
    for (let key in relationModel.attributes) {
      if (key === 'id') continue
      if (shortTypes.includes(relationModel?.attributes[key].type || '')) {
        !firstNameKey && (firstNameKey = key)
      }
    }
    return defaultMainField || firstNameKey || 'id'
  }

  // Component Type
  if (attribute.type === 'component') {
    const relationModel = strapi.getModel(attribute.component)
    const defaultMainField = _getModelDefaultMainField(relationModel)
    return {
      ...attribute,
      mainField: attribute.mainField || defaultMainField,
      targetModel: attribute.component
    }
  }

  // Media Type
  if (isMediaAttribute(attribute) || attribute.type === 'media') {
    const multiple = isUndefined(attribute.multiple) ? !!attribute.collection : attribute.multiple
    return {
      ...attribute,
      type: 'media',
      multiple: multiple,
      required: !!attribute.required,
      allowedTypes: attribute.allowedTypes,
      pluginOptions: attribute.pluginOptions,
    }
  }

  // Relation Type
  const relation = (model.associations || []).find(assoc => assoc.alias === key)
  if (!relation) {
    const errorMsg = `Model [${model.info.name}] Relation [${key}] not found`
    console.error(errorMsg)
    // 异常情况临时兼容
    return {
      ...attribute,
      type: 'relation',
      error: errorMsg
    }
  }
  // Model Relation Main Field
  const relationModel = strapi.getModel(relation.targetUid)
  const defaultMainField = _getModelDefaultMainField(relationModel)
  return {
    ...attribute,
    type: 'relation',
    targetModel: relation.targetUid,
    relationType: relation.nature,
    pluginOptions: attribute.pluginOptions,
    mainField: attribute.mainField || defaultMainField
  }
}

const isVisible = model => getOr(true, 'pluginOptions.content-manager.visible', model) === true

function toContentManagerModel (contentType) {
  let primaryAttributes
  // 临时扩展 primaryKey 不为 id 的情况
  // 用于支持外部数据库与模型
  if (contentType.orm === 'mongoose' && contentType.primaryKey !== '_id') {
    primaryAttributes = {
      [contentType.primaryKey]: {
        type: contentType.primaryKeyType,
      },
    }
  } else if (contentType.orm === 'mingdao') {
    primaryAttributes = {
      id: {
        ref: 'rowid',
      },
    }
  } else {
    primaryAttributes = {
      id: {
        type: contentType.primaryKeyType,
      },
    }
  }
  const formatedAttributes = formatAttributes(contentType)
  const timestampAttributes = getTimestampsAttributes(contentType)
  return {
    ...contentType,
    apiID: contentType.modelName,
    isDisplayed: isVisible(contentType),
    info: {
      ...contentType.info,
      label: formatContentTypeLabel(contentType),
    },
    attributes: {
      ...primaryAttributes,
      ...formatedAttributes,
      ...timestampAttributes,
    },
    associations: contentType?.associations?.map(lv1Assoc => {
      // 明道云处理 - 为避免回环引用,限制多级模型关联展开
      if (lv1Assoc.associationModel) {
        const limitAssocModel = {
          ...omit(lv1Assoc.associationModel, ['associations']),
          associations: lv1Assoc.associationModel.associations.map(lv2Assoc => {
            return omit(lv2Assoc, ['associationModel'])
          })
        }
        return {
          ...omit(lv1Assoc, ['associationModel']),
          associationModel: limitAssocModel
        }
      }
      return lv1Assoc
    }),
    ...(contentType.modelExt ? { modelExt: contentType.modelExt } : {}),
    viewSettings: {
      defaultViewType: modelConfig?.viewSettings?.defaultViewType || 'table',
      defaultViewConfig: {
        optStyle: '2',
        userViewEnabled: true,
        ...(modelConfig?.viewSettings?.defaultViewConfig || {})
      }
    }
  }
}

function findContentType (id) {
  // uid match
  let contentType = strapi.getModel(id)
  return isNil(contentType) ? contentType : toContentManagerModel(contentType)
}

function findComponent (id) {
  // uid match
  let component = strapi.components[id]
  // modelName match
  if (!component && id !== '*') {
    for (let key in strapi.components) {
      const item = strapi.components[key]
      if (item?.modelName === id) {
        component = item
        break
      }
    }
  }
  return isNil(component) ? component : toContentManagerModel(component)
}

function getModel (id) {
  // Model - ContentType
  const contentType = findContentType(id)

  // Model - Component
  const component = findComponent(id)
  return contentType || component
}

function objectDeepFilter (model, fields) {
  // 深度克隆和过滤函数
  function deepFilter (obj) {
    if (Array.isArray(obj)) {
      // 处理数组
      return obj.map(deepFilter)
    } else if (obj && typeof obj === 'object') {
      // 处理对象
      const filtered = {}
      for (const key in obj) {
        if (fields.includes(key)) {
          // 忽略 modelExt 和 control
          continue
        }
        filtered[key] = deepFilter(obj[key])
      }
      return filtered
    }
    // 基本类型直接返回
    return obj
  }

  // 从顶层对象开始过滤
  return deepFilter(model)
}

const modelDtoFields = [
  'uid',
  'orm',
  'isDisplayed',
  'apiID',
  'kind',
  'category',
  'info',
  'options',
  'pluginOptions',
  'attributes',
  'pluginOptions',
  'primaryKey',
  'primaryKeyType',
  'associations',
  'modelExt',
  'viewSettings',
]
const modelToDto = pick(modelDtoFields)

module.exports = {
  async findOneModel (ctx) {
    const { id } = ctx.params
    let model = getModel(id)

    // 明道云模型动态加载
    if (!model) {
      model = await mountMingdaoModel(id)
    } else if (model.syncModel) {
      model = await model.syncModel()
    }
    if (model.orm === 'mingdao') {
      model = toContentManagerModel(model)
    }

    if (!model) {
      return ctx.notFound(`Model ${id} not found`)
    }

    // 单个模型精简明道云数据
    const modelData = modelToDto(model)

    const queryFilters = strapi.config.permission.queryFilters
    let queryOptions = [];
    if (queryFilters) {
      for (let queryFilter of queryFilters) {
        if (queryFilter?.model?.includes(id)) {
          queryOptions = queryOptions.concat(queryFilter.queryOptions)
        }
      }
      queryOptions = uniqBy(queryOptions, 'value')
    }
    modelData.queryOptions = queryOptions

    ctx.body = objectDeepFilter(modelData, ['modelExt','control', 'allAttributes'])
  },

  async manualRefresh(ctx) {
    const {id} = ctx.params
    await mountMingdaoModel(id, {manualRefresh: true})
    return "successRefreshModel"
  },

  async findContentTypes (ctx) {
    // Model - ContentType
    const { kind } = ctx.query
    try {
      await validateKind(kind)
    } catch (error) {
      return ctx.send({ error }, 400)
    }
    let contentTypes = Object.values(strapi.contentTypes).map(toContentManagerModel)
    if (kind) {
      contentTypes = contentTypes.filter(contentTypesUtils.isKind(kind))
    }

    // Model - Component
    const components = Object.values(strapi.components).map(toContentManagerModel)
    const models = [
      ...contentTypes,
      ...components
    ]
    ctx.body = { data: objectDeepFilter(models.map(modelToDto), ['control', 'allAttributes']) }
  },

  async getRowRelationRows (ctx) {
    const { modelId, rowId, targetField, limit, offset } = ctx.request.query
    let model = strapi.db.getModel(modelId)

    const attr = model.attributes[targetField]

    const entityManager = strapi.plugins['users-permissions'].services['entity-manager']
    let entity = await entityManager.findOne(rowId, modelId, [])

    // 明道云处理
    if (model.orm === 'mingdao') {
      return await model.getRowRelationRows({
        modelId: modelId,
        rowId: rowId,
        controlId: targetField,
        limit: limit,
        offset: offset,
      })
    }

    // 字段关联模型获取
    const targetModels = strapi.db.getModelsByAttribute(attr)
    const targetModel = targetModels[0]
    let relations = []
    if (attr.type === 'dynamiczone') {
      // 多模型关联 待实现 ...
      relations = []
    } else {
      // 单模块关联 component model collection
      const relationIds = entity[targetField] || []
      relations = relationIds.length > 0 ? await entityManager.find({
        id_in: relationIds
      }, targetModel.uid, []) : []
    }
    // 模型字段过滤
    relations = relations.map(entity => sanitizeEntity(entity, { model: targetModel }))
    return relations
  },

  async findRelations (ctx) {
    const { model: id, targetField } = ctx.params
    const { ...query } = ctx.request.query
    const { idsToOmit } = ctx.request.body

    if (query?._q === '') {
      delete query?._q
    }

    if (!targetField) {
      return ctx.badRequest()
    }

    let model = strapi.db.getModel(id)

    if (!model) {
      return ctx.notFound('model.notFound')
    }

    let attr = model.attributes[targetField]
    // Ref 映射处理
    if (!attr) {
      for (let key of Object.keys(model.attributes)) {
        const attribute = model.attributes[key]
        if (attribute.ref === targetField) {
          attr = attribute
        }
      }
    }

    if (!attr) {
      return ctx.badRequest('targetField.invalid')
    }

    let target = strapi.db.getModelByAssoc(attr)

    if (!target) {
      return ctx.notFound('target.notFound')
    }

    if (idsToOmit && Array.isArray(idsToOmit)) {
      query._where = query._where || {}
      query._where.id_nin = concat(query._where.id_nin || [], idsToOmit)
    }

    // 增加租户过滤策略
    const branch = ctx.state.user.pBranch
    // 当前用户存在租户 && 当前数据模型存在 pBranch 字段
    if (branch && target.attributes.pBranch) {
      if (model.attributes.pBranch) {
        query.pBranch = branch.id
      }
    }

    const entityManager = strapi.plugins['users-permissions'].services['entity-manager']
    let entities = []

    if (has('_q', query)) {
      entities = await entityManager.search(query, target.uid)
    } else {
      entities = await entityManager.find(query, target.uid)
    }

    // 明道云 MainField 处理 - 临时方案
    // 优先级 1.abstract 2.mainField 3.name
    entities.forEach(e => {
      e[attr.mainField] = e.abstract || e[attr.mainField] || e.name
    })

    if (!entities) {
      return ctx.notFound()
    }

    // 暂时禁用 pick fields - always false
    if (!ctx) {
      const pickFields = [attr.mainField, 'id', target.primaryKey, PUBLISHED_AT_ATTRIBUTE]
      ctx.body = entities.map(pick(pickFields))
    }

    // 模型字段过滤
    entities = entities.map(entity => sanitizeEntity(entity, { model: target }))
    ctx.body = entities
  },
}
