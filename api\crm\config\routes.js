const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/managers',
      controller: 'manager'
    }),
    ...createDefaultRoutes({
      basePath: '/manager-groups',
      controller: 'manager-group'
    }),
    ...createDefaultRoutes({
      basePath: '/manager-units',
      controller: 'manager-unit'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-contacts',
      controller: 'customer-contact'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-services',
      controller: 'customer-service'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-inactive-pools',
      controller: 'customer-inactive-pool'
    }),
    ...createDefaultRoutes({
      basePath: '/facilitate-customers',
      controller: 'facilitate-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-sales-follow-records',
      controller: 'customer-sales-follow-record'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-service-follow-records',
      controller: 'customer-service-follow-record'
    }),
    ...createDefaultRoutes({
      basePath: '/archive-sales-follow-records',
      controller: 'archive-sales-follow-record'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-projects',
      controller: 'sales-project'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-project-items',
      controller: 'sales-project-item'
    }),
    ...createDefaultRoutes({
      basePath: '/manager-problems',
      controller: 'manager-problem'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-projects',
      controller: 'bidding-project'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-project-publics',
      controller: 'bidding-project-public'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-s1-projects',
      controller: 'bidding-s1-project'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-s1-project-logs',
      controller: 'bidding-s1-project-log'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-s2-projects',
      controller: 'bidding-s2-project'
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-project-configs',
      controller: 'bidding-project-config',
    }),
    ...createDefaultRoutes({
      basePath: '/bidding-project-analyses',
      controller: 'bidding-project-analysis'
    }),
    ...createDefaultRoutes({
      basePath: '/recycle-unit-details',
      controller: 'recycle-unit-detail'
    }),
    ...createDefaultRoutes({
      basePath: '/recycle-clue-details',
      controller: 'recycle-clue-detail'
    }),
    {
      method: 'POST',
      path: '/managers/actions/relationMangerUser',
      handler: 'manager.relationMangerUser',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/managers/actions/syncQxUser',
      handler: 'manager.syncQxUser',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/managers/actions/syncBusinessQxUser',
      handler: 'manager.syncBusinessQxUser',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/external/customer-services/updateCustomFields',
      handler: 'customer-service.updateCustomFields',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/external/customer-services/updateMingdaoSchool',
      handler: 'customer-service.updateMingdaoSchool',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/external/customer-services/getSalesFollowCustomers',
      handler: 'customer-service.getSalesFollowCustomers',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/customer-services/action/distributeClueCustomer',
      handler: 'customer-service.distributeClueCustomer',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/customer-services/action/searchCustomerByKey',
      handler: 'customer-service.searchCustomerByKey',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/bidding-projects/action/initMingdaoBiddingProject',
      handler: 'bidding-project.initMingdaoBiddingProject',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/bidding-projects/action/resetBiddingProjectPreprocessed',
      handler: 'bidding-project.resetBiddingProjectPreprocessed',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/archive-sales-follow-records/action/syncMidRecordByMingDao',
      handler: 'archive-sales-follow-record.syncMidRecordByMingDao',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/archive-sales-follow-records/action/archiveRecord',
      handler: 'archive-sales-follow-record.archiveRecord',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/bidding-s1-projects/action/exportRelationData',
      handler: 'bidding-s1-project.exportRelationData',
      config: {
        policies: [],
        prefix: ''
      }
    },
  ]
}
