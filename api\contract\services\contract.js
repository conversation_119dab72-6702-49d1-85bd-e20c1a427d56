const axios = require('axios');
const _ = require('lodash');
const { createCacheFunction } = require('../../crm-mid/utils/cache')
const { getToken } = require('../../xiaoyun/utils/goToken')

async function getOrgUser() {
    let token = await getToken('org')
    const allList = await axios.get(`http://org.yunxiao.io/node/list_all?__go_token=${token}`).then(res => res.data)
    let userMap = {}
    for (const departmentName in allList?.['爱云校']?.[2]) {
        _getOrgUser(userMap, allList?.['爱云校']?.[2][departmentName])
    }
    return userMap
}

function _getOrgUser(userMap, groupMap) {
    let users = []
    if (!_.isEmpty(groupMap[2])) {
        for (const groupName in groupMap[2]) {
            users = users.concat(_getOrgUser(userMap, groupMap[2][groupName]))
        }
    }

    let groupUsers = []
    if (!_.isEmpty(groupMap[3])) {
        for (let key in groupMap[3]) {
            if (!userMap[key]) {
                userMap[key] = {
                    name: groupMap[3][key]?.[0],
                    customId: key,
                    no: groupMap[3][key]?.[4],
                    users: [key]
                }
            }
            groupUsers.push(key)
        }
    }

    let leaders = []
    if (!_.isEmpty(groupMap[1])) {
        for (let key in groupMap[1]) {
            if (!userMap[key]) {
                userMap[key] = {
                    name: groupMap[1][key]?.[0],
                    customId: key,
                    no: groupMap[1][key]?.[4],
                    users: [key]
                }
            }
            leaders.push(key)
            userMap[key].users = _.uniq(userMap[key].users.concat(groupUsers, users));
        }
    }

    return users.concat(groupUsers, leaders)
}

module.exports = {
    getOrgUser: createCacheFunction(getOrgUser, 1 * 60 * 60 * 1000)
}

