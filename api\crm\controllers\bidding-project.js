const _ = require("lodash");
const { CurdRouter } = require("accel-utils");
const { getOrgUser } = require("../../contract/services/contract");
const {
  preparedBiddingProjectAnalysis, scanBiddingProject
} = require("../services/bidding-s1-handle");
const { isUndefinedOrEmpty } = require("../utils/lodashUplevel");
const {retry} = require("../utils/requestRetry");
const curdRouter = new (class extends CurdRouter {
  // ...
  async _getQueryByUser(query, user) {
    // 新经理为本人 没有运营组或没有直营组 的学校
    let roles = user.roles.map((e) => e.type);
    let allowRoles = [
      "admin",
      "SuperAdmin",
      "bossSalesManagement",
      "bidding-project-owner",
      "CEO",
    ];
    if (_.intersection(roles, allowRoles).length === 0) {
      const allUsers = await getOrgUser();
      const qxIds = allUsers?.[user.customId]?.users || [];
      const users = await strapi
        .query("user", "users-permissions")
        .find({ customId_in: qxIds }, []);
      const mingdaoIds = users.map((e) => e.mingdaoId);
      query._where = {
        _or: [
          {
            manager_in: mingdaoIds,
          },
          {
            facilitator_in: mingdaoIds,
          },
          {
            bidder_in: mingdaoIds,
          },
        ],
      };
    }
    return query;
  }

  async count(ctx) {
    const user = ctx.state.user;
    let { query } = this._parseCtx(ctx);
    query = await this._getQueryByUser(query, user);
    return super.count(ctx);
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = await this._getQueryByUser(query, user)
    return super.find(ctx)
  }

  async update(ctx) {
    const { data } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    const result = await super.update(ctx)
    return result
  }

  async updateMany(ctx) {
    const { data: { filter, data } } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    const result = await super.updateMany(ctx)
    return result
  }

  async create(ctx) {
    const { data } = this._parseCtx(ctx);
    if (ctx.state.user.mingdaoId) {
      data.operator = [ctx.state.user.mingdaoId];
    }
    const arr = await strapi.query(this.modelName).find({
      jianyuPageId_eq: data.jianyuPageId,
    });
    if (arr.length) {
      return { resultCode: -2, message: "记录已存在，忽略插入。" };
    }
    return strapi.query(this.modelName).create(data);
  }

})("bidding-project");


let initResult
/**
 * 数据初始化扫描
 * @param ctx
 * @returns {Promise<string>}
 */
async function initMingdaoBiddingProject(ctx = {}) {
  if(!await countInitBiddingProjectCount())
    return;
  if(initResult){
    return initResult
  }
  initResult = retry(initProcessing,3,0).finally(()=>{
    initResult = null
  })
  return initResult
}

async function initProcessing(){

  // 先将明道云的数据同步到mongoose的中间表
  await updateBiddingProjectToMongoDB()

  // 扫描要合并的记录，并一并同步到mingdao的数据库
  await scanBiddingProject()
}

async function countInitBiddingProjectCount(ctx = {}) {
  const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
  let countNum = await strapi.query("bidding-project").count({
    ctime_gt: sixMonthsAgo,
    preprocessed_ne: true,
    ignore_ne: true
  })
  if (countNum > 0) {
    return true;
  }
  return false;
}

async function updateBiddingProjectToMongoDB() {

  const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
  for (let j = 1; j < 40000; j++) {
    let result = await retry(strapi.query("bidding-project").find, 3 , 0,{
      _where: {
        ctime_gt: sixMonthsAgo,
        preprocessed_ne: true,
        ignore_ne: true
      },
      _sort: "ctime:ASC",
      _start: (j - 1) * 100,
      _limit: 100,
    })
    if (isUndefinedOrEmpty(result)) break;

    let handleResult = result.map((item) => {
      return preparedBiddingProjectAnalysis(item);
    })

    result = []
    // 过滤掉已存在的记录
    let existingRecords = await retry(strapi.query("bidding-project-analysis").find,3, 0,{
      _where: {
        biddingProjectId_in: handleResult.map(item => item.biddingProjectId)
      }
    });

    let existingIds = existingRecords.map(record => record.biddingProjectId);
    existingRecords = []
    handleResult = handleResult.filter(item => !existingIds.includes(item.biddingProjectId));

    if (handleResult.length === 0) {
      continue;
    }
    await strapi.query("bidding-project-analysis").model.insertMany(handleResult)
    handleResult = []
  }
}

async function resetBiddingProjectPreprocessed(ctx) {

  const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
  for (let j = 1; j < 40; j++) {
    let arr = await strapi.query("bidding-project").find({
      _where: {
        preprocessed: true
      },
      _limit: 100,
      _start: (j - 1) * 100,
      _sort: "ctime:ASC"
    })
    if (arr.length === 0) {
      continue;
    }
    let arr_ids = arr.map((x) => x.id);
    await strapi.query("bidding-project").model.updateMany({
      ids: arr_ids,
    }, {
      preprocessed: false
    })
  }
}

module.exports = {
  ...curdRouter.createHandlers(),
  initMingdaoBiddingProject,
  resetBiddingProjectPreprocessed
};
