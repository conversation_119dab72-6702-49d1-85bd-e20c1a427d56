'use strict'
const { CurdRouter } = require('accel-utils')
const { ObjectID } = require('mongodb')
const { sendQxNoticeBoss } = require('../../xiaoyun/utils/goToken')
const { inRange } = require('lodash')
const _ = require('lodash')

const curdRouter = new (class extends CurdRouter {
})('data-comment')

async function listDetail (ctx) {
  const { model, dataId } = ctx.request.query
  if (!model || !dataId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let commentList = await strapi.query('data-comment').find({
    model: model,
    dataId: dataId,
    user_null: false,
    deleted_ne: true
  }, ['user', 'replies', 'target'])

  return ctx.wrapper.succ(commentList)
}

async function addComment (ctx) {
  const { model, dataId, content, at, parent, target, notice } = ctx.request.body
  if (!model || !dataId || !content) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  let modelObj = await strapi.query(model).model
  let data = {
    model: model,
    collectionName: modelObj.collectionName,
    user: ctx.state.user.id,
    dataId: dataId,
    content,
    at,
  }

  if (parent) {
    if (!target) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误，parent 存在时，target 不为空')
    }
    // 回复
    let parentObj = await strapi.query('data-comment').findOne({
      id: parent
    }, [])
    if (!parentObj) {
      return ctx.wrapper.error('PARAMETERS_ERROR', '回复的评论不存在')
    }
    data.parent = parent
    data.target = target
  }

  let comment = await strapi.query('data-comment').create(data)
  // 评论成功后，发送boss流程通知
  noticeUsers(at, parent ? target : null, notice, ctx.state.user)
  return ctx.wrapper.succ(comment)
}

async function deleteComment (ctx) {
  const { id } = ctx.request.body
  if (!id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let comment = await strapi.query('data-comment').findOne({
    id: id
  })
  if (!comment) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '评论不存在')
  }
  if (comment.user.id !== ctx.state.user.id) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '无权删除')
  }

  await strapi.query('data-comment').update({ id: id }, { deleted: true })
  return ctx.wrapper.succ()
}

async function noticeUsers (at, replyTarget, notice, userId) {
  if (!notice || !notice.title) return
  // 要通知的 users
  // 回复、评论@
  let userIds = []
  if (at && at.length) {
    userIds = userIds.concat(at)
  }
  if (replyTarget) {
    userIds.push(replyTarget)
  }
  // at replayTarget 去重。 去除自己
  userIds = _.uniq(userIds).filter(user => user !== userId)
  if (!userIds.length) return
  // 仅通知企信用户
  const users = await strapi.query('user', 'users-permissions').find({
    id_in: userIds,
    provider: 'yxWeCom',
    customId_null: false
  }, [])
  if (!users || !users.length) return

  console.log('sendQxNoticeBoss before', users)

  await sendQxNoticeBoss(users.map(user => user.customId), notice)
}

module.exports = {
  listDetail,
  addComment,
  deleteComment,
  ...curdRouter.createHandlers(),
}
