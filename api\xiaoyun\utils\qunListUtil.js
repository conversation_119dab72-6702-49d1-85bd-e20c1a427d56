const { ObjectId } = require('mongodb')
const { getUserGroup } = require('../../crm/controllers/manager-group')

async function getAllQunList (customers) {
  const userMap = getUserMap(customers)

  const adminRole = ['SuperAdmin', 'qun-admin', 'admin', 'service-worker', 'service-admin', 'sales-admin', 'sales-manager', 'sales-observer']
  const leadRole = ['service-group-leader', 'sales-group-leader']
  const memberRole = ['service-group-member', 'sales-group-member']
  const agentRole = ['agent-admin']
  const agentQunIds = await getAgentQunIds()

  let qunTags = await strapi.query('qun-tag').model.find({})
  const qunTagRoleIdMap = {}
  for (const qunTag of qunTags) {
    let targetRoles = qunTag.roles
    if (targetRoles && targetRoles.length > 0) {
      for (const targetRole of targetRoles) {
        if (qunTagRoleIdMap[targetRole.toString()]) {
          qunTagRoleIdMap[targetRole.toString()].push(qunTag._id)
        } else {
          qunTagRoleIdMap[targetRole.toString()] = [qunTag._id]
        }
      }
    }
  }

  let allTagQunMap = await getTagQunMap(qunTags)

  let roles = await strapi.query('role', 'users-permissions').model.find({})
  const roleIdMap = {}
  for (const role of roles) {
    roleIdMap[role.id] = {
      ...role.toJSON(),
      qunTags: qunTagRoleIdMap[role.id] || []
    }
  }

  let users = await strapi.query('qx-user').model.find({
    provider: 'yxWeCom',
    roles: { $in: roles.map(e => e.id) }
  }, {
    customId: 1, roles: 1, role: 1
  })

  let memberGroup = await getUserGroup()

  const userQunMap = {}
  for (const user of users) {
    const userRoleMap = {}
    for (const roleId of user.roles) {
      const role = roleIdMap[roleId.toString()]
      if (!role) continue
      let roleType = roleIdMap[roleId.toString()].type
      let qunList = []
      if (adminRole.includes(roleType)) {
        userRoleMap[roleType] = '*'
        continue
      } else if (leadRole.includes(roleType)) {
        qunList = getGroupList(memberGroup, userMap, user.id)
      } else if (memberRole.includes(roleType)) {
        qunList = userMap[user.id]
      } else if (agentRole.includes(roleType)) {
        userRoleMap[roleType] = agentQunIds
      } else {
        qunList = await getQunIdsByRoleTags(role, allTagQunMap)
      }
      if (qunList && qunList.length) {
        userRoleMap[roleType] = qunList
      }
    }
    if (Object.keys(userRoleMap).length) {
      userQunMap[user.customId] = userRoleMap
    }
  }

  return userQunMap
}

async function getTagQunMap (tags) {
  let qunList = await strapi.query('xiaoyun-qun').model.find({
    qunGroup: 1,
    isDeleted: { $ne: true },
    qunTags: { $in: tags.map(e => e._id) }
  }, { qunId: 1, qunTags: 1 })
  console.log('getTagQunMap', qunList.length)

  const qunMap = {}
  for (const qun of qunList) {
    const tags = qun.qunTags || []
    for (const tag of tags) {
      if (qunMap[tag.toString()]) {
        qunMap[tag.toString()].push(qun.qunId)
      } else {
        qunMap[tag.toString()] = [qun.qunId]
      }
    }
  }
  return qunMap
}

async function getQunIdsByRoleTags (role, allTagQunMap) {
  let tags = role.qunTags || []
  let qunList = new Set()
  if (tags && tags.length > 0) {
    for (const tag of tags) {
      const tagQunList = allTagQunMap[tag.toString()] || []
      tagQunList.forEach(qun => qunList.add(qun))
    }
  }
  return [...qunList]
}

function getGroupList (memberGroup, userMap, userId) {
  let group = memberGroup.find(e => e.leader && e.leader.id.toString() === userId)
  let list = new Set()
  if (group) {
    let members = group.members.map(item => item.id.toString())
    for (const member of members) {
      const memberQun = userMap[member.toString()] || []
      memberQun.forEach(qun => list.add(qun))
    }
  }
  return [...list]
}

async function getAgentQunIds () {
  let agentQuns = await strapi.query('xiaoyun-qun').model.find({ qunGroup: 2, isDeleted: { $ne: true } }, { qunId: 1 })
  return agentQuns.map(e => e.qunId)
}

function getUserMap (customers) {
  const userMap = {}

  for (const customer of customers) {
    let serviceManager = customer.directServiceManager?.toString() || null
    let salesManager = customer.directSalesManager?.toString() || null
    if (!serviceManager && !salesManager) {
      continue
    }
    if (serviceManager === salesManager) {
      salesManager = null
    }

    if (userMap[serviceManager]) {
      userMap[serviceManager].push(customer.qunId)
    } else {
      userMap[serviceManager] = [customer.qunId]
    }

    if (userMap[salesManager]) {
      userMap[salesManager].push(customer.qunId)
    } else {
      userMap[salesManager] = [customer.qunId]
    }
  }

  return userMap
}

module.exports = {
  getAllQunList
}
