const verify = require('jsonwebtoken')
const bcrypt = require('bcryptjs')
const crypto = require('crypto')
const JWT_DEFAULT_EXPIRES = 3600 * 24 * 7
const JWT_DEFAULT_SECRET = 'nq2asABbQHNH9e0NcWYL'

// JWT 生成
function jwtGen (data, secret, options = { expiresIn: JWT_DEFAULT_EXPIRES }) {
  return verify.sign({
    data: data,
  }, secret || JWT_DEFAULT_SECRET, options)
}

// 验证 JWT 有效性
function jwtVerify (tokenStr, secret) {
  return new Promise((resolve, reject) => {
    verify.verify(tokenStr, secret, function (err, decoded) {
      if (err) reject(err)
      resolve(decoded)
    })
  })
}

// 密码加密
async function passwordBcrypt (password) {
  const saltRounds = 10
  return await bcrypt.hash(password, saltRounds)
}

// 密码对比
async function passwordCompare (password, hash) {
  return await bcrypt.compare(password, hash)
}

// 创建随机 Token
const createRandomToken = (length = 20) => {
  return crypto.randomBytes(length).toString('hex')
}

// sha1 加密
const sha1 = (str) => {
  const sha1 = crypto.createHash('sha1')
  sha1.update(str)
  return sha1.digest('hex')
}

module.exports = {
  jwtGen,
  jwtVerify,
  passwordBcrypt,
  passwordCompare,
  createRandomToken,
  sha1,
}
