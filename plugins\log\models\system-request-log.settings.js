module.exports = {
  kind: 'collectionType',
  collectionName: 'SystemRequestLog',
  info: {
    name: 'SystemRequestLogs',
    label: '系统请求日志',
    description: '系统请求日志，包含请求URL、参数、请求时间、请求耗时等信息'
  },
  options: {
    increments: true,
    timestamps: false,
    draftAndPublish: false,
    indexes: [
      {
        keys: {
          requestAt: -1,
          responseTime: -1,
          method: 1,
          originalUrl: 1,
          userAgent: 1,
          userId: 1,
        }
      }
    ]
  },
  pluginOptions: {},
  attributes: {
    ip: {
      label: 'IP',
      type: 'string'
    },
    host: {
      label: 'Host',
      type: 'string'
    },
    originalUrl: {
      label: '原始URL',
      type: 'string'
    },
    method: {
      label: '请求方法',
      type: 'string',
      options: [
        {
          label: 'GET',
          value: 'GET'
        },
        {
          label: 'POST',
          value: 'POST'
        },
        {
          label: 'PUT',
          value: 'PUT'
        },
        {
          label: 'DELETE',
          value: 'DELETE'
        },
        {
          label: 'PATCH',
          value: 'PATCH'
        },
        {
          label: 'OPTION',
          value: 'OPTION'
        },
        {
          label: 'HEAD',
          value: 'HEAD'
        },
        {
          label: 'CONNECT',
          value: 'CONNECT'
        }
      ]
    },
    requestBody: {
      label: '请求体',
      type: 'json'
    },
    responseBody: {
      label: '响应体',
      type: 'json'
    },
    requestAt: {
      label: '请求时间',
      type: 'datetime'
    },
    referer: {
      label: '请求来源',
      type: 'string'
    },
    responseAt: {
      label: '响应时间',
      type: 'datetime'
    },
    responseTime: {
      label: '处理时长',
      type: 'number'
    },
    userAgent: {
      label: '用户代理',
      type: 'string'
    },
    uaInfo: {
      label: '用户代理信息',
      type: 'json',
      jsonSchema: {
        title: '用户代理',
        type: 'object',
        format: 'user-agent',
        properties: {
          browser: {
            title: '浏览器',
            type: 'object',
            properties: {
              name: {
                title: '名称',
                type: 'string'
              },
              version: {
                title: '版本',
                type: 'string'
              }
            }
          },
          engine: {
            title: '引擎',
            type: 'object',
            properties: {
              name: {
                title: '名称',
                type: 'string'
              },
              version: {
                title: '版本',
                type: 'string'
              }
            }
          },
          os: {
            title: '系统',
            type: 'object',
            properties: {
              name: {
                title: '名称',
                type: 'string'
              },
              version: {
                title: '版本',
                type: 'string'
              }
            }
          },
          device: {
            title: '设备',
            type: 'object',
            properties: {
              vendor: {
                title: '厂商',
                type: 'string'
              },
              model: {
                title: '型号',
                type: 'string'
              },
              type: {
                title: '类型',
                type: 'string'
              }
            }
          }
        }
      }
    },
    userId: {
      label: '请求用户',
      plugin: 'users-permissions',
      model: 'user'
    },
    branchId: {
      label: '请求租户',
      plugin: 'users-permissions',
      model: 'branch',
      configurable: false
    }
  }
}
