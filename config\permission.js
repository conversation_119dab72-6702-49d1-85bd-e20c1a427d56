const { createDefaultPermissions } = require('accel-utils')
// app
const apps = [
  {
    id: '6747e9c5c77e9c50b1336696',
    type: 'yxWeCom',
    sId: 'wlyboss',
    name: '未来云BOSS',
    tokenSecret: 'KnRtscLyooyb1qy6aRi1LtJT',
  }
]
// 基础视图配置
const pageGroups = [
  {
    sId: 'PlatformOps',
    name: '平台运维🦸‍♂️',
    pages: [
      {
        sId: 'AccessKeyManagement', name: '访问密钥管理', icon: 'vpn_key',
        meta: {
          modelId: 'access-key',
          modelPath: 'access-keys',
        }
      },
    ],
  },
  {
    sId: 'BidingGroup',
    name: '招投标管理',
    pages: [
      {
        sId: 'BiddingProjectManagement', name: '招标信息', icon: 'pageview',
        meta: {
          modelId: 'bidding-project',
          modelPath: 'bidding-projects',
          dialogSizes: ['fullscreen'],
        }
      },
      {
        sId: 'BiddingS1ProjectManagement', name: '招标项目', icon: 'fact_check',
        meta: {
          modelId: 'bidding-s1-project',
          modelPath: 'bidding-s1-projects',
        }
      },
      {
        sId: 'BiddingS1ProjectLogManagement', name: '招标项目操作记录', icon: 'history',
        meta: {
          modelId: 'bidding-s1-project-log',
          modelPath: 'bidding-s1-project-logs',
        }
      },
      {
        sId: 'BiddingS2ProjectManagement', name: '投标项目', icon: 'business_center',
        meta: {
          modelId: 'bidding-s2-project',
          modelPath: 'bidding-s2-projects',
        }
      },
      {
        sId: 'BiddingProjectFilterManagement', name: '招投标市场信息', icon: 'wysiwyg',
        meta: {
          modelId: 'bidding-project-public',
          modelPath: 'bidding-project-publics',
        }
      },
      {
        sId: 'BiddingProjectConfigManagement',
        name: '招投标项目配置',
        icon: 'settings_applications',
        meta: {
          modelId: 'bidding-project-config',
          modelPath: 'bidding-project-configs',
        },
      },
    ]
  },
  {
    sId: 'crmGroup',
    name: '业务管理',
    icon: 'work',
    pages: [
      {
        sId: 'ContractManagement', name: '合同', icon: 'article',
        meta: {
          modelId: 'contract',
          modelPath: 'contracts',
          dialogSizes: ['fullscreen'],
        }
      },
      {
        sId: 'SalesContractManagement', name: '直营合同', icon: 'article',
        meta: {
          modelId: 'sales-contract',
          modelPath: 'sales-contracts',
        }
      },
      {
        sId: 'CashQuanManagement', name: '权益金记录', icon: 'article',
        meta: {
          modelId: 'cash-quan',
          modelPath: 'cash-quans'
        }
      },
      {
        sId: 'ContractOrderManagement', name: '订单', icon: 'article',
        meta: {
          modelId: 'order',
          modelPath: 'orders',
          dialogSizes: ['fullscreen'],
        }
      },
      {
        sId: 'bossSchoolManagement', name: 'boss学校', icon: 'article',
        meta: {
          modelId: 'boss-school',
          modelPath: 'boss-schools'
        }
      },
      {
        sId: 'bossAgentManagement', name: 'boss经销商', icon: 'article',
        meta: {
          modelId: 'boss-agent',
          modelPath: 'boss-agents'
        }
      },
      {
        sId: 'bossAppUsageConfigManagement', name: 'boss权益开通列表', icon: 'article',
        meta: {
          modelId: 'boss-app-usage-config',
          modelPath: 'boss-app-usage-configs'
        }
      },
      {
        sId: 'ContractSkuProductManagement', name: '商品关联', icon: 'article',
        meta: {
          modelId: 'sale-sku-product',
          modelPath: 'sale-sku-products'
        }
      },
      {
        sId: 'GoodsSkuProductManagement', name: '售卖中台商品', icon: 'shopping_cart',
        meta: {
          modelId: 'goods-sku-product',
          modelPath: 'goods-sku-products'
        }
      },
      {
        sId: 'CustomerAppVersionManagement', name: 'crm产品', icon: 'article',
        meta: {
          modelId: 'customer-app-version',
          modelPath: 'customer-app-versions'
        }
      },
      {
        sId: 'AgentPaymentManagement', name: '经销商缴费', icon: 'article',
        meta: {
          modelId: 'agent-payment',
          modelPath: 'agent-payments'
        }
      },
      {
        sId: 'WhiteNameManagement', name: '白名单', icon: 'article',
        meta: {
          modelId: 'white-name-list',
          modelPath: 'white-name-lists'
        }
      },
      {
        sId: 'ContractTemplateManagement', name: '合同模版管理', icon: 'article',
        meta: {
          modelId: 'contract-template',
          modelPath: 'contract-templates'
        }
      },
      {
        sId: 'CustomerHfs', name: '精准教学客户', icon: 'article',
        meta: {
          modelId: 'customer-service-hfs',
          modelPath: 'customer-service-hfs'
        }
      },
      {
        sId: 'XkwServiceApplyManagement', name: '学科网服务申请', icon: 'school',
        meta: {
          modelId: 'xkw-service-apply',
          modelPath: 'xkw-service-applies',
        }
      }
    ],
  },
  {
    sId: 'ExtendGroup',
    name: '扩展功能管理',
    icon: 'extension',
    pages: [
      {
        sId: 'ApiManagement', name: 'api库管理', icon: 'article',
        meta: {
          modelId: 'api',
          modelPath: 'apis',
        }
      },
      {
        sId: 'WorkflowManagement', name: '工作流管理', icon: 'article',
        meta: {
          modelId: 'workflow',
          modelPath: 'workflows',
        }
      },
      {
        sId: 'ProcessRecordManagement', name: '流程记录', icon: 'article',
        meta: {
          modelId: 'process-record',
          modelPath: 'process-records',
        }
      },
      {
        sId: 'DataSyncManagement', name: '数据同步任务', icon: 'article',
        meta: {
          modelId: 'data-sync',
          modelPath: 'data-syncs',
        }
      },
      {
        sId: 'DataSourceManagement', name: '数据源', icon: 'article',
        meta: {
          modelId: 'data-source',
          modelPath: 'data-sources',
        }
      },
      {
        sId: 'TextManagement', name: '合同JSON修改', icon: 'article',
        meta: {
          modelId: 'text',
          modelPath: 'texts',
        }
      },
      {
        sId: 'ThirdPartyPaymentManagement', name: '三方支付', icon: 'article',
        meta: {
          modelId: 'third-party-payment',
          modelPath: 'third-party-payments'
        }
      },
      {
        sId: 'ThirdPartyRefundManagement', name: '三方支付退费', icon: 'article',
        meta: {
          modelId: 'third-party-refund',
          modelPath: 'third-party-refunds'
        }
      },
      {
        sId: 'ChangeHistoryManagement', name: '数据变更历史', icon: 'article',
        meta: {
          modelId: 'change-history',
          modelPath: 'change-historys'
        }
      },
      {
        sId: 'QuestionCollectionManagement', name: 'Book问题收集', icon: 'article',
        meta: {
          modelId: 'question-collection',
          modelPath: 'question-collections'
        }
      },
      {
        sId: 'MessageCentreManagement', name: '信息发送记录', icon: 'article',
        meta: {
          modelId: 'message-centre',
          modelPath: 'message-centre'
        }
      },
      {
        sId: 'DbCacheManagement', name: '数据库缓存', icon: 'event_note',
        meta: {
          modelId: 'db-cache',
          modelPath: 'db-cache',
        }
      },
      {
        sId: 'HuobanQueueManagement', name: '伙伴学校更新队列', icon: 'event_note',
        meta: {
          modelId: 'huoban-queue',
          modelPath: 'huoban-queue',
        }
      },
      {
        sId: 'HuobanMsgQueueManagement', name: '伙伴订阅消息队列', icon: 'event_note',
        meta: {
          modelId: 'huoban-msg-queue',
          modelPath: 'huoban-msg-queue',
        }
      },
      {
        sId: 'MingdaoQueueManagement', name: '明道数据更新队列', icon: 'event_note',
        meta: {
          modelId: 'mingdao-queue',
          modelPath: 'mingdao-queue',
        }
      },
      {
        sId: 'DataCommentManagement', name: '数据评论列表', icon: 'comment',
        meta: {
          modelId: 'data-comment',
          modelPath: 'data-comments'
        }
      },
      {
        sId: 'BrandHuobanMenuManagement', name: '多品牌伙伴平台菜单', icon: 'menu',
        meta: {
          modelId: 'brand-huoban-menu',
          modelPath: 'brand-huoban-menus'
        }
      },
      {
        sId: 'BrandUserMenuManagement', name: '多品牌用户菜单', icon: 'menu',
        meta: {
          modelId: 'brand-user-menu',
          modelPath: 'brand-user-menus'
        }
      },
      {
        // 应用更新
        sId: 'AppUpdateManagement', name: '应用更新', icon: 'event_note',
        meta: {
          modelId: 'app-update',
          modelPath: 'app-updates',
        }
      }
    ],
  },
  {
    sId: 'ClueManagerGroup',
    name: '资源池',
    pages: [
      {
        sId: 'ClueCustomerManagement', name: '公共资源池', icon: 'waves',
        meta: {
          modelId: 'clue-customer',
          modelPath: 'clue-customers',
        }
      },
      {
        sId: 'NearCustomerManagement', name: '临期校', icon: 'waves',
        meta: {
          modelId: 'near-customer',
          modelPath: 'near-customers',
        }
      },
      {
        sId: 'UnitCustomerManagement', name: '单元池', icon: 'work',
        meta: {
          modelId: 'unit-customer',
          modelPath: 'unit-customers',
        }
      },
      {
        sId: 'ServiceGroupCustomerManagement', name: '运营组池', icon: 'dashboard',
        meta: {
          modelId: 'service-group-customer',
          modelPath: 'service-group-customers',
        }
      },
      {
        sId: 'SalesGroupCustomerManagement', name: '直营组池', icon: 'dashboard',
        meta: {
          modelId: 'sales-group-customer',
          modelPath: 'sales-group-customers',
        }
      },
      {
        sId: 'CustomerInactivePoolManagement', name: '不活跃池', icon: 'inventory_2',
        meta: {
          modelId: 'customer-inactive-pool',
          modelPath: 'customer-inactive-pools',
        }
      },
      {
        sId: 'ServiceDistributeRecordManagement', name: '运营资源流转记录', icon: 'newspaper',
        meta: {
          modelId: 'service-distribute-record',
          modelPath: 'service-distribute-records',
        }
      },
      {
        sId: 'SalesDistributeRecordManagement', name: '直营资源流转记录', icon: 'newspaper',
        meta: {
          modelId: 'sales-distribute-record',
          modelPath: 'sales-distribute-records',
        }
      },
      {
        sId: 'RecycleUnitDetailManagement', name: '回收单元池明细', icon: 'newspaper',
        meta: {
          modelId: 'recycle-unit-detail',
          modelPath: 'recycle-unit-details',
        }
      },
      {
        sId: 'RecycleClueDetailManagement', name: '回收公共池明细', icon: 'newspaper',
        meta: {
          modelId: 'recycle-clue-detail',
          modelPath: 'recycle-clue-details',
        }
      },
    ]
  },
  {
    sId: 'CustomerMdManagerGroup',
    name: '客户管理',
    pages: [
      {
        sId: 'CustomerServiceIndex', name: '我的学校', icon: 'dashboard',
        meta: {
          modelId: 'customer-service',
          modelPath: 'customer-services',
          dialogSizes: ['fullscreen', 'md'],
        }
      },
      {
        sId: 'CustomerSearchIndex', name: '学校快查', icon: 'search',
        meta: {
          modelId: 'customer-service',
          modelPath: 'customer-services',
        }
      },
      {
        sId: 'FacilitateCustomerIndex', name: '我的协助', icon: 'dashboard',
        meta: {
          modelId: 'facilitate-customer',
          modelPath: 'facilitate-customers',
          dialogSizes: ['fullscreen', 'md'],
        }
      },
      {
        sId: 'CustomerServiceFollowRecordIndex', name: '运营跟进', icon: 'support_agent',
        meta: {
          modelId: 'customer-service-follow-record',
          modelPath: 'customer-service-follow-records',
        }
      },
      {
        sId: 'CustomerSalesFollowRecordIndex', name: '直营跟进', icon: 'question_answer',
        meta: {
          modelId: 'customer-sales-follow-record',
          modelPath: 'customer-sales-follow-records',
        }
      },
      {
        sId: 'ArchiveSalesFollowRecordIndex', name: '直营归档跟进', icon: 'question_answer',
        meta: {
          modelId: 'archive-sales-follow-record',
          modelPath: 'archive-sales-follow-records',
        }
      },
      {
        sId: 'QxUserManagement', name: '企信用户管理', icon: 'assignment_ind',
        meta: {
          modelId: 'qx-user',
          modelPath: 'qx-users',
        }
      },
      {
        sId: 'ManagerGroupManagement', name: '运营/直营分组', icon: 'group',
        meta: {
          modelId: 'manager-group',
          modelPath: 'manager-groups',
        }
      },
      {
        sId: 'ManagerUnitManagement', name: '运营/直营单元', icon: 'groups',
        meta: {
          modelId: 'manager-unit',
          modelPath: 'manager-units'
        }
      },
      {
        sId: 'CustomerContactManagement', name: '客户联系人', icon: 'contacts',
        meta: {
          modelId: 'customer-contact',
          modelPath: 'customer-contacts',
        }
      },
      {
        sId: 'SalesProjectManagement', name: '直营项目管理', icon: 'format_list_bulleted',
        meta: {
          modelId: 'sales-project',
          modelPath: 'sales-projects',
          dialogSizes: ['fullscreen', 'md'],
        }
      },
      {
        sId: 'SalesProjectItemManagement', name: '直营项目明细', icon: 'details',
        meta: {
          modelId: 'sales-project-item',
          modelPath: 'sales-project-items',
        }
      },
      {
        sId: 'ManagerProblemManagement', name: '问题反馈', icon: 'event_note',
        meta: {
          modelId: 'manager-problem',
          modelPath: 'manager-problems',
        }
      },
      {
        sId: 'TelephoneRecordManagement', name: '电话拨打记录', icon: 'call',
        meta: {
          modelId: 'telephone-record',
          modelPath: 'telephone-records',
        }
      },
      {
        sId: 'SalesMonthlyProcessDashboard', name: '直营过程量看板', icon: 'event_note',
        meta: {
          modelId: 'sales-monthly-process',
          modelPath: 'sales-monthly-process',
        }
      },
    ]
  },
  {
    sId: 'CustomerManagerGroup',
    name: '客户管理（存档）',
    icon: 'archive',
    pages: [
      {
        sId: 'CustomerServiceMid', name: '学校档案', icon: 'dashboard',
        meta: {
          modelId: 'customer-service-mid',
          modelPath: 'customer-service-mids',
        }
      },
      {
        sId: 'CustomerProblemIndex', name: '客户问题集', icon: 'event_note',
        meta: {
          modelId: 'customer-problem',
          modelPath: 'customer-problems',
        }
      },
      {
        sId: 'CustomerServiceTagManagement', name: '服务标签', icon: 'tag',
        meta: {
          modelId: 'customer-service-tag',
          modelPath: 'customer-service-tags',
        }
      },
      {
        sId: 'ManagerGroupMidManagement', name: '用户分组管理', icon: 'assignment_ind',
        meta: {
          modelId: 'manager-group-mid',
          modelPath: 'manager-group-mids',
        }
      },
      {
        sId: 'CustomerOpLogManagement', name: '客户档案操作记录', icon: 'event_note',
        meta: {
          modelId: 'customer-op-log',
          modelPath: 'customer-op-logs',
        }
      }
    ],
  },
  {
    sId: 'XiaoyunManagementGroup',
    name: '小云群管理',
    pages: [
      {
        sId: 'XiaoyunQunManagement', name: '小云群列表', icon: 'person',
        meta: {
          modelId: 'xiaoyun-qun',
          modelPath: 'xiaoyun-quns',
        }
      },
      {
        sId: 'QunQrCodeManagement', name: '群二维码', icon: 'qr_code',
        meta: {
          modelId: 'qun-qrcode',
          modelPath: 'qun-qrcodes',
        }
      },
      {
        sId: 'QunTagsManagement', name: '群标签', icon: 'person',
        meta: {
          modelId: 'qun-tag',
          modelPath: 'qun-tags',
        }
      },
      {
        sId: 'QunUserManagement', name: '群成员', icon: 'person',
        meta: {
          modelId: 'qun-user',
          modelPath: 'qun-users',
        }
      },
      {
        sId: 'QunDeletedManagement', name: '已删除群列表', icon: 'delete',
        meta: {
          modelId: 'xiaoyun-qun',
          modelPath: 'xiaoyun-quns',
          query: { isDeleted_eq: true },
        }
      },
      {
        sId: 'QunActionLogManagement', name: '群操作记录', icon: 'receipt_long',
        meta: {
          modelId: 'qun-action-log',
          modelPath: 'qun-action-logs',
        }
      },
      {
        sId: 'QunStatisticManagement', name: '群统计', icon: 'analytics',
        meta: {
          modelId: 'qun-statistic',
          modelPath: 'qun-statistics',
        }
      },
      {
        sId: 'QunRobotStatManagement', name: '群机器人统计', icon: 'analytics',
        meta: {
          modelId: 'qun-robot-stat',
          modelPath: 'qun-robot-stats',
        }
      },
      {
        sId: 'QunUserStatisticManagement', name: '群个人看板', icon: 'analytics',
      },
      {
        sId: 'QunTeamStatisticManagement', name: '群小组看板', icon: 'analytics',
      },
      {
        sId: 'NoRobotQunManagement', name: '待手动进群', icon: 'person',
        meta: {
          modelId: 'no-robot-qun',
          modelPath: 'no-robot-quns',
        }
      },
    ],
  },
  {
    sId: 'NegotiationToolManagementGroup',
    name: '直营谈单工具',
    icon: 'ad_units',
    pages: [
      {
        sId: 'NegotiationToolManagement', name: '直营谈单工具', icon: 'ad_units',
        meta: { modelId: 'customer-release-records', modelPath: 'customerReleaseRecords' }
      }
    ]
  },
  {
    sId: 'InformationPageManagementGroup',
    name: '信息页面',
    icon: 'web',
    pages: [
      {
        sId: 'InformationPageManagement', name: '信息页面', icon: 'web',
        meta: { modelId: 'information-page', modelPath: 'informationPage' }
      }
    ]
  },
  {
    sId: 'InformationModuleManagementGroup',
    name: '信息模块',
    icon: 'view_module',
    pages: [
      {
        sId: 'InformationModuleManagement', name: '信息模块', icon: 'view_module',
        meta: { modelId: 'information-module', modelPath: 'informationModule' }
      }
    ]
  },
  {
    sId: 'BiddingProject',
    name: '招投标项目',
    icon: 'event_note',
    pages: [
      {
        sId: 'BiddingProjectManagement', name: '招投标项目', icon: 'event_note',
        meta: {
          modelId: 'bidding-project',
          modelPath: 'bidding-projects',
          dialogSizes: ['fullscreen', 'md'],
        }
      }
    ]
  }
]
// 基础功能配置
const functions = [
  {
    name: 'boss用户工作台',
    sId: 'BossUserFunction',
    pages: [],
    apiPermissions: []
  },
  {
    name: '中校云工作台',
    sId: 'zxCloudFunction',
    pages: [
      'ContractManagement',
    ],
    apiPermissions: [
      { 'type': 'application', 'controller': 'contract', 'action': 'find' },
      { 'type': 'application', 'controller': 'contract', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'contract', 'action': 'count' },
      { 'type': 'application', 'controller': 'contract', 'action': 'create' },
      { 'type': 'application', 'controller': 'contract', 'action': 'update' },
      { 'type': 'application', 'controller': 'contract', 'action': 'delete' },
      { 'type': 'application', 'controller': 'contract', 'action': 'export' },
      { 'type': 'application', 'controller': 'contract', 'action': 'import' },
    ]
  },
  {
    name: '业务工作台',
    sId: 'BusinessFunction',
    pages: [
      'ContractManagement',
      'BiddingProjectManagement',
      'BiddingProjectFilterManagement',
      'BiddingS1ProjectManagement',
      'BiddingS1ProjectLogManagement',
      'BiddingS2ProjectManagement',
    ],
    apiPermissions: [
      { 'type': 'application', 'controller': 'bidding-project', 'action': 'initMingdaoBiddingProject' },
      { 'type': 'application', 'controller': 'bidding-project', 'action': 'resetBiddingProjectPreprocessed' },
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-project' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-project-public' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-s1-project' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-s1-project-log' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-s2-project' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-project-analysis' }),
      { 'type': 'application', 'controller': 'bidding-s1-project', 'action': 'exportRelationData' },
    ]
  },
  {
    name: 'boss销管工作台',
    sId: 'BossSalesManagementFunction',
    pages: [
      'ContractManagement',
      'CashQuanManagement',
      'bossAppUsageConfigManagement',
      'CustomerHfs',
      'XkwServiceApplyManagement',
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'cash-quan' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-app-usage-config' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service-hfs' }),
      ...createDefaultPermissions({ type: 'application', controller: 'xkw-service-apply' }),
      { 'type': 'application', 'controller': 'contract', 'action': 'deleteProcessToBoss' },
    ]
  },
  {
    name: 'CRM数据管理',
    sId: 'CRMManagementFunction',
    pages: [
      'ContractManagement',
      'CashQuanManagement',
      'TransactionOrderManagement',
      'ContractOrderManagement',
      'bossSchoolManagement',
      'bossAgentManagement',
      'bossAppUsageConfigManagement',
      'ContractSkuProductManagement',
      'GoodsSkuProductManagement',
      'CustomerAppVersionManagement',
      'AgentPaymentManagement',
      'WhiteNameManagement',
      'ContractTemplateManagement',
      'BiddingProjectConfigManagement',
      'CustomerHfs',
    ],
    apiPermissions: [
      // 合同增删改查接口权限
      { 'type': 'application', 'controller': 'sale-sku-product', 'action': 'findMany' },
      { 'type': 'application', 'controller': 'agent-payment', 'action': 'getWxPayConfigList' },
      ...createDefaultPermissions({ type: 'application', controller: 'cash-quan' }),
      ...createDefaultPermissions({ type: 'application', controller: 'order' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-school' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-agent' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-app-usage-config' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service-hfs' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sale-sku-product' }),
      ...createDefaultPermissions({ type: 'application', controller: 'goods-sku-product' }),
      { 'type': 'application', 'controller': 'goods-sku-product', 'action': 'findMany' },
      ...createDefaultPermissions({ type: 'application', controller: 'customer-app-version' }),
      ...createDefaultPermissions({ type: 'application', controller: 'agent-payment' }),
      ...createDefaultPermissions({ type: 'application', controller: 'contract-template' }),
      ...createDefaultPermissions({ type: 'application', controller: 'bidding-project-config' }),
    ]
  },
  {
    name: '扩展功能管理',
    sId: 'ExtendManagementFunction',
    pages: [
      'ApiManagement',
      'WorkflowManagement',
      'ProcessRecordManagement',
      'DataSourceManagement',
      'DataSyncManagement',
      'ThirdPartyPaymentManagement',
      'ThirdPartyRefundManagement',
      'ChangeHistoryManagement',
      'TextManagement',
      'QuestionCollectionManagement',
      'MessageCentreManagement',
      'DbCacheManagement',
      'HuobanQueueManagement',
      'HuobanMsgQueueManagement',
      'MingdaoQueueManagement',
      'DataCommentManagement',
      'BrandHuobanMenuManagement',
      'BrandUserMenuManagement',
      'AppUpdateManagement'
    ],
    apiPermissions: [
      { 'type': 'application', 'controller': 'api', 'action': 'find' },
      { 'type': 'application', 'controller': 'api', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'api', 'action': 'count' },
      { 'type': 'application', 'controller': 'api', 'action': 'create' },
      { 'type': 'application', 'controller': 'api', 'action': 'update' },
      { 'type': 'application', 'controller': 'api', 'action': 'delete' },
      { 'type': 'application', 'controller': 'api', 'action': 'export' },
      { 'type': 'application', 'controller': 'api', 'action': 'import' },
      { 'type': 'application', 'controller': 'api', 'action': 'apiExecute' },
      { 'type': 'application', 'controller': 'text', 'action': 'updateFile' },
      { 'type': 'application', 'controller': 'data-sync', 'action': 'dataSyncExecute' },
      { 'type': 'application', 'controller': 'message-centre', 'action': 'mingdaoMsg' },
      ...createDefaultPermissions({ type: 'application', controller: 'workflow' }),
      ...createDefaultPermissions({ type: 'application', controller: 'process-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'data-sync' }),
      ...createDefaultPermissions({ type: 'application', controller: 'data-source' }),
      ...createDefaultPermissions({ type: 'application', controller: 'change-history' }),
      ...createDefaultPermissions({ type: 'application', controller: 'text' }),
      ...createDefaultPermissions({ type: 'application', controller: 'third-party-payment' }),
      ...createDefaultPermissions({ type: 'application', controller: 'message-centre' }),
      ...createDefaultPermissions({ type: 'application', controller: 'db-cache' }),
      ...createDefaultPermissions({ type: 'application', controller: 'huoban-queue' }),
      ...createDefaultPermissions({ type: 'application', controller: 'huoban-msg-queue' }),
      ...createDefaultPermissions({ type: 'application', controller: 'mingdao-queue' }),
      ...createDefaultPermissions({ type: 'application', controller: 'data-comment' }),
      ...createDefaultPermissions({ type: 'application', controller: 'brand-huoban-menu' }),
      ...createDefaultPermissions({ type: 'application', controller: 'brand-user-menu' }),
      ...createDefaultPermissions({ type: 'application', controller: 'app-update' }),
    ]
  },
  {
    name: '访问密钥管理权限',
    sId: 'AccessKeyManagementFunction',
    pages: [
      'AccessKeyManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application', controller: 'access-key',
      })
    ]
  },
  {
    name: '客户管理',
    sId: 'CustomerServiceFunction',
    pages: [
      'CustomerServiceIndex', // 学校档案
      'TelephoneRecordManagement', // 电话拨打记录
      'SalesProjectItemManagement', // 直营项目明细
      'ManagerProblemManagement', // 问题反馈
      'ClueCustomerManagement', // 公共资源池
      'CustomerInactivePoolManagement', // 不活跃池
      'CustomerSearchIndex', // 学校快查
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service' }),
      ...createDefaultPermissions({ type: 'application', controller: 'manager', role: 'read' }),
      ...createDefaultPermissions({ type: 'application', controller: 'manager-group', role: 'read' }),
      ...createDefaultPermissions({ type: 'application', controller: 'manager-unit', role: 'read' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-contact' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-sales-follow-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'archive-sales-follow-record', role: 'read' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service-follow-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'telephone-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-project' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-project-item' }),
      ...createDefaultPermissions({ type: 'application', controller: 'manager-problem' }),
      ...createDefaultPermissions({ type: 'application', controller: 'clue-customer' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-inactive-pool' }),

      { 'type': 'application', 'controller': 'customer', 'action': 'getYjSupportToken' }, // 校端超管跳转阅卷
      { 'type': 'application', 'controller': 'customer-service-mid', 'action': 'bindQunInfo' }, // 绑定/取消群绑定
      { 'type': 'application', 'controller': 'huoban', 'action': 'createHuobanSalesProject' }, // 发起伙伴云建档
      { 'type': 'application', 'controller': 'customer-service', 'action': 'distributeClueCustomer' },
      { 'type': 'application', 'controller': 'customer-service', 'action': 'searchCustomerByKey' },
    ]
  },
  {
    name: '客户管理（归档）',
    sId: 'OldCustomerServiceFunction',
    pages: [
      'CustomerServiceMid', // 学校档案
      'CustomerProblemIndex', // 客户问题集
      'CustomerServiceTagManagement', // 服务标签
      'ManagerGroupMidManagement', // 用户分组管理
      'CustomerOpLogManagement', // 客户档案操作记录
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service-mid' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-problem' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-contact' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service-tag', role: 'read' }),
      ...createDefaultPermissions({ type: 'application', controller: 'manager-group-mid' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-op-log' }),
    ]
  },
  {
    name: '直营工作台',
    sId: 'DirectSalesFunction',
    pages: [
      'CustomerSalesFollowRecordIndex', // 直营跟进
      'SalesGroupCustomerManagement', // 直营组池
      'SalesDistributeRecordManagement', // 直营资源流转记录
      'FacilitateCustomerIndex', // 我的协助
      'RecycleUnitDetailManagement', // 回收单元池明细
      'RecycleClueDetailManagement', // 回收公共池明细
      // 'ArchiveSalesFollowRecordIndex', // 直营归档跟进
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'sales-group-customer' }),
      ...createDefaultPermissions({ type: 'application', controller: 'facilitate-customer' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-distribute-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'recycle-unit-detail' }),
      ...createDefaultPermissions({ type: 'application', controller: 'recycle-clue-detail' }),
    ]
  },
  {
    name: '直营合同',
    sId: 'DirectSalesContracFunction',
    pages: [
      'SalesContractManagement', // 直营合同
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'sales-contract' }),
    ]
  },
  {
    name: '运营工作台',
    sId: 'DirectServiceFunction',
    pages: [
      'CustomerServiceFollowRecordIndex', // 运营跟进
      'ServiceGroupCustomerManagement', // 运营组池
      'ServiceDistributeRecordManagement', // 运营资源流转记录
      'RecycleClueDetailManagement', // 回收公共池明细
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'service-group-customer' }),
      ...createDefaultPermissions({ type: 'application', controller: 'service-distribute-record' }),
      ...createDefaultPermissions({ type: 'application', controller: 'recycle-clue-detail' }),
    ]
  },
  {
    name: '组长管理',
    sId: 'GroupLeaderFunction',
    pages: [
      'CustomerContactManagement', // 客户联系人
      'QxUserManagement', // 企信用户管理
      'ManagerGroupManagement', // 运营/直营分组
      'SalesMonthlyProcessDashboard', // 直营过程量看板
      'UnitCustomerManagement', // 单元池
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'manager-group' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-contact' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qx-user' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-daily-process' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-monthly-process' }),
      ...createDefaultPermissions({ type: 'application', controller: 'unit-customer' }),
      { 'type': 'application', 'controller': 'manager', 'action': 'relationMangerUser' },
      { 'type': 'application', 'controller': 'manager', 'action': 'syncQxUser' },
    ]
  },
  {
    name: '负责人管理',
    sId: 'CustomerManagementFunction',
    pages: [
      'ManagerUnitManagement', // 运营/直营单元
      'SalesProjectManagement', // 直营项目管理
      'NearCustomerManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'manager-unit' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-project' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sales-project-item' }),
      ...createDefaultPermissions({ type: 'application', controller: 'near-customer' }),
    ]
  },
  {
    name: '档案查看',
    sId: 'CustomerServiceShowFunction',
    pages: [
      'CustomerServiceIndex',
    ],
    apiPermissions: [
      { 'type': 'application', 'controller': 'customer', 'action': 'getYjSupportToken' },
      ...createDefaultPermissions({ type: 'application', controller: 'customer-service' }),
    ]
  },
  {
    name: '群信息管理',
    sId: 'QunInfoManagementFunction',
    pages: [
      'XiaoyunQunManagement',
      'QunUserManagement',
      'QunQrCodeManagement',
      'QunCheckManagement',
      'QunUserStatisticManagement',
      'QunTeamStatisticManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'xiaoyun-qun' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-qrcode' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-user' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-check' }),
      { 'type': 'application', 'controller': 'qun-statistic', 'action': 'userStatistic' },
      { 'type': 'application', 'controller': 'qun-statistic', 'action': 'teamStatistic' },
    ]
  },
  {
    name: '群后台管理',
    sId: 'XiaoyunManagementFunction',
    pages: [
      'QunTagsManagement',
      'QunActionLogManagement',
      'QunDeletedManagement',
      'QunStatisticManagement',
      'QunRobotStatManagement',
      'NoRobotQunManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({ type: 'application', controller: 'qun-tag' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-action-log' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-statistic' }),
      ...createDefaultPermissions({ type: 'application', controller: 'qun-robot-stat' }),
      ...createDefaultPermissions({ 'type': 'application', 'controller': 'no-robot-qun' }),
    ]
  },
  {
    name: '直营谈单工具',
    sId: 'NegotiationToolFunction',
    pages: [
      'NegotiationToolManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application',
        controller: 'customer-release-records'
      })
    ]
  },
  {
    name: '信息页面',
    sId: 'InformationPageFunction',
    pages: [
      'InformationPageManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application',
        controller: 'information-page'
      })
    ]
  },
  {
    name: '信息模块',
    sId: 'InformationModuleFunction',
    pages: [
      'InformationModuleManagement'
    ],
    apiPermissions: [
      ...createDefaultPermissions({
        type: 'application',
        controller: 'information-module'
      })
    ]
  },
  {
    name: '已登录',
    sId: 'AuthenticatedFunction',
    pages: [
    ],
    apiPermissions: [
      { 'type': 'application', 'controller': 'qun', 'action': 'userInfo' },
      { 'type': 'application', 'controller': 'qun', 'action': 'userQunList' },
      { 'type': 'application', 'controller': 'qun', 'action': 'qunInfo' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getCustomerByQid' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'addAdmin' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'delAdmin' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'changeName' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'notice' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'configModName' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'configInvite' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'inviteUser' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'kickUser' },
      { 'type': 'application', 'controller': 'qun-manager', 'action': 'transferOwner' },
      { 'type': 'application', 'controller': 'qun-tag', 'action': 'getXiaoyunTags' },
      { 'type': 'application', 'controller': 'contract', 'action': 'find' },
      { 'type': 'application', 'controller': 'contract', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'contract', 'action': 'count' },
      { 'type': 'application', 'controller': 'contract', 'action': 'create' },
      { 'type': 'application', 'controller': 'contract', 'action': 'update' },
      { 'type': 'application', 'controller': 'contract', 'action': 'delete' },
      { 'type': 'application', 'controller': 'contract', 'action': 'export' },
      { 'type': 'application', 'controller': 'contract', 'action': 'import' },
      { 'type': 'application', 'controller': 'sale-sku-product', 'action': 'findMany' },
      { 'type': 'application', 'controller': 'order', 'action': 'createOrder' },
      { 'type': 'application', 'controller': 'order', 'action': 'orderPay' },
      { 'type': 'application', 'controller': 'order', 'action': 'getOrderInfo' },
      { 'type': 'application', 'controller': 'order', 'action': 'orderAppUsageUpdate' },
      { 'type': 'application', 'controller': 'order', 'action': 'orderProductParamUpdate' },
      { 'type': 'application', 'controller': 'order', 'action': 'getOrderListByQuery' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'updateSchoolTag' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'getSchoolTag' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'getSchoolAppUsage' },
      ...createDefaultPermissions({ type: 'application', controller: 'order' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-school' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-agent' }),
      ...createDefaultPermissions({ type: 'application', controller: 'boss-app-usage-config' }),
      ...createDefaultPermissions({ type: 'application', controller: 'sale-sku-product' }),
      ...createDefaultPermissions({ type: 'application', controller: 'customer-app-version' }),
      ...createDefaultPermissions({ type: 'application', controller: 'white-name-list' }),
      { 'type': 'application', 'controller': 'record-app', 'action': 'upload' },
      { 'type': 'application', 'controller': 'record-app', 'action': 'getRecords' },
      { 'type': 'application', 'controller': 'data-comment', 'action': 'listDetail' },
      { 'type': 'application', 'controller': 'data-comment', 'action': 'addComment' },
      { 'type': 'application', 'controller': 'data-comment', 'action': 'deleteComment' },
      { 'type': 'application', 'controller': 'qx-user', 'action': 'find' },
      { 'type': 'application', 'controller': 'customer-service-hfs', 'action': 'openAppUsages' },
    ]
  },
  {
    name: '未登录',
    sId: 'PublicFunction',
    apiPermissions: [
      // 未登录用户允许查看文章和作者信息
      { 'type': 'application', 'controller': 'contract', 'action': 'find' },
      { 'type': 'application', 'controller': 'contract', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'contract', 'action': 'count' },
      { 'type': 'application', 'controller': 'contract', 'action': 'initHuobanJson' },
      { 'type': 'application', 'controller': 'contract', 'action': 'checkContractFieldByBoss' },
      { 'type': 'application', 'controller': 'contract', 'action': 'contractSealProcessToBoss' },
      { 'type': 'application', 'controller': 'workflow', 'action': 'webhookExecute' },
      { 'type': 'application', 'controller': 'workflow', 'action': 'timerExecute' },
      { 'type': 'application', 'controller': 'data-sync', 'action': 'dataSyncExecute' },
      { 'type': 'application', 'controller': 'follow', 'action': 'getFollowUp' },
      { 'type': 'application', 'controller': 'follow', 'action': 'updateFollowUp' },
      { 'type': 'application', 'controller': 'agent-payment', 'action': 'payNative' },
      { 'type': 'application', 'controller': 'agent-payment', 'action': 'forwardBossApi' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'find' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'count' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'findByIds' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'findByQuery' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'wxPay' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'wxPayNotification' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'wxRefund' },
      { 'type': 'application', 'controller': 'third-party-payment', 'action': 'wxRefundNotification' },
      { 'type': 'application', 'controller': 'third-party-refund', 'action': 'find' },
      { 'type': 'application', 'controller': 'third-party-refund', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'third-party-refund', 'action': 'count' },
      { 'type': 'application', 'controller': 'order', 'action': 'getOrderListByQuery' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'find' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'count' },
      { 'type': 'application', 'controller': 'boss-school', 'action': 'update' },
      { 'type': 'application', 'controller': 'question-collection', 'action': 'updateQuestion' },
      { 'type': 'application', 'controller': 'question-collection', 'action': 'deleteQuestion' },
      { 'type': 'application', 'controller': 'utils', 'action': 'getQrCode' },
      { 'type': 'application', 'controller': 'utils', 'action': 'dbQuery' },
      { 'type': 'application', 'controller': 'utils', 'action': 'bookToken' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'updateHuobanSchool' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'updateHuobanSchoolSalesFollow' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'createBossSchool' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'updateBossSchool' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'createBossAgent' },
      // { 'type': 'application', 'controller': 'huoban', 'action': 'updateAgentAuthSchool' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'initHuobanDataJson' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'huobanMessage' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'syncByHuobanId' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'getSalesProjectListByTime' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'getProjectBySchoolId' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'createExpenditureByBoss' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'deleteExpenditureByBoss' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'createPayExpenditureByBoss' },
      { 'type': 'application', 'controller': 'huoban', 'action': 'deletePayExpenditureByBoss' },
      { 'type': 'application', 'controller': 'message-centre', 'action': 'mingdaoMsg' },
      { 'type': 'application', 'controller': 'crm_forward', 'action': 'get_list_by_paging' },
      { 'type': 'upload', 'controller': 'storage', 'action': 'proxyDownload' },
      ...createDefaultPermissions({
        type: 'application',
        controller: 'question-collection'
      }),
      { 'type': 'application', 'controller': 'qun', 'action': 'userInfo' },
      { 'type': 'application', 'controller': 'qun', 'action': 'qrCode' },
      { 'type': 'application', 'controller': 'qun', 'action': 'qunList' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getQunTags' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getAllQunTags' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getWxUsersByRole' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getQunExtraInfo' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getQunExtraInfoList' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getQxUserAvatar' },
      { 'type': 'application', 'controller': 'qun', 'action': 'getQxUserAvatarThumb' },
      { 'type': 'application', 'controller': 'qun-robot-stat', 'action': 'getNoRobotQun' },
      { 'type': 'application', 'controller': 'record-app', 'action': 'loginByQxId' },
      { 'type': 'application', 'controller': 'record-app', 'action': 'loginByQxCode' },
      { 'type': 'application', 'controller': 'record-app', 'action': 'logout' },
      { 'type': 'application', 'controller': 'customer-service-mid', 'action': 'getCustomerManager' },
      { 'type': 'application', 'controller': 'customer-service-mid', 'action': 'getCustomerListByWxId' },
      { 'type': 'application', 'controller': 'customer-service-mid', 'action': 'getSchoolTag' },
      { 'type': 'application', 'controller': 'customer-service-mid', 'action': 'changeSchoolTag' },
      { 'type': 'application', 'controller': 'customer-service', 'action': 'updateCustomFields' },
      { 'type': 'application', 'controller': 'customer-service', 'action': 'updateMingdaoSchool' },
      { 'type': 'application', 'controller': 'customer-service', 'action': 'getSalesFollowCustomers' },
      { 'type': 'application', 'controller': 'contract', 'action': 'loginByBossToken' },
      { 'type': 'application', 'controller': 'sales-monthly-process', 'action': 'initSalesMonthlyProcess' },
      { 'type': 'application', 'controller': 'sales-monthly-process', 'action': 'updateSalesProcessData' },
      ...createDefaultPermissions({ type: 'application', controller: 'yj-school-setting', role: 'read' }),
      { 'type': 'application', 'controller': 'brand-user-menu', 'action': 'getUserMenu' },
      { 'type': 'application', 'controller': 'brand-user-menu', 'action': 'setUserMenu' },

      { 'type': 'application', 'controller': 'customer-release-records', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'information-page', 'action': 'findOne' },
      { 'type': 'application', 'controller': 'information-module', 'action': 'find' },
      { 'type': 'application', 'controller': 'information-module', 'action': 'findOne' },

      { 'type': 'application', 'controller': 'manager', 'action': 'syncQxUser' },
      { 'type': 'application', 'controller': 'manager', 'action': 'syncBusinessQxUser' },
      { 'type': 'application', 'controller': 'app-update', 'action': 'getLatest' },
      { 'type': 'application', 'controller': 'app-update', 'action': 'getDownloadUrl' },

      { 'type': 'application', 'controller': 'archive-sales-follow-record', 'action': 'syncMidRecordByMingDao' },
      { 'type': 'application', 'controller': 'archive-sales-follow-record', 'action': 'archiveRecord' },
      { 'type': 'application', 'controller': 'boss-io', 'action': 'getUserAuthSchoolList' },
    ]
  }
]

const roles = [
  {
    name: '管理员',
    type: 'admin',
    description: '平台管理员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'CoreContentPermissionFunction',
      'AccessKeyManagementFunction',
      'CRMManagementFunction',
      'ExtendManagementFunction',
      'BossSalesManagementFunction',
      'MingdaoAdminFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'NegotiationToolFunction',
      'InformationModuleFunction',
      'GroupLeaderFunction',
      'CustomerManagementFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ]
  },
  {
    name: 'boss用户',
    type: 'boss',
    description: 'boss用户同步创建，可以查看合同管理',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'AgentPaymentManagement'
    ]
  },
  {
    name: 'boss代理商用户',
    type: 'bossAgent',
    description: 'boss代理商用户同步创建，可以查看合同管理',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ]
  },
  {
    name: 'boss销管用户',
    type: 'bossSalesManagement',
    description: 'boss销管用户，可以作销售信息管理',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'BossSalesManagementFunction',
      'AgentPaymentManagement',
    ]
  },
  {
    name: '运营负责人',
    type: 'service-admin',
    description: '运营负责人',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'CustomerServiceFunction',
      'GroupLeaderFunction',
      'AgentPaymentManagement',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'CustomerManagementFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '运营组长',
    type: 'service-group-leader',
    description: '运营组长',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectServiceFunction',
      'GroupLeaderFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '运营经理',
    type: 'service-group-member',
    description: '运营经理',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectServiceFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '服务台',
    type: 'service-worker',
    description: '服务台',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '直营负责人',
    type: 'sales-admin',
    description: '直营负责人',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'GroupLeaderFunction',
      'CustomerManagementFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ],
  },
  {
    name: '直营组长',
    type: 'sales-group-leader',
    description: '直营组长',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'GroupLeaderFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ],
  },
  {
    name: '直营经理',
    type: 'sales-group-member',
    description: '直营经理',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '直营阿米巴负责人',
    type: 'sales-manager',
    description: '直营阿米巴负责人',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'GroupLeaderFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ],
  },
  {
    name: '直营观察员',
    type: 'sales-observer',
    description: '直营观察员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'QunInfoManagementFunction',
      'XiaoyunManagementFunction',
      'AgentPaymentManagement',
      'CustomerServiceFunction',
      'DirectSalesFunction',
      'DirectServiceFunction',
      'GroupLeaderFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ],
  },
  {
    name: '排课',
    type: 'paike',
    description: '排课',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ],
  },
  {
    name: '实施服务',
    type: 'service-deploy',
    description: '实施服务',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ],
  },
  {
    name: '群管理员',
    type: 'qun-admin',
    description: '群管理员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'XiaoyunManagementFunction',
    ],
  },
  {
    name: '线索查看',
    type: 'operation-observer',
    description: '线索查看',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'CustomerServiceShowFunction'
    ],
  },
  {
    name: '业务负责人',
    type: 'business-owner',
    description: '业务负责人',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '招投标负责人',
    type: 'bidding-project-owner',
    description: '招投标负责人',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'BusinessFunction',
    ],
  },
  {
    name: '业务人员',
    type: 'business-member',
    description: '业务人员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'BusinessFunction',
    ]
  },
  {
    name: '职能人员',
    type: 'functional-member',
    description: '职能人员',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
    ]
  },
  {
    name: 'CEO',
    type: 'CEO',
    description: 'CEO',
    modules: [
      'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'DirectSalesFunction',
      'BusinessFunction',
      'DirectSalesContracFunction',
    ],
  },
  {
    name: '中校云业务',
    type: 'zx-cloud-member',
    description: '中校云业务',
    modules: [
      // 'AuthenticatedFunction',
      'PublicFunction',
      'FileUploadFunction',
      'zxCloudFunction',
    ],
  },
]

// 查询过滤器
const queryFilters = [
  {
    // FE filter
    queryOptions: [
      {
        value: '_user',
        label: '是我',
        options: [
          { label: '本人', value: 'user.mingdaoId' },
          { label: '小组', value: 'user.mingdaoGroupIds' },
          { label: '单元', value: 'user.mingdaoUnitIds' },
          // { label: '名字', value: 'user.username' },
          // { label: '企信', value: 'user.customId' },
          // { label: '手机号', value: 'user.phone' },
        ]
      }
    ],

    // BE
    // model字段 前端获取模型筛选器使用（findOneModel接口）
    model: ['customer-service'],
    type: 'application',
    controller: ['customer-service'],
    action: /.*/,
    callback (ctx) {
      const { user } = ctx.state
      const queryKeys = Object.keys(ctx.query)
      const userQueryKeys = queryKeys.filter(e => /_user/.test(e))
      if (userQueryKeys.length > 0) {
        for (const userQueryKey of userQueryKeys) {
          const replaceKey = userQueryKey.replace('_user', '')
          ctx.query[replaceKey] = eval(ctx.query[userQueryKey])
          delete ctx.query[userQueryKey]
        }
      }
    }
  }
]

module.exports = {
  pageGroups,
  functions,
  roles,
  apps,
  queryFilters,
}
