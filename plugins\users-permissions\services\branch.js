'use strict'

module.exports = {
  async getCtxDomainBranch (ctx) {
    const host = ctx.request.header.host
    const domain = (host.match(/^(.*)-api/) || ['', ''])[1]
    if (!domain) throw Error('三级域名异常')
    return await strapi.query('branch', 'users-permissions').findOne({ domain: domain })
  },
  async getCtxDefaultBranch () {
    return await strapi.query('branch', 'users-permissions').findOne({ type: 'default' })
  },
}
