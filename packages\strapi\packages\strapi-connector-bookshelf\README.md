# strapi-connector-bookshelf

[![npm version](https://img.shields.io/npm/v/strapi-connector-bookshelf.svg)](https://www.npmjs.org/package/strapi-connector-bookshelf)
[![npm downloads](https://img.shields.io/npm/dm/strapi-connector-bookshelf.svg)](https://www.npmjs.org/package/strapi-connector-bookshelf)
[![npm dependencies](https://david-dm.org/strapi/strapi-connector-bookshelf.svg)](https://david-dm.org/strapi/strapi-connector-bookshelf)
[![Build status](https://travis-ci.org/strapi/strapi-connector-bookshelf.svg?branch=master)](https://travis-ci.org/strapi/strapi-connector-bookshelf)
[![Slack status](https://slack.strapi.io/badge.svg)](https://slack.strapi.io)

This built-in connector allows you to use the [Bookshelf ORM](http://bookshelfjs.org/) using the `strapi-connector-knex` connector.

[Bookshelf ORM](http://bookshelfjs.org/) is a JavaScript ORM for Node.js, built on the [Knex node module](http://knexjs.org/) SQL query builder. Featuring both promise based and traditional callback interfaces, providing transaction support, eager/nested-eager relation loading, polymorphic associations, and support for one-to-one, one-to-many, and many-to-many relations.

It is designed to work well with SQLite3, PostgreSQL, MySQL, MariaDB, Microsoft SQL Server and Oracle.

## Resources

- [License](LICENSE)

## Links

- [Strapi website](https://strapi.io/)
- [Strapi community on Slack](https://slack.strapi.io)
- [Strapi news on Twitter](https://twitter.com/strapijs)
