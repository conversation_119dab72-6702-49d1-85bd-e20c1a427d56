module.exports = {
    "kind": "collectionType",
    "collectionName": strapi.config.server.mingdaoConfig.managerUnitId,
    "connection": "mingdao",
    "info": {
        name: 'ManagerUnit',
        label: '企信用户组',
        description: ''
    },
    "options": {},
    "pluginOptions": {},
    "attributes": {
        name: {
            // label: '名称',
            "ref": "6750436aba60f67ec34c1d5c"
        },
        groups: {
            // label: '小组',
            "ref": "6750436aba60f67ec34c1d5d"
        },
        leaders: {
            // label: '负责人',
            "ref": "67516f8dba60f67ec34c30c0"
        },
    }
}
