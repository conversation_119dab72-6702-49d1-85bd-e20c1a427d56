'use strict';

module.exports = {
    "collectionName": "saleSkuProduct",
    "info": {
        "name": "SaleSkuProduct",
        "label": "商品",
        "description": "售卖中台商品"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": false
    },
    "pluginOptions": {},
    "attributes": {
        "productId": {
            "label": "商品id",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "name": {
            "label": "商品名称",
            // "editable": false,
            "size": 3,
            "type": "string"
        },
        "status": {
            "label": "上架状态",
            "size": 3,
            "default": true,
            "type": "boolean"
        },
        "category": {
            "label": "类目",
            "editable": false,
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "categoryId": {
            "label": "类目id",
            "editable": false,
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "parentCategory": {
            "label": "父类目",
            "editable": false,
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "parentCategoryId": {
            "label": "父类目id",
            "editable": false,
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "customCode": {
            "label": "自定义编码",
            "editable": false,
            "visible": false,
            "size": 3,
            "type": "string"
        },
        "price": {
            "label": "售价",
            "editable": false,
            "size": 2,
            "format": "rmb",
            "type": "number"
        },
        "costPrice": {
            "label": "成本价",
            "editable": false,
            "size": 2,
            "format": "rmb",
            "type": "number"
        },
        "originalPrice": {
            "label": "原价",
            "editable": false,
            "size": 2,
            "format": "rmb",
            "type": "number"
        },
        "customerAppVersion": {
            "label": "关联crm产品",
            "mainField": "name",
            "collection": "customer-app-version"
        },
        "productParam": {
            "label": "参数默认值",
            "type": "json"
        }
    }
}