module.exports = {
    collectionName: 'db-cache',
    info: {
        name: 'DbCache',
        label: '缓存数据库',
        description: '缓存数据库'
    },
    options: {
        draftAndPublish: false,
        timestamps: true,
        indexes: [
            { keys: { expireAt: 1 }, options: { expireAfterSeconds: 0 } },
            { keys: { key: 1 }, options: { unique: true } },
        ],
    },
    attributes: {
        expireAt: {
            label: '过期时间',
            type: 'datetime'
        },
        key: {
            label: 'key',
            "unique": true,
            type: 'string'
        },
        type: {
            label: '类型',
            type: 'string',
            default: 'lock',
            options: [
                {
                    label: '锁',
                    value: 'lock'
                },
                {
                    label: '缓存',
                    value: 'cache'
                },
            ]
        },
        content: {
            label: '内容',
            type: 'json'
        },
    }
}