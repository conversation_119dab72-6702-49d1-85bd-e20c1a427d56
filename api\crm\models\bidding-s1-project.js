module.exports = {
  kind: 'collectionType',
  collectionName: strapi.config.server.mingdaoConfig.biddingS1ProjectId,
  connection: 'mingdao',
  info: {
    name: 'BiddingS1Project',
    label: '招标项目',
    description: '',
  },
  options: {},
  pluginOptions: {},
  attributes: {
    // 招标项目名称
    title: {
      ref: '678fb8c79e13a09bfffe928a'
    },
    // 核心招标信息
    mainBiddingProjects: {
      ref: '678fb9629e13a09bfffe92af'
    },
    // 关联招标信息
    relationBiddingProjects: {
      ref: '678fc0099e13a09bfffe93bc'
    },
    // 招标单位
    buyer: {
      ref: '678fbfa59e13a09bfffe9398',
    },
    // 预算（万）
    budget: {
      ref: '678fbfe99e13a09bfffe93b5',
    },
    // 招标项目状态
    status: {
      ref: '678fb9b79e13a09bfffe92b9',
    },
    // 是否关注
    isFollowing: {
      ref: '67c859d09e13a09bff1518d3',
    },
    // 发布日期
    publishedAt: {
      ref: '678fbf859e13a09bfffe9394',
    },
    // 已读时间
    readAt: {
      ref: '678fb9ca9e13a09bfffe92bd',
    },
    // 操作日志
    optLog: {
      ref: '678fbe1d9e13a09bfffe936b',
    },
    // 当前关注人
    follower: {
      ref: '678fc0b99e13a09bfffe93d1',
    },
    // 省份
    province: {
      ref: '67a5e0749e13a09bff032388'
    },
    // 公告类型
    biddingProgress: {
      ref: '67a5e2d19e13a09bff032592'
    },
    // 公告类型(大类)
    type: {
      ref: '67caa3839e13a09bff160943'
    }
  }
}
