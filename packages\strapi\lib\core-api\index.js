/**
 * Core API
 */
'use strict';

const _ = require('lodash');

const createController = require('./controller');
const { createService } = require('./service');
const { CurdRouter } = require('accel-utils')

/**
 * Returns a service and a controller built based on the content type passed
 */
function createCoreApi({ api, model, strapi }) {
  const { modelName } = model;

  // find corresponding service and controller
  const userService = _.get(api, ['services', modelName], {});
  const userController = _.get(api, ['controllers', modelName], {});

  const service = Object.assign(createService({ model, strapi }), userService);

  const defaultCurdRouter = new CurdRouter(modelName)
  const defaultController = defaultCurdRouter.createHandlers()
  // const oldController = createController({ service, model })
  const controller = Object.assign(defaultController, userController);

  return {
    service,
    controller,
  };
}

module.exports = {
  createCoreApi,
};
