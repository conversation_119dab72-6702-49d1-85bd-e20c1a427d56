'use strict'

const { initCos } = require('./cos')
const { initBos } = require('./bos')
const { objectStorageConfig } = require('../config/object-storage')

module.exports = {}

if (objectStorageConfig.target === 'cos') {
  const cos = initCos(objectStorageConfig.config)
  // 浏览器直接对接对象存储上传获取临时Token
  module.exports.getSecurityToken = async () => {
    return await cos.getSecurityToken()
  }
  // 对接对象存储的普通文件上传接口
  module.exports.uploadFileToBucket = async (fileName, filePath, dir) => {
    let uploadInfo = await cos.uploadFileToBucket(fileName, filePath, dir)
    return {
      key: uploadInfo.key,
      size: uploadInfo.size,
      buffer: uploadInfo.buffer,
      url: `https://${uploadInfo.Location}`
    }
  }
}

if (objectStorageConfig.target === 'bos') {
  const customHost = objectStorageConfig.customHost
  const customHostConfig = {}
  if (customHost) {
    customHostConfig.Bucket = ''
    customHostConfig.Endpoint = customHost
  }
  const bos = initBos({
    ...objectStorageConfig.config,
    ...customHostConfig,
  })
  // 浏览器直接对接对象存储上传获取临时Token
  module.exports.getSecurityToken = async () => {
    return await bos.getSecurityToken()
  }
  // 对接对象存储的普通文件上传接口
  module.exports.uploadFileToBucket = async (fileName, filePath, dir) => {
    let uploadInfo = await bos.uploadFileToBucket(fileName, filePath, dir)
    return {
      key: uploadInfo.key,
      size: uploadInfo.size,
      buffer: uploadInfo.buffer,
      url: uploadInfo.Location
    }
  }
}
