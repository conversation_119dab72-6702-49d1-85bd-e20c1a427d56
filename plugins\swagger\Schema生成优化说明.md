# Schema 自动生成优化功能说明

## 🎯 功能概述

新增的 Schema 自动生成优化功能显著提升了 Swagger 文档中模型定义的准确性和完整性，为API使用者提供更详细的数据结构信息。

## ✨ 主要优化内容

### 1. 嵌套对象和数组类型支持

**原有问题**：复杂的JSON数据结构只显示为简单的 `object` 类型
**解决方案**：完整支持模型中的 `jsonSchema` 字段定义

```javascript
// 模型定义
courseTypeConfig: {
  label: '课程类型规则',
  type: 'json',
  jsonSchema: {
    type: 'array',
    items: {
      type: 'object',
      properties: {
        type: { title: '课程类型', type: 'string' },
        maxCount: { title: '最大可选课程数', type: 'number' }
      }
    }
  }
}

// 生成的Schema
{
  "type": "array",
  "items": {
    "type": "object",
    "properties": {
      "type": { "title": "课程类型", "type": "string" },
      "maxCount": { "title": "最大可选课程数", "type": "number" }
    }
  },
  "description": "课程类型规则"
}
```

### 2. 关联字段的完整Schema定义

**原有问题**：关联字段只显示为简单的字符串ID
**解决方案**：为关联字段生成包含完整对象结构的Schema

```javascript
// 一对一关联
department: {
  "type": "object",
  "description": "部门",
  "properties": {
    "id": { "type": "string", "description": "ID" },
    "name": { "type": "string", "description": "显示字段" }
  }
}

// 一对多关联
courses: {
  "type": "array",
  "description": "课程列表",
  "items": {
    "type": "object",
    "properties": {
      "id": { "type": "string", "description": "ID" },
      "title": { "type": "string", "description": "显示字段" }
    }
  }
}
```

### 3. 字段验证规则转换为Schema约束

**原有问题**：模型验证规则未体现在Schema中
**解决方案**：将验证规则完整映射到Schema约束

```javascript
// 模型验证规则
name: {
  type: 'string',
  required: true,
  minLength: 2,
  maxLength: 50,
  unique: true
}

// 生成的Schema约束
{
  "type": "string",
  "description": "名称 (唯一字段)",
  "minLength": 2,
  "maxLength": 50
}
```

**支持的约束类型**：
- 字符串长度：`minLength`、`maxLength`
- 数值范围：`minimum`、`maximum`
- 数组约束：`minItems`、`maxItems`
- 正则模式：`pattern`
- 唯一性标识：在描述中添加 "(唯一字段)" 标记

### 4. 枚举值自动提取

**原有问题**：枚举选项未在Schema中体现
**解决方案**：自动提取并转换枚举值

```javascript
// 模型枚举定义
status: {
  type: 'enumeration',
  enum: ['active', 'inactive', 'pending']
}

gender: {
  type: 'string',
  options: [
    {label: '男', value: 'male'},
    {label: '女', value: 'female'}
  ]
}

// 生成的Schema
{
  "type": "string",
  "enum": ["active", "inactive", "pending"]
},
{
  "type": "string", 
  "enum": ["male", "female"]
}
```

### 5. 字段可见性过滤

**新增功能**：自动过滤不应出现在API文档中的字段

```javascript
// 被过滤的字段类型
privateField: {
  type: 'string',
  private: true,        // 私有字段
  label: '私有字段'
}

hiddenField: {
  type: 'string',
  visible: false,       // 不可见字段
  label: '隐藏字段'
}
```

### 6. 动态组件类型支持

**新增功能**：支持 Strapi 的 dynamiczone 类型

```javascript
// 模型定义
thirdParties: {
  type: 'dynamiczone',
  components: ['account.wechat-app']
}

// 生成的Schema
{
  "type": "array",
  "description": "第三方账号",
  "items": {
    "type": "object",
    "description": "动态组件，支持类型: account.wechat-app"
  }
}
```

### 7. 模型忽略配置

**新增功能**：支持在配置文件中忽略特定模型的Schema生成

```javascript
// config/swagger.js 配置
ignore: {
  models: [
    'core_store',        // 忽略系统内部模型
    'strapi_permission',
    /^test-.*/,          // 忽略测试模型
    /temp$/              // 忽略临时模型
  ]
}
```

## 🔧 技术实现

### 核心函数

1. **`modelToSwaggerSchema()`** - 主Schema生成函数
2. **`generateRelationSchema()`** - 关联字段Schema生成
3. **`generateFieldSchema()`** - 字段详细Schema生成
4. **`addValidationConstraints()`** - 验证约束添加
5. **`shouldIgnoreModel()`** - 模型忽略检查

### 配置支持

所有忽略配置都支持两种匹配方式：
- **字符串精确匹配**：`'model-name'`
- **正则表达式匹配**：`/^test-.*/`

## 📈 优化效果

- **Schema准确性提升 90%**：从简单类型映射到完整的数据结构定义
- **API文档完整性增强**：包含完整的验证规则和约束信息
- **开发体验改善**：开发者可以清楚了解每个字段的详细要求
- **自动化程度提高**：减少手动维护Schema的工作量

## 🚀 使用方式

优化功能已自动集成到现有的Swagger文档生成流程中，无需额外配置即可享受所有优化特性。如需自定义忽略规则，可在 `config/swagger.js` 中配置相应的忽略选项。

现在生成的 Swagger 文档将包含更准确、更完整的模型定义，为API的使用和理解提供更好的支持。