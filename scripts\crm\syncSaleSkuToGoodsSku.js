const { MongoClient, ObjectId } = require('mongodb')
const moment = require('moment')
const _ = require('lodash')

// 数据库连接配置
const DB_ENV = process.env.NODE_ENV || 'test'

// 从 config/database.js 中获取的配置
const DB_CONFIG = {
  production: {
    boss: 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary',
    config: 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_config?replicaSet=Replset00&readPreference=primary'
  },
  test: {
    boss: 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred',
    config: 'mongodb://testwly_write:<EMAIL>:6010/testwly_config'
  },
  local: {
    boss: 'mongodb://localhost:27017/testwly-boss',
    config: 'mongodb://localhost:27017/testwly_config'
  }
}

const BOSS_DB_URL = DB_CONFIG[DB_ENV].boss
const CONFIG_DB_URL = DB_CONFIG[DB_ENV].config

let bossDb, bossDbClient
let configDb, configDbClient

// 主执行函数
(async function () {
  bossDbClient = await MongoClient.connect(BOSS_DB_URL)
  bossDb = bossDbClient.db()
  configDbClient = await MongoClient.connect(CONFIG_DB_URL)
  configDb = configDbClient.db()

  try {
    logger('同步开始')
    logger(`环境: ${DB_ENV}`)
    await main()
    logger('同步结束')
  } catch (e) {
    console.error('同步失败:', e.stack || e)
  } finally {
    await bossDbClient.close()
    await configDbClient.close()
    setTimeout(() => {
      process.exit(0)
    }, 1000)
  }
})()

// 日志输出函数
function logger (msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg}`)
}

// 将 CRM 参数类型转换为 equity-plan 参数类型
function convertParamType(crmType) {
  if (!crmType) return 'string'

  // CRM 参数类型映射到 equity-plan 参数类型
  const typeMap = {
    // CRM 定义的类型
    'Text': 'string',           // 单行文本 -> 字符串
    'Number': 'number',         // 数字输入 -> 数字
    'SingleSelect': 'select',   // 单项选择 -> 选项
    'DateRange': 'date-range',  // 时间范围 -> 日期范围

    // 兼容其他可能的格式
    'text': 'string',
    'number': 'number',
    'singleSelect': 'select',
    'dateRange': 'date-range',
    'select': 'select',
    'date': 'date',
    'string': 'string'
  }

  return typeMap[crmType] || 'string'  // 默认返回 string
}

// 主处理函数
async function main () {
  logger('获取 saleSkuProduct 表数据...')

  // 获取 saleSkuProduct 数据
  const saleSkuProducts = await bossDb.collection('saleSkuProduct').find({}).toArray()
  logger(`找到 ${saleSkuProducts.length} 个商品`)

  // 清理已存在的 goods-sku-product 数据（可选）
  const cleanExisting = process.argv.includes('--clean')
  if (cleanExisting) {
    logger('清理已存在的 goods-sku-product 数据...')
    const deleteResult = await bossDb.collection('goods-sku-product').deleteMany({})
    logger(`删除了 ${deleteResult.deletedCount} 条数据`)
  }

  // 建立版本类型映射缓存
  const versionTypeMap = {}
  const equityPlanMap = {}

  let successCount = 0
  let errorCount = 0

  // 处理每个商品
  for (const saleSkuProduct of saleSkuProducts) {
    try {
      // 1. 处理 customerAppVersion -> equityPlans 映射
      let equityPlanIds = []

      if (saleSkuProduct.customerAppVersion && saleSkuProduct.customerAppVersion.length > 0) {
        // 获取 customerAppVersion 数据
        const customerAppVersionIds = saleSkuProduct.customerAppVersion.map(id => {
          try {
            return new ObjectId(id)
          } catch (e) {
            return id
          }
        })

        const customerAppVersions = await bossDb.collection('customerAppVersion').find({
          _id: { $in: customerAppVersionIds }
        }).toArray()

        // 获取对应的 type 值
        const types = customerAppVersions.map(v => v.type).filter(t => t)

        if (types.length > 0) {
          // 查找 equity-plan 表中对应的记录
          const equityPlans = await configDb.collection('equity-plan').find({
            type: { $in: types }
          }).toArray()

          equityPlanIds = equityPlans.map(p => p._id)

          // 缓存映射关系
          for (const plan of equityPlans) {
            equityPlanMap[plan.type] = plan
          }

          for (const version of customerAppVersions) {
            if (version.type) {
              versionTypeMap[version._id.toString()] = version
            }
          }
        }
      }

      // 2. 处理 productParam 映射
      let transformedProductParams = []
      let customerTypesIntersection = null // 用于存储 customerTypes 的交集

      if (saleSkuProduct.productParam && Array.isArray(saleSkuProduct.productParam)) {
        // 收集所有 productParam 中的 customerTypes 用于计算交集
        const allCustomerTypes = saleSkuProduct.productParam
          .filter(param => param.customerTypes && Array.isArray(param.customerTypes))
          .map(param => param.customerTypes)

        // 计算 customerTypes 交集
        if (allCustomerTypes.length > 0) {
          customerTypesIntersection = allCustomerTypes.reduce((intersection, current) => {
            return intersection.filter(type => current.includes(type))
          }, allCustomerTypes[0])
        }

        transformedProductParams = saleSkuProduct.productParam.map(param => {
          // 获取对应的 customerAppVersion 信息
          const versionInfo = versionTypeMap[param._id] || {}
          const equityPlan = equityPlanMap[versionInfo.type] || {}

          // 构建产品参数
          const transformedParam = {
            _id: param._id,
            equityProductCode: param.appCategory || '',
            equityProductCategory: param.productCategory || '',
            name: param.appName && param.versionName
              ? `${param.appName}-${param.versionName}`
              : param.appName || param.versionName || '',
            equityProductName: param.appName || '',
            equityPlanType: param.type || versionInfo.type || null,
            equityPlanName: param.versionName || '',
            id: param._id
          }

          // 处理 usageParams
          if (param.usageParams && Array.isArray(param.usageParams)) {
            transformedParam.usageParams = param.usageParams.map(usageParam => ({
              name: usageParam.name || '',
              type: convertParamType(usageParam.mode || usageParam.type),
              options: usageParam.options || [],
              required: usageParam.required || false,
              requiredInOrder: usageParam.required_in_order || false
            }))
          } else {
            transformedParam.usageParams = []
          }

          return transformedParam
        })
      }

      // 3. 构建 goods-sku-product 数据
      const goodsSkuProduct = {
        productId: saleSkuProduct.productId || '',
        name: saleSkuProduct.name || '',
        status: saleSkuProduct.status !== undefined ? saleSkuProduct.status : true,
        category: saleSkuProduct.category || '',
        categoryId: saleSkuProduct.categoryId || '',
        parentCategory: saleSkuProduct.parentCategory || '',
        parentCategoryId: saleSkuProduct.parentCategoryId || '',
        customCode: saleSkuProduct.customCode || '',
        price: saleSkuProduct.price || 0,
        costPrice: saleSkuProduct.costPrice || 0,
        originalPrice: saleSkuProduct.originalPrice || 0,
        customerTypes: customerTypesIntersection || [], // 所有 productParam 中 customerTypes 的交集
        equityPlans: equityPlanIds,
        productParam: transformedProductParams,
        _sync_id: saleSkuProduct._id.toString(), // 保存原始ID用于追踪
        createdAt: saleSkuProduct.createdAt || new Date(),
        updatedAt: new Date()
      }

      // 检查是否已存在（通过 _sync_id）
      const existing = await bossDb.collection('goods-sku-product').findOne({
        _sync_id: saleSkuProduct._id.toString()
      })

      if (existing) {
        // 更新现有记录
        await bossDb.collection('goods-sku-product').updateOne(
          { _id: existing._id },
          { $set: goodsSkuProduct }
        )
        logger(`更新商品: ${saleSkuProduct.name}`)
      } else {
        // 插入新记录
        await bossDb.collection('goods-sku-product').insertOne(goodsSkuProduct)
        logger(`创建商品: ${saleSkuProduct.name}`)
      }

      successCount++
    } catch (e) {
      errorCount++
      logger(`处理商品失败: ${saleSkuProduct.name || saleSkuProduct._id} - ${e.message}`)
    }
  }

  // 统计结果
  logger(`同步完成！`)
  logger(`成功: ${successCount} 条`)
  logger(`失败: ${errorCount} 条`)

  // 显示最终数据统计
  const totalCount = await bossDb.collection('goods-sku-product').countDocuments({})
  const syncedCount = await bossDb.collection('goods-sku-product').countDocuments({
    _sync_id: { $exists: true }
  })

  logger(`goods-sku-product 表总数: ${totalCount}`)
  logger(`其中同步的数据数: ${syncedCount}`)
}

/*
业务逻辑如下：
将 saleSkuProduct 表数据刷到 goods-sku-product 表

数据库连接：
- 根据 DATABASE 环境变量选择数据库连接
- local: 本地数据库
- test: 测试环境
- prodDev: 生产开发环境
- prod: 生产环境

字段映射关系：
1. customerAppVersion -> equityPlans
   - 查询 customerAppVersion id 数组对应的 customerAppVersion 表的 type 值
   - 通过 type 值查找 equity-plan 表中对应的记录

2. saleSkuProduct.productParam -> goods-sku-product.productParam
   - _id: saleSkuProduct.productParam._id
   - equityProductCode: saleSkuProduct.productParam.appCategory
   - equityProductCategory: saleSkuProduct.productParam.productCategory
   - name: ${saleSkuProduct.productParam.appName}-${saleSkuProduct.productParam.versionName}
   - equityProductName: saleSkuProduct.productParam.appName
   - equityPlanType: saleSkuProduct.productParam.type
   - equityPlanName: saleSkuProduct.productParam.versionName
   - usageParams: 转换参数格式
     - name: saleSkuProduct.productParam.usageParams.name
     - type: convertParamType(saleSkuProduct.productParam.usageParams.mode)
     - options: saleSkuProduct.productParam.usageParams.options
     - required: saleSkuProduct.productParam.required
     - requiredInOrder: saleSkuProduct.productParam.required_in_order

3. goods-sku-product.customerTypes
   - 值为 saleSkuProduct.productParam 数组里所有 customerTypes 字段的值的交集
   - 如果没有交集或没有 customerTypes 字段，则为空数组

执行方式：
DATABASE=local node scripts/syncSaleSkuToGoodsSku.js [--clean]
DATABASE=test node scripts/syncSaleSkuToGoodsSku.js [--clean]
DATABASE=prod node scripts/syncSaleSkuToGoodsSku.js [--clean]

--clean: 清理已存在的 goods-sku-product 数据
*/