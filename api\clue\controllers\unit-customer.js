const { CurdRouter } = require('accel-utils')
const { getBaseQuery, } = require('../../crm/services/customer-service')

const curdRouter = new (class extends CurdRouter {

  // ...
  _getIntersection(arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }

  _getQueryByUser(query, user) {
    query._where = {
      unit_null: false,
      _or: [
        {
          directServiceTeam_null: true,
        },
        {
          directSalesTeam_null: true,
        },
      ],
    };
    // 有单元 没有运营组或没有直营组 的学校
    // 除了admin 外只可以查看 本单元全部资源
    if (!['admin', 'SuperAdmin'].includes(user.role.type)) {
      if (query.unit_in) {
        query.unit_in = query.unit_in
          ? this._getIntersection(query.unit_in, user.mingdaoUnitIds || [])
          : user.mingdaoUnitIds || []
        delete query.unit_in
      } else {
        query.unit_in = user.mingdaoUnitIds || []
      }

      if (['service-group-leader'].includes(user.role.type)) {
        query.directServiceTeam_null = true
      }
      if (['sales-group-leader'].includes(user.role.type)) {
        query.directSalesTeam_null = true
      }
    }
    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.find(ctx)
  }
})('unit-customer')

module.exports = {
  ...curdRouter.createHandlers(),
}
