const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const _ = require('lodash')
const ExcelJS = require('exceljs')
const fs = require('fs')
const path = require('path')
const moment = require('moment')

const huoban = require('../../contract/utils/huoban')
const { sendQxNoticeBoss } = require('../../xiaoyun/utils/goToken')

const huobanConfig = strapi.config.server.huoban
const createSchoolSyncInfo = {
    lastExecutionTime: new Date(),
    // pending   - 执行中
    // fulfilled - 执行完成
    // rejected  - 执行失败
    status: 'fulfilled'
}
const updateSchoolSyncInfo = {
    lastExecutionTime: new Date(),
    // pending   - 执行中
    // fulfilled - 执行完成
    // rejected  - 执行失败
    status: 'fulfilled'
}

async function huobanMessage (ctx) {
    try {
        let { type, challenge } = ctx.request.body
        if (type && type === 'url_verification') {
            // 仅用于配置回调地址验证
            return { challenge: challenge }
        }

        let { data, header: { event_id: eventId, event_type: eventType }, schema } = ctx.request.body
        // TODO 后续改为针对不同表配置
        const eventTypes = [
            'item.create',
            'item.update',
        ]
        const tableIds = [
            huobanConfig.project_table.table_id,
            huobanConfig.school_table.table_id,
            huobanConfig.sales_msg_table.table_id,
            // huobanConfig.sales_follow_table.table_id,
        ]

        // 批量接口结果通知 跳过
        if (eventType === 'bulk_result') {
            return ctx.wrapper.succ({})
        }
        if (!eventTypes.includes(eventType)) {
            return ctx.wrapper.succ({})
        }
        if (!tableIds.includes(data?.table_id?.toString())) {
            return ctx.wrapper.succ({})
        }

        const now = new Date()
        const insertData = {
            // type: 'subscribe',
            // relatedId: eventId,
            // status: 'success',
            status: 'prepared',
            createdAt: now,
            updatedAt: now,
            // key: `${eventId}-${data.item?.item_id}-subscribe`,
            data: ctx.request.body,
            eventId: eventId, // 事件ID
            eventType: eventType,
            tableId: data.table_id.toString(),
            bulk: data.bulk,
            itemId: data.item?.item_id,
            // },
        }
        let insertArray = []
        try {
            if (data.bulk === false) {
                await _huobanMessage(data.table_id.toString(), data.item.item_id, data.item.fields)
                insertData.status = 'success'
                // insertArrayinsertData)
                await strapi.query('huoban-msg-queue').create(insertData)
            } else {
                for (const id of data.item_ids) {
                    insertArray.push(Object.assign({}, insertData, {
                        itemId: id,
                    }))
                }
            }
        } catch (error) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                msgtype: 'text',
                text: {
                    content: `伙伴订阅消息更新,id: ${data.item?.item_id}, 表: ${data.table_id.toString()}, 原因:${error}`,
                }
            })
            await strapi.query('huoban-msg-queue').create(insertData)
        }

        if (insertArray?.length > 0) {
            await strapi.query('huoban-msg-queue').model.insertMany(insertArray)
        }
        return ctx.wrapper.succ({})
    } catch (e) {
        throw e
    }
}

async function syncByHuobanId (ctx) {
    // try {
    let { tableId, itemId } = ctx.request.body
    if (!tableId || !itemId) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    await _huobanMessage(tableId.toString(), itemId.toString())
    return ctx.wrapper.succ({})
    // } catch (e) {
    // return ctx.wrapper.error('HANDLE_ERROR', e.message || e)
    // }
}

async function _huobanMessage (tableId, itemId, fields) {
    try {
        switch (tableId) {
            case huobanConfig.project_table.table_id:// 立项
                await _createContractBySync(itemId, fields)
                break
            case huobanConfig.school_table.table_id:// 学校
                //await _huobanMessagesBySchool(data, eventType)
                await _updateSchoolBySync(itemId, fields)
                break
            case huobanConfig.sales_msg_table.table_id:// 业务消息
                //await _huobanMessagesBySchool(data, eventType)
                await _senQxMsgBySync(itemId, fields)
                break
            default:
                break
        }
    } catch (err) {
        throw (err)
    }
}

async function _updateSchoolBySync (id, fields) {
    try {
        // throw new Error(id)
        let itemInfo
        if (!fields) itemInfo = await huoban.getTableItem(id)
        let schoolData = fields || itemInfo.item.fields
        if (!schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']])
            return

        console.log(`id: ${id},`
            + `立项编号:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']]},`
            + `直营流转:${schoolData[huobanConfig.school_table.fieldsMap['直营流转']]?.[0]?.name},`
            + `立项审核:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项审核']]?.[0]?.name},`
            + `项目状态:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-项目状态']]?.[0]?.name},`
            + `项目标签:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-项目标签']]?.[0]?.title},`
            + `沟通目的:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-沟通目的']]?.[0]?.title},`
            + `项目经理:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-项目经理']]?.[0]?.title},`
            + `项目经理企信ID:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-企信ID']]},`
            + `审批备注:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-审批备注']]},`
        )

        const curCustomers = await strapi.query('customer-service').find({ projectNo: schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']], _projection: { id: 1 } }, [])
        if (!curCustomers || curCustomers.length === 0) {
            console.log(`立项编号:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']]}，更新客户`)
            const qxId = schoolData[huobanConfig.school_table.fieldsMap['直营项目-企信ID']]
            let curUser = await strapi.query('user', 'users-permissions').findOne({ customId: qxId })
            const updateData = {
                salesArchiveStatus: schoolData[huobanConfig.school_table.fieldsMap['直营流转']]?.[0]?.name,
                projectNo: schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']],
                projectApproval: schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项审核']]?.[0]?.name,
                projectStatus: schoolData[huobanConfig.school_table.fieldsMap['直营项目-项目状态']]?.[0]?.name,
                projectManger: curUser?.mingdaoId ? [curUser?.mingdaoId] : [],
                projectTag: schoolData[huobanConfig.school_table.fieldsMap['直营项目-项目标签']]?.[0]?.title,
                projectObjective: schoolData[huobanConfig.school_table.fieldsMap['直营项目-沟通目的']]?.[0]?.title,
                projectApprovalRemark: schoolData[huobanConfig.school_table.fieldsMap['直营项目-审批备注']],
                // directSalesManager: curMangers?.[0]?.id ? [curMangers?.[0]?.id] : [],
            }
            // 不根据伙伴云项目经理修改明道云直营经理
            // if (curUser?.mingdaoId) {
            //     let salesType = ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member',]
            //     curUser = curUser?.roles?.filter(e => salesType.includes(e.type))?.length > 0 ? curUser : undefined
            //     if (curUser) updateData.directSalesManager = [curUser?.mingdaoId]
            // }
            const customers = await strapi.query('customer-service').find({ schoolId: schoolData[huobanConfig.school_table.fieldsMap['学校ID']], _projection: { id: 1 } }, [])
            if (!customers || customers?.length === 0) {
                await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                    msgtype: 'text',
                    text: {
                        content: `伙伴学校在wly未找到, school: ${schoolData[huobanConfig.school_table.fieldsMap['学校ID']]}, id: ${id}`,
                    }
                })
            } else {
                // await strapi.query('customer-service').update({ id: customers?.[0]?.id }, updateData);
                await strapi.entityService.update({
                    params: {
                        id: customers?.[0]?.id
                    },
                    data: updateData
                }, { model: 'customer-service' })
            }
        }
        return
    } catch (err) {
        console.log(err)
        console.error(err)
        throw (err)
    }
}

async function _createContractBySync (id, fields) {
    const schoolTypeEnumsTransfer = {
        '普通': 'school',
        '教研室': 'jiaoyanshi',
        '联盟': 'lianmeng',
        '集团校': 'jituanxiao',
        '联考': 'liankao',
        '教育局': 'jiaoyuju',
        '培训机构': 'peixunjigou',
        '中心校': 'zhongxinxiao',
    }
    try {
        let itemInfo
        if (!fields) itemInfo = await huoban.getTableItem(id)
        let contractData = fields || itemInfo.item.fields

        console.log(`id: ${id}, `
            + `no: ${contractData[huobanConfig.project_table.fieldsMap['立项编号']]}, `
            + `立项审核:${contractData[huobanConfig.project_table.fieldsMap['立项审核']]?.[0]?.name}, `
            + `项目状态:${contractData[huobanConfig.project_table.fieldsMap['项目状态']]?.[0]?.name}, `
            + `申请制作合同:${contractData[huobanConfig.project_table.fieldsMap['申请制作合同']]?.[0]?.name}, `
            + `阿米巴单元:${contractData[huobanConfig.project_table.fieldsMap['阿米巴单元-阿米巴单元']]}, `
            + `项目经理:${contractData[huobanConfig.project_table.fieldsMap['项目经理']]?.[0]?.title}, `
            + `项目经理企信ID:${contractData[huobanConfig.project_table.fieldsMap['项目经理-企信ID']]}, `
            + `项目标签:${contractData[huobanConfig.project_table.fieldsMap['项目标签']]?.[0]?.title}, `
            + `沟通目的:${contractData[huobanConfig.project_table.fieldsMap['沟通目的']]?.[0]?.title}, `
            + `项目属性:${contractData[huobanConfig.project_table.fieldsMap['项目属性']]?.[0]?.title}, `
            + `审批备注:${contractData[huobanConfig.project_table.fieldsMap['审批备注']]}, `
        )
        if (!contractData[huobanConfig.project_table.fieldsMap['立项编号']]) {
            return
        }

        const curCustomers = await strapi.query('customer-service').find({ projectNo: contractData[huobanConfig.project_table.fieldsMap['立项编号']], _projection: { id: 1 } }, [])
        if (curCustomers?.length > 0) {
            console.log(`立项编号:${contractData[huobanConfig.project_table.fieldsMap['立项编号']]}，更新客户`)
            const qxId = contractData[huobanConfig.project_table.fieldsMap['项目经理-企信ID']]
            let curUser = await strapi.query('user', 'users-permissions').findOne({ customId: qxId, _projection: { id: 1, roles: 1, mingdaoId: 1 } }, ['roles'])
            const updateData = {
                projectNo: contractData[huobanConfig.project_table.fieldsMap['立项编号']],
                projectApproval: contractData[huobanConfig.project_table.fieldsMap['立项审核']]?.[0]?.name,
                projectStatus: contractData[huobanConfig.project_table.fieldsMap['项目状态']]?.[0]?.name,
                projectManger: curUser?.mingdaoId ? [curUser?.mingdaoId] : [],
                projectTag: contractData[huobanConfig.project_table.fieldsMap['项目标签']]?.[0]?.title,
                projectObjective: contractData[huobanConfig.project_table.fieldsMap['沟通目的']]?.[0]?.title,
                projectApprovalRemark: contractData[huobanConfig.project_table.fieldsMap['审批备注']],
                // directSalesManager: curUser ? [curUser?.mingdaoId] : [],
            }
            // 不根据伙伴云项目经理修改明道云直营经理
            // if (curUser?.mingdaoId) {
            //     let salesType = ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member',]
            //     curUser = curUser?.roles?.filter(e => salesType.includes(e.type))?.length > 0 ? curUser : undefined
            //     if (curUser) updateData.directSalesManager = [curUser?.mingdaoId]
            // }
            // await strapi.query('customer-service').update({ id: curCustomers?.[0]?.id }, updateData);
            await strapi.entityService.update({
                params: {
                    id: curCustomers?.[0]?.id
                },
                data: updateData
            }, { model: 'customer-service' })
        }
        if (contractData[huobanConfig.project_table.fieldsMap['立项审核']]?.[0]?.name !== '立项成功'
            || contractData[huobanConfig.project_table.fieldsMap['项目状态']]?.[0]?.name !== '进行中'
            || !contractData[huobanConfig.project_table.fieldsMap['申请制作合同']]?.[0]?.name
            || contractData[huobanConfig.project_table.fieldsMap['申请制作合同']]?.[0]?.name === '否'
            || _.isEmpty(contractData[huobanConfig.project_table.fieldsMap['阿米巴单元-阿米巴单元']]))
            return

        const curContract = await strapi.query('contract').findOne({ no: contractData[huobanConfig.project_table.fieldsMap['立项编号']] }, [])
        const contractModel = await strapi.getModel('contract')

        let schIds = [], schools = [], schObjIds = []
        for (let sch of contractData[huobanConfig.project_table.fieldsMap['学校/教育局']] || []) {
            const matchInfo = sch?.title.match(/\(\d{4,}\)/) && sch?.title.match(/\(\d{4,}\)/)[0]
            // const matchInfo = sch?.title.match(/\{\d{4,}\}/) && sch?.title.match(/\{\d{4,}\}/)[0];
            schIds.push(matchInfo && matchInfo.match(/\d{4,}/) && matchInfo.match(/\d{4,}/)[0])
        }
        schIds = _.compact(schIds)

        if (schIds.length > 0) {
            schools = await strapi.query('boss-school').find({ schoolId: { $in: schIds } })
            schObjIds = schools.map(e => e._id)
        }

        let agentNo = contractData[huobanConfig.project_table.fieldsMap['经销商-经销商编号']]
        let agent, cooperation = contractData[huobanConfig.project_table.fieldsMap['合作方式（存档）']]?.[0]?.name
        if (agentNo && agentNo !== '直营') {
            agent = await strapi.query('boss-agent').findOne({ no: agentNo })
        }

        // let partB = {};
        // 合作方式（存档） cooperation 已经不使用了
        // if (cooperation === '直营') {
        //     if (schools.length === 1) {
        //         partB = {
        //             partBType: schoolTypeEnumsTransfer[schools?.[0]?.type],
        //             partBName: schools?.[0]?.name,
        //         };
        //     }
        // } else {
        //     partB = {
        //         partBType: 'agent',
        //         partBNo: agentNo,
        //         partBName: agent?.company,
        //     }
        // }
        let partB = {
            partBType: 'agent',
            partBNo: agentNo,
            partBName: contractData[huobanConfig.project_table.fieldsMap['经销商']]?.[0]?.title,
        }
        if (agentNo === '直营') {
            partB = {
                partBType: schoolTypeEnumsTransfer[schools?.[0]?.type],
                partBName: schools?.[0]?.name,
            }
        } else {
            if (agent?.id) {
                partB = {
                    partBType: 'agent',
                    partBNo: agentNo,
                    partBName: agent?.company,
                }
            }
        }

        let defaultFields = {}
        _.forEach(contractModel?.allAttributes, function (value, key) {
            if (!_.isNil(value?.default) && _.isNil(curContract && curContract[key])) defaultFields[key] = value.default
        })
        if (_.isNil(curContract && curContract.isStandard)) defaultFields.isStandard = contractData[huobanConfig.project_table.fieldsMap['申请制作合同']]?.[0]?.name === '标准方案' ? '标准价格' : '非标价格'
        let updateData = _.assign(defaultFields, {
            itemId: id,
            schools: schObjIds,
            'projectApproval': contractData[huobanConfig.project_table.fieldsMap['立项审核']]?.[0]?.id,
            'projectStatus': contractData[huobanConfig.project_table.fieldsMap['项目状态']]?.[0]?.id,
            projectProperty: contractData[huobanConfig.project_table.fieldsMap['项目属性']]?.[0]?.title,
            'projectDate': contractData[huobanConfig.project_table.fieldsMap['立项日期']],
            'amoeba': contractData[huobanConfig.project_table.fieldsMap['阿米巴单元-阿米巴单元']],
            'cooperation': cooperation,
            'needMeeting': contractData[huobanConfig.project_table.fieldsMap['申请制作合同']]?.[0]?.name,
            // 'performanceAttribution': contractData[huobanConfig.project_table.fieldsMap['业绩归属']][0]?.id,
            'projectName': contractData[huobanConfig.project_table.fieldsMap['项目名称']],
            // applicationTarget,
            // quanSchool,
            partASalesName: contractData[huobanConfig.project_table.fieldsMap['项目经理-员工姓名']],
            partASalesWxId: contractData[huobanConfig.project_table.fieldsMap['项目经理-企信ID']],
            agent: agent ? agent._id : null,
            partBType: partB.partBType,
            partBNo: partB.partBNo,
            partBName: partB.partBName,
        })
        if (curContract) {
            await strapi.query('contract').update({ id: curContract.id }, updateData)
        } else {
            await strapi.query('contract').create(_.assign(updateData, {
                'no': contractData[huobanConfig.project_table.fieldsMap['立项编号']],
                'status': 1,
            }))
        }
        return
    } catch (err) {
        console.log(err)
        console.error(err)
        return
    }
}

async function _senQxMsgBySync (id, fields) {
    try {
        let itemInfo
        if (!fields) itemInfo = await huoban.getTableItem(id)
        let huobanData = fields || itemInfo.item.fields
        if (!huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-1-企信ID']] && !huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-2-企信ID']])
            return

        console.log(`id: ${id},`
            + `时间戳:${huobanData[huobanConfig.sales_msg_table.fieldsMap['时间戳']]},`
            + `消息类型:${huobanData[huobanConfig.sales_msg_table.fieldsMap['消息类型']]?.[0]?.name},`
            + `消息摘要:${huobanData[huobanConfig.sales_msg_table.fieldsMap['消息摘要']]},`
            + `学校ID:${huobanData[huobanConfig.sales_msg_table.fieldsMap['学校ID']]},`
            + `项目编号:${huobanData[huobanConfig.sales_msg_table.fieldsMap['项目编号']]},`
            + `提醒对象-1-企信ID:${huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-1-企信ID']]},`
            + `提醒对象-2-企信ID:${huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-2-企信ID']]},`
        )

        let qxIds = _.compact([
            huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-1-企信ID']],
            huobanData[huobanConfig.sales_msg_table.fieldsMap['提醒对象-2-企信ID']]
        ])
        const desc = `${huobanData[huobanConfig.sales_msg_table.fieldsMap['消息摘要']]} (学校ID：${huobanData[huobanConfig.sales_msg_table.fieldsMap['学校ID']] || '无'}；立项编号：${huobanData[huobanConfig.sales_msg_table.fieldsMap['项目编号']] || '无'})`
        const url = huobanData[huobanConfig.sales_msg_table.fieldsMap['学校ID']] ? `https://wly-boss.iyunxiao.com/admin/CustomerService?schoolId_eq=${huobanData[huobanConfig.sales_msg_table.fieldsMap['学校ID']]}` : undefined
        await sendQxNoticeBoss(qxIds, {
            title: huobanData[huobanConfig.sales_msg_table.fieldsMap['消息类型']]?.[0]?.name,
            desc: desc,
            url: url,
        })
        return
    } catch (err) {
        console.log(err)
        console.error(err)
        throw (err)
    }
}

async function updateBossSchool (ctx) {
    let { ids } = ctx.request.body
    let bossDbClient
    try {
        const dataSource = await strapi.query('data-source').findOne({ name: 'boss-io', })
        bossDbClient = await MongoClient.connect(dataSource?.url)
        const bossDb = bossDbClient.db()
        const now = new Date()
        if (updateSchoolSyncInfo.status !== 'pending') {
            updateSchoolSyncInfo.status = 'pending'
            let msgs = []

            let query = {}
            if (ids) {
                query['school_id_20'] = { $in: ids }
            } else {
                // let logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
                //     coll: '@CustomerAppUsageConfig',
                //     filter: {
                //         create_time: { $gt: moment(updateSchoolSyncInfo.lastExecutionTime).subtract(24, 'h').toDate() },
                //         enabled: 1,
                //         is_trial: 0,
                //     },
                //     limit: 1000
                // }).then(({ data }) => { return data });

                const logs = await bossDb.collection('@CustomerAppUsageConfig').find({
                    create_time: { $gt: moment(updateSchoolSyncInfo.lastExecutionTime).subtract(24, 'h').toDate() },
                    enabled: 1,
                    // is_trial: 0,
                }).sort({ _id: -1 }).limit(1000).toArray()
                const curIds = _.compact(logs.map(e => ObjectId(e.customer_id)))
                query['_id'] = { $in: curIds }
            }

            // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@Customer',
            //     filter: query,
            //     limit: 300
            // }).then(({ data }) => { return data });
            let customers = await bossDb.collection('@Customer').find(query).sort({ _id: -1 }).limit(300).toArray()
            const customerIds = _.compact(customers.map(customer => customer.school_id_20?.toString() || '0'))
            const schoolItems = await huoban.getSchoolList(customerIds)
            const yjTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['阅卷版本'])?.config?.options
            const fxTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['分析版本'])?.config?.options

            customers = await _getAppVersions(bossDb, customers)
            let noIds = []
            for (const customer of customers) {
                let schoolItem = schoolItems?.find(item => +item.fields[huobanConfig.school_table.fieldsMap['学校ID']] == customer.school_id_20)
                if (!schoolItem) {
                    noIds.push(customer.school_id_20)
                    continue
                }

                let yjType = yjTypeOptions.find(e => e.name === customer.yjVersion)
                let fxType = fxTypeOptions.find(e => e.name === customer.fxVersion)

                let fields = {}
                fields[huobanConfig.school_table.fieldsMap['阅卷版本']] = yjType ? { id: yjType.id } : null
                fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']] = customer.yjEndTime ? moment(customer.yjEndTime).format('YYYY-MM-DD') : null
                fields[huobanConfig.school_table.fieldsMap['分析版本']] = fxType ? { id: fxType.id } : null
                fields[huobanConfig.school_table.fieldsMap['分析到期日期']] = customer.fxEndTime ? moment(customer.fxEndTime).format('YYYY-MM-DD') : null
                fields[huobanConfig.school_table.fieldsMap['账号到期分类']] = schoolItem.fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.[0]?.id ? { id: schoolItem.fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.[0]?.id } : null
                if (customer.yjEndTime && customer.yjEndTime > new Date()) {
                    fields[huobanConfig.school_table.fieldsMap['账号到期分类']] = null
                }
                if (schoolItem.fields[huobanConfig.school_table.fieldsMap['阅卷版本']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['阅卷版本']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']] == fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']]
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['分析版本']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['分析版本']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['分析到期日期']] == fields[huobanConfig.school_table.fieldsMap['分析到期日期']]
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.id
                ) {
                    continue
                }

                const result = await huoban.updateSchoolItem(schoolItem.item_id, fields)
                if (result.code !== 0) {
                    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                        msgtype: 'text',
                        text: {
                            content: `boss学校更新失败,id: ${customer.school_id_20}, 名: ${customer.name}, 原因:${result.msg}`,
                        }
                    })
                } else {
                    msgs.push(`id: ${customer.school_id_20}, 名: ${customer.name}`)
                }
            }

            updateSchoolSyncInfo.lastExecutionTime = now

            if (!_.isEmpty(msgs) || !_.isEmpty(noIds)) {
                msgs = _.concat([`boss学校等待更新数目: ${customerIds.length}, 伙伴学校数目: ${schoolItems.length}`], msgs)
                if (!_.isEmpty(noIds)) {
                    msgs = msgs.concat([`无伙伴学校ids: ${JSON.stringify(noIds)}`])
                }
                await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                    msgtype: 'text',
                    text: {
                        content: msgs.join('\n'),
                    }
                })
            }
        }
        updateSchoolSyncInfo.status = 'fulfilled'

        return ctx.wrapper.succ({})
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    } finally {
        await bossDbClient.close()
    }
}

// TODO 待删除
async function updateAgentAuthSchool (ctx) {
    let { infos } = ctx.request.body
    try {

        const ids = infos.map(info => info.id)
        const schoolIds = infos.map(info => info.customer?.schoolId?.toString())
        const nos = infos.map(info => info.agent.no)
        const projectNos = _.compact(infos.map(info => info?.certificate?.data))
        if (_.isEmpty(ids) || _.isEmpty(schoolIds) || _.isEmpty(nos)) {
            return ctx.badRequest('参数错误')
        }
        const agentAuths = await huoban.getAgentAuthList(ids)
        const schools = await huoban.getSchoolList(schoolIds)
        const agents = await huoban.getAgentList(nos)
        const projects = await huoban.getProjectList(projectNos)

        for (let info of infos) {
            let { id, customer, agent, begin_time, end_time, authorisation_type, product, certificate } = info
            let fields = {}
            if (!customer?.schoolId) {
                continue
            }

            const curAuth = agentAuths?.find(item => item.fields[huobanConfig.agent_auth_table.fieldsMap['CRMID']] === id)

            if (!curAuth && info.deleted == 1) {
                continue
            }
            if (curAuth && info.deleted == 1) {
                const result = await huoban.deleteTableItem(curAuth.item_id)
                if (result.code !== 0) {
                    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                        msgtype: 'text',
                        text: {
                            content: `授权记录删除失败,id: ${id}, 名: ${customer.name}, 原因:${result.msg}`,
                        }
                    })
                    throw new Error(result.msg)
                }
                continue
            }

            const curAgent = agents?.find(item => item.fields[huobanConfig.agent_table.fieldsMap['经销商编号']] === agent.no)
            const curSchool = schools?.find(item => item.fields[huobanConfig.school_table.fieldsMap['学校ID']] === customer?.schoolId?.toString())
            const curProject = projects?.find(item => item.fields[huobanConfig.project_table.fieldsMap['立项编号']] === certificate?.data)
            const productOptions = huobanConfig.agent_auth_table.fields.find(e => e.field_id === huobanConfig.agent_auth_table.fieldsMap['授权产品'])?.config?.options
            const productType = productOptions.find(e => e.name === product?.name)
            const authorOptions = huobanConfig.agent_auth_table.fields.find(e => e.field_id === huobanConfig.agent_auth_table.fieldsMap['授权类型'])?.config?.options
            const authorType = authorOptions.find(e => e.name === authorisation_type)
            const certificateOptions = huobanConfig.agent_auth_table.fields.find(e => e.field_id === huobanConfig.agent_auth_table.fieldsMap['业务类型'])?.config?.options
            const certificateType = certificateOptions.find(e => e.name === certificate?.type)

            fields[huobanConfig.agent_auth_table.fieldsMap['学校/教育局']] = curSchool ? { item_id: curSchool.item_id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['学校ID']] = customer?.schoolId
            fields[huobanConfig.agent_auth_table.fieldsMap['经销商']] = curAgent ? { item_id: curAgent.item_id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['授权产品']] = productType ? { id: productType.id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['授权类型']] = authorType ? { id: authorType.id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['授权开始日期']] = begin_time ? moment(new Date(begin_time)).format('YYYY-MM-DD') : null
            fields[huobanConfig.agent_auth_table.fieldsMap['授权结束日期']] = end_time ? moment(new Date(end_time)).format('YYYY-MM-DD') : null
            fields[huobanConfig.agent_auth_table.fieldsMap['CRMID']] = id
            fields[huobanConfig.agent_auth_table.fieldsMap['业务类型']] = certificateType ? { id: certificateType.id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['项目管理']] = curProject ? { item_id: curProject.item_id } : null
            fields[huobanConfig.agent_auth_table.fieldsMap['订单号/合同编号']] = certificate?.data ? certificate.data : null

            if (curAuth) {
                const result = await huoban.updateTableItem(curAuth.item_id, fields)
                if (result.code !== 0) {
                    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                        msgtype: 'text',
                        text: {
                            content: `授权记录更新失败,id: ${id}, 名: ${customer.name}, 原因:${result.msg}`,
                        }
                    })
                    throw new Error(result.msg)
                }
            } else {
                const result = await huoban.createTableItem(huobanConfig.agent_auth_table.table_id, fields)
                if (result.code !== 0) {
                    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                        msgtype: 'text',
                        text: {
                            content: `授权记录创建失败,id: ${id}, 名: ${customer.name}, 原因:${result.msg}`,
                        }
                    })
                    throw new Error(result.msg)
                }
            }
        }
        return ctx.wrapper.succ({})
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    }
}

async function createBossSchool (ctx) {
    let { ids } = ctx.request.body
    let bossDbClient
    const schoolTypeEnumsTransfer = {
        '普通': '学校',
        '教研室': '统考平台',
        '联盟': '联盟平台',
        '集团校': '集团校平台',
        '联考': '单次联考',
        '教育局': '教育局',
        '培训机构': '培训机构',
        '中心校': '中心校平台',
    }
    try {
        const dataSource = await strapi.query('data-source').findOne({ name: 'boss-io', })
        bossDbClient = await MongoClient.connect(dataSource?.url)
        const bossDb = bossDbClient.db()
        const now = new Date()
        if (createSchoolSyncInfo.status !== 'pending') {
            createSchoolSyncInfo.status = 'pending'
            let msgs = []

            let query = {}
            if (ids) {
                query['school_id_20'] = { $in: ids }
            } else {
                const logs = await bossDb.collection('@CustomerActionLog').find({
                    create_time: { $gt: moment(createSchoolSyncInfo.lastExecutionTime).subtract(60, 'm').toDate() },
                    target: 'yuejuanWebsite20',
                }).sort({ _id: -1 }).limit(5000).toArray()
                const curIds = _.compact(logs.map(e => e.customer_id))
                query['_id'] = { $in: curIds }
            }

            // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@Customer',
            //     filter: query,
            //     limit: 300
            // }).then(({ data }) => { return data });
            let customers = await bossDb.collection('@Customer').find(query).sort({ _id: -1 }).limit(300).toArray()
            const customerIds = _.compact(customers.map(customer => customer.school_id_20?.toString() || '0'))
            const schoolItems = await huoban.getSchoolList(customerIds)
            const schoolTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['账号类型'])?.config?.options
            const yjTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['阅卷版本'])?.config?.options
            const fxTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['分析版本'])?.config?.options
            const eduSystemOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['学制'])?.config?.options

            customers = await _getAppVersions(bossDb, customers)
            for (const customer of customers) {
                let schoolItem = schoolItems?.find(item => +item.fields[huobanConfig.school_table.fieldsMap['学校ID']] == customer.school_id_20)
                if (schoolItem) continue

                let schoolType = schoolTypeOptions.find(e => e.name === schoolTypeEnumsTransfer[customer.type])
                let yjType = yjTypeOptions.find(e => e.name === customer.yjVersion)
                let fxType = fxTypeOptions.find(e => e.name === customer.fxVersion)
                let eduSystem = eduSystemOptions.find(e => e.name === customer.edu_system)
                const province = customer?.location?.province
                const provinceId = require('../../contract/controllers/tables/业务数据/省.json').find(e => e.省名 === province)?.数据唯一编号
                const city = customer?.location?.city
                const cityId = require('../../contract/controllers/tables/业务数据/市.json').find(e => e.省名 === province && e.市名 === city)?.数据唯一编号
                const district = customer?.location?.district
                const districtId = require('../../contract/controllers/tables/业务数据/区.json').find(e => e.省名 === province && e.市名 === city && e['区/县名'] === district)?.数据唯一编号

                let fields = {}
                fields[huobanConfig.school_table.fieldsMap['学校ID']] = customer.school_id_20?.toString()
                fields[huobanConfig.school_table.fieldsMap['账号类型']] = { id: schoolType.id }
                fields[huobanConfig.school_table.fieldsMap['学制']] = eduSystem ? { id: eduSystem.id } : null
                fields[huobanConfig.school_table.fieldsMap['学校名称']] = customer.name
                fields[huobanConfig.school_table.fieldsMap['省份']] = provinceId ? { item_id: provinceId } : null
                fields[huobanConfig.school_table.fieldsMap['地市']] = cityId ? { item_id: cityId } : null
                fields[huobanConfig.school_table.fieldsMap['区县']] = districtId ? { item_id: districtId } : null
                fields[huobanConfig.school_table.fieldsMap['阅卷版本']] = yjType ? { id: yjType.id } : null
                fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']] = customer.yjEndTime ? moment(customer.yjEndTime).format('YYYY-MM-DD') : null
                fields[huobanConfig.school_table.fieldsMap['分析版本']] = fxType ? { id: fxType.id } : null
                fields[huobanConfig.school_table.fieldsMap['分析到期日期']] = customer.fxEndTime ? moment(customer.fxEndTime).format('YYYY-MM-DD') : null

                const result = await huoban.createSchoolItem(fields)
                if (result.code !== 0) {
                    await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                        msgtype: 'text',
                        text: {
                            content: `boss学校同步失败,id: ${customer.school_id_20}, 名: ${customer.name}, 原因:${result.msg}`,
                        }
                    })
                } else {
                    msgs.push(`id: ${customer.school_id_20}, 名: ${customer.name}`)

                }
            }

            createSchoolSyncInfo.lastExecutionTime = now
            if (!_.isEmpty(msgs)) {
                msgs = _.concat([`boss学校等待同步数目: ${customerIds?.length || 0}, 伙伴学校数目: ${schoolItems?.length || 0}`], msgs)
                await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                    msgtype: 'text',
                    text: {
                        content: msgs.join('\n'),
                    }
                })
            }
        }
        createSchoolSyncInfo.status = 'fulfilled'

        return ctx.wrapper.succ({})
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    } finally {
        await bossDbClient.close()
    }
}

async function createBossAgent (ctx) {
    let { nos } = ctx.request.body
    let bossDbClient

    try {
        let query = { no: { $exists: true }, }
        if (nos) {
            query['no'] = { $in: nos }
        } else {
            query['create_time'] = { $gt: moment().subtract(1, 'day').startOf('day').toDate() }
        }
        const dataSource = await strapi.query('data-source').findOne({ name: 'boss-io', })
        bossDbClient = await MongoClient.connect(dataSource?.url)
        const bossDb = bossDbClient.db()

        const agents = await bossDb.collection('@Agent').find(query).sort({ _id: -1 }).limit(100).toArray()
        const agentNos = _.compact(agents.map(agent => agent.no || ''))
        const agentItems = await huoban.getAgentList(agentNos)
        for (const agent of agents) {
            let agentItem = agentItems?.find(item => item.fields[huobanConfig.agent_table.fieldsMap['经销商编号']] == agent.no)
            if (agentItem) continue

            const province = agent?.location?.province
            const provinceId = require('../../contract/controllers/tables/业务数据/省.json').find(e => e.省名 === province)?.数据唯一编号

            let fields = {}
            fields[huobanConfig.agent_table.fieldsMap['经销商编号']] = agent.no
            fields[huobanConfig.agent_table.fieldsMap['公司名称']] = agent.company
            fields[huobanConfig.agent_table.fieldsMap['主营省份']] = provinceId ? { item_id: provinceId } : null

            const result = await huoban.createAgentItem(fields)
            if (result.code !== 0) {
                await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                    msgtype: 'text',
                    text: {
                        content: `boss经销商同步失败,编号: ${agent.no}, 名: ${agent.company}, 原因:${result.msg}`,
                    }
                })
            } else {
                await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                    msgtype: 'text',
                    text: {
                        content: `boss经销商同步成功,编号: ${agent.no}, 名: ${agent.company}`,
                    }
                })
            }
        }
        return ctx.wrapper.succ({})
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    } finally {
        await bossDbClient.close()
    }
}

async function updateHuobanSchool (ctx) {
    let { customerIds } = ctx.request.body
    let bossDbClient
    const schoolTypeEnumsTransfer = {
        '普通': '学校',
        '教研室': '统考平台',
        '联盟': '联盟平台',
        '集团校': '集团校平台',
        '联考': '单次联考',
        '教育局': '教育局',
        '培训机构': '培训机构',
        '中心校': '中心校平台',
    }
    try {
        const dataSource = await strapi.query('data-source').findOne({ name: 'boss-io', })
        bossDbClient = await MongoClient.connect(dataSource?.url)
        const bossDb = bossDbClient.db()
        let msgs = []
        let query = {
            '_id': { $in: customerIds.map(e => ObjectId(e)) }
        }

        // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
        //     coll: '@Customer',
        //     filter: query,
        //     limit: 100
        // }).then(({ data }) => { return data });
        let customers = await bossDb.collection('@Customer').find(query).sort({ _id: -1 }).limit(100).toArray()
        if (customers.length === 0) {
            return ctx.wrapper.succ({})
        }
        const schoolIds = _.compact(customers.map(customer => customer.school_id_20?.toString() || '0'))
        const schoolItems = await huoban.getSchoolList(schoolIds)
        const agentIds = _.flatten(customers.map(e => e?.['[proxy]']?.map(p => ObjectId(p.id))))
        // let bossAgents = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
        //     coll: '@Agent',
        //     filter: { _id: { $in: agentIds } },
        //     limit: 100
        // }).then(({ data }) => { return data });
        const bossAgents = await bossDb.collection('@Agent').find({ _id: { $in: agentIds } }).sort({ _id: -1 }).toArray()
        const agentNos = _.compact(bossAgents.map(agent => agent.no || ''))
        const agentItems = await huoban.getAgentList(agentNos)

        const schoolTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['账号类型'])?.config?.options
        const yjTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['阅卷版本'])?.config?.options
        const fxTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['分析版本'])?.config?.options
        const yjExpiredOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['账号到期分类'])?.config?.options
        const yjTagOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['阅卷签约标签'])?.config?.options
        const authTypeOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['授权类型'])?.config?.options
        const eduSystemOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['学制'])?.config?.options

        // const schools = await axios.get(`https://wly-space-api.yunxiao.com/external/customer-services/getSchoolTag`, {
        const schools = await axios.get(`${strapi.config.server.wlySpaceApi.url}/external/customer-services/getSchoolTag`, {
            params: {
                customerIds: customerIds.join(',')
            }
        }).then(({ data }) => { return data })
        customers = await _getAppVersions(bossDb, customers)
        let items = []
        let now = new Date()
        const newAuthTypes = ['运营授权', '项目合作', '直营合作', '开拓授权']
        for (const customer of customers) {
            if (!customer.school_id_20) {
                continue
            }
            let schoolItem = schoolItems?.find(item => +item.fields[huobanConfig.school_table.fieldsMap['学校ID']] == customer.school_id_20)
            const curSchool = schools?.find(item => +item.schoolId == customer.school_id_20)
            let yjType = yjTypeOptions.find(e => e.name === customer.yjVersion)
            let fxType = fxTypeOptions.find(e => e.name === customer.fxVersion)
            let yjTag = yjTagOptions.find(e => e.name === curSchool?.yjTag)
            let eduSystem = eduSystemOptions.find(e => e.name === customer.edu_system)

            let yjExpiredType
            let curProxyList = _.orderBy(customer?.['[proxy]'], ['update_time'], ['desc'])
            let newAuths = curProxyList?.filter(e => newAuthTypes.includes(e.authorisation_type) && e?.product?.sign === 'saas')
            let oldAuths = curProxyList?.filter(e => !newAuthTypes.includes(e.authorisation_type))
            let auth = newAuths?.[0] || oldAuths?.[0]

            // let newAuth = customer?.['[proxy]']?.find(e => e.disabled !== 1 && newAuthTypes.includes(e.authorisation_type) && e?.product?.sign === 'saas' && new Date(e.end_time) > now)
            // let oldAuth = customer?.['[proxy]']?.find(e => e.disabled !== 1 && !newAuthTypes.includes(e.authorisation_type) && new Date(e.end_time) > now)
            // let auth = newAuth || oldAuth
            let curAuthType = newAuthTypes.includes(auth?.authorisation_type) ? auth?.authorisation_type : (auth?.authorisation_type ? '旧授权' : auth?.authorisation_type)
            let authType = authTypeOptions.find(e => e.name === curAuthType)
            const curBossAgent = bossAgents?.find(e => e._id.toString() === auth?.id)
            const curAgent = agentItems?.find(item => item.fields[huobanConfig.agent_table.fieldsMap['经销商编号']] === curBossAgent?.no)

            if (curSchool?.yjTag === '付费校' && customer.type !== '联考') {
                const now = new Date()
                let oneMonthsAfter = moment().endOf('d').add(1, 'M').toDate()
                let towMonthsAfter = moment().endOf('d').add(2, 'M').toDate()
                let threeMonthsAfter = moment().endOf('d').add(3, 'M').toDate()
                if (!customer.yjEndTime) {
                    yjExpiredType = null
                } else if (oneMonthsAfter >= customer.yjEndTime && customer.yjEndTime >= now) {
                    yjExpiredType = yjExpiredOptions.find(e => e.name === '临期-1个月')
                } else if (towMonthsAfter >= customer.yjEndTime && customer.yjEndTime > oneMonthsAfter) {
                    yjExpiredType = yjExpiredOptions.find(e => e.name === '临期-2个月')
                } else if (threeMonthsAfter >= customer.yjEndTime && customer.yjEndTime > towMonthsAfter) {
                    yjExpiredType = yjExpiredOptions.find(e => e.name === '临期-3个月')
                }
            }
            let fields = {}
            fields[huobanConfig.school_table.fieldsMap['阅卷签约标签']] = yjTag ? { id: yjTag.id } : null
            fields[huobanConfig.school_table.fieldsMap['阅卷版本']] = yjType ? { id: yjType.id } : null
            fields[huobanConfig.school_table.fieldsMap['学制']] = eduSystem ? { id: eduSystem.id } : null
            fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']] = customer.yjEndTime ? moment(customer.yjEndTime).format('YYYY-MM-DD') : null
            fields[huobanConfig.school_table.fieldsMap['分析版本']] = fxType ? { id: fxType.id } : null
            fields[huobanConfig.school_table.fieldsMap['分析到期日期']] = customer.fxEndTime ? moment(customer.fxEndTime).format('YYYY-MM-DD') : null
            fields[huobanConfig.school_table.fieldsMap['账号到期分类']] = yjExpiredType ? { id: yjExpiredType.id } : null
            fields[huobanConfig.school_table.fieldsMap['授权经销商']] = curAgent ? { item_id: curAgent.item_id } : null
            fields[huobanConfig.school_table.fieldsMap['授权到期日期']] = auth?.end_time ? moment(auth.end_time).format('YYYY-MM-DD') : null
            fields[huobanConfig.school_table.fieldsMap['授权类型']] = authType ? { id: authType.id } : null

            if (schoolItem) {
                if (schoolItem.fields[huobanConfig.school_table.fieldsMap['阅卷签约标签']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['阅卷签约标签']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['阅卷版本']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['阅卷版本']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['学制']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['学制']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']] == fields[huobanConfig.school_table.fieldsMap['阅卷到期日期']]
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['分析版本']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['分析版本']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['分析到期日期']] == fields[huobanConfig.school_table.fieldsMap['分析到期日期']]
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['账号到期分类']]?.id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['授权经销商']]?.[0]?.item_id == fields[huobanConfig.school_table.fieldsMap['授权经销商']]?.item_id
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['授权到期日期']] == fields[huobanConfig.school_table.fieldsMap['授权到期日期']]
                    && schoolItem.fields[huobanConfig.school_table.fieldsMap['授权类型']]?.[0]?.id == fields[huobanConfig.school_table.fieldsMap['授权类型']]?.id
                ) {
                    continue
                }
                items.push({
                    item_id: schoolItem.item_id,
                    fields: fields
                })
            } else {
                let schoolType = schoolTypeOptions.find(e => e.name === schoolTypeEnumsTransfer[customer.type])
                const province = customer?.location?.province
                const provinceId = require('../../contract/controllers/tables/业务数据/省.json').find(e => e.省名 === province)?.数据唯一编号
                const city = customer?.location?.city
                const cityId = require('../../contract/controllers/tables/业务数据/市.json').find(e => e.省名 === province && e.市名 === city)?.数据唯一编号
                const district = customer?.location?.district
                const districtId = require('../../contract/controllers/tables/业务数据/区.json').find(e => e.省名 === province && e.市名 === city && e['区/县名'] === district)?.数据唯一编号

                fields[huobanConfig.school_table.fieldsMap['学校ID']] = customer.school_id_20?.toString()
                fields[huobanConfig.school_table.fieldsMap['账号类型']] = { id: schoolType.id }
                fields[huobanConfig.school_table.fieldsMap['学校名称']] = customer.name
                fields[huobanConfig.school_table.fieldsMap['省份']] = provinceId ? { item_id: provinceId } : null
                fields[huobanConfig.school_table.fieldsMap['地市']] = cityId ? { item_id: cityId } : null
                fields[huobanConfig.school_table.fieldsMap['区县']] = districtId ? { item_id: districtId } : null
                items.push({
                    // item_id: schoolItem.item_id,
                    fields: fields
                })
            }
            msgs.push(`id: ${customer.school_id_20}, 名: ${customer.name}`)
        }

        if (items.length === 0) {
            return ctx.wrapper.succ({})
        }
        const result = await huoban.batchUpdateTableItems(huobanConfig.school_table.table_id, items)
        if (result.code !== 0) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                msgtype: 'text',
                text: {
                    content: `伙伴学校同步失败, 数量: ${customerIds.length}, ids: ${customerIds.join(',')},  原因:${result.msg}`,
                }
            })
        }

        if (!_.isEmpty(msgs)) {
            msgs = _.concat([`伙伴学校等待同步数目: ${customerIds?.length || 0}, 伙伴学校数目: ${schoolItems?.length || 0}`], msgs)
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                msgtype: 'text',
                text: {
                    content: msgs.join('\n'),
                }
            })
        }

        return ctx.wrapper.succ({})
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    } finally {
        await bossDbClient.close()
    }
}

async function updateHuobanSchoolSalesFollow (ctx) {
    let { customerIds } = ctx.request.body

    if (!customerIds || !customerIds.length === 0) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    const customers = await strapi.query('customer-service').find({ customerId_in: customerIds }, ['directSalesManager'])

    const mingdaoIds = _.compact(customers.map(e => e.directSalesManager?.[0]?.rowid))
    const users = await strapi.query('user', 'users-permissions').find({ mingdaoId_in: mingdaoIds }, [])
    const schoolIds = _.compact(customers.map(e => e.schoolId))
    const qxids = _.compact(users.map(e => e.customId))
    const schoolList = await huoban.getSchoolList(schoolIds)
    const salesList = await huoban.getUserByQxIds(qxids)

    let items = [], msgs = []
    for (const schoolItem of schoolList) {
        let customer = customers.find(e => +e.schoolId === +schoolItem.fields[huobanConfig.school_table.fieldsMap['学校ID']])
        if (!customer) continue
        const directSalesManager = users.find(e => e.mingdaoId && e.mingdaoId === customer?.directSalesManager?.[0]?.rowid)
        if (!directSalesManager) continue
        let salesInfo = salesList?.find(e => e.fields[huobanConfig.staff_table.fieldsMap['企信ID']] === directSalesManager?.customId)
        if (!salesInfo) continue
        let fields = {}
        fields[huobanConfig.school_table.fieldsMap['远程项目编号']] = customer.projectNo
        fields[huobanConfig.school_table.fieldsMap['远程直营经理']] = { id: salesInfo.item_id }

        items.push({
            item_id: schoolItem.item_id,
            fields: fields
        })
        msgs.push(`id: ${customer.schoolId}, 名: ${customer.name}, 经理: ${directSalesManager.username}, 编号: ${customer.projectNo}`)
    }
    if (items.length === 0) {
        return ctx.wrapper.succ({})
    }
    const result = await huoban.batchUpdateTableItems(huobanConfig.school_table.table_id, items)
    if (result.code !== 0) {
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `更新伙伴云项目经理失败, 数量: ${customerIds.length}, ids: ${customerIds.join(',')},  原因:${result.msg}`,
            }
        })
    }
    if (!_.isEmpty(msgs)) {
        msgs = _.concat([`更新伙伴云项目经理同步数目: ${customerIds?.length || 0}`], msgs)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: msgs.join('\n'),
            }
        })
    }

    return ctx.wrapper.succ({})
}

async function initHuobanDataJson (ctx) {
    try {
        const fileUrls = [
            {
                src: '../../contract/controllers/tables/业务数据/省_20240529175947.xlsx',
                dst: '../../contract/controllers/tables/业务数据/省.json',
            },
            {
                src: '../../contract/controllers/tables/业务数据/市_20240529175958.xlsx',
                dst: '../../contract/controllers/tables/业务数据/市.json',
            },
            {
                src: '../../contract/controllers/tables/业务数据/区县_20240529175704.xlsx',
                dst: '../../contract/controllers/tables/业务数据/区.json',
            },
            {
                src: '../../contract/controllers/tables/业务数据/直营SaaS项目特点_20241206104013.xlsx',
                dst: '../../contract/controllers/tables/业务数据/直营SaaS项目特点.json',
            },
            {
                src: '../../contract/controllers/tables/业务数据/直营SaaS谈判目的_20241206104021.xlsx',
                dst: '../../contract/controllers/tables/业务数据/直营SaaS谈判目的.json',
            },
            {
                src: '../../contract/controllers/tables/业务数据/AMB成本类型_20250407173934.xlsx',
                dst: '../../contract/controllers/tables/业务数据/AMB成本类型.json',
            },
        ]
        for (let fileUrl of fileUrls) {
            await _excelToJson(fileUrl.src, fileUrl.dst)
        }
        return ctx.wrapper.succ({})
    } catch (e) {
        throw e
    }
}

// 发起伙伴云建档立项
async function createHuobanSalesProject (ctx) {
    let { schoolId } = ctx.request.body
    if (!schoolId) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    if (process.env.NODE_ENV !== 'production' && schoolId != '31570') {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    const lock = await strapi.query('db-cache').findOne({
        key: `createHuobanSalesProject-lock-${schoolId}`,
        type: 'lock',
    })
    if (lock) {
        return ctx.wrapper.error('HANDLE_ERROR', '请勿重复发起')
    }

    async function _schoolLock () {
        await strapi.query('db-cache').create({
            key: `createHuobanSalesProject-lock-${schoolId}`,
            type: 'lock',
            expireAt: moment().add(30, 'm').toDate()
        })
    }
    const customers = await strapi.query('customer-service').find({
        schoolId: schoolId,
        _projection: { id: 1, projectNo: 1, remoteProjectTag: 1, remoteProjectObjective: 1, directSalesManager: 1 }
    }, [])
    const customer = customers?.[0]
    if (!customer || customer.projectNo || !customer.directSalesManager || !customer.directSalesManager?.length === 0) {
        await _schoolLock()
        return ctx.wrapper.error('HANDLE_ERROR', '不允许发起')
    }
    let curUser = await strapi.query('user', 'users-permissions').findOne({ mingdaoId: customer?.directSalesManager?.[0]?.id })
    if (!curUser || !curUser.customId) {
        await _schoolLock()
        return ctx.wrapper.error('HANDLE_ERROR', '跟进人异常')

    }
    const schoolItem = await huoban.getSchoolById(schoolId)
    const schoolData = schoolItem?.fields
    // console.log(
    //     `立项编号:${schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']]},`
    //     + `远程立项任务:${schoolData[huobanConfig.school_table.fieldsMap['远程立项任务']]?.[0]?.name},`
    // );

    const projectTasks = ['远程待立项', '远程立项成功', '远程立项失败']
    if (schoolData[huobanConfig.school_table.fieldsMap['直营项目-立项编号']]
        || projectTasks.includes(schoolData[huobanConfig.school_table.fieldsMap['远程立项任务']]?.[0]?.name)) {
        await _schoolLock()
        return ctx.wrapper.error('HANDLE_ERROR', '请勿重复发起')
    }

    const projectTaskOptions = huobanConfig.school_table.fields.find(e => e.field_id === huobanConfig.school_table.fieldsMap['远程立项任务'])?.config?.options
    const projectTask = projectTaskOptions.find(e => e.name === '远程待立项')
    const salesInfo = await huoban.getUserByQxId(curUser.customId)
    if (!salesInfo) {
        await _schoolLock()
        return ctx.wrapper.error('HANDLE_ERROR', '跟进人异常')
    }
    const customerModel = await strapi.getModel('customer-service')
    const remoteProjectTagOption = customerModel.attributes.remoteProjectTag?.options?.find(e => e?.value === customer?.remoteProjectTag)
    const remoteProjectObjectiveOption = customerModel.attributes.remoteProjectObjective?.options?.find(e => e?.value === customer?.remoteProjectObjective)
    const projectTagId = require('../../contract/controllers/tables/业务数据/直营SaaS项目特点.json').find(e => e.项目特点 === remoteProjectTagOption?.label)?.数据唯一编号
    const projectObjectiveId = require('../../contract/controllers/tables/业务数据/直营SaaS谈判目的.json').find(e => e.谈判目的 === remoteProjectObjectiveOption?.label)?.数据唯一编号

    let fields = {}
    fields[huobanConfig.school_table.fieldsMap['远程立项任务']] = { id: projectTask.id }
    fields[huobanConfig.school_table.fieldsMap['远程项目经理']] = { id: salesInfo.item_id }
    if (projectTagId) {
        fields[huobanConfig.school_table.fieldsMap['远程项目标签']] = { id: projectTagId }
    }
    if (projectObjectiveId) {
        fields[huobanConfig.school_table.fieldsMap['远程沟通目的']] = { id: projectObjectiveId }
    }
    const result = await huoban.updateSchoolItem(schoolItem.item_id, fields)
    await _schoolLock()
    return ctx.wrapper.succ({})
}

async function _excelToJson (src, dst) {
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.readFile(path.resolve(__dirname, src))
    const worksheet = workbook.getWorksheet(1) //获取第一个worksheet
    const result = []
    let keys = []
    worksheet.eachRow((row, rowNumber) => {
        let obj = {}
        // cell.type单元格类型：6-公式 ;2-数值；3-字符串
        row.eachCell((cell, colNumber) => {
            const value = cell.value.replace(/[\t]/, '')
            if (rowNumber === 1) keys.push(value)
            else obj[keys[colNumber - 1]] = value
        })
        if (rowNumber > 1) result.push(obj)
    })
    // console.log(result)
    // 写入流
    await fs.writeFileSync(path.resolve(__dirname, dst), JSON.stringify(result))
    return
}

async function _getAppVersions (bossDb, customers) {
    // let appVersions = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //     coll: '@CustomerAppVersion',
    //     filter: {
    //         deleted: { $ne: 1 },
    //     },
    //     limit: 10000
    // }).then(({ data }) => { return data });
    // let customerAppsUsages = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //     coll: '@CustomerAppsUsage',
    //     filter: {
    //         customer_id: { $in: customers.map(e => e._id.toString()) },
    //     },
    //     limit: 10000
    // }).then(({ data }) => { return data });
    let appVersions = await bossDb.collection('@CustomerAppVersion').find({ deleted: { $ne: 1 }, }).toArray()
    let customerAppsUsages = await bossDb.collection('@CustomerAppsUsage').find({ customer_id: { $in: customers.map(e => e._id.toString()) } }).toArray()

    for (let i = 0; i < customers.length; i++) {
        const customerAppsUsage = customerAppsUsages.find(e => e.customer_id === customers[i]._id.toString())
        let yjVersion, yjStartTime, yjEndTime, fxVersion, fxStartTime, fxEndTime, tkVersion, tkStartTime, tkEndTime
        for (const usage of (customerAppsUsage?.usages || [])) {
            const appVersion = appVersions.find(appVersion => appVersion.type === usage.type)

            if (appVersion?.product_category === 'saas') {
                yjVersion = appVersion.name
                yjStartTime = new Date(usage.begin_time)
                yjEndTime = new Date(usage.end_time)
            }
            if (appVersion?.product_category === 'fenxi') {
                fxVersion = appVersion.name
                fxStartTime = new Date(usage.begin_time)
                fxEndTime = new Date(usage.end_time)
            }
            if (appVersion?.product_category === 'p_2') {
                tkVersion = appVersion.name
                tkStartTime = new Date(usage.begin_time)
                tkEndTime = new Date(usage.end_time)
            }
        }
        customers[i].yjVersion = yjVersion
        customers[i].yjStartTime = yjStartTime
        customers[i].yjEndTime = yjEndTime
        customers[i].fxVersion = fxVersion
        customers[i].fxStartTime = fxStartTime
        customers[i].fxEndTime = fxEndTime
        customers[i].tkVersion = tkVersion
        customers[i].tkStartTime = tkStartTime
        customers[i].tkEndTime = tkEndTime
    }
    return customers
}

async function getSalesProjectListByTime (ctx) {
    try {
        let { time } = ctx.request.query
        const list = await huoban.getSalesProjectListByTime(moment(time).startOf('day').toDate())
        return list
    } catch (e) {
        throw e
    }
}

async function getProjectBySchoolId (ctx) {
    let { schoolId, fields } = ctx.request.query
    if (fields) {
        fields = fields?.split(',')
    }
    if (!schoolId) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    try {
        const schoolData = await huoban.getSchoolById(schoolId)
        let projectList = await huoban.getProjectListBySchool([schoolData.item_id])

        if (projectList && projectList.length > 0) {
            projectList = _huobanKeyToName(huobanConfig.project_table, projectList, fields)
        }
        return ctx.wrapper.succ(projectList || [])
    } catch (e) {
        console.log(e)
        console.error(e)
        throw e
    }
}

async function createExpenditureByBoss (ctx) {
    let { data, approve, promoterName, workflowViewId, workflowName, workflowViewName, } = ctx.request.body
    if (!data || !data.details || !data.details.归属项目 || data.details.归属项目.length === 0) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    const unitName = data.费用归属单元
    if (!unitName) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '归属单位错误')
    }

    try {
        const unit = await huoban.getOneBusinessUnitByName(unitName)
        if (!unit) {
            return ctx.wrapper.error('PARAMETERS_ERROR', '归属单位异常')
        }

        const projectNos = _.uniq(data.details.归属项目.map(e => _.trim(e.项目编号)))
        const projects = await huoban.getProjectList(projectNos)
        // if (!projects || projects.length !== projectNos.length) {
        //     return ctx.wrapper.error('PARAMETERS_ERROR', '项目编号异常')
        // }

        let details = []
        let index = 0, length = data.details.归属项目.length
        // 获取成本类型ID
        const costTypeId = require('../../contract/controllers/tables/业务数据/AMB成本类型.json').find(e => e.成本类型 === '普通开支')?.数据唯一编号
        for (const project of data.details.归属项目) {
            index++
            const curProject = projects?.find(item => item.fields[huobanConfig.project_table.fieldsMap['立项编号']] === project.项目编号)

            const payStatusOptions = huobanConfig.expenditure_table.fields.find(e => e.field_id === huobanConfig.expenditure_table.fieldsMap['付款状态'])?.config?.options
            const payType = payStatusOptions.find(e => e.name === '待付款')

            let fields = {}
            fields[huobanConfig.expenditure_table.fieldsMap['支出方单元']] = curProject?.fields[huobanConfig.project_table.fieldsMap['阿米巴单元']] ? { item_id: curProject?.fields[huobanConfig.project_table.fieldsMap['阿米巴单元']].item_id } : null
            if (!fields[huobanConfig.expenditure_table.fieldsMap['支出方单元']]) {
                fields[huobanConfig.expenditure_table.fieldsMap['支出方单元']] = unit ? { item_id: unit.item_id } : null
            }
            fields[huobanConfig.expenditure_table.fieldsMap['归属项目']] = curProject ? { item_id: curProject.item_id } : null
            fields[huobanConfig.expenditure_table.fieldsMap['支出金额']] = project?.金额
            fields[huobanConfig.expenditure_table.fieldsMap['月份备注']] = moment().format('YYMM')
            fields[huobanConfig.expenditure_table.fieldsMap['付款状态']] = payType ? { id: payType.id } : null
            fields[huobanConfig.expenditure_table.fieldsMap['成本类型']] = costTypeId ? { id: costTypeId } : null
            fields[huobanConfig.expenditure_table.fieldsMap['报销流程单号']] = workflowViewId
            if (length > 1) {
                fields[huobanConfig.expenditure_table.fieldsMap['支出内容']] = `${workflowViewName || workflowName}-${promoterName}-${workflowViewId}-${data.报销主体}-差旅报销（${index}/${length}）`
            } else {
                fields[huobanConfig.expenditure_table.fieldsMap['支出内容']] = `${workflowViewName || workflowName}-${promoterName}-${workflowViewId}-${data.报销主体}-差旅报销`
            }
            details.push({
                fields: fields
            })
        }

        const result = await huoban.batchUpdateTableItems(huobanConfig.expenditure_table.table_id, details)
        if (result.code !== 0) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                msgtype: 'text',
                text: {
                    content: `报销支出同步失败, 流程: ${workflowViewId}, 数量: ${details.length}, 原因:${result.msg}`,
                }
            })
            throw new Error(result.msg)
        }

        return ctx.wrapper.succ({})
    } catch (e) {
        console.error(e)
        throw e
    }
}

async function deleteExpenditureByBoss (ctx) {
    let { workflowViewId, } = ctx.request.body
    try {
        const list = await huoban.getExpenditureListByNo([workflowViewId])
        const itemIds = list.map(e => e.item_id)

        if (itemIds.length === 0) {
            return ctx.wrapper.succ({})
        }

        const result = await huoban.batchDeleteTableItems(huobanConfig.expenditure_table.table_id, itemIds)
        if (result.code !== 0) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                msgtype: 'text',
                text: {
                    content: `报销支出删除失败, 流程: ${workflowViewId}, 数量: ${itemIds.length}, 原因:${result.msg}`,
                }
            })
            throw new Error(result.msg)
        }

        return ctx.wrapper.succ({})
    } catch (e) {
        console.error(e)
        throw e
    }
}

async function createPayExpenditureByBoss (ctx) {
    let { data, approve, promoterName, workflowViewId, workflowName, workflowViewName, } = ctx.request.body
    if (!data || !data.details || !data.details.费用详情 || data.details.费用详情.length === 0) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
    }
    const unitName = data.费用归属
    if (!unitName) {
        return ctx.wrapper.error('PARAMETERS_ERROR', '归属单位错误')
    }
    const filterUnit = ['7#集团总部', '集团总部-公共服务', '集团总部-历史问题处理', '退费']
    if (filterUnit.includes(unitName)) {
        return ctx.wrapper.succ({}) // 归属单位在过滤列表中，直接返回成功
    }
    try {
        const unit = await huoban.getOneBusinessUnitByName(unitName)
        if (!unit) {
            return ctx.wrapper.succ({}) // 归属单位不存在，直接返回成功
        }

        const projectNos = _.uniq(data.details.费用详情.map(e => _.trim(e['项目归属（立项编号）'])))
        const projects = await huoban.getProjectList(projectNos)
        // if (!projects || projects.length !== projectNos.length) {
        //     return ctx.wrapper.error('PARAMETERS_ERROR', '项目编号异常')
        // }

        let details = []
        let index = 0, length = data.details.费用详情.length
        const costTypeId = require('../../contract/controllers/tables/业务数据/AMB成本类型.json').find(e => e.成本类型 === '普通开支')?.数据唯一编号
        for (const project of data.details.费用详情) {
            index++
            const curProject = projects?.find(item => item.fields[huobanConfig.project_table.fieldsMap['立项编号']] === project['项目归属（立项编号）'])

            const payStatusOptions = huobanConfig.expenditure_table.fields.find(e => e.field_id === huobanConfig.expenditure_table.fieldsMap['付款状态'])?.config?.options
            const payType = payStatusOptions.find(e => e.name === '已付款')

            let fields = {}
            fields[huobanConfig.expenditure_table.fieldsMap['支出方单元']] = unit ? { item_id: unit.item_id } : null
            fields[huobanConfig.expenditure_table.fieldsMap['归属项目']] = curProject ? { item_id: curProject.item_id } : null
            fields[huobanConfig.expenditure_table.fieldsMap['支出金额']] = project?.金额
            fields[huobanConfig.expenditure_table.fieldsMap['月份备注']] = moment().format('YYMM')
            fields[huobanConfig.expenditure_table.fieldsMap['付款状态']] = payType ? { id: payType.id } : null
            fields[huobanConfig.expenditure_table.fieldsMap['成本类型']] = costTypeId ? { id: costTypeId } : null
            fields[huobanConfig.expenditure_table.fieldsMap['BOSS流程编号（付款单号）']] = workflowViewId
            if (length > 1) {
                fields[huobanConfig.expenditure_table.fieldsMap['支出内容']] = `付款单-${promoterName}-${workflowViewId}-${project.公司名称}-${data.费用名称}（${index}/${length}）`
            } else {
                fields[huobanConfig.expenditure_table.fieldsMap['支出内容']] = `付款单-${promoterName}-${workflowViewId}-${project.公司名称}-${data.费用名称}）`
            }
            fields[huobanConfig.expenditure_table.fieldsMap['备注']] = project?.申请原因
            details.push({
                fields: fields
            })
        }

        const result = await huoban.batchUpdateTableItems(huobanConfig.expenditure_table.table_id, details)
        if (result.code !== 0) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                msgtype: 'text',
                text: {
                    content: `付款支出同步失败, 流程: ${workflowViewId}, 数量: ${details.length}, 原因:${result.msg}`,
                }
            })
            throw new Error(result.msg)
        }

        return ctx.wrapper.succ({})
    } catch (e) {
        console.error(e)
        throw e
    }
}

async function deletePayExpenditureByBoss (ctx) {
    let { workflowViewId, } = ctx.request.body
    try {
        const list = await huoban.getExpenditureListByNo([workflowViewId])
        const itemIds = list.map(e => e.item_id)

        if (itemIds.length === 0) {
            return ctx.wrapper.succ({})
        }

        const result = await huoban.batchDeleteTableItems(huobanConfig.expenditure_table.table_id, itemIds)
        if (result.code !== 0) {
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8a7a37bc-d69f-4977-9cae-daeec6de392a', {
                msgtype: 'text',
                text: {
                    content: `付款支出删除失败, 流程: ${workflowViewId}, 数量: ${itemIds.length}, 原因:${result.msg}`,
                }
            })
            throw new Error(result.msg)
        }

        return ctx.wrapper.succ({})
    } catch (e) {
        console.error(e)
        throw e
    }
}

function _huobanKeyToName (table, list, fields) {
    for (const info of list) {
        let curFields = {}
        for (const key in info.fields) {
            const curItem = table.fields.find(e => e.field_id === key)
            if (fields && fields.includes(curItem?.name)) {
                curFields[curItem.name] = info.fields[key]
            } else if (!fields) {
                curFields[curItem.name] = info.fields[key]
            }
        }
        info.fields = curFields
    }
    return list
}

module.exports = {
    huobanMessage,
    syncByHuobanId,
    getSalesProjectListByTime,
    getProjectBySchoolId,
    initHuobanDataJson,
    updateHuobanSchool,
    updateHuobanSchoolSalesFollow,
    createBossSchool,
    updateBossSchool,
    createBossAgent,
    createHuobanSalesProject,
    updateAgentAuthSchool,
    createExpenditureByBoss,
    deleteExpenditureByBoss,
    createPayExpenditureByBoss,
    deletePayExpenditureByBoss,
}
