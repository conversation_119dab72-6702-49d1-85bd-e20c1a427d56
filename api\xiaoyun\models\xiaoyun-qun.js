module.exports = {
  collectionName: 'xiaoyun-qun',
  info: {
    name: '<PERSON>yun<PERSON><PERSON>',
    label: '小云群列表',
    description: '小云群列表'
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
    indexes: [
      { keys: { qunId: -1 } }
    ],
  },
  pluginOptions: {},
  attributes: {
    qunId: {
      label: '群ID',
      type: 'string',
      unique: true,
      editable: false
    },
    name: {
      label: '群名称',
      type: 'string',
      editable: false
    },
    qunCreatorId: {
      label: '群主Id',
      type: 'string',
      editable: false
    },
    qunCreatorName: {
      label: '群主名称',
      type: 'string',
      editable: false
    },
    qunCreatorAvatar: {
      label: '群主头像',
      type: 'string',
      editable: false
    },
    qunCreateTime: {
      label: '群创建时间',
      type: 'number',
      editable: false
    },
    qunType: {
      label: '群类型',
      type: 'number',
      editable: false
    },
    userCount: {
      label: '群人数',
      type: 'number',
      editable: false
    },
    qunUsers: {
      label: '群成员列表',
      collection: 'qun-user',
      mainField: 'name',
      editable: false
    },
    qunGroup: {
      label: '群分组',
      type: 'number',
      options: [
        {
          label: '学校群',
          value: 1
        },
        {
          label: '经销商群',
          value: 2
        },
        {
          label: '其他',
          value: 3
        }
      ]
    },
    qunTags: {
      label: '群标签',
      collection: 'qun-tag'
    },
    qunExternalUserCount: {
      label: '外部成员数量',
      type: 'integer',
      editable: false
    },
    parseCustomerName: {
      label: '匹配客户名',
      type: 'string',
      editable: false
    },
    customer: {
      label: '匹配客户',
      model: 'customer-service-mid',
      via: 'qun',
      meta: {
        query: {
          'deleted_ne': true,
          'qun_null': true
        },
      },
      editable: false
    },
    schoolId: {
      label: '学校ID',
      type: 'string',
      editable: false
    },
    isDeleted: {
      label: '是否删除',
      type: 'boolean',
      editable: false
    },
    forceTime: {
      label: '强制更新时间',
      type: 'datetime',
      editable: false
    },
    syncTime: {
      label: '同步时间',
      type: 'datetime',
      editable: false
    },
    isProcessed: {
      label: '是否已处理',
      type: 'boolean',
      editable: false
    }
  }
}
