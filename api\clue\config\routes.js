const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/clue-customers',
      controller: 'clue-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/near-customers',
      controller: 'near-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/unit-customers',
      controller: 'unit-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-group-customers',
      controller: 'sales-group-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/service-group-customers',
      controller: 'service-group-customer'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-distribute-records',
      controller: 'sales-distribute-record'
    }),
    ...createDefaultRoutes({
      basePath: '/service-distribute-records',
      controller: 'service-distribute-record'
    }),
  ]
}
