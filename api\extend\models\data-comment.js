module.exports = {
  'collectionName': 'data-comment',
  'info': {
    'name': 'dataComment',
    'label': '评论',
    'description': '评论'
  },
  'options': {
    'draftAndPublish': false,
    'timestamps': true
  },
  'pluginOptions': {},
  'attributes': {
    model: {
      label: '数据表',
      type: 'string',
      editable: false
    },
    collectionName: {
      label: '数据表名',
      type: 'string',
      editable: false
    },
    dataId: {
      label: '数据ID',
      type: 'string',
      editable: false
    },
    user: {
      label: '用户',
      model: 'user',
      plugin: 'users-permissions',
      required: true,
      editable: false
    },
    content: {
      label: '内容',
      type: 'text',
      required: true,
      editable: false
    },
    parent: {
      label: '父评论',
      type: 'string',
      editable: false
    },
    target: {
      label: '回复对象',
      model: 'user',
      plugin: 'users-permissions',
      editable: false
    },
    at: {
      label: '@',
      collection: 'user',
      plugin: 'users-permissions',
      editable: false
    },
    deleted: {
      label: '已删除',
      type: 'boolean',
      default: false,
      editable: false
    }
  }
}
