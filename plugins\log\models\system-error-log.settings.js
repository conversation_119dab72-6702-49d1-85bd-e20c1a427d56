const systemRequestLogModel = require('./system-request-log.settings')

module.exports = {
  kind: 'collectionType',
  collectionName: 'SystemErrorLog',
  info: {
    name: 'SystemErrorLogs',
    label: '系统错误日志',
    description: '系统错误日志'
  },
  options: {
    increments: true,
    timestamps: false,
    draftAndPublish: false,
    indexes: [
      {
        keys: {
          requestAt: -1,
          responseTime: -1,
          method: 1,
          originalUrl: 1,
          userAgent: 1,
          userId: 1,
        }
      }
    ]
  },
  pluginOptions: {},
  attributes: {
    // 基于系统请求日志数据模型
    ...systemRequestLogModel.attributes,
    // 错误信息扩展字段
    name: {
      label: '报错类型',
      type: 'string'
    },
    message: {
      label: '报错信息',
      type: 'string'
    },
    stack: {
      label: '报错调用栈',
      type: 'text'
    },
    userId: {
      label: '请求用户',
      plugin: 'users-permissions',
      model: 'user'
    }
  }
}
