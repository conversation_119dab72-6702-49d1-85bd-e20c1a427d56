'use strict';

module.exports = {
    "collectionName": "changeHistory",
    "info": {
        "name": "ChangeHistory",
        "label": "数据变更历史",
        "description": "数据变更历史"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true,
    },
    "pluginOptions": {},
    "attributes": {
        "model": {
            "label": "关联模版",
            "size": 3,
            "default": 'contract',
            "type": "string"
        },
        "modelId": {
            "label": "关联数据ID",
            "type": "string",
            "size": 3,
        },
        "beforeData": {
            "label": "历史版本",
            "size": 12,
            "type": "json",
        },
        "afterData": {
            "label": "当前版本",
            "size": 12,
            "type": "json",
        },
        userId: {
            label: '请求用户',
            plugin: 'users-permissions',
            model: 'user'
        }
    }
}