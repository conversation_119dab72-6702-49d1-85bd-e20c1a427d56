module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.customerContactId,
  "connection": "mingdao",
  info: {
    name: 'CustomerContact',
    label: '客户联系人',
    description: '客户联系人'
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    customerService: {
      // label: '客户',
      "ref": "672d92ec10071fb4b5f9e2ec"
    },
    operator: {
      // label: '操作人',
      "ref": "672d8ee8962c1f8ca4a20859"
    },
    creator: {
      // label: '创建人',
      "ref": "672d8ee8962c1f8ca4a2085a"
    },
    name: {
      // label: '姓名',
      "ref": "672d8ee8962c1f8ca4a2085b"
    },
    phone: {
      // label: '联系方式',
      "ref": "672d8ee8962c1f8ca4a2085c"
    },
    job: {
      // label: '职务',
      "ref": "672d8ee8962c1f8ca4a2085d"
    },
    gender: {
      // label: '性别',
      "ref": "672d8ee8962c1f8ca4a2085e"
    },
    status: {
      // label: '状态',
      "ref": "672d8ee8962c1f8ca4a2085f"
    },
    tag: {
      // label: '是否关键人',
      "ref": "672d8ee8962c1f8ca4a20860"
    },
    wxStatus: {
      // label: '微信状态',
      "ref": "672d8ee8962c1f8ca4a20861"
    },
    wxId: {
      // label: '微信号',
      "ref": "672d8ee8962c1f8ca4a20862"
    },
    isJoinQun: {
      // label: '是否已进群',
      "ref": "6732d6ae1637ee6db9afedc8"
    },
    wxAddUser: {
      // label: '微信添加人',
      "ref": "672d8ee8962c1f8ca4a20864"
    },
    wxAddTime: {
      // label: '微信添加时间',
      "ref": "672d8ee8962c1f8ca4a20865"
    },
    serviceIsAddWx: {
      // label: '直服是否已加微信',
      "ref": "672d8ee8962c1f8ca4a20866"
    },
    salesIsAddWx: {
      // label: '直营是否已加微信',
      "ref": "6732d6ae1637ee6db9afedc9"
    },
    giveGift: {
      // label: '是否已赠送礼包',
      "ref": "6732d6ae1637ee6db9afedca"
    },
    likeFeature: {
      // label: '感兴趣的产品/功能',
      "ref": "672d8ee8962c1f8ca4a20869"
    },
    comment: {
      // label: '备注',
      "ref": "672d8ee8962c1f8ca4a2086a"
    },
    title: {
      // label: '标题字段	',
      "ref": "673ffb47ba60f67ec34988bf"
    },
  }
}