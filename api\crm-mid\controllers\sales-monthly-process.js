const { CurdRouter } = require('accel-utils')
const _ = require('lodash')
const moment = require('moment')
const initSalesProcess = require('../services/initSalesMonthlyProcess')
const { getUserGroup } = require('../../crm/controllers/manager-group')

const curdRouter = new (class extends CurdRouter {
  async find(ctx) {
    const { query } = this._parseCtx(ctx)
    let dailyArr = []
    if (query.time_gte && query.time_lte) {
      dailyArr = await strapi.query('sales-daily-process').model.find({
        time: {
          $gte: query.time_gte,
          $lte: query.time_lte,
        }
      })
      dailyArr = dailyArr.map(e => {
        e._doc.id = e._id?.toString()
        return e
      })
      query.time_eq = moment(query.time_gte).startOf('month').toDate()
      query._limit = 1000
      delete query.time_gte
      delete query.time_lte
    }

    let monthlyArr = await strapi.query('sales-monthly-process').find(query)
    monthlyArr = monthlyArr.filter(e => !e.salesManager?.leaved)
    for (const monthlyData of monthlyArr) {
      if (dailyArr.length) {
        monthlyData.salesDailyProcess = _.orderBy(dailyArr.filter(e => monthlyData.salesManager && e.salesManager && e.salesManager.toString() === monthlyData.salesManager.id).map(e => e._doc), ['time'], ['asc'])
      }

      monthlyData.callArr = []
      monthlyData.callbackArr = []
      monthlyData.validCallArr = []
      monthlyData.wxContactArr = []
      monthlyData.requiredArr = []
      for (const dailyData of monthlyData.salesDailyProcess) {
        monthlyData.callArr = monthlyData.callArr.concat(dailyData.callArr)
        monthlyData.callbackArr = monthlyData.callbackArr.concat(dailyData.callbackArr)
        monthlyData.validCallArr = monthlyData.validCallArr.concat(dailyData.validCallArr)
        monthlyData.wxContactArr = _.uniq((monthlyData.wxContactArr).concat(dailyData.wxContactArr))
        monthlyData.requiredArr = _.uniq((monthlyData.requiredArr).concat(dailyData.requiredArr))
      }
      monthlyData.poster = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.poster, 0)
      monthlyData.wxForward = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.wxForward, 0)
      monthlyData.personalCreation = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.personalCreation, 0)
      monthlyData.call = monthlyData.callArr.length
      monthlyData.callback = monthlyData.callbackArr.length
      monthlyData.validCall = monthlyData.validCallArr.length
      monthlyData.wxContact = monthlyData.wxContactArr.length
      monthlyData.required = monthlyData.requiredArr.length
    }
    monthlyArr = _.orderBy(monthlyArr, ['unitName', 'groupName', 'salesManager.name'], ['asc', 'asc', 'asc'])
    return monthlyArr
  }

})('sales-monthly-process')

async function updateSalesProcessData(ctx) {
  try {
    let { time = new Date() } = ctx.request.body
    await updateDailyData(time)
    await updateMonthlyData(time)
    return ctx.wrapper.succ({})
  } catch (e) {
    console.log(e)
    console.error(e)
    return ctx.badRequest(e)
  }
}

async function updateDailyData(time) {
  const startTime = moment(time).startOf('month').toDate()
  const endTime = moment(time).endOf('month').toDate()
  const dailyArr = await strapi.query('sales-daily-process').model.find({
    time: { $gte: startTime, $lte: endTime, },
  })
  const opLogs = await strapi.query('customer-op-log').model.find({
    createdAt: { $gte: startTime, $lte: endTime, },
  })
  const records = await strapi.query('customer-follow-record').model.find({
    time: { $gte: startTime, $lte: endTime, },
    recordInvalid: { $ne: true },
  }, { _id: 1, customerService: 1, follower: 1, time: 1 })

  const monthlyArr = await strapi.query('sales-monthly-process').find({
    time: moment(time).startOf('month').toDate(),
    _limit: 1000,
  })
  const customerServiceIds = _.compact(records.map(e => e.customerService))
  const allRecords = await strapi.query('customer-follow-record').model.find({
    customerService: { $in: customerServiceIds },
    recordInvalid: { $ne: true },
  }, {
    _id: 1,
    customerService: 1,
    follower: 1,
    time: 1
  })

  const recordMap = {}
  for (const record of allRecords) {
    if (!recordMap[record.customerService.toString()]) {
      recordMap[record.customerService.toString()] = 1
    } else {
      recordMap[record.customerService.toString()]++
    }
    record.no = recordMap[record.customerService.toString()]
  }
  for (const monthlyData of monthlyArr) {
    monthlyData.salesDailyProcess = _.orderBy(dailyArr.filter(e => e.salesManager.toString() === monthlyData.salesManager.id).map(e => e._doc), ['time'], ['asc'])

    const curOpLogs = opLogs.filter(e => e.operator?.toString() === monthlyData.salesManager.id && e.customerService)
    const curAllRecords = allRecords.filter(e => e.follower?.toString() === monthlyData.salesManager.id)

    for (const dailyData of monthlyData.salesDailyProcess) {
      const curDailyRecords = curAllRecords.filter(e => new Date(e.time).getTime() >= moment(dailyData.time).startOf('day').valueOf() && new Date(e.time).getTime() <= moment(dailyData.time).endOf('day').valueOf())
      const curDailyOpLogs = curOpLogs.filter(e => new Date(e.createdAt).getTime() >= moment(dailyData.time).startOf('day').valueOf() && new Date(e.createdAt).getTime() <= moment(dailyData.time).endOf('day').valueOf())

      dailyData.callArr = _.uniq(curDailyRecords.filter(e => e.no === 1).map(e => e.customerService.toString()))
      dailyData.callbackArr = _.difference(_.uniq(curDailyRecords.filter(e => e.no > 1).map(e => e.customerService.toString())), dailyData.callArr)
      dailyData.validCallArr = _.uniq(curDailyRecords.map(e => e.customerService.toString()))
      dailyData.wxContactArr = _.uniq(curDailyOpLogs.filter(e => e.type === 'wx').map(e => e.customerService.toString()))
      dailyData.requiredArr = _.uniq(curDailyOpLogs.filter(e => e.type === 'required').map(e => e.customerService.toString()))

      dailyData.call = dailyData.callArr.length
      dailyData.callback = dailyData.callbackArr.length
      dailyData.validCall = dailyData.validCallArr.length
      dailyData.wxContact = dailyData.wxContactArr.length
      dailyData.required = dailyData.requiredArr.length

      await strapi.query('sales-daily-process').update({
        id: dailyData._id
      }, {
        call: dailyData.call,
        callback: dailyData.callback,
        validCall: dailyData.validCall,
        wxContact: dailyData.wxContact,
        required: dailyData.required,
        callArr: dailyData.callArr,
        callbackArr: dailyData.callbackArr,
        validCallArr: dailyData.validCallArr,
        wxContactArr: dailyData.wxContactArr,
        requiredArr: dailyData.requiredArr,
      })
    }
  }
}

async function updateMonthlyData(time) {
  const monthlyArr = await strapi.query('sales-monthly-process').find({
    time: moment(time).startOf('month').toDate(),
    _limit: 1000,
  })

  let managerGroups = await getUserGroup()
  const groups = managerGroups.filter(item => item.type === '直营小组');
  for (const monthlyData of monthlyArr) {
    monthlyData.poster = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.poster, 0)
    monthlyData.wxForward = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.wxForward, 0)
    monthlyData.personalCreation = monthlyData.salesDailyProcess.reduce((acc, cur) => acc + cur.personalCreation, 0)

    monthlyData.callArr = []
    monthlyData.callbackArr = []
    monthlyData.validCallArr = []
    monthlyData.wxContactArr = []
    monthlyData.requiredArr = []
    for (const dailyData of monthlyData.salesDailyProcess) {
      monthlyData.callArr = monthlyData.callArr.concat(dailyData.callArr)
      monthlyData.callbackArr = monthlyData.callbackArr.concat(dailyData.callbackArr)
      monthlyData.validCallArr = monthlyData.validCallArr.concat(dailyData.validCallArr)
      monthlyData.wxContactArr = _.uniq((monthlyData.wxContactArr).concat(dailyData.wxContactArr))
      monthlyData.requiredArr = _.uniq((monthlyData.requiredArr).concat(dailyData.requiredArr))
    }

    monthlyData.call = monthlyData.callArr.length
    monthlyData.callback = monthlyData.callbackArr.length
    monthlyData.validCall = monthlyData.validCallArr.length
    monthlyData.wxContact = monthlyData.wxContactArr.length
    monthlyData.required = monthlyData.requiredArr.length

    monthlyData.posterRate = monthlyData.posterTarget && monthlyData.poster ? Math.floor(monthlyData.poster / monthlyData.posterTarget * 100) : 0
    monthlyData.wxForwardRate = monthlyData.wxForwardTarget && monthlyData.wxForward ? Math.floor(monthlyData.wxForward / monthlyData.wxForwardTarget * 100) : 0
    monthlyData.personalCreationRate = monthlyData.personalCreationTarget && monthlyData.personalCreation ? Math.floor(monthlyData.personalCreation / monthlyData.personalCreationTarget * 100) : 0
    monthlyData.callRate = monthlyData.callTarget && monthlyData.call ? Math.floor(monthlyData.call / monthlyData.callTarget * 100) : 0
    monthlyData.callbackRate = monthlyData.callbackTarget && monthlyData.callback ? Math.floor(monthlyData.callback / monthlyData.callbackTarget * 100) : 0
    monthlyData.validCallRate = monthlyData.validCallTarget && monthlyData.validCall ? Math.floor(monthlyData.validCall / monthlyData.validCallTarget * 100) : 0
    monthlyData.wxContactRate = monthlyData.wxContactTarget && monthlyData.wxContact ? Math.floor(monthlyData.wxContact / monthlyData.wxContactTarget * 100) : 0
    monthlyData.requiredRate = monthlyData.requiredTarget && monthlyData.required ? Math.floor(monthlyData.required / monthlyData.requiredTarget * 100) : 0

    const group = groups.find(e => e.members?.map(member => member.id).includes(monthlyData.salesManager.id))
    await strapi.query('sales-monthly-process').update({
      id: monthlyData.id
    }, {
      groupName: group?.name,
      unitName: group?.unit,

      poster: monthlyData.poster,
      wxForward: monthlyData.wxForward,
      personalCreation: monthlyData.personalCreation,
      call: monthlyData.call,
      callback: monthlyData.callback,
      validCall: monthlyData.validCall,
      wxContact: monthlyData.wxContact,
      required: monthlyData.required,

      callArr: monthlyData.callArr,
      callbackArr: monthlyData.callbackArr,
      validCallArr: monthlyData.validCallArr,
      wxContactArr: monthlyData.wxContactArr,
      requiredArr: monthlyData.requiredArr,

      posterRate: monthlyData.posterRate,
      wxForwardRate: monthlyData.wxForwardRate,
      personalCreationRate: monthlyData.personalCreationRate,
      callRate: monthlyData.callRate,
      callbackRate: monthlyData.callbackRate,
      validCallRate: monthlyData.validCallRate,

      wxContactRate: monthlyData.wxContactRate,
      requiredRate: monthlyData.requiredRate,
    })
  }
  return
}

async function initSalesMonthlyProcess(ctx) {
  try {
    let { time = new Date() } = ctx.request.body
    await initSalesProcess.initSalesMonthlyProcess(time)
    return ctx.wrapper.succ({})
  } catch (e) {
    console.log(e)
    console.error(e)
    return ctx.badRequest(e)
  }
}

module.exports = {
  updateSalesProcessData,
  initSalesMonthlyProcess,
  ...curdRouter.createHandlers(),
}
