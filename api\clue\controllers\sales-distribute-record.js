const { CurdRouter } = require('accel-utils')
const curdRouter = new (class extends CurdRouter {
  async _getQueryByUser(query, user) {
    // 新经理为本人 没有运营组或没有直营组 的学校
    if (['sales-group-member'].includes(user.role.type)) {
      query.manager_eq = [user.mingdaoId]
    }
    if (['sales-group-leader'].includes(user.role.type)) {
      const users = await strapi.query('manager').find({ groups: user.mingdaoGroupIds, _limit: 9999 })
      query.manager_eq = users.map(e => e.id)
    }
    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = await this._getQueryByUser(query, user)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = await this._getQueryByUser(query, user)
    return super.find(ctx)
  }
})('sales-distribute-record')

module.exports = {
  ...curdRouter.createHandlers(),
}
