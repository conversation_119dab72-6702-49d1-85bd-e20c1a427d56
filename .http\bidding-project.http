@baseUrl = http://localhost:3015

# @name login
POST {{baseUrl}}/uc/loginByAccount HTTP/1.1
Content-Type: application/json

{
  "account": "<EMAIL>",
  "password": "123456",
  "appId": "61b062c033f23d6a74933ec8"
}

###

@token = Bearer {{login.response.body.data.jwt}}

# @name getEnv
GET {{baseUrl}}/users/me HTTP/1.1
Authorization: {{token}}
Content-Type: application/json

###
POST {{baseUrl}}/bidding-s1-projects/action/exportRelationData HTTP/1.1
Authorization: {{token}}
Content-Type: application/json

{}


### 获取书本数据
GET http://localhost:8004/book-storages
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.RB8R2j-zoI9DgOqCvvSpD4Eod2i3EuoJ0LeHfEkGvCE

### 获取书本数据
GET http://localhost:8004/book-storages/678e29e0be9c325c680edd1f/data
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.RB8R2j-zoI9DgOqCvvSpD4Eod2i3EuoJ0LeHfEkGvCE

### 创建书本数据
POST http://localhost:8004/book-storages
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************.RB8R2j-zoI9DgOqCvvSpD4Eod2i3EuoJ0LeHfEkGvCE
Content-Type: application/json

{
    "key": "11112222",
    "data": {
        "name": "test2"
    }
}

### 新增作者
POST http://localhost:8004/authors
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OWNmNTBmYjg1ZTExN2QwMTc1NDVlZSIsImlhdCI6MTczNzcwOTE5Nn0.TSioY72qFseKju9BehhP5KvqZVYEFi2xoW98wtfd4aM
Content-Type: application/json

{
    "name":  "authorzzz",
    "description":  "描述1",
    "type":  "signed",
    "signNo": "111222"
}
### 新增文章
POST http://localhost:8004/posts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OWNmNTBmYjg1ZTExN2QwMTc1NDVlZSIsImlhdCI6MTczNzcwOTE5Nn0.TSioY72qFseKju9BehhP5KvqZVYEFi2xoW98wtfd4aM
Content-Type: application/json

{
  "_id": "aaa1234592234434",
  "author": 111223,
  "title": "aaa",
  "description":  "描述1"
}

### 查看文章
GET http://localhost:8004/posts
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OWNmNTBmYjg1ZTExN2QwMTc1NDVlZSIsImlhdCI6MTczNzcwOTE5Nn0.TSioY72qFseKju9BehhP5KvqZVYEFi2xoW98wtfd4aM
Content-Type: application/json

### 查询文章
POST http://localhost:8004/posts/123321/getPostData
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OWNmNTBmYjg1ZTExN2QwMTc1NDVlZSIsImlhdCI6MTczNzcwOTE5Nn0.TSioY72qFseKju9BehhP5KvqZVYEFi2xoW98wtfd4aM
Content-Type: application/json


### 新增招标项目
POST http://localhost:3015/bidding-projects
Authorization: Bearer eyJhbGciOiJIUzI0NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3YTU4MTEzZjEyYmU0Yzc4Y2U3MjRhMCIsImlhdCI6MTczODk4NzExOH0.p_0zZXsS_uAr4iwIVoMo6Andp9bhUFzbAuIzIJpyqUs
Content-Type: application/json

{
  "title": "2123122",
  "buyer": "弋阳县公安局",
  "winner": "弋阳县远洋科技服务中心",
  "bidAmount": "0.71",
  "type": "8e8849be-0630-49f9-a777-3be776774047",
  "subtype": "7a8183cb-b699-41c6-bfac-f7aa318a9516",
  "publishTime": "2025-02-08"
}

### 查找合并的招标数据 eyJhbGciOiJIUzI0NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3YTU4MTEzZjEyYmU0Yzc4Y2U3MjRhMCIsImlhdCI6MTczODk4NzExOH0.p_0zZXsS_uAr4iwIVoMo6Andp9bhUFzbAuIzIJpyqUs
POST http://localhost:3015/bidding-projects/action/groupByAttr
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3YTU4MTEzZjEyYmU0Yzc4Y2U3MjRhMCIsImlhdCI6MTczODk4NzExOH0.p_0zZXsS_uAr4iwIVoMo6Andp9bhUFzbAuIzIJpyqUs
Content-Type: application/json

{
  "title": "墨"
}

### 清除招标项目
DELETE http://localhost:3015/bidding-projects
Authorization: Bearer eyJhbGciOiJIUzI0NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY3YTU4MTEzZjEyYmU0Yzc4Y2U3MjRhMCIsImlhdCI6MTczODk4NzExOH0.p_0zZXsS_uAr4iwIVoMo6Andp9bhUFzbAuIzIJpyqUs
Content-Type: application/json

{

}
