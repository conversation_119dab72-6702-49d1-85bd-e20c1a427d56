const fs = require('fs');
const path = require('path');
const { generateCrudSwaggerDocs } = require('./helper');
const { getTags } = require('./tags');
const { createDefaultPermissions } = require('accel-utils');

// 缓存权限配置
let cachedPermissions = null;

/**
 * 检查是否应该忽略某个控制器
 * @param {string} controller 控制器名称
 * @returns {boolean} 是否应该忽略
 */
function shouldIgnoreController(controller) {
  try {
    const tagsConfig = getTags();
    const ignoreConfig = tagsConfig.ignore || { controllers: [], paths: [] };
    const ignoreControllers = ignoreConfig.controllers || [];
    
    for (const pattern of ignoreControllers) {
      // 如果是正则表达式
      if (pattern instanceof RegExp) {
        if (pattern.test(controller)) {
          return true;
        }
      }
      // 如果是字符串，进行精确匹配
      else if (typeof pattern === 'string') {
        if (pattern === controller) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error('检查控制器忽略配置失败:', error);
    return false;
  }
}

/**
 * 检查是否应该忽略某个路径
 * @param {string} path 路径
 * @returns {boolean} 是否应该忽略
 */
function shouldIgnorePath(path) {
  try {
    const tagsConfig = getTags();
    const ignoreConfig = tagsConfig.ignore || { controllers: [], paths: [] };
    const ignorePaths = ignoreConfig.paths || [];
    
    for (const pattern of ignorePaths) {
      // 如果是正则表达式
      if (pattern instanceof RegExp) {
        if (pattern.test(path)) {
          return true;
        }
      }
      // 如果是字符串，进行精确匹配
      else if (typeof pattern === 'string') {
        if (pattern === path) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error('检查路径忽略配置失败:', error);
    return false;
  }
}

/**
 * 检查是否应该忽略某个模型
 * @param {string} modelName 模型名称
 * @returns {boolean} 是否应该忽略
 */
function shouldIgnoreModel(modelName) {
  try {
    const tagsConfig = getTags();
    const ignoreConfig = tagsConfig.ignore || { controllers: [], paths: [], models: [] };
    const ignoreModels = ignoreConfig.models || [];
    
    for (const pattern of ignoreModels) {
      // 如果是正则表达式
      if (pattern instanceof RegExp) {
        if (pattern.test(modelName)) {
          return true;
        }
      }
      // 如果是字符串，进行精确匹配
      else if (typeof pattern === 'string') {
        if (pattern === modelName) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error('检查模型忽略配置失败:', error);
    return false;
  }
}

/**
 * 加载权限配置
 */
function loadPermissions() {
  if (cachedPermissions) {
    return cachedPermissions;
  }
  
  try {
    const permissionPath = path.join(process.cwd(), 'config', 'permission.js');
    if (fs.existsSync(permissionPath)) {
      delete require.cache[permissionPath];
      cachedPermissions = require(permissionPath);
      return cachedPermissions;
    }
  } catch (error) {
    console.error('加载权限配置失败:', error);
  }
  
  return { functions: [] };
}

/**
 * 展开权限配置中的 createDefaultPermissions
 */
function expandPermissions(permissions) {
  const expanded = [];
  
  permissions.forEach(perm => {
    // 如果是普通权限对象，直接添加
    if (perm && typeof perm === 'object' && perm.type) {
      expanded.push(perm);
    }
  });
  
  return expanded;
}

/**
 * 检查接口是否为公开接口（无需认证）
 */
function isPublicEndpoint(controller, action) {
  const permissions = loadPermissions();
  const publicFunction = permissions.functions.find(f => f.sId === 'PublicFunction');
  
  if (!publicFunction || !publicFunction.apiPermissions) {
    return false;
  }
  
  // 读取permission.js文件内容来检测createDefaultPermissions的使用
  let hasCreateDefaultPermissions = false;
  let defaultPermissionControllers = [];
  
  try {
    const permissionPath = path.join(process.cwd(), 'config', 'permission.js');
    const permissionContent = fs.readFileSync(permissionPath, 'utf8');
    
    // 查找PublicFunction部分
    const publicFunctionMatch = permissionContent.match(/sId\s*:\s*['"]PublicFunction['"]\s*,[\s\S]*?apiPermissions\s*:\s*\[([\s\S]*?)\]/);
    
    if (publicFunctionMatch) {
      const apiPermissionsContent = publicFunctionMatch[1];
      
      // 查找所有 createDefaultPermissions 调用
      const createDefaultPermissionsRegex = /\.\.\.createDefaultPermissions\s*\(\s*\{[^}]*controller\s*:\s*['"]([^'"]+)['"]/g;
      let match;
      
      while ((match = createDefaultPermissionsRegex.exec(apiPermissionsContent)) !== null) {
        defaultPermissionControllers.push(match[1]);
      }
    }
  } catch (error) {
    console.error('解析权限文件失败:', error);
  }
  
  // 如果是通过 createDefaultPermissions 添加的控制器
  if (defaultPermissionControllers.includes(controller)) {
    const crudActions = ['find', 'findOne', 'create', 'update', 'delete', 'count'];
    return crudActions.includes(action);
  }
  
  // 检查具体的权限定义
  const expandedPermissions = expandPermissions(publicFunction.apiPermissions);
  const hasExplicitPermission = expandedPermissions.some(perm => 
    perm.type === 'application' && 
    perm.controller === controller && 
    perm.action === action
  );
  
  return hasExplicitPermission;
}

/**
 * 仅加载模型的基本信息（用于获取label等，不受ignore.models影响）
 * @param {string} controller 控制器名称
 * @returns {Object|null} 模型定义对象或null
 */
function loadModelInfoOnly(controller) {
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const extensions = ['.js', '.json'];
    
    // 首先尝试基于 controller 名称的目录
    const controllerDir = controller.split('-')[0];
    
    for (const ext of extensions) {
      const directPath = path.join(apiDir, controllerDir, 'models', `${controller}${ext}`);
      if (fs.existsSync(directPath)) {
        if (ext === '.js') {
          delete require.cache[directPath];
        }
        return require(directPath);
      }
    }
    
    // 如果没找到，扫描所有 API 目录
    const modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });
    
    for (const module of modules) {
      const modelsDir = path.join(apiDir, module, 'models');
      if (fs.existsSync(modelsDir)) {
        for (const ext of extensions) {
          const modelPath = path.join(modelsDir, `${controller}${ext}`);
          if (fs.existsSync(modelPath)) {
            if (ext === '.js') {
              delete require.cache[modelPath];
            }
            return require(modelPath);
          }
        }
      }
    }
    
    return null;
  } catch (error) {
    console.error(`加载模型信息失败: ${controller}`, error);
    return null;
  }
}

/**
 * 加载模型定义（用于Schema生成，受ignore.models影响）
 */
function loadModelDefinition(controller) {
  try {
    // 检查是否应该忽略这个模型
    if (shouldIgnoreModel(controller)) {
      console.log(`忽略模型: ${controller} (根据过滤配置)`);
      return null;
    }
    
    const apiDir = path.join(process.cwd(), 'api');
    const extensions = ['.js', '.json'];
    
    // 首先尝试基于 controller 名称的目录
    const controllerDir = controller.split('-')[0];
    
    for (const ext of extensions) {
      const directPath = path.join(apiDir, controllerDir, 'models', `${controller}${ext}`);
      if (fs.existsSync(directPath)) {
        if (ext === '.js') {
          delete require.cache[directPath];
        }
        return require(directPath);
      }
    }
    
    // 如果没找到，扫描所有 API 目录
    const modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });
    
    for (const moduleName of modules) {
      for (const ext of extensions) {
        const modelPath = path.join(apiDir, moduleName, 'models', `${controller}${ext}`);
        if (fs.existsSync(modelPath)) {
          if (ext === '.js') {
            delete require.cache[modelPath];
          }
          return require(modelPath);
        }
      }
    }
  } catch (error) {
    console.error(`加载模型 ${controller} 失败:`, error);
  }
  
  return null;
}

/**
 * 将模型属性转换为 Swagger Schema
 */
function modelToSwaggerSchema(model, isCreateOrUpdate = false) {
  if (!model || !model.attributes) {
    return { type: 'object' };
  }
  
  const schema = {
    type: 'object',
    properties: {},
    required: []
  };
  
  Object.entries(model.attributes).forEach(([key, attr]) => {
    // 跳过不可见字段
    if (attr.visible === false || attr.private === true) {
      return;
    }
    
    // 处理关联字段
    if (attr.model || attr.collection) {
      schema.properties[key] = generateRelationSchema(attr, key);
      return;
    }
    
    // 处理动态组件类型
    if (attr.type === 'dynamiczone') {
      schema.properties[key] = {
        type: 'array',
        description: attr.label || key,
        items: {
          type: 'object',
          description: `动态组件，支持类型: ${attr.components ? attr.components.join(', ') : '未指定'}`
        }
      };
      return;
    }
    
    // 使用优化的字段Schema生成
    schema.properties[key] = generateFieldSchema(attr, key);
    
    // 标记必填字段
    if (attr.required && isCreateOrUpdate) {
      schema.required.push(key);
    }
  });
  
  // 如果不是创建或更新操作，添加系统字段
  if (!isCreateOrUpdate) {
    schema.properties.id = {
      type: 'string',
      description: 'ID'
    };
    
    if (model.options?.timestamps !== false) {
      schema.properties.createdAt = {
        type: 'string',
        format: 'date-time',
        description: '创建时间'
      };
      schema.properties.updatedAt = {
        type: 'string',
        format: 'date-time',
        description: '更新时间'
      };
    }
  }
  
  return schema;
}

/**
 * 生成关联字段的Schema
 */
function generateRelationSchema(attr, key) {
  if (attr.collection) {
    // 一对多关联
    return {
      type: 'array',
      description: attr.label || `${attr.collection} 集合`,
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', description: 'ID' },
          [attr.mainField || 'name']: { type: 'string', description: '显示字段' }
        }
      }
    };
  } else if (attr.model) {
    // 一对一关联
    return {
      type: 'object',
      description: attr.label || `${attr.model} 对象`,
      properties: {
        id: { type: 'string', description: 'ID' },
        [attr.mainField || 'name']: { type: 'string', description: '显示字段' }
      }
    };
  }
  
  return { type: 'string', description: attr.label || key };
}

/**
 * 生成字段的详细Schema
 */
function generateFieldSchema(attr, key) {
  const fieldSchema = {
    description: attr.label || key
  };
  
  // 处理JSON Schema
  if (attr.jsonSchema) {
    return {
      ...attr.jsonSchema,
      description: attr.label || key
    };
  }
  
  // 基础类型映射
  const typeMap = {
    'string': 'string',
    'text': 'string',
    'richtext': 'string',
    'email': 'string',
    'password': 'string',
    'uid': 'string',
    'enumeration': 'string',
    'integer': 'integer',
    'biginteger': 'integer',
    'float': 'number',
    'decimal': 'number',
    'boolean': 'boolean',
    'date': 'string',
    'datetime': 'string',
    'time': 'string',
    'json': 'object',
    'jsonb': 'object',
    'array': 'array'
  };
  
  fieldSchema.type = typeMap[attr.type] || 'string';
  
  // 添加格式信息
  if (attr.type === 'email') {
    fieldSchema.format = 'email';
  } else if (attr.type === 'date') {
    fieldSchema.format = 'date';
  } else if (attr.type === 'datetime') {
    fieldSchema.format = 'date-time';
  } else if (attr.type === 'password') {
    fieldSchema.format = 'password';
  }
  
  // 处理数组类型
  if (attr.type === 'array' || fieldSchema.type === 'array') {
    fieldSchema.items = {
      type: 'string',
      description: '数组元素'
    };
  }
  
  // 添加枚举值
  if (attr.enum && Array.isArray(attr.enum)) {
    fieldSchema.enum = attr.enum;
  } else if (attr.options && Array.isArray(attr.options)) {
    fieldSchema.enum = attr.options.map(opt => 
      typeof opt === 'object' ? opt.value : opt
    );
  }
  
  // 添加验证约束
  addValidationConstraints(fieldSchema, attr);
  
  // 添加默认值
  if (attr.default !== undefined) {
    fieldSchema.example = attr.default;
  }
  
  return fieldSchema;
}

/**
 * 添加验证约束到Schema
 */
function addValidationConstraints(schema, attr) {
  // 字符串长度约束
  if (attr.minLength !== undefined) {
    schema.minLength = attr.minLength;
  }
  if (attr.maxLength !== undefined) {
    schema.maxLength = attr.maxLength;
  }
  
  // 数值范围约束
  if (attr.min !== undefined) {
    schema.minimum = attr.min;
  }
  if (attr.max !== undefined) {
    schema.maximum = attr.max;
  }
  
  // 数组约束
  if (attr.minItems !== undefined) {
    schema.minItems = attr.minItems;
  }
  if (attr.maxItems !== undefined) {
    schema.maxItems = attr.maxItems;
  }
  
  // 唯一性标识
  if (attr.unique === true) {
    schema.description = `${schema.description || ''} (唯一字段)`.trim();
  }
  
  // 正则表达式约束
  if (attr.regex) {
    schema.pattern = attr.regex;
  }
}

/**
 * 构建查询参数
 * @param {Object} model - 模型定义
 * @returns {Array} 查询参数数组
 */
function buildQueryParameters(model) {
  // 基础查询参数
  const queryParameters = [
    {
      name: '_limit',
      in: 'query',
      description: '每页返回数量',
      schema: { type: 'integer', default: 10 }
    },
    {
      name: '_start',
      in: 'query',
      description: '偏移量',
      schema: { type: 'integer', default: 0 }
    },
    {
      name: '_sort',
      in: 'query',
      description: '排序字段:排序方式 (如: name:ASC)',
      schema: { type: 'string' }
    },
    {
      name: '_q',
      in: 'query',
      description: '搜索关键词',
      schema: { type: 'string' }
    },
    {
      name: '_where',
      in: 'query',
      description: 'MongoDB查询条件(JSON格式)',
      schema: { type: 'string' }
    }
  ];
  
  // 添加模型字段作为查询参数
  if (model && model.attributes) {
    const fieldParams = [];
    
    Object.entries(model.attributes).forEach(([fieldName, fieldConfig]) => {
      // 跳过不可见和关联字段
      if (fieldConfig.visible === false || fieldConfig.model || fieldConfig.collection) {
        return;
      }
      
      // 跳过某些系统字段
      if (['id', '_id', 'created_at', 'updated_at', 'createdAt', 'updatedAt'].includes(fieldName)) {
        return;
      }
      
      // 生成字段查询参数
      const fieldType = fieldConfig.type || 'string';
      let paramType = 'string';
      
      // 类型映射
      const typeMap = {
        'string': 'string',
        'text': 'string',
        'richtext': 'string',
        'email': 'string',
        'password': 'string',
        'uid': 'string',
        'enumeration': 'string',
        'integer': 'integer',
        'biginteger': 'integer',
        'float': 'number',
        'decimal': 'number',
        'boolean': 'boolean',
        'date': 'string',
        'datetime': 'string',
        'time': 'string',
        'json': 'object',
        'array': 'array'
      };
      
      paramType = typeMap[fieldType] || 'string';
      
      const fieldParam = {
        name: fieldName,
        in: 'query',
        description: `按${fieldConfig.label || fieldName}查询`,
        schema: { type: paramType }
      };
      
      // 特殊字段类型处理
      if (fieldConfig.type === 'boolean') {
        fieldParam.description += '（true/false）';
      } else if (fieldConfig.type === 'date' || fieldConfig.type === 'datetime') {
        fieldParam.description += '（ISO 8601格式）';
        fieldParam.schema.format = 'date-time';
      } else if (fieldConfig.type === 'enumeration' && fieldConfig.enum) {
        fieldParam.schema.enum = fieldConfig.enum;
        fieldParam.description += `（可选值：${fieldConfig.enum.join(', ')}）`;
      } else if (fieldConfig.options && Array.isArray(fieldConfig.options)) {
        const values = fieldConfig.options.map(opt => 
          typeof opt === 'object' ? opt.value : opt
        ).filter(Boolean);
        if (values.length > 0) {
          fieldParam.schema.enum = values;
          fieldParam.description += `（可选值：${values.join(', ')}）`;
        }
      }
      
      // 添加范围查询参数（对于数字和日期类型）
      if (['integer', 'decimal', 'float', 'biginteger', 'date', 'datetime'].includes(fieldConfig.type)) {
        // 大于等于
        fieldParams.push({
          name: `${fieldName}_gte`,
          in: 'query',
          description: `${fieldConfig.label || fieldName}大于等于`,
          schema: { type: paramType, format: fieldParam.schema.format }
        });
        // 小于等于
        fieldParams.push({
          name: `${fieldName}_lte`,
          in: 'query',
          description: `${fieldConfig.label || fieldName}小于等于`,
          schema: { type: paramType, format: fieldParam.schema.format }
        });
      }
      
      // 添加模糊查询参数（对于字符串类型）
      if (fieldConfig.type === 'string' || fieldConfig.type === 'text') {
        fieldParams.push({
          name: `${fieldName}_contains`,
          in: 'query',
          description: `${fieldConfig.label || fieldName}包含（模糊查询）`,
          schema: { type: 'string' }
        });
      }
      
      fieldParams.push(fieldParam);
    });
    
    // 将字段参数添加到查询参数列表
    queryParameters.push(...fieldParams);
  }
  
  return queryParameters;
}

/**
 * 自动扫描并生成 Swagger 路径定义
 * 识别所有使用 createDefaultRoutes 创建的路由
 * @param {string|Array} filterModules - 要扫描的模块，如果为空则扫描全部
 * @param {string} filterController - 要扫描的控制器，如果为空则扫描全部
 */
function generateAutoSwaggerPaths(filterModules = null, filterController = null) {
  const paths = {};
  const apiDir = path.join(process.cwd(), 'api');
  
  // 扫描所有 API 模块
  let modules = fs.readdirSync(apiDir).filter(file => {
    const stat = fs.statSync(path.join(apiDir, file));
    return stat.isDirectory();
  });
  
  // 如果指定了模块过滤，只扫描指定的模块
  if (filterModules) {
    const targetModules = Array.isArray(filterModules) ? filterModules : [filterModules];
    modules = modules.filter(module => targetModules.includes(module));
    console.log(`只扫描指定模块: ${targetModules.join(', ')}`);
  }

  modules.forEach(moduleName => {
    const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
    const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
    const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
    
    // 检查 routes.js、route.js 或 routers.js
    for (const routeFilePath of [routesPath, routePath, routersPath]) {
      if (fs.existsSync(routeFilePath)) {
        try {
          // 读取路由文件内容
          const routesContent = fs.readFileSync(routeFilePath, 'utf8');
        
        // 使用正则匹配 createDefaultRoutes 调用，支持controller和basePath的任意顺序
        const createDefaultRoutesRegex = /createDefaultRoutes\s*\(\s*\{([^}]+)\}\s*\)/g;
        
        let match;
        while ((match = createDefaultRoutesRegex.exec(routesContent)) !== null) {
          const configContent = match[1];
          
          // 分别提取controller和basePath
          const controllerMatch = configContent.match(/controller\s*:\s*['"`]([^'"`]+)['"`]/);
          const basePathMatch = configContent.match(/basePath\s*:\s*['"`]([^'"`]+)['"`]/);
          
          if (controllerMatch && basePathMatch) {
            const controller = controllerMatch[1];
            const basePath = basePathMatch[1];
            
            // 检查是否应该忽略这个控制器
            if (shouldIgnoreController(controller)) {
              console.log(`忽略控制器: ${controller} (根据过滤配置)`);
              continue;
            }
            
            // 如果指定了控制器过滤，只处理指定的控制器
            if (filterController && controller !== filterController) {
              continue;
            }
            
            // 检查是否应该忽略这个路径
            if (shouldIgnorePath(basePath)) {
              console.log(`忽略路径: ${basePath} (根据过滤配置)`);
              continue;
            }
            
            // 使用 generateCrudSwaggerTags 来生成带有正确标签的路径
            const crudPaths = generateCrudSwaggerTags(basePath, controller);
            
            // 合并路径
            Object.assign(paths, crudPaths);
          }
        }
        
        // 扫描自定义路由 - 支持字段任意顺序
        const customRoutesRegex = /\{[^}]*\}/g;
        const routes = routesContent.match(customRoutesRegex);
        
        if (routes) {
          routes.forEach(routeBlock => {
            // 提取路由配置的各个字段
            const pathMatch = routeBlock.match(/['"]?path['"]?\s*:\s*[`'"]([^`'"]+)[`'"]/);
            const methodMatch = routeBlock.match(/['"]?method['"]?\s*:\s*[`'"]([^`'"]+)[`'"]/);
            const handlerMatch = routeBlock.match(/['"]?handler['"]?\s*:\s*[`'"]([^`'"]+)[`'"]/);
            
            // 如果三个字段都存在，则处理这个路由
            if (pathMatch && methodMatch && handlerMatch) {
              const routePath = pathMatch[1];
              const method = methodMatch[1].toLowerCase();
              const handler = handlerMatch[1];
              
              
              // 跳过已经通过 createDefaultRoutes 处理的路径
              const normalizedPath = routePath.replace(/:(\w+)/g, '{$1}');
              
              // 检查是否是标准CRUD路径（精确匹配）
              const isDefaultRoute = Object.keys(paths).includes(normalizedPath);
              
              if (!isDefaultRoute) {
                
                // 获取控制器名称和方法名
                const [controllerName, methodName] = handler.split('.');
                
                // 检查是否应该忽略这个控制器
                if (shouldIgnoreController(controllerName)) {
                  console.log(`忽略自定义路由控制器: ${controllerName} (根据过滤配置)`);
                  return; // 使用 return 而不是 continue
                }
                
                // 如果指定了控制器过滤，只处理指定的控制器
                if (filterController && controllerName !== filterController) {
                  return; // 使用 return 而不是 continue
                }
                
                // 检查是否应该忽略这个路径
                if (shouldIgnorePath(routePath) || shouldIgnorePath(normalizedPath)) {
                  console.log(`忽略自定义路径: ${routePath} (根据过滤配置)`);
                  return; // 使用 return 而不是 continue
                }
                
                // 检查控制器文件是否存在且方法是否有效
                if (!isValidControllerMethod(moduleName, controllerName, methodName)) {
                  console.log(`跳过无效的控制器方法: ${handler}`);
                  return; // 使用 return 而不是 continue
                }
                
                if (!paths[normalizedPath]) {
                  paths[normalizedPath] = {};
                }
                
                const tag = getModelDescription(controllerName);
                
                // 为自定义路由生成基础文档
                paths[normalizedPath][method] = {
                  tags: [tag],
                  summary: `${methodName}`,
                  description: `${method.toUpperCase()} ${routePath} - ${handler}`,
                  responses: {
                    200: { description: '成功' }
                  }
            };
            
            // 特殊处理开放接口
            const tagsConfig = getTags();
            if (routePath.startsWith('/open/')) {
              paths[normalizedPath][method].security = [];
              const openTag = tagsConfig.pathTags && tagsConfig.pathTags['/open'];
              if (openTag && openTag.name) {
                paths[normalizedPath][method].tags = [openTag.name];
              }
            } else if (routePath.startsWith('/external/')) {
              paths[normalizedPath][method].security = [{ accessKey: [] }];
              const externalTag = tagsConfig.pathTags && tagsConfig.pathTags['/external'];
              if (externalTag && externalTag.name) {
                paths[normalizedPath][method].tags = [externalTag.name];
              }
            } else {
              // 检查自定义接口是否为公开接口
              if (isPublicEndpoint(controllerName, methodName)) {
                paths[normalizedPath][method].security = [];
              }
            }
          }
            }
          });
        }
        
        } catch (error) {
          console.error(`处理路由文件失败: ${routePath}`, error);
        }
      }
    }
  });

  // 扫描并生成扩展方法的文档
  const extendMethodPaths = scanExtendMethods(filterModules, filterController);
  Object.assign(paths, extendMethodPaths);
  
  return paths;
}

/**
 * 扫描控制器中的扩展方法并生成对应的路由文档
 * @param {string|Array} filterModules - 要扫描的模块，如果为空则扫描全部
 * @param {string} filterController - 要扫描的控制器，如果为空则扫描全部
 * @returns {Object} 扩展方法的路由文档
 */
function scanExtendMethods(filterModules = null, filterController = null) {
  const paths = {};
  
  try {
    const apiDir = path.join(process.cwd(), 'api');
    let modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });
    
    // 如果指定了模块过滤，只扫描指定的模块
    if (filterModules) {
      const targetModules = Array.isArray(filterModules) ? filterModules : [filterModules];
      modules = modules.filter(module => targetModules.includes(module));
    }

    for (const moduleName of modules) {
      // 获取模块的路由配置，找到 basePath
      const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
      const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
      const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
      
      for (const routeFilePath of [routesPath, routePath, routersPath]) {
        if (!fs.existsSync(routeFilePath)) continue;
        
        try {
          const routesContent = fs.readFileSync(routeFilePath, 'utf8');
          
          // 收集该模块中已经单独配置的扩展方法
          const configuredExtendMethods = collectConfiguredExtendMethods(routesContent);
          
          // 查找所有的 createDefaultRoutes 配置
          const createDefaultRoutesRegex = /createDefaultRoutes\s*\(\s*\{([^}]+)\}\s*\)/g;
          let match;
          
          while ((match = createDefaultRoutesRegex.exec(routesContent)) !== null) {
            const configContent = match[1];
            const controllerMatch = configContent.match(/controller\s*:\s*['"`]([^'"`]+)['"`]/);
            const basePathMatch = configContent.match(/basePath\s*:\s*['"`]([^'"`]+)['"`]/);
            
            if (controllerMatch && basePathMatch) {
              const controller = controllerMatch[1];
              const basePath = basePathMatch[1];
              
              // 检查是否应该忽略这个控制器
              if (shouldIgnoreController(controller)) {
                continue;
              }
              
              // 如果指定了控制器过滤，只处理指定的控制器
              if (filterController && controller !== filterController) {
                continue;
              }
              
              // 获取扩展方法并生成文档（只有在控制器确实有路由配置时才生成）
              const extendMethods = getControllerExtendMethods(controller);
              
              if (extendMethods.length > 0) {
                // 过滤掉已经在 routes.js 中单独配置的扩展方法
                const unconfiguredMethods = extendMethods.filter(methodName => {
                  const methodKey = `${controller}.${methodName}`;
                  return !configuredExtendMethods.has(methodKey);
                });
                
                if (unconfiguredMethods.length > 0) {
                  console.log(`发现控制器 ${controller} 的未配置扩展方法:`, unconfiguredMethods);
                  console.log(`已配置的扩展方法:`, Array.from(configuredExtendMethods).filter(m => m.startsWith(`${controller}.`)));
                  
                  for (const methodName of unconfiguredMethods) {
                    const methodPath = `${basePath}/${methodName}`;
                    const normalizedPath = methodPath.replace(/:(\w+)/g, '{$1}');
                    
                    // 检查是否应该忽略这个路径
                    if (shouldIgnorePath(methodPath) || shouldIgnorePath(normalizedPath)) {
                      continue;
                    }
                    
                    if (!paths[normalizedPath]) {
                      paths[normalizedPath] = {};
                    }
                    
                    const tag = getModelDescription(controller);
                    
                    // 为扩展方法生成基础文档
                    // 扩展方法如果没有在 routes.js 中单独定义，统一按照 GET 请求方法处理
                    const httpMethod = inferHttpMethodFromName(methodName);
                    
                    paths[normalizedPath][httpMethod] = {
                      tags: [tag],
                      summary: `${methodName} - 扩展方法`,
                      description: `控制器 ${controller} 的扩展方法`,
                      parameters: [
                        {
                          name: '_limit',
                          in: 'query',
                          description: '限制返回数量',
                          schema: { type: 'integer' }
                        },
                        {
                          name: '_start',
                          in: 'query',
                          description: '偏移量',
                          schema: { type: 'integer' }
                        }
                      ],
                      responses: {
                        200: { description: '成功' }
                      }
                    };
                  }
                }
              }
            }
          }
          
        } catch (error) {
          console.error(`处理扩展方法路由失败: ${routePath}`, error);
        }
      }
    }
    
  } catch (error) {
    console.error('扫描扩展方法失败:', error);
  }
  
  return paths;
}

/**
 * 收集routes.js中已经单独配置的扩展方法
 * @param {string} routesContent 路由文件内容
 * @returns {Set} 已配置的扩展方法集合 (格式: controller.method)
 */
function collectConfiguredExtendMethods(routesContent) {
  const configuredMethods = new Set();
  
  try {
    // 过滤掉注释行
    const lines = routesContent.split('\n');
    const activeLines = lines.filter(line => {
      const trimmed = line.trim();
      return !trimmed.startsWith('//') && !trimmed.startsWith('*') && trimmed.length > 0;
    });
    const activeContent = activeLines.join('\n');
    
    // 匹配手动配置的路由
    const customRoutesRegex = /\{[^}]*\}/g;
    const routes = activeContent.match(customRoutesRegex);
    
    if (routes) {
      routes.forEach(routeBlock => {
        // 提取handler配置
        const handlerMatch = routeBlock.match(/['"]?handler['"]?\s*:\s*[`'"]([^`'"]+)[`'"]/);
        
        if (handlerMatch) {
          const handler = handlerMatch[1];
          const [controllerName, methodName] = handler.split('.');
          
          if (controllerName && methodName) {
            // 检查这个方法是否是扩展方法（排除标准CRUD方法）
            const standardMethods = [
              'find', 'count', 'findOne', 'create', 'update', 'updateMany', 'delete', 'deleteMany',
              'branchFind', 'branchCount', 'branchFindOne', 'branchCreate', 'branchUpdate', 'branchUpdateMany', 
              'branchDelete', 'branchDeleteMany', 'branchExport', 'branchImport',
              'branchSelfFind', 'branchSelfCount', 'branchSelfFindOne', 'branchSelfCreate', 'branchSelfUpdate', 
              'branchSelfUpdateMany', 'branchSelfDelete', 'branchSelfDeleteMany', 'branchSelfExport', 'branchSelfImport',
              'export', 'import', 'selfFind', 'selfCount', 'selfFindOne', 'selfCreate', 'selfUpdate', 
              'selfUpdateMany', 'selfDelete', 'selfDeleteMany', 'selfExport', 'selfImport'
            ];
            
            // 如果不是标准CRUD方法，则认为是扩展方法
            if (!standardMethods.includes(methodName)) {
              configuredMethods.add(handler);
            }
          }
        }
      });
    }
  } catch (error) {
    console.error('收集已配置扩展方法失败:', error);
  }
  
  return configuredMethods;
}

/**
 * 获取控制器中的扩展方法
 * @param {string} controller 控制器名称
 * @returns {Array} 扩展方法名称数组
 */
function getControllerExtendMethods(controller) {
  const extendMethods = [];
  
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });

    for (const moduleName of modules) {
      const controllerPath = path.join(apiDir, moduleName, 'controllers', `${controller}.js`);
      
      if (fs.existsSync(controllerPath)) {
        const controllerContent = fs.readFileSync(controllerPath, 'utf8');
        
        // 检查是否使用了 CurdRouter 或 BranchCurdRouter
        const routerUsageRegex = /(?:const|let|var)\s*\{[^}]*(?:CurdRouter|BranchCurdRouter)[^}]*\}\s*=\s*require\s*\(\s*['"`]accel-utils['"`]\s*\)/;
        const routerInstantiationRegex = /new\s*\(\s*class\s+extends\s+(BranchCurdRouter|CurdRouter)/;
        
        if (routerUsageRegex.test(controllerContent) || routerInstantiationRegex.test(controllerContent)) {
          
          // 提取类中定义的方法 - 匹配类方法定义
          const classMethodRegex = /^\s*(?:async\s+)?(\w+)\s*\([^)]*\)\s*\{/gm;
          
          // 标准的 CRUD 方法，需要排除
          const standardMethods = [
            'constructor', 'find', 'count', 'findOne', 'create', 'update', 'updateMany', 'delete', 'deleteMany',
            'branchFind', 'branchCount', 'branchFindOne', 'branchCreate', 'branchUpdate', 'branchUpdateMany', 
            'branchDelete', 'branchDeleteMany', 'branchExport', 'branchImport',
            'branchSelfFind', 'branchSelfCount', 'branchSelfFindOne', 'branchSelfCreate', 'branchSelfUpdate', 
            'branchSelfUpdateMany', 'branchSelfDelete', 'branchSelfDeleteMany', 'branchSelfExport', 'branchSelfImport',
            'export', 'import', 'selfFind', 'selfCount', 'selfFindOne', 'selfCreate', 'selfUpdate', 
            'selfUpdateMany', 'selfDelete', 'selfDeleteMany', 'selfExport', 'selfImport'
          ];
          
          let methodMatch;
          while ((methodMatch = classMethodRegex.exec(controllerContent)) !== null) {
            const methodName = methodMatch[1];
            
            // 排除构造函数、私有方法、JavaScript关键字和标准 CRUD 方法
            const jsKeywords = ['if', 'else', 'for', 'while', 'do', 'switch', 'case', 'try', 'catch', 'finally', 'return', 'throw', 'new', 'typeof', 'instanceof'];
            
            if (methodName && 
                !methodName.startsWith('_') && 
                !standardMethods.includes(methodName) && 
                !jsKeywords.includes(methodName) &&
                !extendMethods.includes(methodName)) {
              extendMethods.push(methodName);
            }
          }
        }
        
        break; // 找到控制器文件就跳出
      }
    }
  } catch (error) {
    console.error(`获取控制器 ${controller} 扩展方法失败:`, error);
  }
  
  return extendMethods;
}

/**
 * 根据方法名推断 HTTP 方法
 * @param {string} methodName 方法名
 * @returns {string} HTTP 方法 (默认为 get)
 */
function inferHttpMethodFromName(methodName) {
  // 扩展方法如果没有在 routes.js 中单独定义并指定请求方式，统一按照 GET 请求方法处理
  return 'get';
}

/**
 * 验证控制器方法是否有效
 * @param {string} moduleName 模块名称
 * @param {string} controllerName 控制器名称
 * @param {string} methodName 方法名称
 * @returns {boolean} 是否有效
 */
function isValidControllerMethod(moduleName, controllerName, methodName) {
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const controllerPath = path.join(apiDir, moduleName, 'controllers', `${controllerName}.js`);
    
    
    // 检查控制器文件是否存在
    if (!fs.existsSync(controllerPath)) {
      return false;
    }
    
    const controllerContent = fs.readFileSync(controllerPath, 'utf8');
    
    // 排除 createHandlers 这种系统函数，它不是业务函数
    if (methodName === 'createHandlers') {
      return false;
    }
    
    // 检查是否导出了该方法
    const moduleExportsRegex = /module\.exports\s*=\s*\{([^}]+)\}/s;
    const exportsMatch = controllerContent.match(moduleExportsRegex);
    
    if (!exportsMatch) {
      return false;
    }
    
    const exportsContent = exportsMatch[1];
    
    // 过滤掉注释行
    const exportsLines = exportsContent.split('\n');
    const activeExportsLines = exportsLines.filter(line => {
      const trimmed = line.trim();
      return !trimmed.startsWith('//') && !trimmed.startsWith('*') && trimmed.length > 0;
    });
    const activeExportsContent = activeExportsLines.join('\n');
    
    // 检查该方法是否在路由中有配置（这是最重要的验证）
    if (!hasMethodRouteConfig(moduleName, controllerName, methodName)) {
      return false;
    }
    
    // 检查方法是否在导出中 OR 在代码中定义（支持类方法）
    const methodExportRegex = new RegExp(`\\b${methodName}\\b`);
    const isInExports = methodExportRegex.test(activeExportsContent);
    
    // 检查是否在控制器代码中定义了该函数
    const codeLines = controllerContent.split('\n');
    const activeCodeLines = codeLines.filter(line => {
      const trimmed = line.trim();
      return !trimmed.startsWith('//') && !trimmed.startsWith('*') && trimmed.length > 0;
    });
    const activeCodeContent = activeCodeLines.join('\n');
    
    const functionDefRegex = new RegExp(`(?:const|let|var|function)\\s+${methodName}\\s*[=\\(]|(?:async\\s+)?${methodName}\\s*\\(`);
    const isInCode = functionDefRegex.test(activeCodeContent);
    
    // 方法必须在导出中 OR 在代码中定义（支持继承的类方法）
    if (!isInExports && !isInCode) {
      return false;
    }
    
    return true;
  } catch (error) {
    console.error(`验证控制器方法 ${controllerName}.${methodName} 失败:`, error);
    return false;
  }
}

/**
 * 检查特定方法是否在路由中有配置
 * @param {string} moduleName 模块名称
 * @param {string} controllerName 控制器名称
 * @param {string} methodName 方法名称
 * @returns {boolean} 是否有路由配置
 */
function hasMethodRouteConfig(moduleName, controllerName, methodName) {
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
    const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
    const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
    
    for (const routeFilePath of [routesPath, routePath, routersPath]) {
      if (fs.existsSync(routeFilePath)) {
        const routesContent = fs.readFileSync(routeFilePath, 'utf8');
        
        // 过滤掉注释行
        const lines = routesContent.split('\n');
        const activeLines = lines.filter(line => {
          const trimmed = line.trim();
          return !trimmed.startsWith('//') && !trimmed.startsWith('*') && trimmed.length > 0;
        });
        const activeContent = activeLines.join('\n');
        
        // 检查特定方法的路由配置
        const methodHandlerPattern = new RegExp(`['"\`]${controllerName}\\.${methodName}['"\`]`);
        if (methodHandlerPattern.test(activeContent)) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error(`检查方法 ${controllerName}.${methodName} 路由配置失败:`, error);
    return false;
  }
}

/**
 * 检查控制器是否在路由中有任何配置
 * @param {string} moduleName 模块名称
 * @param {string} controller 控制器名称
 * @returns {boolean} 是否有路由配置
 */
function hasControllerRouteConfig(moduleName, controller) {
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
    const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
    const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
    
    for (const routeFilePath of [routesPath, routePath, routersPath]) {
      if (fs.existsSync(routeFilePath)) {
        const routesContent = fs.readFileSync(routeFilePath, 'utf8');
        
        // 检查 createDefaultRoutes 配置
        const createDefaultRoutesRegex = /createDefaultRoutes\s*\(\s*\{([^}]+)\}\s*\)/g;
        let match;
        while ((match = createDefaultRoutesRegex.exec(routesContent)) !== null) {
          const configContent = match[1];
          const controllerMatch = configContent.match(/controller\s*:\s*['"`]([^'"`]+)['"`]/);
          if (controllerMatch && controllerMatch[1] === controller) {
            return true;
          }
        }
        
        // 检查手工路由配置（非注释的）
        const lines = routesContent.split('\n');
        const activeLines = lines.filter(line => {
          const trimmed = line.trim();
          return !trimmed.startsWith('//') && !trimmed.startsWith('*') && trimmed.length > 0;
        });
        
        const activeContent = activeLines.join('\n');
        const handlerPattern = new RegExp(`['"\`]${controller}\\.\\w+['"\`]`, 'g');
        if (handlerPattern.test(activeContent)) {
          return true;
        }
      }
    }
    
    return false;
  } catch (error) {
    console.error(`检查控制器 ${controller} 路由配置失败:`, error);
    return false;
  }
}

/**
 * 检查控制器是否使用了 CurdRouter 或 BranchCurdRouter
 * 通过两种方式检查：
 * 1. 扫描路由文件中的 createDefaultRoutes 调用
 * 2. 检查控制器文件中是否直接实例化了 CurdRouter 或 BranchCurdRouter
 */
function isUsingCrudRouter(controller) {
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });

    // 方法1: 检查路由配置中的 createDefaultRoutes
    for (const moduleName of modules) {
      const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
      const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
      const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
      
      // 检查 routes.js、route.js 或 routers.js
      for (const routeFilePath of [routesPath, routePath, routersPath]) {
        if (fs.existsSync(routeFilePath)) {
          const routesContent = fs.readFileSync(routeFilePath, 'utf8');
          
          // 检查是否有使用 createDefaultRoutes 并指定了该控制器
          const createDefaultRoutesRegex = /createDefaultRoutes\s*\(\s*\{([^}]+)\}\s*\)/g;
          
          let match;
          while ((match = createDefaultRoutesRegex.exec(routesContent)) !== null) {
            const configContent = match[1];
            const controllerMatch = configContent.match(/controller\s*:\s*['"`]([^'"`]+)['"`]/);
            
            if (controllerMatch && controllerMatch[1] === controller) {
              return true;
            }
          }
        }
      }
    }
    
    // 方法2: 检查控制器文件中是否直接实例化了 CurdRouter 或 BranchCurdRouter
    for (const moduleName of modules) {
      const controllerPath = path.join(apiDir, moduleName, 'controllers', `${controller}.js`);
      
      if (fs.existsSync(controllerPath)) {
        const controllerContent = fs.readFileSync(controllerPath, 'utf8');
        
        // 检查是否导入了 CurdRouter 或 BranchCurdRouter
        const importRegex = /(?:const|let|var)\s*\{[^}]*(?:CurdRouter|BranchCurdRouter)[^}]*\}\s*=\s*require\s*\(\s*['"`]accel-utils['"`]\s*\)/;
        
        // 检查是否实例化了 CurdRouter 或 BranchCurdRouter
        const instanceRegex = /new\s+(?:BranchCurdRouter|CurdRouter)\s*\(\s*['"`]([^'"`]+)['"`]/;
        
        if (importRegex.test(controllerContent)) {
          const instanceMatch = instanceRegex.exec(controllerContent);
          if (instanceMatch && instanceMatch[1] === controller) {
            return true;
          }
        }
      }
    }
  } catch (error) {
    console.error(`检查控制器 ${controller} 是否使用CRUD路由失败:`, error);
  }
  
  return false;
}

/**
 * 根据控制器名称获取模型描述
 */
/**
 * 从标签名反向获取控制器名
 * @param {string} tagName - 标签名
 * @returns {string|null} 控制器名或null
 */
function getControllerNameFromTag(tagName) {
  const tagsConfig = getTags();
  
  // 检查默认标签配置
  for (const [controller, tagConfig] of Object.entries(tagsConfig.defaultTags || {})) {
    if (tagConfig && tagConfig.name === tagName) {
      return controller;
    }
  }
  
  return null;
}

function getModelDescription(controller) {
  let tagName = '';
  
  // 1. 优先从配置文件读取标签
  const tagsConfig = getTags();
  const tagConfig = tagsConfig.defaultTags && tagsConfig.defaultTags[controller];
  if (tagConfig && tagConfig.name) {
    tagName = tagConfig.name;
  }
  // 2. 如果控制器使用了CurdRouter或BranchCurdRouter，尝试从模型文件的 info.label 读取
  else if (isUsingCrudRouter(controller)) {
    const model = loadModelInfoOnly(controller);
    if (model && model.info && model.info.label) {
      tagName = model.info.label;
    } else {
      // 3. 最后使用格式化的控制器名称
      tagName = controller.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  }
  else {
    // 3. 最后使用格式化的控制器名称
    tagName = controller.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
  
  // 在标签名后面添加控制器名称（用括号括住）
  return `${tagName} (${controller})`;
}

/**
 * 扫描所有控制器文件并生成文档
 * 包括那些没有在路由中明确定义的控制器
 * @returns {Object} 控制器方法的路由文档
 */
function scanAllControllers() {
  const paths = {};
  const processedMethods = new Set(); // 记录已处理的方法，避免重复
  
  try {
    const apiDir = path.join(process.cwd(), 'api');
    const modules = fs.readdirSync(apiDir).filter(file => {
      const stat = fs.statSync(path.join(apiDir, file));
      return stat.isDirectory();
    });

    // 首先收集所有已经在路由中定义的handler
    const definedHandlers = collectDefinedHandlers(modules);
    
    // 收集每个控制器是否在路由中有任何配置
    const controllersInRoutes = new Set();
    definedHandlers.forEach(handler => {
      const controllerName = handler.split('.')[0];
      controllersInRoutes.add(controllerName);
    });

    for (const moduleName of modules) {
      const controllersDir = path.join(apiDir, moduleName, 'controllers');
      
      if (!fs.existsSync(controllersDir)) continue;
      
      const controllerFiles = fs.readdirSync(controllersDir).filter(file => {
        return file.endsWith('.js') && !file.startsWith('.');
      });
      
      for (const controllerFile of controllerFiles) {
        const controllerName = controllerFile.replace('.js', '');
        
        // 检查是否应该忽略这个控制器
        if (shouldIgnoreController(controllerName)) {
          console.log(`忽略控制器: ${controllerName} (根据过滤配置)`);
          continue;
        }
        
        // 检查控制器是否在路由中有任何配置
        if (!controllersInRoutes.has(controllerName)) {
          console.log(`忽略控制器: ${controllerName} (未在路由中配置任何方法)`);
          continue;
        }
        
        const controllerPath = path.join(controllersDir, controllerFile);
        
        try {
          const controllerContent = fs.readFileSync(controllerPath, 'utf8');
          
          // 提取导出的所有方法
          const exportedMethods = extractExportedMethods(controllerContent);
          
          if (exportedMethods.length > 0) {
            // 过滤掉已经在路由中定义的方法
            const undefinedMethods = exportedMethods.filter(methodName => {
              const handler = `${controllerName}.${methodName}`;
              return !definedHandlers.has(handler);
            });
            
            if (undefinedMethods.length > 0) {
              console.log(`发现控制器 ${controllerName} 的未定义路由方法:`, undefinedMethods);
              
              // 为每个未定义的方法生成文档
              for (const methodName of undefinedMethods) {
                const methodKey = `${controllerName}.${methodName}`;
                
                // 跳过已处理的方法
                if (processedMethods.has(methodKey)) continue;
                
                // 构造默认路径
                const methodPath = `/${moduleName}/${controllerName.replace(/-/g, '_')}/${methodName}`;
                const normalizedPath = methodPath.replace(/:(\w+)/g, '{$1}');
                
                // 检查是否应该忽略这个路径
                if (shouldIgnorePath(methodPath) || shouldIgnorePath(normalizedPath)) {
                  continue;
                }
                
                const tag = getModelDescription(controllerName);
                
                // 根据方法名推断 HTTP 方法
                const httpMethod = inferHttpMethodFromMethodName(methodName);
                
                if (!paths[normalizedPath]) {
                  paths[normalizedPath] = {};
                }
                
                paths[normalizedPath][httpMethod] = {
                  tags: [tag],
                  summary: `${methodName}`,
                  description: `控制器 ${controllerName} 的方法 ${methodName} (自动发现)`,
                  responses: {
                    200: { description: '成功' }
                  }
                };
                
                // 如果是 POST/PUT 方法，添加请求体
                if (httpMethod === 'post' || httpMethod === 'put') {
                  paths[normalizedPath][httpMethod].requestBody = {
                    required: true,
                    content: {
                      'application/json': {
                        schema: {
                          type: 'object',
                          description: '请求参数'
                        }
                      }
                    }
                  };
                }
                
                processedMethods.add(methodKey);
              }
            }
          }
          
        } catch (error) {
          console.error(`处理控制器文件失败: ${controllerPath}`, error);
        }
      }
    }
    
  } catch (error) {
    console.error('扫描所有控制器失败:', error);
  }
  
  return paths;
}

/**
 * 收集所有已经在路由中定义的handler
 * @param {Array} modules API模块列表
 * @returns {Set} 已定义的handler集合
 */
function collectDefinedHandlers(modules) {
  const handlers = new Set();
  
  try {
    const apiDir = path.join(process.cwd(), 'api');
    
    for (const moduleName of modules) {
      const routesPath = path.join(apiDir, moduleName, 'config', 'routes.js');
      const routePath = path.join(apiDir, moduleName, 'config', 'route.js');
      const routersPath = path.join(apiDir, moduleName, 'config', 'routers.js');
      
      for (const routeFilePath of [routesPath, routePath, routersPath]) {
        if (!fs.existsSync(routeFilePath)) continue;
        
        try {
          const routesContent = fs.readFileSync(routeFilePath, 'utf8');
          
          // 匹配所有handler定义
          const handlerRegex = /['"]?handler['"]?\s*:\s*['"]([^'"]+)['"]/g;
          let match;
          
          while ((match = handlerRegex.exec(routesContent)) !== null) {
            handlers.add(match[1]);
          }
          
          // 匹配 createDefaultRoutes 中的控制器
          const createDefaultRoutesRegex = /createDefaultRoutes\s*\(\s*\{[^}]*controller\s*:\s*['"]([^'"]+)['"]/g;
          
          while ((match = createDefaultRoutesRegex.exec(routesContent)) !== null) {
            const controller = match[1];
            // 为标准CRUD方法添加handler
            const crudMethods = ['find', 'findOne', 'create', 'update', 'delete', 'count'];
            crudMethods.forEach(method => {
              handlers.add(`${controller}.${method}`);
              handlers.add(`${controller}.branch${method.charAt(0).toUpperCase() + method.slice(1)}`);
            });
          }
          
        } catch (error) {
          console.error(`处理路由文件失败: ${routePath}`, error);
        }
      }
    }
  } catch (error) {
    console.error('收集已定义handler失败:', error);
  }
  
  return handlers;
}

/**
 * 提取控制器文件中导出的方法
 * @param {string} content 控制器文件内容
 * @returns {Array} 导出的方法名数组
 */
function extractExportedMethods(content) {
  const methods = [];
  
  try {
    // 匹配 module.exports = { ... } 中的方法
    const moduleExportsRegex = /module\.exports\s*=\s*\{([^}]+)\}/s;
    const match = content.match(moduleExportsRegex);
    
    if (match) {
      const exportsContent = match[1];
      
      // 匹配方法名
      const methodRegex = /(\w+)\s*[,:]|\.\.\.(\w+)\.(\w+)\(\)/g;
      let methodMatch;
      
      while ((methodMatch = methodRegex.exec(exportsContent)) !== null) {
        const methodName = methodMatch[1] || methodMatch[3];
        if (methodName && !methods.includes(methodName)) {
          methods.push(methodName);
        }
      }
    }
    
    // 匹配单独导出的函数
    const functionRegex = /(?:const|let|var|function)\s+(\w+)\s*=?\s*(?:async\s+)?\(?[^)]*\)?\s*(?:=>|\{)/g;
    let funcMatch;
    const declaredFunctions = [];
    
    while ((funcMatch = functionRegex.exec(content)) !== null) {
      const funcName = funcMatch[1];
      if (funcName && !funcName.startsWith('_')) {
        declaredFunctions.push(funcName);
      }
    }
    
    // 检查这些函数是否被导出
    declaredFunctions.forEach(funcName => {
      const exportRegex = new RegExp(`exports\\.${funcName}\\s*=|module\\.exports\\.${funcName}\\s*=`);
      if (exportRegex.test(content) && !methods.includes(funcName)) {
        methods.push(funcName);
      }
    });
    
  } catch (error) {
    console.error('提取导出方法失败:', error);
  }
  
  return methods;
}

/**
 * 根据方法名推断 HTTP 方法
 * @param {string} methodName 方法名
 * @returns {string} HTTP 方法
 */
function inferHttpMethodFromMethodName(methodName) {
  const lowerName = methodName.toLowerCase();
  
  if (lowerName.includes('create') || lowerName.includes('add') || lowerName.includes('insert') || lowerName.includes('save')) {
    return 'post';
  }
  if (lowerName.includes('update') || lowerName.includes('modify') || lowerName.includes('edit')) {
    return 'put';
  }
  if (lowerName.includes('delete') || lowerName.includes('remove')) {
    return 'delete';
  }
  if (lowerName.includes('get') || lowerName.includes('find') || lowerName.includes('query') || lowerName.includes('list') || lowerName.includes('search')) {
    return 'get';
  }
  
  // 默认为 POST（处理 Handler 后缀的方法）
  if (methodName.endsWith('Handler')) {
    return 'post';
  }
  
  return 'get';
}

/**
 * 根据控制器名称获取标签描述
 */
function getTagDescription(controller) {
  // 1. 优先从配置文件读取标签描述
  const tagsConfig = getTags();
  const tagConfig = tagsConfig.defaultTags && tagsConfig.defaultTags[controller];
  if (tagConfig && tagConfig.description) {
    return tagConfig.description;
  }
  
  // 2. 如果控制器使用了CurdRouter或BranchCurdRouter，尝试从模型文件的 info.description 读取
  if (isUsingCrudRouter(controller)) {
    const model = loadModelDefinition(controller);
    if (model && model.info && model.info.description) {
      return model.info.description;
    }
  }
  
  // 3. 如果没有配置，返回默认描述
  // 获取标签名（不包含控制器名称，所以需要重新计算）
  let baseTagName = '';
  if (isUsingCrudRouter(controller)) {
    const model = loadModelInfoOnly(controller);
    if (model && model.info && model.info.label) {
      baseTagName = model.info.label;
    } else {
      baseTagName = controller.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  } else {
    baseTagName = controller.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }
  return `${baseTagName}相关接口`;
}

/**
 * 生成标准 CRUD 路由的 Swagger 标签
 */
function generateCrudSwaggerTags(basePath, controller) {
  const description = getModelDescription(controller);
  const tag = description;
  const modelName = controller.replace(/-/g, '');
  
  // 加载模型定义
  const model = loadModelDefinition(controller);
  const responseSchema = modelToSwaggerSchema(model, false);
  const createSchema = modelToSwaggerSchema(model, true);
  const updateSchema = modelToSwaggerSchema(model, true);
  
  // 构建查询参数
  const queryParameters = buildQueryParameters(model);
  
  // 基于路径生成的 Swagger 定义
  const paths = {};
  
  // GET 列表
  paths[basePath] = {
    get: {
      tags: [tag],
      summary: `获取${description}列表`,
      description: model && model.attributes ? 
        `查询${description}列表，支持分页、排序和筛选。可用字段：${Object.keys(model.attributes).filter(k => model.attributes[k].visible !== false).join(', ')}` : 
        `查询${description}列表，支持分页、排序和筛选`,
      operationId: `find${modelName}`,
      parameters: queryParameters,
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: responseSchema
              }
            }
          }
        }
      }
    },
    post: {
      tags: [tag],
      summary: `创建${description}`,
      operationId: `create${modelName}`,
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: createSchema
          }
        }
      },
      responses: {
        200: {
          description: '创建成功',
          content: {
            'application/json': {
              schema: responseSchema
            }
          }
        },
        400: {
          description: '参数错误'
        }
      }
    }
  };
  
  // 检查是否为公开接口，设置相应的安全配置
  if (isPublicEndpoint(controller, 'find')) {
    paths[basePath].get.security = [];
  }
  if (isPublicEndpoint(controller, 'create')) {
    paths[basePath].post.security = [];
  }
  
  // GET 单个
  paths[`${basePath}/{id}`] = {
    get: {
      tags: [tag],
      summary: `根据ID获取${description}`,
      operationId: `findOne${modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          description: `${description}ID`,
          schema: { type: 'string' }
        }
      ],
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: responseSchema
            }
          }
        },
        404: { description: '未找到' }
      }
    },
    put: {
      tags: [tag],
      summary: `更新${description}`,
      operationId: `update${modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          description: `${description}ID`,
          schema: { type: 'string' }
        }
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: updateSchema
          }
        }
      },
      responses: {
        200: {
          description: '更新成功',
          content: {
            'application/json': {
              schema: responseSchema
            }
          }
        },
        404: { description: '未找到' }
      }
    },
    delete: {
      tags: [tag],
      summary: `删除${description}`,
      operationId: `delete${modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          required: true,
          description: `${description}ID`,
          schema: { type: 'string' }
        }
      ],
      responses: {
        200: {
          description: '删除成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  id: { type: 'string' }
                }
              }
            }
          }
        },
        404: { description: '未找到' }
      }
    }
  };
  
  // 检查单个接口是否为公开接口
  if (isPublicEndpoint(controller, 'findOne')) {
    paths[`${basePath}/{id}`].get.security = [];
  }
  if (isPublicEndpoint(controller, 'update')) {
    paths[`${basePath}/{id}`].put.security = [];
  }
  if (isPublicEndpoint(controller, 'delete')) {
    paths[`${basePath}/{id}`].delete.security = [];
  }
  
  // 其他标准路由
  paths[`${basePath}/count`] = {
    get: {
      tags: [tag],
      summary: `获取${description}总数`,
      description: `统计符合条件的${description}数量，支持与列表接口相同的查询参数`,
      operationId: `count${modelName}`,
      parameters: queryParameters, // 使用与 find 接口相同的查询参数
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  count: { 
                    type: 'integer',
                    description: '符合条件的记录数量'
                  }
                }
              }
            }
          }
        }
      }
    }
  };
  
  // 检查count接口是否为公开接口
  if (isPublicEndpoint(controller, 'count')) {
    paths[`${basePath}/count`].get.security = [];
  }
  
  paths[`${basePath}/actions/deleteMany`] = {
    post: {
      tags: [tag],
      summary: `批量删除${description}`,
      operationId: `deleteMany${modelName}`,
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ids: {
                  type: 'array',
                  items: { type: 'string' }
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '删除成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  count: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    }
  };
  
  paths[`${basePath}/export`] = {
    get: {
      tags: [tag],
      summary: `导出${description}数据`,
      operationId: `export${modelName}`,
      responses: {
        200: {
          description: '导出成功',
          content: {
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': {
              schema: {
                type: 'string',
                format: 'binary'
              }
            }
          }
        }
      }
    }
  };
  
  paths[`${basePath}/import`] = {
    post: {
      tags: [tag],
      summary: `导入${description}数据`,
      operationId: `import${modelName}`,
      requestBody: {
        required: true,
        content: {
          'multipart/form-data': {
            schema: {
              type: 'object',
              properties: {
                file: {
                  type: 'string',
                  format: 'binary'
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '导入成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: { type: 'integer' },
                  failed: { type: 'integer' },
                  errors: {
                    type: 'array',
                    items: { type: 'string' }
                  }
                }
              }
            }
          }
        }
      }
    }
  };
  
  return paths;
}

/**
 * 集成到 swagger-jsdoc 配置
 * @param {Object} spec - swagger规范对象
 * @param {string|Array} filterModules - 要扫描的模块，如果为空则扫描全部
 * @param {string} filterController - 要扫描的控制器，如果为空则扫描全部
 */
function extendSwaggerSpec(spec, filterModules = null, filterController = null) {
  const autoPaths = generateAutoSwaggerPaths(filterModules, filterController);
  
  // 智能合并自动生成的路径和已有路径
  // 优先使用 @swagger 注释的文档，自动生成的文档作为补充
  const mergedPaths = { ...autoPaths };
  
  // 遍历 swagger-jsdoc 解析的路径（来自 @swagger 注释）
  Object.keys(spec.paths || {}).forEach(path => {
    // 如果指定了控制器过滤，需要检查这个路径是否属于指定的控制器
    if (filterController) {
      let belongsToController = false;
      
      // 检查路径下的所有操作，看是否有标签匹配指定的控制器
      const pathObj = spec.paths[path];
      Object.values(pathObj).forEach(operation => {
        if (operation.tags) {
          operation.tags.forEach(tag => {
            // 获取标签对应的控制器名
            const controllerName = getControllerNameFromTag(tag);
            if (controllerName === filterController) {
              belongsToController = true;
            }
          });
        }
      });
      
      // 如果不属于指定控制器，跳过这个路径
      if (!belongsToController) {
        return;
      }
    }
    
    if (mergedPaths[path]) {
      // 如果路径已存在，智能合并每个 HTTP 方法
      // @swagger 注释的详细文档会覆盖自动生成的基础文档
      mergedPaths[path] = {
        ...mergedPaths[path],
        ...spec.paths[path]
      };
    } else {
      // 如果路径不存在，直接添加
      mergedPaths[path] = spec.paths[path];
    }
  });
  
  spec.paths = mergedPaths;
  
  // 收集所有使用的标签
  const usedTags = new Set();
  Object.values(spec.paths || {}).forEach(pathObj => {
    Object.values(pathObj).forEach(operation => {
      if (operation.tags) {
        operation.tags.forEach(tag => usedTags.add(tag));
      }
    });
  });
  Object.values(autoPaths).forEach(pathObj => {
    Object.values(pathObj).forEach(operation => {
      if (operation.tags) {
        operation.tags.forEach(tag => usedTags.add(tag));
      }
    });
  });
  
  // 自动生成使用到的标签定义
  const generatedTags = [];
  
  // 获取最新的标签配置
  const tagsConfig = getTags();
  
  // 添加使用到的默认标签
  Object.entries(tagsConfig.defaultTags || {}).forEach(([controller, tagConfig]) => {
    if (tagConfig && tagConfig.name && usedTags.has(tagConfig.name)) {
      generatedTags.push({
        name: tagConfig.name,
        description: tagConfig.description || ''
      });
    }
  });
  
  // 添加特殊路径标签
  Object.values(tagsConfig.pathTags || {}).forEach(tagConfig => {
    if (tagConfig && tagConfig.name && usedTags.has(tagConfig.name)) {
      generatedTags.push({
        name: tagConfig.name,
        description: tagConfig.description
      });
    }
  });
  
  // 添加全局标签（如果被使用）
  tagsConfig.globalTags.forEach(tagConfig => {
    if (usedTags.has(tagConfig.name)) {
      generatedTags.push({
        name: tagConfig.name,
        description: tagConfig.description
      });
    }
  });
  
  // 合并标签（避免重复）
  const existingTagNames = new Set((spec.tags || []).map(t => t.name));
  generatedTags.forEach(tag => {
    if (!existingTagNames.has(tag.name)) {
      spec.tags = spec.tags || [];
      spec.tags.push(tag);
    }
  });
  
  // 加载扩展配置（如 plugins 的文档）
  try {
    const { getExtensions } = require('./extensions');
    
    // 如果指定了模块过滤，需要特殊处理扩展模块
    let extensionModules = null;
    if (filterModules) {
      // 检查过滤的模块中是否包含扩展模块
      const knownExtensionModules = ['users-permissions', 'upload'];
      const filterArray = Array.isArray(filterModules) ? filterModules : [filterModules];
      extensionModules = filterArray.filter(module => knownExtensionModules.includes(module));
      
      console.log(`模块过滤: ${filterArray.join(', ')}`);
      console.log(`扩展模块过滤: ${extensionModules.length > 0 ? extensionModules.join(', ') : '无'}`);
      
      // 如果过滤的模块都不是扩展模块（即都是 API 业务模块），则不加载任何扩展配置
      if (extensionModules.length === 0) {
        extensionModules = [];  // 使用空数组，明确表示不加载任何扩展
        console.log('当前过滤的都是业务模块，不加载扩展配置');
      }
    }
    
    const extensions = getExtensions(extensionModules);
    
    // 合并扩展的标签
    if (extensions.additionalTags) {
      spec.tags = [...(spec.tags || []), ...extensions.additionalTags];
    }
    
    // 合并各个模块的扩展路径
    Object.entries(extensions).forEach(([moduleName, moduleConfig]) => {
      if (moduleName !== 'additionalTags' && moduleName !== 'components' && moduleConfig.paths) {
        // 合并标签
        if (moduleConfig.tags) {
          spec.tags = [...(spec.tags || []), ...moduleConfig.tags];
        }
        // 合并路径 - 只合并 paths 属性内容，不要整个 moduleConfig
        Object.assign(spec.paths, moduleConfig.paths);
      }
    });
    
    // 合并组件定义
    if (extensions.components && extensions.components.schemas) {
      if (!spec.components) {
        spec.components = {};
      }
      if (!spec.components.schemas) {
        spec.components.schemas = {};
      }
      Object.assign(spec.components.schemas, extensions.components.schemas);
    }
  } catch (error) {
    console.warn('加载扩展配置失败:', error);
  }
  
  // 对标签进行排序
  if (spec.tags && Array.isArray(spec.tags)) {
    spec.tags.sort((a, b) => {
      // 定义标签优先级
      const tagPriority = {
        '用户认证': 1,
        '认证授权': 1,
        '用户管理': 2,
        '用户权限': 2,
        '角色管理': 3,
        '文件上传': 4
      };
      
      // 获取标签的优先级，如果没有定义则为100
      const priorityA = tagPriority[a.name] || 100;
      const priorityB = tagPriority[b.name] || 100;
      
      // 如果优先级相同，按字母顺序排序
      if (priorityA === priorityB) {
        return a.name.localeCompare(b.name, 'zh-CN');
      }
      
      // 否则按优先级排序
      return priorityA - priorityB;
    });
  }
  
  // 为标准路径添加正确的参数格式
  Object.keys(spec.paths).forEach(path => {
    // 将路径参数从 :id 格式转换为 {id} 格式
    const swaggerPath = path.replace(/:(\w+)/g, '{$1}');
    if (swaggerPath !== path) {
      spec.paths[swaggerPath] = spec.paths[path];
      delete spec.paths[path];
    }
  });
  
  return spec;
}

module.exports = {
  generateAutoSwaggerPaths,
  extendSwaggerSpec
};