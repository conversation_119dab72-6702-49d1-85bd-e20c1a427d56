module.exports = {
  collectionName: 'qun-action-log',
  info: {
    name: 'QunActionLog',
    label: '群操作记录',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    qunId: {
      label: '群ID',
      type: 'string',
      editable: false
    },
    operator: {
      label: '操作人',
      model: 'user',
      plugin: 'users-permissions',
      editable: false
    },
    operatedAt: {
      label: '操作时间',
      type: 'datetime',
      editable: false
    },
    action: {
      label: '操作类型',
      type: 'string',
      editable: false
    },
    oldInfo: {
      label: '旧信息',
      type: 'json',
      editable: false
    },
    actionInfo: {
      label: '操作信息',
      type: 'json',
      editable: false
    },
    result: {
      label: '操作结果',
      type: 'json',
      editable: false
    }
  }
}
