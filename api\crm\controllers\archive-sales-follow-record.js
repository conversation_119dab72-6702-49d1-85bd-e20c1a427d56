const { <PERSON>urd<PERSON>outer } = require('accel-utils')
const moment = require('moment')
const axios = require('axios')
const _ = require('lodash')
const { htmlToText } = require('html-to-text')
const { MongoClient, ObjectId } = require('mongodb')
const dbUrl = strapi.config.get('database.connections.default.settings.uri')
const curdRouter = new CurdRouter('archive-sales-follow-record')

async function syncMidRecordByMingDao (ctx) {
    await _syncMidRecordByMingDao()
    return ctx.wrapper.succ()
}

async function _syncMidRecordByMingDao () {
    let mdRecords
    let limit = 1000, skip = 0
    while (true) {
        const conditions = {
            _start: skip,
            _limit: limit,
            _sort: 'ctime:ASC'
        }

        mdRecords = await strapi.query('customer-sales-follow-record').find(conditions)
        const mdRecordIds = mdRecords.map(e => e._id)
        const midRecords = await strapi.query('archive-sales-follow-record-mid').find({
            id: {
                $in: mdRecordIds
            },
            _limit: -1,
            _projection: { _id: 1 }
        })
        const midRecordMap = {}
        for (const midRecord of midRecords) {
            midRecordMap[midRecord._id] = midRecord
        }
        const mdModel = await strapi.getModel('customer-sales-follow-record')
        const recordModel = await strapi.getModel('archive-sales-follow-record-mid')

        let bulkWriteArray = [], insertList = []
        for (const mdRecord of mdRecords) {
            let record = {
                createdAt: new Date(mdRecord.ctime),
                updatedAt: new Date(mdRecord.utime),
            }
            for (const key in recordModel.attributes) {
                // if (key === '673462101637ee6db9affd57' || key === '673462271637ee6db9affd61') {
                //     if (!_.isEmpty(mdRecord[key])) {
                //         record[key] = new ObjectId(mdRecord[key][0]._id)
                //     }
                // } else
                if (recordModel.attributes[key].type === 'json') {
                    if (!_.isEmpty(mdRecord[key])) {
                        record[key] = {
                            name: mdRecord[key][0][`${mdModel.attributes[key].mainField}`],
                            ...mdRecord[key][0]
                        }
                    }
                } else if (recordModel.attributes[key].type === 'boolean') {
                    record[key] = !_.isNil(mdRecord[key]) ? mdRecord[key] : false
                } else if (recordModel.attributes[key].type === 'datetime') {
                    record[key] = !isNaN(Date.parse(mdRecord[key])) ? new Date(mdRecord[key]) : null
                } else if (recordModel.attributes[key].type === 'richtext') {
                    record[key] = mdRecord[key] ? htmlToText(mdRecord[key]) : ''
                } else {
                    record[key] = mdRecord[key]
                }
            }
            if (midRecordMap[mdRecord._id]) {
                bulkWriteArray.push({
                    updateOne: {
                        filter: {
                            _id: mdRecord._id
                        },
                        update: {
                            $set: {
                                ...record,
                                _id: mdRecord._id,
                                rowid: mdRecord.id,
                            }
                        },
                    }
                })
            } else {
                insertList.push({
                    ...record,
                    _id: mdRecord._id,
                    rowid: mdRecord.id,
                })
            }
        }
        if (bulkWriteArray.length > 0) {
            let result = await strapi.query('archive-sales-follow-record-mid').model.bulkWrite(bulkWriteArray)
            // console.log(result)
        }
        if (insertList.length > 0) {
            let result = await strapi.query('archive-sales-follow-record-mid').model.insertMany(insertList)
            // console.log(result)
        }

        if (mdRecords.length === 0) { break }
        skip = skip + limit
        if (mdRecords.length < limit) { break }
    }

    return mdRecords
}

// 初始化归档记录
async function initArchiveRecord (ctx) {
    await _syncMidRecordByMingDao()
    let limit = 5000, lastRecordId
    while (true) {
        const conditions = {
            _start: 0,
            _limit: limit,
            _sort: 'id:ASC',
            // 计划跟进日期 不为空在今天之后 
            _where: {
                _or: [
                    { '673461cc1637ee6db9affd1c_lt': moment().startOf('day').toDate(), },
                    { '673461cc1637ee6db9affd1c_null': true, },
                ]
            },
            createdAt_gt: moment('2024-10-09').startOf('day').toDate(),
            createdAt_lt: moment('2024-12-01').endOf('day').toDate(),
        }
        if (lastRecordId) { conditions.id_gt = lastRecordId }
        const midRecords = await strapi.query('archive-sales-follow-record-mid').find(conditions)

        const midRecordRowIds = midRecords.map(e => e.rowid)
        if (midRecords?.length > 0) {
            await strapi.query('archive-sales-follow-record').model.insertMany(midRecords)
            await strapi.query('customer-sales-follow-record').model.deleteMany({
                id_in: midRecordRowIds
            })

            const msgs = `直营跟进记录归档数目: ${midRecords?.length}`
            await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
                msgtype: 'text',
                text: {
                    content: msgs,
                }
            })
        }
        if (midRecords.length === 0) { break }
        lastRecordId = midRecords[midRecords.length - 1].id
        if (midRecords.length < limit) { break }
    }
    return ctx.wrapper.succ()
}


async function archiveRecord (ctx) {
    const lastRecord = await strapi.query('archive-sales-follow-record').findOne({
        _projection: { id: 1, rowid: 1, createdAt: 1 },
        _sort: 'id:DESC'
    }, [])
    const lastRecordId = lastRecord ? lastRecord.id : '000000000000000000000000'
    let mdRecords = await _syncMidRecordByMingDao()

    const midRecords = await strapi.query('archive-sales-follow-record-mid').find({
        id: {
            $gt: lastRecordId
        },
        '673461cc1637ee6db9affd1c_lt': moment().startOf('day').toDate(),
        // createdAt_gt: moment('2024-10-09').startOf('day').toDate(),
        // createdAt_lt: moment('2024-12-01').endOf('day').toDate(),
        createdAt_gt: moment().subtract(3, 'month').startOf('day').toDate(),
        createdAt_lt: moment().subtract(3, 'month').endOf('day').toDate(),
        _limit: -1,
    })

    const midRecordRowIds = midRecords.map(e => e.rowid)

    if (midRecords?.length > 0) {
        await strapi.query('archive-sales-follow-record').model.insertMany(midRecords)
        await strapi.query('customer-sales-follow-record').model.deleteMany({
            id_in: midRecordRowIds
        })

        const msgs = `直营跟进记录归档数目: ${midRecords?.length}`
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: msgs,
            }
        })
    }


    return ctx.wrapper.succ()
}


module.exports = {
    syncMidRecordByMingDao,
    archiveRecord: archiveRecord,
    ...curdRouter.createHandlers(),
}
