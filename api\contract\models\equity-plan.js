

module.exports = {
  connection: 'wlyConfig',
  collectionName: 'equity-plan',
  info: {
    name: 'equity-plan',
    label: '版本定义',
    description: '',
    defaultMainField: 'name',
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
    defaultMainField: 'name',
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '版本名称',
      type: 'string',
      required: true,
    },
    type: {
      label: '版本类型值',
      type: 'number',
      // required: true,
    },
    code: {
      label: '自定义编码',
      type: 'string',
      // required: true,
    },
    description: {
      label: '描述',
      type: 'string',
    },
    price: {
      label: '版本价格',
      type: 'number',
      required: true,
    },
    equityProduct: {
      label: '权益产品',
      type: 'string',
      // model: 'equity-product',
    },
    level: {
      label: '版本级别',
      type: 'number',
      required: true,
      default: 1,
    },
    equityFeature: {
      type: 'json',
      label: '权益功能列表'
    },
    status: {
      label: '状态',
      type: 'boolean',
      default: true,
    },
    version: {
      label: '版本',
      type: 'number',
    },
    params: {
      label: '参数列表',
      type: 'json',
      jsonSchema: {
        type: 'array',
        items: {
          title: '参数',
          type: 'object',
          properties: {
            name: {
              title: '参数名称',
              type: 'string',
            },
            type: {
              title: '参数类型',
              type: 'string',
              options: [
                { value: 'string', label: '字符串' },
                { value: 'number', label: '数字' },
                { value: 'select', label: '选项' },
                { value: 'date', label: '日期' },
                { value: 'date-range', label: '日期范围' }
              ]
            },
            required: {
              title: '是否必填',
              type: 'boolean'
            },
            requiredInOrder: {
              title: '订单开通是否必填',
              type: 'boolean'
            },
            // 参数值列表
            paramValues: {
              title: '参数值列表',
              type: 'array',
            }
          }
        }
      }
    }
  }
}

