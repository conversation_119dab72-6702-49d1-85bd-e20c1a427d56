{"collectionName": "components_contract_quan_school", "info": {"name": "QuanSchool", "icon": "comment", "description": ""}, "options": {}, "attributes": {"school": {"label": "学校", "type": "json"}, "agentType": {"label": "授权类型", "type": "string", "options": [{"label": "产品分销：360会员", "value": "360"}, {"label": "产品分销：错题本", "value": "ctb"}]}, "beginTime": {"label": "开始日期", "type": "date"}, "endTime": {"label": "结束日期", "type": "date"}, "task": {"label": "任务量（券数量）", "type": "number"}, "remark": {"label": "备注", "type": "string"}}}