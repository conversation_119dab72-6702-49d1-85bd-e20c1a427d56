{"fieldsMap": {"经营单元": "2200000457504387", "类型": "2200000462655327", "健康度": "2200000473012679", "单元负责人": "2200000457712611", "团队成员": "2200000457712612", "归属联盟": "2200000471047940", "联盟负责人": "2200000464416918", "账户余额": "2200000471560676", "投资与注资": "2200000471560672", "累计收入": "2200000471560673", "累计费用支出": "2200000471560674", "累计人力成本": "2200000471560675", "冻结资金": "2200000477464398", "未核销信用支出": "2200000471533808", "参考1": "2200000473018475", "参考2": "2200000473018476", "联盟（临时）": "2200000487579444", "当前可用收入": "2200000464446604", "已发提成（含已计提）": "2200000464620031", "年度存量目标": "2200000474361151", "年度增量目标": "2200000474361152", "渠道存量业绩计划": "2200000464421894", "渠道增量业绩计划": "2200000464421895", "渠道业绩达成": "2200000471531846", "渠道存量业绩达成": "2200000473106771", "补缺后渠道存量达成": "2200000471531847", "渠道增量业绩达成": "2200000473106772", "补缺后渠道增量达成": "2200000471531848", "渠道收入": "2200000473118617", "补缺后收入": "2200000473118592", "渠道增量+项目": "2200000473118920", "项目业绩": "2200000464436562", "项目收入": "2200000464436564", "可用收入手动调整": "2200000464421912", "可用收入手动调整说明": "2200000464446605", "归属联盟-阿米巴联盟": "1153001101000000", "归属联盟-联盟盟主": "1153001113000000"}, "table_id": "2100000055636966", "name": "经营单元", "alias": "", "space_id": "4000000003570865", "created_on": "2024-01-31 15:54:50", "fields": [{"field_id": "2200000457504387", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000462655327", "name": "类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "渠道业务"}, {"id": "4", "name": "会员业务"}, {"id": "3", "name": "直营业务"}, {"id": "6", "name": "直服业务"}, {"id": "2", "name": "非业务"}, {"id": "5", "name": "调整"}, {"id": "8", "name": "已转移数据-可删除"}]}}, {"field_id": "2200000473012679", "name": "健康度", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "健康"}, {"id": "2", "name": "吃紧"}, {"id": "3", "name": "危险"}, {"id": "4", "name": "封存"}, {"id": "5", "name": "破产"}]}}, {"field_id": "2200000457712611", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000457712612", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000471047940", "name": "归属联盟", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056608623", "space_id": "4000000003570865"}}, {"field_id": "2200000464416918", "name": "联盟负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000471560676", "name": "账户余额", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471560672", "name": "投资与注资", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471560673", "name": "累计收入", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471560674", "name": "累计费用支出", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471560675", "name": "累计人力成本", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000477464398", "name": "冻结资金", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471533808", "name": "未核销信用支出", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473018475", "name": "参考1", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473018476", "name": "参考2", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000487579444", "name": "联盟（临时）", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059885507", "space_id": "4000000003570865"}}, {"field_id": "2200000464446604", "name": "当前可用收入", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464620031", "name": "已发提成（含已计提）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000474361151", "name": "年度存量目标", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000474361152", "name": "年度增量目标", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464421894", "name": "渠道存量业绩计划", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464421895", "name": "渠道增量业绩计划", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471531846", "name": "渠道业绩达成", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473106771", "name": "渠道存量业绩达成", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471531847", "name": "补缺后渠道存量达成", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473106772", "name": "渠道增量业绩达成", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471531848", "name": "补缺后渠道增量达成", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473118617", "name": "渠道收入", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473118592", "name": "补缺后收入", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000473118920", "name": "渠道增量+项目", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464436562", "name": "项目业绩", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464436564", "name": "项目收入", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464421912", "name": "可用收入手动调整", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464446605", "name": "可用收入手动调整说明", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "1153001101000000", "name": "阿米巴联盟", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471047940}, "required": false, "description": "", "config": {}}, {"field_id": "1153001113000000", "name": "联盟盟主", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000471047940}, "required": false, "description": "", "config": {"is_multi": 0}}]}