{"fieldsMap": {"阿米巴单元": "2200000486534467", "项目": "2200000486372048", "当前项目阶段": "2200000486383987", "项目经理": "2200000486751879", "计划跟进日期": "2200000486372052", "具体动作": "2200000486372050", "实际跟进日期": "2200000486372053", "跟进方式": "2200000486534469", "本阶段成果": "2200000486538399", "本阶段跟进成果": "2200000486536623", "成果产出": "2200000486538398", "本次跟进记录": "2200000486751819", "项目类型": "2200000486973981", "调整项目阶段": "2200000487049663", "下次跟进日期": "2200000486699642", "下次跟进具体动作": "2200000486699644", "下次待办&跟进": "2200000486995324", "是否有效": "2200000488105232", "备注": "2200000488105233", "上次待办&跟进": "2200000486997175", "关联学校": "2200000488475901", "阿米巴单元-阿米巴负责人": "1124001114000000", "阿米巴单元-团队成员": "1124001115000000", "阿米巴单元-联盟盟主": "1124001153001113", "项目-立项编号": "1112001155000000", "项目-项目属性": "1112001342000000", "项目-项目类型": "1112001329000000", "项目-项目标签": "1112001348000000", "项目-项目名称": "1112001101000000", "项目-项目经理": "1112001102000000", "项目-系统账号": "1112001102001102", "项目-学校/教育局": "1112001196000000", "项目-立项审核": "1112001151000000", "项目-项目状态": "1112001201000000", "项目-项目阶段": "1112001330000000", "当前项目阶段-阶段提示": "1123001113000000", "当前项目阶段-序号": "1123001115000000", "项目经理-员工姓名": "1135001101000000", "项目经理-系统账号": "1135001102000000", "项目经理-外部用户": "1135001121000000", "项目经理-直营小组": "1135001118000000", "项目经理-直营小组长": "1135001120000000", "项目经理-外部用户-组长": "1135001122000000", "关联学校-学校名称": "1143001102000000", "关联学校-学校ID": "1143001101000000"}, "table_id": "2100000059700201", "name": "项目跟进记录（直营）", "alias": "", "space_id": "4000000003570865", "created_on": "2024-10-20 14:11:11", "fields": [{"field_id": "2200000486534467", "name": "阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000486372048", "name": "项目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000486383987", "name": "当前项目阶段", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692208", "space_id": "4000000003570865"}}, {"field_id": "2200000486751879", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000486372052", "name": "计划跟进日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486372050", "name": "具体动作", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000486372053", "name": "实际跟进日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486534469", "name": "跟进方式", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "电话"}, {"id": "2", "name": "微信"}, {"id": "3", "name": "面谈"}]}}, {"field_id": "2200000486538399", "name": "本阶段成果", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "暂无阶段成果"}, {"id": "2", "name": "已有阶段成果"}]}}, {"field_id": "2200000486536623", "name": "本阶段跟进成果", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 1, "table_id": "2100000059696894", "space_id": "4000000003570865"}}, {"field_id": "2200000486538398", "name": "成果产出", "alias": "", "field_type": "file", "data_type": "file", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 1, "is_watermark": 0}}, {"field_id": "2200000486751819", "name": "本次跟进记录", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000486973981", "name": "项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692209", "space_id": "4000000003570865"}}, {"field_id": "2200000487049663", "name": "调整项目阶段", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "调整后，可同步修改项目管理中的项目阶段", "config": {"is_multi": 0, "table_id": "2100000059692208", "space_id": "4000000003570865"}}, {"field_id": "2200000486699642", "name": "下次跟进日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486699644", "name": "下次跟进具体动作", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000486995324", "name": "下次待办&跟进", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059700201", "space_id": "4000000003570865"}}, {"field_id": "2200000488105232", "name": "是否有效", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "否"}, {"id": "2", "name": "是"}]}}, {"field_id": "2200000488105233", "name": "备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000486997175", "name": "上次待办&跟进", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059700201", "space_id": "4000000003570865"}}, {"field_id": "2200000488475901", "name": "关联学校", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "1124001114000000", "name": "阿米巴负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486534467}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1124001115000000", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486534467}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1124001153001113", "name": "联盟盟主", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486534467}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1112001155000000", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486372048}, "required": false, "description": "", "config": {}}, {"field_id": "1112001342000000", "name": "项目属性", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059740536", "space_id": "4000000003570865"}}, {"field_id": "1112001329000000", "name": "项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692209", "space_id": "4000000003570865"}}, {"field_id": "1112001348000000", "name": "项目标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059982481", "space_id": "4000000003570865"}}, {"field_id": "1112001101000000", "name": "项目名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {}}, {"field_id": "1112001102000000", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1112001102001102", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486372048}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1112001196000000", "name": "学校/教育局", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "必填，每个学校/教育局单独立项，若无账号请先处理账号问题", "config": {"is_multi": 1, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "1112001151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486372048}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "其他待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1112001201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486372048}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "1112001330000000", "name": "项目阶段", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486372048}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692208", "space_id": "4000000003570865"}}, {"field_id": "1123001113000000", "name": "阶段提示", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {"field_id": 2200000486383987}, "required": false, "description": "", "config": {}}, {"field_id": "1123001115000000", "name": "序号", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000486383987}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1135001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486751879}, "required": true, "description": "", "config": {}}, {"field_id": "1135001102000000", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486751879}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1135001121000000", "name": "外部用户", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486751879}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1135001118000000", "name": "直营小组", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486751879}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059956977", "space_id": "4000000003570865"}}, {"field_id": "1135001120000000", "name": "直营小组长", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000486751879}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1135001122000000", "name": "外部用户-组长", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486751879}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1143001102000000", "name": "学校名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000488475901}, "required": true, "description": "", "config": {}}, {"field_id": "1143001101000000", "name": "学校ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000488475901}, "required": true, "description": "", "config": {}}]}