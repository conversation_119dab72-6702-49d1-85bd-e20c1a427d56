# Swagger 插件技术规范与实现

## 概述

本插件为 Strapi v3.6.8 项目提供自动化的 API 文档生成功能，基于 OpenAPI 3.0 规范，支持自动扫描路由、识别权限、读取模型结构等高级特性。

## 核心特性

### 1. 自动路由扫描
- 自动识别使用 `createDefaultRoutes` 创建的标准 CRUD 路由
- 支持自定义路由的 `@swagger` 注释
- 智能识别路由模式（标准 CRUD、导入导出、批量操作等）

### 2. 权限感知
- 读取 `/config/permission.js` 中的 PublicFunction 配置
- 自动标记无需认证的公开接口（`security: []`）
- 支持 `createDefaultPermissions` 扩展语法解析

### 3. 模型驱动文档
- 自动读取模型定义（支持 `.js` 和 `.json` 格式）
- 根据模型属性生成准确的请求/响应 Schema
- 智能生成查询参数（精确查询、范围查询、模糊查询）

### 4. 扩展配置
- 支持 YAML 配置文件定义插件接口文档
- 支持外部 JavaScript 配置文件
- 配置文件热重载

## 技术架构

### 文件结构
```
plugins/swagger/
├── config/
│   ├── functions/bootstrap.js  # 插件启动逻辑
│   └── routes.js              # Swagger UI 路由
├── middlewares/
│   └── swagger/               # Swagger UI 中间件
├── services/
│   ├── auto-generator.js      # 核心生成逻辑
│   ├── extensions.js          # 扩展配置加载器
│   ├── extensions.yaml        # 插件接口文档配置
│   ├── helper.js              # 工具函数
│   ├── swagger.js             # Swagger 服务
│   └── tags.js                # 标签管理
└── docs/                      # 文档目录
```

### 核心模块

#### 1. auto-generator.js
主要功能：
- `generateAutoSwaggerPaths()`: 扫描所有 API 路由并生成 Swagger 路径定义
- `isPublicEndpoint()`: 检查接口是否为公开访问
- `loadModelDefinition()`: 加载模型定义文件
- `modelToSwaggerSchema()`: 将模型转换为 OpenAPI Schema
- `buildQueryParameters()`: 构建查询参数列表
- `extendSwaggerSpec()`: 扩展和优化 Swagger 规范

#### 2. extensions.js/yaml
用于定义无法修改源代码的模块（如 plugins）的 API 文档：
- 支持多文件配置合并
- YAML 格式便于维护
- 支持完整的 OpenAPI 规范

#### 3. swagger 中间件
- 提供 Swagger UI 界面
- 处理文档请求和静态资源
- 支持自定义 UI 配置

## 配置说明

### 项目配置文件：`/config/swagger.js`
```javascript
module.exports = {
  // API 文档信息
  title: 'API 文档',
  version: '1.0.0',
  description: 'API 接口文档',
  
  // 标签定义
  defaultTags: {
    'access-key': '访问密钥',
    'user': '用户管理',
    // ...
  },
  
  // UI 配置
  uiOptions: {
    docExpansion: 'none',
    tagsSorter: false,  // 使用后端定义的顺序
    defaultModelsExpandDepth: -1
  }
};
```

### 扩展配置：`plugins/swagger/services/extensions.yaml`
```yaml
users-permissions:
  tags:
    - name: 用户认证
      description: 用户注册、登录等认证相关接口
  paths:
    /auth/local:
      post:
        tags: [用户认证]
        summary: 用户登录
        # ... 完整的 OpenAPI 定义
```

## 使用指南

### 基本使用
1. 启动项目后访问：http://localhost:3015/api-docs
2. 所有标准 CRUD 接口会自动生成文档
3. 无需额外配置即可使用

### 高级功能

#### 1. 自定义接口注释
参考 `plugins/swagger/docs/自定义接口注释指南.md`

#### 2. 配置公开接口
在 `/config/permission.js` 中定义：
```javascript
{
  name: 'PublicFunction',
  apiPermissions: [
    { type: 'application', controller: 'benefit', action: 'getCustomerProductBenefit' },
    ...createDefaultPermissions({ type: 'application', controller: 'customer' })
  ]
}
```

#### 3. 扩展插件文档
编辑 `plugins/swagger/services/extensions.yaml` 添加插件接口文档

## 查询参数说明

系统会根据模型字段自动生成以下查询参数：

### 基础参数
- `_limit`: 每页返回数量（默认 10）
- `_start`: 偏移量（用于分页）
- `_sort`: 排序字段（如：name:ASC）
- `_q`: 全文搜索关键词
- `_where`: MongoDB 查询条件（JSON 格式）

### 字段查询参数
- 精确查询：`fieldName=value`
- 模糊查询：`fieldName_contains=value`（字符串类型）
- 范围查询：`fieldName_gte=value`、`fieldName_lte=value`（数字/日期类型）

## 最佳实践

1. **保持模型定义完整**：确保模型包含 `label`、`required`、`default` 等元信息
2. **使用语义化的标签**：在 `/config/swagger.js` 中定义清晰的中文标签
3. **及时更新权限配置**：新增公开接口时同步更新 `permission.js`
4. **合理组织扩展文档**：按模块划分 extensions.yaml 的内容

## 故障排查

### 常见问题

1. **文档未更新**
   - 检查是否有语法错误
   - 重启服务器以重新加载配置

2. **模型字段未显示**
   - 确认模型文件路径正确
   - 检查字段是否设置了 `visible: false`

3. **权限标记错误**
   - 验证 permission.js 配置
   - 检查 createDefaultPermissions 语法

## 扩展开发

如需扩展插件功能，主要修改点：
1. `auto-generator.js`: 添加新的文档生成逻辑
2. `extensions.yaml`: 添加新的接口文档
3. `swagger 中间件`: 自定义 UI 行为

## 相关链接

- [自定义接口注释指南](./自定义接口注释指南.md) - 详细的注释模板
- [OpenAPI 3.0 规范](https://swagger.io/specification/) - 官方规范文档
