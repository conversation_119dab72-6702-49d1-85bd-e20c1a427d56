const { CurdRouter } = require('accel-utils')
const {
  getBaseQuery,
  getQueryFollowerChange,
  getUserQueryByRole,
  getFollowerNowGroup,
  getFollowerRecord,
} = require('../../crm/services/customer-service')

const curdRouter = new (class extends CurdRouter {
  // ...
  _getIntersection (arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }


  async count (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    getUserQueryByRole(query, user, groups)
    return super.count(ctx)
  }

  async find (ctx) {
    const user = ctx.state.user
    const { query } = this._parseCtx(ctx)

    const groups = await strapi.query('manager-group').find({})
    getBaseQuery(query)
    getUserQueryByRole(query, user, groups)

    return await super.find(ctx)
  }

})('recycle-clue-detail')

module.exports = {
  ...curdRouter.createHandlers(),
}
