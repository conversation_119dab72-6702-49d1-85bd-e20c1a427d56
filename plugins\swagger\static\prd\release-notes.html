<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Strapi Swagger Plugin 发版记录，包含新功能、改进和修复的详细说明">
    <meta name="keywords" content="<PERSON><PERSON><PERSON>, Swagger, 发版记录, 更新日志, 版本历史">
    <title>发版记录 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="layout">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>📚 Strapi Swagger</h1>
                <p>发版记录</p>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-back">
                    <a href="index.html">← 返回首页</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">版本历史</div>
                    <a href="#v0.1.0" class="nav-item active">
                        <i>🎯</i>
                        <span>v0.1.0 初始版本</span>
                    </a>
                    <a href="#v0.0.9" class="nav-item">
                        <i>🔧</i>
                        <span>v0.0.9 功能完善</span>
                    </a>
                    <a href="#v0.0.8" class="nav-item">
                        <i>⚡</i>
                        <span>v0.0.8 性能优化</span>
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <div class="content-body">
                <!-- 当前版本信息 -->
                <div class="current-version-banner">
                    <div class="current-version-info">
                        <div class="version-badge-large">v0.1.0</div>
                        <div class="version-details">
                            <h3>📦 当前版本</h3>
                            <p>最新稳定版本，支持模块化文档生成和响应式界面设计</p>
                            <div class="version-features">
                                <h4>主要特性</h4>
                                <ul>
                                    <li>OpenAPI JSON 支持下拉选项调整具体模块</li>
                                    <li>实现按模块过滤的 Swagger 文档功能</li>
                                    <li>优化 Swagger 文档页面模块选择器的 UI 样式</li>
                                    <li>智能接口扫描和自动文档生成</li>
                                    <li>现代化粘性头部导航设计</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="timeline">
                <!-- v0.1.0 - 最新版本 -->
                <div class="section" id="v0.1.0">
                    <div class="release-date-badge">07-16</div>
                    <div class="card">
                        <div class="version-header">
                            <span class="version-tag">v0.1.0</span>
                            <span class="release-date">📅 2024年7月16日</span>
                            <span class="release-type initial">初始版本</span>
                        </div>
                        
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-number">12</div>
                                <div class="stat-label">新功能</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">8</div>
                                <div class="stat-label">界面优化</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-number">6</div>
                                <div class="stat-label">问题修复</div>
                            </div>
                        </div>

                        <div class="changes-grid">
                            <div class="change-section new">
                                <div class="change-header">
                                    <span class="change-icon">✨</span>
                                    <h4 class="change-title">核心功能</h4>
                                </div>
                                <ul class="change-list">
                                    <li>OpenAPI JSON 支持下拉选项调整具体模块</li>
                                    <li>实现按模块过滤的 Swagger 文档功能</li>
                                    <li>完善 swagger.json API 端点的模块和控制器过滤功能</li>
                                    <li>智能接口扫描和自动文档生成</li>
                                    <li>模块化文档浏览和导航</li>
                                    <li>支持 JSDoc @swagger 注释自定义文档</li>
                                    <li>权限感知的接口分类</li>
                                </ul>
                            </div>

                            <div class="change-section improved">
                                <div class="change-header">
                                    <span class="change-icon">🎨</span>
                                    <h4 class="change-title">界面体验</h4>
                                </div>
                                <ul class="change-list">
                                    <li>优化 Swagger 文档页面模块选择器的 UI 样式</li>
                                    <li>实现粘性头部导航设计，提升浏览体验</li>
                                    <li>现代化渐变背景和毛玻璃效果</li>
                                    <li>响应式设计，支持移动端浏览</li>
                                    <li>优化下拉选择器的位置和样式一致性</li>
                                    <li>改善小窗口下的内容布局和换行处理</li>
                                </ul>
                            </div>

                            <div class="change-section fixed">
                                <div class="change-header">
                                    <span class="change-icon">🐛</span>
                                    <h4 class="change-title">问题修复</h4>
                                </div>
                                <ul class="change-list">
                                    <li>修复业务模块显示扩展模块接口的混合问题</li>
                                    <li>完善控制器过滤，解决第三方接口出现在父控制器的问题</li>
                                    <li>修复固定头部遮挡内容的问题</li>
                                    <li>优化接口文档访问权限和路由配置</li>
                                </ul>
                            </div>
                        </div>

                        <div class="highlight-box">
                            <div class="highlight-title">
                                <span>🚀</span>
                                快速开始
                            </div>
                            <div class="highlight-content">
                                <strong>1. 添加子模块：</strong><br>
                                <code>git <NAME_EMAIL>:wly/strapi-swagger.git plugins/swagger</code><br><br>
                                
                                <strong>2. 安装依赖：</strong><br>
                                <code>cd plugins/swagger && npm install && cd ../..</code><br><br>
                                
                                <strong>3. 启动项目：</strong><br>
                                <code>npm run dev</code><br><br>
                                
                                <strong>4. 访问文档：</strong><br>
                                <code>http://localhost:8108/api-docs</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- v0.0.9 -->
                <div class="release-item">
                    <div class="release-date-badge">07-15</div>
                    <div class="card">
                        <div class="version-header">
                            <span class="version-tag">v0.0.9</span>
                            <span class="release-date">📅 2024年7月15日</span>
                            <span class="release-type patch">功能完善</span>
                        </div>

                        <div class="changes-grid">
                            <div class="change-section improved">
                                <div class="change-header">
                                    <span class="change-icon">🔧</span>
                                    <h4 class="change-title">功能改进</h4>
                                </div>
                                <ul class="change-list">
                                    <li>完善接口扫描逻辑，支持更多路由类型</li>
                                    <li>优化控制器方法验证机制</li>
                                    <li>改进自动生成接口的标签和分组</li>
                                </ul>
                            </div>

                            <div class="change-section fixed">
                                <div class="change-header">
                                    <span class="change-icon">🐛</span>
                                    <h4 class="change-title">问题修复</h4>
                                </div>
                                <ul class="change-list">
                                    <li>修复部分接口无法正确生成文档的问题</li>
                                    <li>解决扩展模块配置加载异常</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- v0.0.8 -->
                <div class="release-item">
                    <div class="release-date-badge">07-14</div>
                    <div class="card">
                        <div class="version-header">
                            <span class="version-tag">v0.0.8</span>
                            <span class="release-date">📅 2024年7月14日</span>
                            <span class="release-type patch">稳定性改进</span>
                        </div>

                        <div class="changes-grid">
                            <div class="change-section improved">
                                <div class="change-header">
                                    <span class="change-icon">⚡</span>
                                    <h4 class="change-title">性能优化</h4>
                                </div>
                                <ul class="change-list">
                                    <li>优化文档生成性能，减少重复扫描</li>
                                    <li>改进缓存机制，提升响应速度</li>
                                    <li>精简自动生成的接口描述</li>
                                </ul>
                            </div>

                            <div class="change-section fixed">
                                <div class="change-header">
                                    <span class="change-icon">🐛</span>
                                    <h4 class="change-title">Bug 修复</h4>
                                </div>
                                <ul class="change-list">
                                    <li>修复开发环境下的热重载问题</li>
                                    <li>解决某些控制器方法无法识别的问题</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 更多版本提示 -->
                <div class="release-item">
                    <div class="footer-section">
                        <h3 class="footer-title">🎯 版本历史</h3>
                        <p class="footer-description">
                            更多详细的版本历史和技术变更请查看项目的 Git 提交记录，
                            或体验最新版本的功能特性
                        </p>
                        <div class="footer-links">
                            <a href="#" class="footer-link" target="_blank">
                                📝 查看提交历史
                            </a>
                            <a href="/api-docs" class="footer-link secondary" target="_blank">
                                🌐 体验最新版本
                            </a>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </main>
    </div>

    <script src="scripts.js"></script>
</body>
</html>