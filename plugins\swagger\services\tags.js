/**
 * Swagger 标签配置文件
 * 用于管理所有 API 模块的默认标签和描述
 * 支持外部扩展配置
 */

const fs = require('fs');
const path = require('path');

// 缓存外部配置
let cachedExternalTags = null;
let cachedMergedConfig = null;

// 内置的默认标签配置（通用型）
const builtInTags = {
  // API 模块默认标签映射
  defaultTags: {},

  // 特殊路径标签映射
  pathTags: {},

  // 全局标签（这些标签会添加到所有文档中）
  globalTags: [
    {
      name: '系统管理',
      description: '系统配置和管理相关接口'
    },
    {
      name: '用户管理',
      description: '用户信息管理相关接口'
    }
  ]
};

/**
 * 加载外部标签配置
 * @returns {Object} 外部配置对象
 */
function loadExternalTags() {
  const externalConfigPaths = [
    // 项目根目录的统一配置文件
    path.join(process.cwd(), 'config', 'swagger.js'),
    // 项目根目录的 JSON 配置
    path.join(process.cwd(), 'config', 'swagger.json')
  ];

  for (const configPath of externalConfigPaths) {
    try {
      if (fs.existsSync(configPath)) {
        const ext = path.extname(configPath);
        let externalConfig;

        if (ext === '.js') {
          // 清除 require 缓存以支持热重载
          delete require.cache[configPath];
          externalConfig = require(configPath);
        } else if (ext === '.json') {
          const content = fs.readFileSync(configPath, 'utf8');
          externalConfig = JSON.parse(content);
        }

        console.log(`已加载外部 Swagger 标签配置: ${configPath}`);

        // 如果是统一配置文件，返回标签相关的配置
        if (configPath.includes('swagger.js') || configPath.includes('swagger.json')) {
          return {
            defaultTags: externalConfig.defaultTags || {},
            pathTags: externalConfig.pathTags || {},
            additionalGlobalTags: externalConfig.additionalGlobalTags || [],
            ignore: externalConfig.ignore || { controllers: [], paths: [] }
          };
        }

        return externalConfig;
      }
    } catch (error) {
      console.error(`加载外部标签配置失败 (${configPath}):`, error);
    }
  }

  return {};
}

/**
 * 深度合并两个对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
function deepMerge(target, source) {
  const output = { ...target };

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
          output[key] = deepMerge(target[key], source[key]);
        } else {
          output[key] = source[key];
        }
      } else {
        output[key] = source[key];
      }
    }
  }

  return output;
}

/**
 * 获取最终的标签配置（带缓存）
 * @param {boolean} forceReload 是否强制重新加载
 * @returns {Object} 合并后的标签配置
 */
function getTags(forceReload = false) {
  // 如果已有缓存且不需要强制重载，直接返回缓存
  if (cachedMergedConfig && !forceReload) {
    return cachedMergedConfig;
  }

  // 如果外部配置已缓存且不需要强制重载，使用缓存的外部配置
  const externalTags = (cachedExternalTags && !forceReload) 
    ? cachedExternalTags 
    : (cachedExternalTags = loadExternalTags());

  // 深度合并配置，外部配置优先级更高
  const mergedConfig = deepMerge(builtInTags, externalTags);

  // 如果外部配置有 additionalGlobalTags，追加到 globalTags
  if (externalTags.additionalGlobalTags && Array.isArray(externalTags.additionalGlobalTags)) {
    mergedConfig.globalTags = [
      ...(mergedConfig.globalTags || []),
      ...externalTags.additionalGlobalTags
    ];
  }

  // 缓存合并后的配置
  cachedMergedConfig = mergedConfig;
  
  return mergedConfig;
}

// 导出配置对象和工具函数
module.exports = getTags();

// 同时导出工具函数供其他模块使用
module.exports.getTags = getTags;
module.exports.loadExternalTags = loadExternalTags;
module.exports.builtInTags = builtInTags;
