const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('api')
const axios = require('axios');
const { ObjectId } = require('mongodb');
const _ = require('lodash')

async function apiExecute(ctx) {
    try {
        const { id } = ctx.params;
        const params = _.assign({}, ctx.request.body, ctx.request.query);

        let data = await internalApiExecute(id, params);
        return ctx.wrapper.succ(data);
    } catch (e) {
        console.log(e)
        return ctx.wrapper.error('HANDLE_ERROR', e.message || e)
    }
}

async function internalApiExecute(id, params) {
    const reg = /\$\{(.+?)\}/g;

    // 获取API信息
    const api = await strapi.query('api').findOne({ id: ObjectId(id) }, [])
    const method = api.method;
    let url = api.url.replace(reg, (match, key) => {
        let value = api.inputParam.find(item => item.key === key)?.default || match;
        return data.hasOwnProperty(key) ? data[key] : value;
    });
    let data = {}, headers = {}, query, body = {};
    if (api.headers) {
        headers = JSON.parse(api.headers.replace(reg, (match, key) => {
            let value = api.inputParam.find(item => item.key === key)?.default || match;
            return params.hasOwnProperty(key) ? params[key] : value;
        }));
    }
    if (api.query) {
        query = api.query.replace(reg, (match, key) => {
            let value = api.inputParam.find(item => item.key === key)?.default || match;
            return params.hasOwnProperty(key) ? params[key] : value;
        });
        url = url + `${query ? '?' + query : ''}`;
    }
    if (api.body) {
        body = JSON.parse(api.body.replace(reg, (match, key) => {
            let value = api.inputParam.find(item => item.key === key)?.default || match;
            return params.hasOwnProperty(key) ? params[key] : value;
        }));
    }

    let response;
    try {
        response = await axios({ url, method, headers, data: body });
    } catch (e) {
        throw new Error(e?.response?.data?.message || e.message || e)
    }
    if (response.status === 200) {
        data = response.data;
    }
    return data;
}

module.exports = {
    apiExecute,
    internalApiExecute,
    ...curdRouter.createHandlers(),
}
