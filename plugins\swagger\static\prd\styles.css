/* 📚 Strapi Swagger Plugin Documentation - 现代化样式设计 */

:root {
    /* 主色调 - 现代蓝紫色系 */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --primary-gradient: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);

    /* 中性色系 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 语义化颜色 */
    --success-color: #059669;
    --success-bg: #ecfdf5;
    --warning-color: #d97706;
    --warning-bg: #fffbeb;
    --error-color: #dc2626;
    --error-bg: #fef2f2;
    --info-color: #0ea5e9;
    --info-bg: #f0f9ff;

    /* 阴影系统 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 边框半径 */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* 间距系统 */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;

    /* 过渡动画 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;

    /* 字体系统 */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-700);
    background: var(--gray-50);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
}

/* 主布局容器 */
.layout {
    display: flex;
    min-height: 100vh;
}

/* 左侧导航栏 - 现代化设计 */
.sidebar {
    width: 280px;
    background: var(--gray-800);
    color: white;
    padding: 0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: var(--shadow-2xl);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header {
    padding: var(--space-6);
    background: var(--primary-gradient);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.sidebar-header h1 {
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin: 0;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.sidebar-header p {
    font-size: var(--font-size-sm);
    opacity: 0.9;
    margin: var(--space-2) 0 0 0;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--gray-400);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0 var(--space-6);
    margin-bottom: var(--space-2);
    position: relative;
}

.nav-section-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: var(--space-6);
    width: 2rem;
    height: 2px;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
}

.nav-item {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-6);
    color: var(--gray-300);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-left: 3px solid transparent;
    position: relative;
    margin: var(--space-1) 0;
    border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.nav-item:hover {
    background: rgba(255,255,255,0.08);
    border-left-color: var(--primary-color);
    color: white;
    transform: translateX(2px);
}

.nav-item.active {
    background: rgba(255,255,255,0.15);
    border-left-color: var(--primary-color);
    color: white;
    font-weight: 600;
    transform: none;
}

.nav-item i {
    margin-right: var(--space-3);
    width: 1.2rem;
    text-align: center;
    font-size: var(--font-size-base);
    position: relative;
    z-index: 1;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
}

.nav-item span {
    position: relative;
    z-index: 1;
    font-weight: 500;
}

/* 右侧内容区域 - 优化布局 */
.content {
    flex: 1;
    margin-left: 280px;
    padding: 0;
    background: var(--gray-50);
    min-height: 100vh;
    position: relative;
}

.content-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: var(--space-12) var(--space-8);
    border-radius: var(--radius-2xl);
    margin-bottom: var(--space-8);
    position: relative;
    overflow: hidden;
}

.content-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);
    opacity: 0.8;
}

.content-header h1 {
    font-size: var(--font-size-5xl);
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    font-weight: 800;
    line-height: 1.1;
    position: relative;
    z-index: 1;
    letter-spacing: -0.02em;
}

.content-header p {
    font-size: var(--font-size-xl);
    color: var(--gray-700);
    line-height: 1.6;
    font-weight: 500;
    max-width: 70ch;
    position: relative;
    z-index: 1;
}

.content-body {
    padding: var(--space-8);
    max-width: 1200px;
    margin: 0 auto;
}

/* 返回导航 - 现代化设计 */
.nav-back {
    background: rgba(255,255,255,0.05);
    padding: var(--space-4) var(--space-6);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    margin-bottom: 0;
    backdrop-filter: blur(10px);
}

.nav-back a {
    color: var(--gray-300);
    text-decoration: none;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    font-weight: 500;
}

.nav-back a::before {
    content: '←';
    font-size: var(--font-size-base);
    transition: transform var(--transition-fast);
}

.nav-back a:hover {
    color: white;
    transform: translateX(-2px);
}

.nav-back a:hover::before {
    transform: translateX(-2px);
}

/* 文档内容系统 - 优化设计 */
.section {
    margin: var(--space-10) 0;
    position: relative;
}

.section:first-child {
    margin-top: 0;
}

.section h2 {
    color: var(--gray-800);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--space-4);
    border-bottom: 3px solid var(--gray-200);
    padding-bottom: var(--space-3);
    font-weight: 700;
    position: relative;
    line-height: 1.3;
}

.section h2::before {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 0;
    width: 3rem;
    height: 3px;
    background: var(--primary-gradient);
    border-radius: var(--radius-sm);
}

.section h3 {
    color: var(--gray-700);
    font-size: var(--font-size-xl);
    margin: var(--space-6) 0 var(--space-3) 0;
    font-weight: 600;
    line-height: 1.4;
    position: relative;
    padding-left: var(--space-4);
}

.section h3::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 1.2em;
    background: var(--primary-color);
    border-radius: var(--radius-sm);
}

.section h4 {
    color: var(--gray-700);
    font-size: var(--font-size-lg);
    margin: var(--space-4) 0 var(--space-2) 0;
    font-weight: 600;
}

.section p {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--space-4);
    max-width: 70ch;
}

.section ul,
.section ol {
    margin: var(--space-4) 0;
    padding-left: var(--space-6);
    color: var(--gray-600);
    line-height: 1.7;
}

.section li {
    margin-bottom: var(--space-2);
    position: relative;
}

.section ul li::marker {
    color: var(--primary-color);
}

.section ol li::marker {
    color: var(--primary-color);
    font-weight: 600;
}

.section blockquote {
    border-left: 4px solid var(--primary-color);
    padding: var(--space-4) var(--space-6);
    margin: var(--space-4) 0;
    background: var(--gray-50);
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    color: var(--gray-700);
    font-style: italic;
    position: relative;
}

.section blockquote::before {
    content: '“';
    position: absolute;
    top: var(--space-2);
    left: var(--space-4);
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    line-height: 1;
}

.section a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    border-bottom: 1px solid transparent;
    transition: all var(--transition-fast);
}

.section a:hover {
    border-bottom-color: var(--primary-color);
    color: var(--primary-dark);
}

.section code:not(.code-block code) {
    background: var(--gray-100);
    color: var(--primary-color);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.9em;
    font-weight: 500;
    border: 1px solid var(--gray-200);
}

.section pre {
    margin: var(--space-4) 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.section img {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    margin: var(--space-4) 0;
}

.section hr {
    border: none;
    height: 2px;
    background: var(--gray-200);
    margin: var(--space-8) 0;
    border-radius: var(--radius-sm);
}

/* 卡片系统 - 现代化设计 */
.card {
    background: white;
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    margin: var(--space-4) 0;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.example-box {
    background: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-left: 4px solid var(--info-color);
    padding: var(--space-6);
    margin: var(--space-4) 0;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    position: relative;
    transition: all var(--transition-normal);
}

.example-box::before {
    content: '📝';
    position: absolute;
    top: var(--space-4);
    right: var(--space-4);
    font-size: var(--font-size-lg);
    opacity: 0.6;
}

.example-box:hover {
    border-left-color: var(--primary-color);
    background: white;
    box-shadow: var(--shadow-md);
}

.example-box h4 {
    color: var(--gray-800);
    margin-bottom: var(--space-3);
    font-weight: 600;
    position: relative;
    z-index: 1;
}

/* 提示框系统 - 语义化设计 */
.tip-box, .info-box {
    background: var(--success-bg);
    border: 1px solid var(--success-color);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin: var(--space-4) 0;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.tip-box::before, .info-box::before {
    content: '✓';
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    width: 1.5rem;
    height: 1.5rem;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.tip-box h4, .info-box h4 {
    color: var(--success-color);
    margin-bottom: var(--space-2);
    margin-left: var(--space-8);
    font-weight: 600;
}

.tip-box p, .info-box p {
    color: var(--success-color);
    margin-left: var(--space-8);
    line-height: 1.6;
}

.warning-box {
    background: var(--warning-bg);
    border: 1px solid var(--warning-color);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin: var(--space-4) 0;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.warning-box::before {
    content: '⚠';
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    width: 1.5rem;
    height: 1.5rem;
    background: var(--warning-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.warning-box h4 {
    color: var(--warning-color);
    margin-bottom: var(--space-2);
    margin-left: var(--space-8);
    font-weight: 600;
}

.warning-box p {
    color: var(--warning-color);
    margin-left: var(--space-8);
    line-height: 1.6;
}

.error-box {
    background: var(--error-bg);
    border: 1px solid var(--error-color);
    border-radius: var(--radius-lg);
    padding: var(--space-5);
    margin: var(--space-4) 0;
    position: relative;
    box-shadow: var(--shadow-sm);
}

.error-box::before {
    content: '✗';
    position: absolute;
    top: var(--space-3);
    left: var(--space-3);
    width: 1.5rem;
    height: 1.5rem;
    background: var(--error-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

.error-box h4 {
    color: var(--error-color);
    margin-bottom: var(--space-2);
    margin-left: var(--space-8);
    font-weight: 600;
}

.error-box p {
    color: var(--error-color);
    margin-left: var(--space-8);
    line-height: 1.6;
}

/* 代码块系统 - 现代化设计 */
.code-block {
    overflow: hidden;
    background: var(--gray-900);
    color: var(--gray-100);
    padding: 0 var(--space-4);
    border-radius: var(--radius-lg);
    overflow-x: auto;
    margin: var(--space-4) 0;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, 'Liberation Mono', Menlo, Monaco, 'Courier New', monospace;
    line-height: 1.2;
    position: relative;
    white-space: pre;
    word-wrap: normal;
    word-break: normal;
    border: 1px solid var(--gray-700);
    box-shadow: var(--shadow-lg);
    position: relative; /* 确保 copy-button 的绝对定位是相对于此元素 */
    box-sizing: border-box;
}
.code-block > pre {
    margin: -42px 10px -40px 0px;
}
.code-block::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 0; /* 原为1px，改为0，去除顶部空行 */
    background: none; /* 或直接移除背景 */
}

.copy-button {
    position: absolute;
    top: 6px;
    right: 6px;
    background: var(--gray-600);
    color: white;
    border: none;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-md);
    font-size: var(--font-size-xs);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-weight: 500;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.5; /* 默认状态下半透明 */
}

.copy-button:hover {
    background: var(--gray-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
    opacity: 1; /* 鼠标悬浮时不透明 */
}

.copy-button:active {
    transform: translateY(0);
}

.copy-button.copied {
    background: var(--success-color);
    color: white;
    transform: scale(1.05);
}

/* 表格系统 - 现代化设计 */
.table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--space-4) 0;
    background: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
}

.table th,
.table td {
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-4);
    text-align: left;
    vertical-align: top;
}

.table th {
    background: var(--primary-gradient);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: var(--font-size-sm);
    letter-spacing: 0.025em;
    position: relative;
}

.table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: rgba(255, 255, 255, 0.3);
}

.table tr:nth-child(even) {
    background: var(--gray-50);
}

.table tr:hover {
    background: var(--info-bg);
    transition: background-color var(--transition-fast);
}

.table td:first-child {
    font-weight: 500;
    color: var(--gray-800);
}

.table code {
    background: var(--gray-100);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: 0.875em;
    font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Consolas, monospace;
    color: var(--primary-color);
    border: 1px solid var(--gray-200);
}

/* 目录系统 - 优化设计 */
.toc {
    background: white;
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin: var(--space-4) 0;
    border: 1px solid var(--gray-200);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: calc(var(--space-20) + 2rem);
}

.toc h3 {
    color: var(--gray-800);
    margin-bottom: var(--space-4);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.toc h3::before {
    content: '📚';
    font-size: var(--font-size-lg);
}

.toc ul {
    list-style: none;
    padding-left: 0;
}

.toc li {
    margin: var(--space-2) 0;
    position: relative;
    padding-left: var(--space-4);
}

.toc li::before {
    content: '→';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-weight: bold;
    transition: transform var(--transition-fast);
}

.toc li:hover::before {
    transform: translateX(2px);
}

.toc a {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast);
    display: block;
    padding: var(--space-1) 0;
}

.toc a:hover {
    color: var(--primary-color);
    text-decoration: none;
}


/* 移动端响应式设计 */
@media (max-width: 1024px) {
    .content-header {
        padding: var(--space-8);
        margin-bottom: var(--space-6);
        border-radius: var(--radius-xl);
    }

    .content-body {
        padding: var(--space-6);
    }

    .content-header h1 {
        font-size: var(--font-size-4xl);
    }
}

@media (max-width: 768px) {

    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
        box-shadow: none;
    }

    .sidebar.open {
        transform: translateX(0);
        box-shadow: var(--shadow-2xl);
    }

    .content {
        margin-left: 0;
    }

    .content-header {
        padding: var(--space-6);
        margin-bottom: var(--space-6);
        border-radius: var(--radius-xl);
    }

    .content-header h1 {
        font-size: var(--font-size-3xl);
        margin-top: 0;
    }

    .content-header p {
        font-size: var(--font-size-base);
    }

    .content-body {
        padding: var(--space-4);
    }

    .features {
        grid-template-columns: 1fr;
    }

    .card {
        padding: var(--space-6);
    }

    .toc {
        position: static;
        margin: var(--space-6) 0;
    }
}

@media (max-width: 480px) {
    .content-header {
        padding: var(--space-3);
        padding-top: calc(var(--space-3) + 3rem);
    }

    .content-body {
        padding: var(--space-3);
    }

    .content-header h1 {
        font-size: var(--font-size-xl);
    }

    .card {
        padding: var(--space-4);
        margin: var(--space-3) 0;
    }

    .code-block {
        padding: var(--space-3);
        font-size: var(--font-size-sm);
    }

    .table th,
    .table td {
        padding: var(--space-2);
        font-size: var(--font-size-sm);
    }
}

/* 滚动条系统 - 现代化设计 */
*::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

*::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-sm);
}

*::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

*::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: var(--gray-800);
}

.sidebar::-webkit-scrollbar-thumb {
    background: var(--gray-600);
    border-radius: var(--radius-sm);
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 支持 Firefox 滚动条 */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--gray-400) var(--gray-100);
}

.sidebar {
    scrollbar-color: var(--gray-600) var(--gray-800);
}

/* 加载动画系统 */
.loading {
    text-align: center;
    padding: var(--space-8);
    color: var(--gray-500);
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading::after {
    content: "加载中...";
    animation: pulse 1.5s infinite;
}

/* 版本徽章美化 */
.version-badge {
    display: inline-block;
    background: var(--primary-gradient);
    color: white;
    padding: var(--space-2) var(--space-4);
    border-radius: 2rem;
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-4);
    font-weight: 600;
    box-shadow: var(--shadow-md);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 当前版本信息横幅 */
.current-version-banner {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    color: white;
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    margin-bottom: var(--space-8);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.current-version-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
    animation: shimmer 3s ease-in-out infinite;
}

.current-version-info {
    display: flex;
    align-items: center;
    gap: var(--space-6);
    position: relative;
    z-index: 1;
}

.version-badge-large {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: var(--space-4) var(--space-6);
    border-radius: var(--radius-xl);
    font-size: var(--font-size-2xl);
    font-weight: 800;
    text-align: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    min-width: 120px;
}

.version-details h3 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-xl);
    font-weight: 600;
}

.version-details p {
    margin: 0 0 var(--space-4) 0;
    opacity: 0.9;
    font-size: var(--font-size-base);
}

.version-features h4 {
    margin: 0 0 var(--space-2) 0;
    font-size: var(--font-size-base);
    font-weight: 600;
}

.version-features ul {
    margin: 0;
    padding-left: var(--space-4);
    list-style: none;
}

.version-features li {
    position: relative;
    padding-left: var(--space-4);
    margin-bottom: var(--space-1);
    font-size: var(--font-size-sm);
}

.version-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: rgba(255, 255, 255, 0.8);
    font-weight: bold;
}

/* 产品介绍区域 */
.product-intro {
    margin-bottom: var(--space-12);
}

.intro-hero {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-8);
    align-items: center;
    margin-bottom: var(--space-8);
}

.hero-content h2 {
    font-size: var(--font-size-3xl);
    color: var(--gray-900);
    margin-bottom: var(--space-4);
    font-weight: 800;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--space-6);
}

.hero-stats {
    display: flex;
    gap: var(--space-6);
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 800;
    color: var(--primary-color);
    line-height: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-top: var(--space-1);
}

.hero-demo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.demo-preview {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    width: 100%;
    max-width: 400px;
}

.demo-header {
    background: var(--gray-100);
    padding: var(--space-3);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.demo-dots {
    display: flex;
    gap: var(--space-1);
}

.demo-dots span {
    width: 8px;
    height: 8px;
    background: var(--gray-400);
    border-radius: 50%;
}

.demo-title {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    font-weight: 500;
}

.demo-content {
    padding: var(--space-4);
}

.demo-endpoint {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    padding: var(--space-2) 0;
    border-bottom: 1px solid var(--gray-100);
}

.demo-endpoint:last-child {
    border-bottom: none;
}

.method {
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    color: white;
    text-transform: uppercase;
}

.method.get {
    background: var(--success-color);
}

.method.post {
    background: var(--primary-color);
}

.method.put {
    background: var(--warning-color);
}

.path {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: var(--font-size-sm);
    color: var(--gray-700);
}

/* 核心价值区域 */
.value-proposition {
    margin-bottom: var(--space-12);
}

.value-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-6);
}

.value-card {
    background: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    text-align: center;
    transition: all var(--transition-normal);
}

.value-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.value-icon {
    font-size: 3rem;
    margin-bottom: var(--space-4);
}

.value-card h3 {
    font-size: var(--font-size-xl);
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    font-weight: 600;
}

.value-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 快速开始区域优化 */
.quick-start {
    margin-bottom: var(--space-12);
}

.quick-start-intro {
    margin-bottom: var(--space-8);
}

.quick-start-intro p {
    font-size: var(--font-size-lg);
    color: var(--gray-700);
    margin-bottom: var(--space-4);
}

.prerequisites {
    background: var(--info-bg);
    border: 1px solid var(--info-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4);
    margin-top: var(--space-4);
}

.prerequisites h4 {
    color: var(--info-color);
    margin-bottom: var(--space-2);
    font-size: var(--font-size-base);
}

.prerequisites ul {
    margin: 0;
    padding-left: var(--space-4);
}

.prerequisites li {
    color: var(--info-color);
    margin-bottom: var(--space-1);
}

.steps {
    display: grid;
    gap: var(--space-6);
}

.step-result {
    margin-top: var(--space-4);
    padding: var(--space-4);
    background: var(--success-bg);
    border: 1px solid var(--success-color);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.result-icon {
    font-size: var(--font-size-xl);
}

.step-result p {
    color: var(--success-color);
    margin: 0;
}

/* 功能特性区域 */
.features {
    margin-bottom: var(--space-12);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--space-6);
    margin-top: var(--space-6);
}

.feature-highlight {
    background: var(--primary-color);
    color: white;
    font-size: var(--font-size-xs);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    margin-top: var(--space-3);
    display: inline-block;
    font-weight: 500;
}

/* 下一步引导区域 */
.next-steps {
    margin-top: var(--space-12);
}

.next-steps-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--space-6);
    margin: var(--space-6) 0;
}

.next-step-card {
    background: white;
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.next-step-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.next-step-icon {
    font-size: 2rem;
    margin-bottom: var(--space-4);
}

.next-step-card h3 {
    color: var(--gray-900);
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-3);
    font-weight: 600;
}

.next-step-card p {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--space-4);
}

.next-step-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    transition: all var(--transition-fast);
}

.next-step-link:hover {
    color: var(--primary-dark);
    transform: translateX(2px);
}

.support-section {
    background: var(--gray-50);
    padding: var(--space-6);
    border-radius: var(--radius-lg);
    text-align: center;
    margin-top: var(--space-8);
}

.support-section h3 {
    color: var(--gray-900);
    margin-bottom: var(--space-3);
    font-size: var(--font-size-lg);
}

.support-section p {
    color: var(--gray-600);
    margin: 0;
}

.support-section a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.support-section a:hover {
    text-decoration: underline;
}

/* 特性网格布局 */
.features {
    display: block;
    margin: var(--space-8) 0;
}

.feature-card {
    background: white;
    padding: var(--space-8);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.feature-icon {
    font-size: var(--font-size-5xl);
    margin-bottom: var(--space-4);
    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
}

.feature-card h3 {
    color: var(--gray-800);
    margin-bottom: var(--space-3);
    font-weight: 600;
    font-size: var(--font-size-xl);
}

.feature-card p {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 内容页面切换动画 */
.page-content {
    display: none;
}

.page-content.active {
    display: block;
    animation: fadeIn 0.6s ease-out;
}

/* 步骤样式优化 */
.step {
    background: var(--gray-50);
    border-left: 4px solid var(--info-color);
    padding: var(--space-6);
    margin: var(--space-4) 0;
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    transition: all var(--transition-normal);
    position: relative;
}

.step:hover {
    border-left-color: var(--primary-color);
    background: white;
    box-shadow: var(--shadow-md);
}

.step-header {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-4);
}

.step-number {
    background: var(--info-color);
    color: white;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: var(--space-4);
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.step:hover .step-number {
    background: var(--primary-color);
    transform: scale(1.1);
}

.step h3,
.step-header h3 {
    margin: 0 !important;
    color: var(--gray-800) !important;
    font-size: var(--font-size-xl) !important;
    font-weight: 600 !important;
}

/* 动画系统 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 动画应用 */
.page-content {
    animation: fadeIn 0.6s ease-out;
}

.feature-card {
    animation: fadeIn 0.6s ease-out;
    animation-fill-mode: both;
}

.feature-card:nth-child(1) { animation-delay: 0.1s; }
.feature-card:nth-child(2) { animation-delay: 0.2s; }
.feature-card:nth-child(3) { animation-delay: 0.3s; }
.feature-card:nth-child(4) { animation-delay: 0.4s; }
.feature-card:nth-child(5) { animation-delay: 0.5s; }
.feature-card:nth-child(6) { animation-delay: 0.6s; }

/* 工具类 */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.font-light {
    font-weight: 300;
}

.font-normal {
    font-weight: 400;
}

.font-medium {
    font-weight: 500;
}

.font-semibold {
    font-weight: 600;
}

.font-bold {
    font-weight: 700;
}

.text-xs {
    font-size: var(--font-size-xs);
}

.text-sm {
    font-size: var(--font-size-sm);
}

.text-base {
    font-size: var(--font-size-base);
}

.text-lg {
    font-size: var(--font-size-lg);
}

.text-xl {
    font-size: var(--font-size-xl);
}

.text-2xl {
    font-size: var(--font-size-2xl);
}

.text-3xl {
    font-size: var(--font-size-3xl);
}

.mb-1 {
    margin-bottom: var(--space-1);
}

.mb-2 {
    margin-bottom: var(--space-2);
}

.mb-3 {
    margin-bottom: var(--space-3);
}

.mb-4 {
    margin-bottom: var(--space-4);
}

.mb-6 {
    margin-bottom: var(--space-6);
}

.mb-8 {
    margin-bottom: var(--space-8);
}

.mt-1 {
    margin-top: var(--space-1);
}

.mt-2 {
    margin-top: var(--space-2);
}

.mt-3 {
    margin-top: var(--space-3);
}

.mt-4 {
    margin-top: var(--space-4);
}

.mt-6 {
    margin-top: var(--space-6);
}

.mt-8 {
    margin-top: var(--space-8);
}

.p-2 {
    padding: var(--space-2);
}

.p-4 {
    padding: var(--space-4);
}

.p-6 {
    padding: var(--space-6);
}

.p-8 {
    padding: var(--space-8);
}

.px-4 {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
}

.py-2 {
    padding-top: var(--space-2);
    padding-bottom: var(--space-2);
}

.rounded {
    border-radius: var(--radius-md);
}

.rounded-lg {
    border-radius: var(--radius-lg);
}

.shadow {
    box-shadow: var(--shadow-md);
}

.shadow-lg {
    box-shadow: var(--shadow-lg);
}

.border {
    border: 1px solid var(--gray-200);
}

.border-gray-300 {
    border-color: var(--gray-300);
}

.bg-white {
    background-color: white;
}

.bg-gray-50 {
    background-color: var(--gray-50);
}

.bg-gray-100 {
    background-color: var(--gray-100);
}

.text-gray-600 {
    color: var(--gray-600);
}

.text-gray-700 {
    color: var(--gray-700);
}

.text-gray-800 {
    color: var(--gray-800);
}

.text-primary {
    color: var(--primary-color);
}

.bg-primary {
    background-color: var(--primary-color);
}

/* 响应式显示隐藏 */
.hidden {
    display: none;
}

.block {
    display: block;
}

.inline-block {
    display: inline-block;
}

.flex {
    display: flex;
}

.inline-flex {
    display: inline-flex;
}

.grid {
    display: grid;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.gap-2 {
    gap: var(--space-2);
}

.gap-4 {
    gap: var(--space-4);
}

/* 特殊效果 */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.gradient-text {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: transform var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-4px);
}

/* 无障碍设计 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border-width: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0066cc;
        --gray-600: #000000;
        --gray-700: #000000;
    }

    .card {
        border: 2px solid var(--gray-600);
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .auto-dark {
        --gray-50: #1a1a1a;
        --gray-100: #2a2a2a;
        --gray-200: #3a3a3a;
        --gray-300: #4a4a4a;
        --gray-600: #9a9a9a;
        --gray-700: #dadada;
        --gray-800: #f0f0f0;
    }
}

/* 移动端新增样式 */
@media (max-width: 768px) {
    /* 移动端产品介绍 */
    .intro-hero {
        grid-template-columns: 1fr;
        gap: var(--space-6);
        text-align: center;
    }

    .hero-content h2 {
        font-size: var(--font-size-2xl);
    }

    .hero-description {
        font-size: var(--font-size-base);
    }

    .hero-stats {
        justify-content: center;
        gap: var(--space-4);
    }

    .demo-preview {
        max-width: 100%;
    }

    /* 移动端价值卡片 */
    .value-cards {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .value-card {
        padding: var(--space-4);
    }

    .value-icon {
        font-size: 2.5rem;
    }

    /* 移动端快速开始 */
    .quick-start-intro p {
        font-size: var(--font-size-base);
    }

    .steps {
        gap: var(--space-4);
    }

    .step {
        padding: var(--space-4);
    }

    .step-number {
        width: 2rem;
        height: 2rem;
        font-size: var(--font-size-sm);
    }

    /* 移动端功能特性 */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .feature-card {
        padding: var(--space-4);
    }

    /* 移动端下一步引导 */
    .next-steps-content {
        grid-template-columns: 1fr;
        gap: var(--space-4);
    }

    .next-step-card {
        padding: var(--space-4);
    }

    .next-step-icon {
        font-size: 1.5rem;
    }

    /* 移动端当前版本信息 */
    .current-version-info {
        flex-direction: column;
        text-align: center;
        gap: var(--space-4);
    }

    .version-badge-large {
        min-width: auto;
        font-size: var(--font-size-xl);
        padding: var(--space-3) var(--space-4);
    }

    .version-details h3 {
        font-size: var(--font-size-lg);
    }

    .version-features h4 {
        font-size: var(--font-size-sm);
    }

    .version-features li {
        font-size: var(--font-size-xs);
    }
}

/* API 端点样式 */
.endpoint-header {
    display: flex;
    align-items: center;
    gap: var(--space-3);
    margin-bottom: var(--space-4);
    padding: var(--space-3);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.method-badge {
    display: inline-block;
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    color: white;
    min-width: 3.5rem;
    text-align: center;
}

.method-get {
    background: var(--success-color);
}

.method-post {
    background: var(--primary-color);
}

.method-put {
    background: var(--warning-color);
}

.method-delete {
    background: var(--error-color);
}

.endpoint-url {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: var(--font-size-base);
    color: var(--gray-800);
    font-weight: 500;
    background: rgba(255, 255, 255, 0.7);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);
    border: 1px solid var(--gray-200);
}
