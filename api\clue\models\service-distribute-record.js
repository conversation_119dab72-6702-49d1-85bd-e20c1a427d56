module.exports = {
    "kind": "collectionType",
    "collectionName": strapi.config.server.mingdaoConfig.serviceDistributeRecordId,
    "connection": "mingdao",
    info: {
        name: 'ServiceDistributeRecord',
        label: '运营经理流转记录',
        description: '运营经理流转记录'
    },
    "options": {},
    "pluginOptions": {},
    "attributes": {
        manager: {
            // label: '新经理',
            "ref": "67518882ba60f67ec34c3773"
        },
    }
}