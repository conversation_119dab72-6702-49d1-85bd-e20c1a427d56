const { createDefaultPermissions: CP } = require('accel-utils')

const pageGroups = [
  {
    sId: 'PlatformOps',
    name: '平台运维🦸‍♂️',
    isSystem: true,
    pages: [
      {
        sId: 'FileManagement', name: '文件管理', icon: 'folder',
        meta: {
          modelId: 'file',
          modelPath: 'files',
        }
      },
    ],
  }
]

const functions = [
  {
    name: '文件上传',
    sId: 'FileUploadFunction',
    pages: [],
    apiPermissions: [
      // 上传权限
      { 'type': 'upload', 'controller': 'storage', 'action': 'getSts' },
      // 普通文件上传
      { 'type': 'upload', 'controller': 'storage', 'action': 'upload' },
      { 'type': 'upload', 'controller': 'storage', 'action': 'uploadToLocal' },
      // 富文本上传接口
      { 'type': 'upload', 'controller': 'storage', 'action': 'richTextUpload' },
      { 'type': 'upload', 'controller': 'storage', 'action': 'richTextUploadToLocal' },
      // 创建 File 实例
      { 'type': 'upload', 'controller': 'upload', 'action': 'create' },
    ]
  },
  {
    name: '上传管理',
    sId: 'UploadManagementFunction',
    pages: [
      'FileManagement',
    ],
    apiPermissions: [
      ...CP({ type: 'upload', controller: 'file' }),
    ],
  },
]

module.exports = {
  usersPermissionsConfig: {
    pageGroups,
    functions,
  }
}
