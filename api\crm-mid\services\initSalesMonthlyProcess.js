const moment = require('moment')
const _ = require('lodash')
const { getUserGroup } = require('../../crm/controllers/manager-group')

async function initSalesMonthlyProcess(time) {
    const monthlyArr = await strapi.query('sales-monthly-process').find({ time: moment(time).startOf('month').toDate(),_limit: 1000 })
    let managerGroups = await getUserGroup()
    const groups = managerGroups.filter(item => item.type === '直营小组');
    let managers = _.compact(_.uniq(_.flatten(groups.map(e => e.members)).map(e => e?.id)))
    managers = managers.filter(e => !monthlyArr.find(monthly => monthly.salesManager.id === e));
    for (const manager of managers) {
        let monthly = await strapi.query('sales-monthly-process').create({
            time: moment(time).startOf('month').toDate(),
            salesManager: manager,
            poster: 0,
            wxForward: 0,
            personalCreation: 0,
        })
        const salesDailyProcess = []

        for (let index = 0; index < moment(time).daysInMonth(); index++) {
            const curTime = moment(time).startOf('month').add(index, 'day').toDate();
            let daily = await strapi.query('sales-daily-process').create({
                time: curTime,
                salesManager: manager,
                poster: 0,
                wxForward: 0,
                personalCreation: 0,
                salesMonthlyProcess: monthly.id,
            })
            salesDailyProcess.push(daily.id)
        }
        await strapi.query('sales-monthly-process').update({ id: monthly.id, }, { salesDailyProcess: salesDailyProcess })
    }
    return;
}

module.exports = {
    initSalesMonthlyProcess,
}
