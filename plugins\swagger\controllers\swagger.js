'use strict';

const fs = require('fs');
const path = require('path');
const marked = require('marked');

module.exports = {
  /**
   * 渲染 Markdown 文档为 HTML
   */
  async renderMarkdown(ctx) {
    const { filename } = ctx.params;
    
    // 安全检查：只允许访问 docs 目录下的 .md 文件
    if (!filename.endsWith('.md') || filename.includes('..')) {
      return ctx.notFound('文档不存在');
    }
    
    const docPath = path.join(__dirname, '../docs', filename);
    
    if (!fs.existsSync(docPath)) {
      return ctx.notFound('文档不存在');
    }
    
    try {
      const markdown = fs.readFileSync(docPath, 'utf8');
      
      // 配置 marked
      marked.setOptions({
        breaks: true,
        gfm: true,
        tables: true,
        highlight: function(code, lang) {
          // 简单的代码高亮
          return `<pre class="language-${lang}"><code>${escapeHtml(code)}</code></pre>`;
        }
      });
      
      const html = marked(markdown);
      
      // 生成完整的 HTML 页面
      const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${path.basename(filename, '.md')} - API 文档</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1, h2, h3, h4, h5, h6 {
      color: #2c3e50;
      margin-top: 24px;
      margin-bottom: 16px;
    }
    h1 {
      border-bottom: 2px solid #e1e4e8;
      padding-bottom: 10px;
    }
    h2 {
      border-bottom: 1px solid #e1e4e8;
      padding-bottom: 8px;
    }
    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
      font-size: 0.9em;
    }
    pre {
      background: #f6f8fa;
      padding: 16px;
      border-radius: 6px;
      overflow-x: auto;
      line-height: 1.45;
    }
    pre code {
      background: none;
      padding: 0;
    }
    blockquote {
      border-left: 4px solid #dfe2e5;
      margin: 0;
      padding-left: 16px;
      color: #6a737d;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 16px 0;
    }
    table th, table td {
      border: 1px solid #dfe2e5;
      padding: 8px 12px;
      text-align: left;
    }
    table th {
      background: #f6f8fa;
      font-weight: 600;
    }
    table tr:nth-child(even) {
      background: #f6f8fa;
    }
    a {
      color: #0366d6;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    ul, ol {
      padding-left: 24px;
      margin: 16px 0;
    }
    li {
      margin: 4px 0;
    }
    .back-link {
      display: inline-block;
      margin-bottom: 20px;
      color: #0366d6;
      text-decoration: none;
      font-size: 14px;
    }
    .back-link:hover {
      text-decoration: underline;
    }
    .back-link::before {
      content: "← ";
    }
    .language-javascript, .language-js,
    .language-json, .language-yaml {
      position: relative;
    }
    .language-javascript::before, .language-js::before {
      content: "JavaScript";
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 8px;
      font-size: 12px;
      background: #f7df1e;
      color: #000;
      border-radius: 0 6px 0 6px;
    }
    .language-json::before {
      content: "JSON";
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 8px;
      font-size: 12px;
      background: #00897b;
      color: #fff;
      border-radius: 0 6px 0 6px;
    }
    .language-yaml::before {
      content: "YAML";
      position: absolute;
      top: 0;
      right: 0;
      padding: 2px 8px;
      font-size: 12px;
      background: #ff5252;
      color: #fff;
      border-radius: 0 6px 0 6px;
    }
  </style>
</head>
<body>
  <div class="container">
    <a href="/api-docs" class="back-link">返回 API 文档</a>
    ${html}
  </div>
</body>
</html>
      `;
      
      ctx.type = 'html';
      ctx.body = fullHtml;
      
    } catch (error) {
      strapi.log.error('渲染 Markdown 失败:', error);
      ctx.internalServerError('文档渲染失败');
    }
  }
};

function escapeHtml(unsafe) {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#039;");
}