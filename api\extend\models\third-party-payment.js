'use strict';

module.exports = {
    "collectionName": "thirdPartyPayment",
    "info": {
        "name": "ThirdPartyPayment",
        "label": "三方支付记录",
        "description": "三方支付记录"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true
    },
    "pluginOptions": {},
    "attributes": {
        "source": {
            "label": "来源",
            "type": "json",
            "editable": false,
        },
        "attach": {
            "label": "附加信息",
            "editable": false,
            "type": "json"
        },
        "amount": {
            "label": "金额",
            "editable": false,
            "format": "rmb",
            "type": "number"
        },
        "status": {
            "label": "状态",
            "type": "string",
            "editable": false,
            "default": "发起支付",
            "options": [
                {
                    "value": "发起支付",
                    "label": "发起支付"
                },
                {
                    "value": "支付成功",
                    "label": "支付成功"
                },
                {
                    "value": "支付失败",
                    "label": "支付失败"
                },
                {
                    "value": "交易关闭",
                    "label": "交易关闭"
                },
                {
                    "value": "取消支付",
                    "label": "取消支付"
                }
            ]
        },
        "payThrough": {
            "label": "支付方式",
            "editable": false,
            "type": "string",
            "default": "微信支付-二维码",
            "options": [
                {
                    "value": "微信支付-二维码",
                    "label": "微信支付-二维码"
                },
                {
                    "value": "未知",
                    "label": "未知"
                }
            ]
        },
        "tradeNo": {
            "label": "商户订单号",
            "editable": false,
            "visible": false,
            "type": "string"
        },
        "merchantId": {
            "label": "商户ID",
            "editable": false,
            "visible": false,
            "type": "string"
        },
        "payTime": {
            "label": "支付日期",
            "editable": false,
            "type": "datetime"
        },
        // "refundTime": {
        //     "label": "退款日期",
        //     "editable": false,
        //     "type": "datetime"
        // },
        "callbackUrl": {
            "label": "回调执行Url",
            "visible": false,
            "type": "string"
        },
        "callbackResult": {
            "label": "回调执行结构",
            "visible": false,
            "type": "json"
        },
        "remark": {
            "label": "备注",
            "editable": false,
            "type": "string",
        },
        "refundRecord": {
            "label": "关联退费记录",
            "via": "paymentRecord",
            "mainField": "refundTime",
            "collection": "third-party-refund",
            "editable": false
        }
    }
}