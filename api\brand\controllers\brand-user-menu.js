const { CurdRouter } = require('accel-utils')
const { cloneDeep } = require('lodash')
const curdRouter = new CurdRouter('brand-user-menu')

const menusMap = {}

const tmpMenu = {
  "ctime": "2024-12-05 18:59:59",
  "utime": "2025-02-07 12:09:00",
  "name": "单次报告订单",
  "icon": "",
  "no": "21",
  "role": [
    "d31b04e4-10cf-42d6-9e9d-444beb961325",
    "4daf970d-dd1b-467f-82da-ee42a1441d7f",
    "53f9bc46-42a4-4c4c-a1a1-09a2bfefcb6e"
  ],
  "priority": "8",
  "isHttp": true,
  "router": "https://mobile.yuejuanjia.com/pages/growth/reportHistory",
  "component": "",
  "params": "",
  "disabled": false,
  "cache": false,
  "parent": [
    {
      "_id": "675187affc6ceab68073fd3b",
      "wsid": "67510d79ba60f67ec34c2080",
      "rowid": "63f8a983-23cf-477a-a8d1-60bd93d5667f",
      "status": 1,
      "67510d79ba60f67ec34c2081": "伙伴信息",
      "unreads": false,
      "autoid": 1,
      "allowedit": false,
      "allowdelete": false,
      "controlpermissions": "",
      "discussunreads": false,
      "rq67510dddba60f67ec34c20b1": 7,
      "id": "63f8a983-23cf-477a-a8d1-60bd93d5667f",
      "name": "伙伴信息"
    }
  ],
  "_children": [],
  "brandName": "阅卷家",
  "brandApp": "yuejuanjia",
  "checked": true,
  "id": "5c07d65f-ae0c-4a17-afdb-b45d2fa4d216"
}

async function getMenuByRole (brandApp, role, force = false) {
  if (!role) {
    role = 'all'
  }
  const key = `${brandApp}-${role}`
  if (!force) {
    if (menusMap[key] && menusMap[key].data && Date.now() - menusMap[key].lastUpdate < 10 * 60 * 60 * 1000) {
      console.log(`${brandApp} menu use cache`)
      return menusMap[key].data
    }
  }
  let menuList = []
  if (role !== 'all') {
    menuList = await strapi.query(`brand-huoban-menu`).find({
      role_eq: role,
      _sort: 'priority',
      disabled_ne: true,
      brandApp
    })
  } else {
    menuList = await strapi.query(`brand-huoban-menu`).find({
      _sort: 'priority',
      disabled_ne: true,
      brandApp
    })
  }

  menusMap[key] = {
    data: menuList,
    lastUpdate: Date.now()
  }
  return menuList
}

async function setUserMenu (ctx) {
  const { userId, menu } = ctx.request.body
  const brandApp = ctx.request.query.brandApp || ctx.request.body.brandApp || 'yuejuanjia'
  if (!userId || !menu || !Array.isArray(menu) || menu.length === 0) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let oldData = await strapi.query('brand-user-menu').findOne({
    id: userId,
    brandApp
  })
  if (oldData) {
    await strapi.query('brand-user-menu').update({
      id: userId
    }, {
      menu
    })
  } else {
    await strapi.query('brand-user-menu').create({
      id: userId,
      brandApp,
      menu
    })
  }

  return ctx.wrapper.succ()
}

async function getUserMenu (ctx) {
  const { role, userId, all, force, brandApp = 'yuejuanjia' } = ctx.request.query
  if (!userId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  let menuList = await getMenuByRole(brandApp, role, force === 'true' || force === true)
  let userMenu = await strapi.query('brand-user-menu').findOne({
    id: userId,
    brandApp
  })


  let allMenuList = cloneDeep(menuList).map(menu => {
    let newMenu = {
      ...menu
    }
    if (!userMenu || !userMenu.menu || !userMenu.menu.length) {
      newMenu.checked = true
    } else {
      newMenu.checked = !!userMenu.menu.includes(menu.id);
    }
    return newMenu
  })

  if (userId === '6865e8db000192b2a55970b9') {
    allMenuList.push(tmpMenu)
  }

  if (all === '1' || all === 1) {
    allMenuList = allMenuList.map(menu => {
      if (!userMenu || !userMenu.menu || !userMenu.menu.length) {
        menu.checked = true
      } else {
        menu.checked = !!userMenu.menu.includes(menu.id);
      }
      return menu
    })
    return ctx.wrapper.succ(convertTree(allMenuList))
  } else {
    let allMenuTree = convertTree(allMenuList)
    return ctx.wrapper.succ(filterTreeCheck(allMenuTree, userMenu?.menu))
  }

}

function filterTreeCheck(tree, checkedMenuIds) {
  if (!tree || !tree.length)  return []
  if (!checkedMenuIds || !checkedMenuIds.length) return tree
  let allMenu = []
  for (const item of tree) {
    let newItem = null
    // 处理 item.children 为 undefined 的情况
    let children = filterTreeCheck(item.children || [], checkedMenuIds)
    if (checkedMenuIds.includes(item.id) || children.length > 0) {
      newItem = {
        ...item,
        children: children
      }
      // 将 newItem 添加到 allMenu 数组
      allMenu.push(newItem)
    }
  }
  return allMenu
}

function convertTree (menuList) {
  let menuTree = []
  menuList.forEach(menu => {
    if (!menu.parent || menu.parent.length === 0) {
      menuTree.push(menu)
    } else {
      let parentMenu = menuList.find(item => item.id === menu.parent[0].id)
      if (parentMenu) {
        if (!parentMenu.children) {
          parentMenu.children = []
        }
        parentMenu.children.push(menu)
      }
    }
  })
  return menuTree
}

module.exports = {
  ...curdRouter.createHandlers(),
  getUserMenu,
  setUserMenu
}
