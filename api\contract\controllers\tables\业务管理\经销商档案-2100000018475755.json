{"fieldsMap": {"商标签": "2200000457345862", "合作态度": "2200000487723971", "合作摘要": "2200000487729586", "盘点状态": "2200000500715721", "主营省份": "2200000176855409", "简称": "2200000476876746", "公司名称": "2200000174341500", "经销商编号": "2200000174512475", "25年认领单元": "2200000505001973", "25年负责人": "2200000500870824", "25年存量金额": "2200000500874943", "25年累计回款（扣除收入前支出）": "2200000507522078", "25年累计存量业绩": "2200000511243027", "存量业绩是否达标": "2200000511243028", "25年回捞商": "2200000509666875", "初始认领单元": "2200000471534158", "初始认领人": "2200000471534159", "初始竞拍价（万元）": "2200000471534160", "底价（万元）": "2200000457333585", "A资源认领单元": "2200000458340710", "A资源认领人": "2200000456997831", "A竞拍价（万元）": "2200000457333584", "B资源认领单元": "2200000471112205", "B资源认领人": "2200000471112206", "B竞拍价（万元）": "2200000471112207", "AB切换日期": "2200000471125475", "AB切换备注": "2200000471124142", "2024财年累计回款（元）": "2200000461385634", "24财年累计存量（元）": "2200000462649135", "24财年累计增量（元）": "2200000462649136", "24财年项目业绩（扣除收入前支出）": "2200000502024398", "成员": "2200000463496640", "授权学校数量": "2200000462243049", "企信架构": "2200000457736646", "企信标签": "2200000457736647", "合作状态": "2200000441982127", "账户状态": "2200000441979823", "账户冻结日期": "2200000441979824", "主营地市": "2200000197931877", "主营区县": "2200000443397181", "合作类型": "2200000443385462", "合作等级": "2200000443385463", "有效项目数": "2200000439343965", "项目单元重点对接业务类型": "2200000447601364", "备注": "2200000476863858", "商务资源能力": "2200000197833314", "SaaS开拓能力": "2200000197833625", "SaaS续购能力": "2200000197833626", "错题本开拓能力": "2200000197833627", "错题本复购能力": "2200000197833628", "S码": "2200000464703002", "M码": "2200000464703003", "L码": "2200000464703004", "XL": "2200000464703005", "2XL": "2200000464703006", "3XL": "2200000464703007", "4XL": "2200000464703008", "5XL": "2200000464703009", "收件人姓名": "2200000464706262", "收件人电话": "2200000464706263", "收件省份": "2200000464706264", "收件地市": "2200000464706265", "详细收件地址": "2200000464706266", "联系人-身份-手机号": "2200000443400106", "公司主营业务": "2200000443385466", "团队人数、考试服务能力、C端运营能力": "2200000443385467", "商务客情与资源情况": "2200000443385468", "进群业务人员（多对一经销商专属服务群）": "2200000474039081", "经销商是否已进群": "2200000477170590", "经销商企信路径": "2200000476848627", "合作标签": "2200000197833653", "业务现状分析": "2200000174507043", "合作风险点": "2200000443385464", "近期业务规划": "2200000443385465", "下一步重点动作": "2200000443400161", "合作约定/承诺": "2200000443390327", "历史遗留问题/冲突争议": "2200000443390328", "2022年回款金额": "2200000443400431", "2022年回款+消耗": "2200000443400432", "账户余额": "2200000443400433", "单校存量数": "2200000443400435", "统考存量数": "2200000443400436", "联盟存量数": "2200000443400437", "2022错题本营收": "2200000443400466", "2023错题本营收": "2200000443400467", "2022会员营收": "2200000443400468", "2023会员营收": "2200000443400469", "主营省份-权限一组": "1135001109000000", "主营省份-权限二组": "1135001110000000", "主营省份-权限三组": "1135001111000000", "25年认领单元-经营单元": "1242001101000000", "25年负责人-员工姓名": "1239001101000000", "25年负责人-企信ID": "1239001115000000", "25年负责人-系统账号": "1239001102000000", "25年负责人-所属阿米巴单元": "1239001116000000", "A资源认领单元-经营单元": "1195001101000000", "A资源认领单元-单元负责人": "1195001114000000", "A资源认领单元-团队成员": "1195001115000000", "A资源认领人-员工姓名": "1189001101000000", "A资源认领人-企信ID": "1189001115000000", "A资源认领人-系统账号": "1189001102000000", "B资源认领单元-经营单元": "1217001101000000", "B资源认领单元-单元负责人": "1217001114000000", "B资源认领单元-团队成员": "1217001115000000", "B资源认领单元-归属联盟": "1217001153000000", "B资源认领单元-阿米巴联盟": "1217001153001101", "B资源认领人-员工姓名": "1218001101000000", "B资源认领人-企信ID": "1218001115000000", "B资源认领人-系统账号": "1218001102000000"}, "table_id": "2100000018475755", "name": "经销商档案", "alias": "", "space_id": "4000000003570865", "created_on": "2021-11-07 16:34:57", "fields": [{"field_id": "2200000457345862", "name": "商标签", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "项目商"}, {"id": "2", "name": "经销商"}, {"id": "3", "name": "项目商-24年新增"}, {"id": "4", "name": "经销商-24年新增"}, {"id": "5", "name": "24年渠道合作"}]}}, {"field_id": "2200000487723971", "name": "合作态度", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "有增量"}, {"id": "3", "name": "标准对接"}, {"id": "2", "name": "终止合作"}, {"id": "4", "name": "待转化"}, {"id": "5", "name": "合同收款"}, {"id": "6", "name": "项目在谈"}]}}, {"field_id": "2200000487729586", "name": "合作摘要", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000500715721", "name": "盘点状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已盘点"}, {"id": "2", "name": "未盘点"}, {"id": "3", "name": "其他"}]}}, {"field_id": "2200000176855409", "name": "主营省份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000476876746", "name": "简称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000174341500", "name": "公司名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000174512475", "name": "经销商编号", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000505001973", "name": "25年认领单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000500870824", "name": "25年负责人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000500874943", "name": "25年存量金额", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000507522078", "name": "25年累计回款（扣除收入前支出）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000511243027", "name": "25年累计存量业绩", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000511243028", "name": "存量业绩是否达标", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000509666875", "name": "25年回捞商", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}]}}, {"field_id": "2200000471534158", "name": "初始认领单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000471534159", "name": "初始认领人", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000471534160", "name": "初始竞拍价（万元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000457333585", "name": "底价（万元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000458340710", "name": "A资源认领单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000456997831", "name": "A资源认领人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000457333584", "name": "A竞拍价（万元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471112205", "name": "B资源认领单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000471112206", "name": "B资源认领人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000471112207", "name": "B竞拍价（万元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000471125475", "name": "AB切换日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000471124142", "name": "AB切换备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000461385634", "name": "2024财年累计回款（元）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000462649135", "name": "24财年累计存量（元）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000462649136", "name": "24财年累计增量（元）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000502024398", "name": "24财年项目业绩（扣除收入前支出）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000463496640", "name": "成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000462243049", "name": "授权学校数量", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000457736646", "name": "企信架构", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "有"}, {"id": "2", "name": "无"}]}}, {"field_id": "2200000457736647", "name": "企信标签", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "已设置"}, {"id": "2", "name": "未设置"}]}}, {"field_id": "2200000441982127", "name": "合作状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "未入库"}, {"id": "4", "name": "未签约"}, {"id": "5", "name": "正常合作"}, {"id": "3", "name": "终止合作"}, {"id": "6", "name": "违规未处理"}, {"id": "7", "name": "历史问题未处理"}]}}, {"field_id": "2200000441979823", "name": "账户状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "账户正常"}, {"id": "2", "name": "账户冻结"}, {"id": "3", "name": "无账户"}]}}, {"field_id": "2200000441979824", "name": "账户冻结日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000197931877", "name": "主营地市", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000018524705", "space_id": "4000000003570865"}}, {"field_id": "2200000443397181", "name": "主营区县", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000018524706", "space_id": "4000000003570865"}}, {"field_id": "2200000443385462", "name": "合作类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "请根据经销商政策的经营权约定来填写", "config": {"is_multi": 0, "table_id": "2100000053142466", "space_id": "4000000003570865"}}, {"field_id": "2200000443385463", "name": "合作等级", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "核心商"}, {"id": "2", "name": "小商"}, {"id": "3", "name": "潜力商"}, {"id": "4", "name": "不合作-无业务"}]}}, {"field_id": "2200000439343965", "name": "有效项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000447601364", "name": "项目单元重点对接业务类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "1", "name": "信息化"}, {"id": "2", "name": "360会员"}, {"id": "3", "name": "SaaS打包"}, {"id": "4", "name": "区域独家"}, {"id": "5", "name": "大联考"}]}}, {"field_id": "2200000476863858", "name": "备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000197833314", "name": "商务资源能力", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "综合型商（多种产品）"}, {"id": "2", "name": "SaaS区县商"}, {"id": "3", "name": "单品合作商"}]}}, {"field_id": "2200000197833625", "name": "SaaS开拓能力", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021705680", "space_id": "4000000003570865"}}, {"field_id": "2200000197833626", "name": "SaaS续购能力", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021705680", "space_id": "4000000003570865"}}, {"field_id": "2200000197833627", "name": "错题本开拓能力", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021705680", "space_id": "4000000003570865"}}, {"field_id": "2200000197833628", "name": "错题本复购能力", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021705680", "space_id": "4000000003570865"}}, {"field_id": "2200000464703002", "name": "S码", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703003", "name": "M码", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703004", "name": "L码", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703005", "name": "XL", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703006", "name": "2XL", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703007", "name": "3XL", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703008", "name": "4XL", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464703009", "name": "5XL", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000464706262", "name": "收件人姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000464706263", "name": "收件人电话", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000464706264", "name": "收件省份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000464706265", "name": "收件地市", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524705", "space_id": "4000000003570865"}}, {"field_id": "2200000464706266", "name": "详细收件地址", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443400106", "name": "联系人-身份-手机号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443385466", "name": "公司主营业务", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "描述公司实际经营业务，如：建筑工程行业、打印机等硬件设备、教辅试卷等", "config": {}}, {"field_id": "2200000443385467", "name": "团队人数、考试服务能力、C端运营能力", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443385468", "name": "商务客情与资源情况", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000474039081", "name": "进群业务人员（多对一经销商专属服务群）", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000477170590", "name": "经销商是否已进群", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}, {"id": "3", "name": "未拉群"}]}}, {"field_id": "2200000476848627", "name": "经销商企信路径", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000197833653", "name": "合作标签", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "1", "name": "积极合作"}, {"id": "5", "name": "消极合作"}, {"id": "2", "name": "业务暂停"}, {"id": "3", "name": "要求解约"}, {"id": "6", "name": "错题本大户"}, {"id": "7", "name": "能干会员"}, {"id": "8", "name": "联考多"}, {"id": "9", "name": "教育局关系深"}]}}, {"field_id": "2200000174507043", "name": "业务现状分析", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443385464", "name": "合作风险点", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443385465", "name": "近期业务规划", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443400161", "name": "下一步重点动作", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443390327", "name": "合作约定/承诺", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443390328", "name": "历史遗留问题/冲突争议", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000443400431", "name": "2022年回款金额", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400432", "name": "2022年回款+消耗", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400433", "name": "账户余额", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "货款暂存+待消耗余额", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400435", "name": "单校存量数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400436", "name": "统考存量数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400437", "name": "联盟存量数", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400466", "name": "2022错题本营收", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400467", "name": "2023错题本营收", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400468", "name": "2022会员营收", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443400469", "name": "2023会员营收", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1135001109000000", "name": "权限一组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000176855409}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1135001110000000", "name": "权限二组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000176855409}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1135001111000000", "name": "权限三组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000176855409}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1242001101000000", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000505001973}, "required": false, "description": "", "config": {}}, {"field_id": "1239001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000500870824}, "required": true, "description": "", "config": {}}, {"field_id": "1239001115000000", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000500870824}, "required": true, "description": "", "config": {}}, {"field_id": "1239001102000000", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000500870824}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1239001116000000", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000500870824}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1195001101000000", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000458340710}, "required": false, "description": "", "config": {}}, {"field_id": "1195001114000000", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000458340710}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1195001115000000", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000458340710}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1189001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000456997831}, "required": true, "description": "", "config": {}}, {"field_id": "1189001115000000", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000456997831}, "required": true, "description": "", "config": {}}, {"field_id": "1189001102000000", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000456997831}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1217001101000000", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471112205}, "required": false, "description": "", "config": {}}, {"field_id": "1217001114000000", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000471112205}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1217001115000000", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000471112205}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1217001153000000", "name": "归属联盟", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000471112205}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056608623", "space_id": "4000000003570865"}}, {"field_id": "1217001153001101", "name": "阿米巴联盟", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471112205}, "required": false, "description": "", "config": {}}, {"field_id": "1218001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471112206}, "required": true, "description": "", "config": {}}, {"field_id": "1218001115000000", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471112206}, "required": true, "description": "", "config": {}}, {"field_id": "1218001102000000", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000471112206}, "required": false, "description": "", "config": {"is_multi": 0}}]}