'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const { CurdRouter } = require('accel-utils')
const {getPermissions} = require("./UsersPermissions");

const _ = require("lodash");
const curdRouter = new CurdRouter('group', { pluginName: 'users-permissions' })

async function checkAndDeletePermissions(id, body) {
  // 先查出update之前的完整的group的permissions
  const oldGroup = await strapi.query('group', 'users-permissions').findOne({id})
  const oldPermissions = oldGroup.apiPermissions

  // 比较oldPermissions 与 body中的 apiPermissions相比有什么差异，oldPermissions如果少了，不用管
  // 如果多了，记录差异的记
  // 获取新旧权限列表
  const newPermissions = body.apiPermissions || []

  // 使用 lodash difference 找出在旧功能中存在但新功能中不存在的权限(被移除的权限)
  return _.differenceWith(oldPermissions, newPermissions, _.isEqual);
}

module.exports = {
  ...curdRouter.createHandlers(),
  async update (ctx) {
    const {
      params: { id },
      request: { body }
    } = ctx


    const removedPermissions = await checkAndDeletePermissions(id, body);


    const userPermissions = await strapi.query('group', 'users-permissions').update({ id }, body)
    // 更新功能模块关联的角色的接口权限
    for (let role of userPermissions.roles) {
      // 将被移除的权限设置为禁用状态
      if (removedPermissions && removedPermissions.length > 0) {
        for (const permission of removedPermissions) {
          await strapi.query('permission', 'users-permissions').update(
            {role: role.id, type: permission.type, controller: permission.controller, action: permission.action},
            {enabled: false}
          )
        }
      }

      await strapi.plugins['users-permissions'].services.userspermissions.updateRole(
        role.id,
        {
          modules: role.modules,
          noRequired: false
        }
      )

    }
    return userPermissions
  },
}