# Swagger 扩展配置文件
# 用于定义无法修改源代码的模块（如 plugins）的 API 文档

users-permissions:
  tags:
    - name: 用户认证
      description: 用户注册、登录等认证相关接口
    - name: 用户管理
      description: 用户信息管理相关接口

  paths:
    # /uc/开头的认证接口
    /uc/loginByYxWeCom:
      post:
        tags: [用户认证]
        summary: 企业微信登录
        description: 使用企业微信账号登录
        security: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                required: [code]
                properties:
                  code:
                    type: string
                    description: 企业微信授权码
        responses:
          200:
            description: 登录成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    jwt:
                      type: string
                    user:
                      type: object
          401:
            description: 授权失败

    /uc/loginByTokenKey:
      post:
        tags: [用户认证]
        summary: 令牌密钥登录
        description: 使用令牌密钥进行登录
        security: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                required: [tokenKey]
                properties:
                  tokenKey:
                    type: string
                    description: 令牌密钥
        responses:
          200:
            description: 登录成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    jwt:
                      type: string
                    user:
                      type: object
          401:
            description: 令牌无效

    /uc/register:
      post:
        tags: [用户认证]
        summary: 用户注册（UC）
        description: 注册新用户账号
        security: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                required: [username, email, password]
                properties:
                  username:
                    type: string
                    description: 用户名
                  email:
                    type: string
                    format: email
                    description: 邮箱地址
                  password:
                    type: string
                    format: password
                    description: 密码
        responses:
          200:
            description: 注册成功
          400:
            description: 参数错误或注册失败

    /uc/loginByAccount:
      post:
        tags: [用户认证]
        summary: 账号密码登录
        description: 使用账号密码登录
        security: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                required: [account, password, appId]
                properties:
                  account:
                    type: string
                    description: 账号（邮箱/手机号/用户名）
                  password:
                    type: string
                    format: password
                    description: 密码
                  appId:
                    type: string
                    format: password
                    description: 应用ID
        responses:
          200:
            description: 登录成功

    /uc/superQuickLogin:
      post:
        tags: [用户认证]
        summary: 超级快速登录
        description: 管理员使用的快速登录接口
        security: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                required: [secretKey, userId]
                properties:
                  secretKey:
                    type: string
                    description: 管理员密钥
                  userId:
                    type: string
                    description: 要登录的用户ID
        responses:
          200:
            description: 登录成功
            content:
              application/json:
                schema:
                  type: object
                  properties:
                    jwt:
                      type: string
                    user:
                      type: object
          401:
            description: 密钥无效或权限不足

    # 用户管理相关接口
    /users/me:
      get:
        tags: [用户管理]
        summary: 获取当前用户信息
        description: 获取当前登录用户的详细信息
        security:
          - bearerAuth: []
        responses:
          200:
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/User'
          401:
            description: 未登录

      put:
        tags: [用户管理]
        summary: 更新当前用户信息
        description: 更新当前登录用户信息
        security:
          - bearerAuth: []
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  username:
                    type: string
                    description: 用户名
                  email:
                    type: string
                    format: email
                    description: 邮箱
                  password:
                    type: string
                    format: password
                    description: 新密码
        responses:
          200:
            description: 更新成功
          401:
            description: 未登录

# 文件上传插件的API文档
upload:
  tags:
    - name: 文件上传
      description: 文件上传和管理相关接口

  paths:
    /upload:
      post:
        tags: [文件上传]
        summary: 上传文件
        description: 上传单个或多个文件
        security:
          - bearerAuth: []
        requestBody:
          required: true
          content:
            multipart/form-data:
              schema:
                type: object
                properties:
                  files:
                    type: array
                    items:
                      type: string
                      format: binary
                    description: 要上传的文件
                  path:
                    type: string
                    description: 文件保存路径
                  refId:
                    type: string
                    description: 关联的实体ID
                  ref:
                    type: string
                    description: 关联的实体类型
                  source:
                    type: string
                    description: 上传来源
                  field:
                    type: string
                    description: 关联的字段名
        responses:
          200:
            description: 上传成功
            content:
              application/json:
                schema:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                      name:
                        type: string
                      hash:
                        type: string
                      ext:
                        type: string
                      mime:
                        type: string
                      size:
                        type: number
                      url:
                        type: string
                      provider:
                        type: string
          413:
            description: 文件太大
          500:
            description: 上传失败

    /upload/files:
      get:
        tags: [文件上传]
        summary: 获取文件列表
        description: 获取已上传的文件列表
        security:
          - bearerAuth: []
        parameters:
          - name: _limit
            in: query
            schema:
              type: integer
              default: 10
          - name: _start
            in: query
            schema:
              type: integer
              default: 0
          - name: _sort
            in: query
            schema:
              type: string
              example: created_at:DESC
        responses:
          200:
            description: 成功
            content:
              application/json:
                schema:
                  type: array
                  items:
                    $ref: '#/components/schemas/File'

    /upload/files/{id}:
      get:
        tags: [文件上传]
        summary: 获取文件信息
        description: 根据ID获取文件详细信息
        security:
          - bearerAuth: []
        parameters:
          - name: id
            in: path
            required: true
            schema:
              type: string
        responses:
          200:
            description: 成功
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/File'
          404:
            description: 文件不存在

      delete:
        tags: [文件上传]
        summary: 删除文件
        description: 删除指定文件
        security:
          - bearerAuth: []
        parameters:
          - name: id
            in: path
            required: true
            schema:
              type: string
        responses:
          200:
            description: 删除成功
          404:
            description: 文件不存在

# Schema 定义
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
        username:
          type: string
          description: 用户名
        email:
          type: string
          format: email
          description: 邮箱
        provider:
          type: string
          description: 认证提供商
        confirmed:
          type: boolean
          description: 邮箱是否已验证
        blocked:
          type: boolean
          description: 是否被禁用
        role:
          type: object
          properties:
            id:
              type: string
            name:
              type: string
            type:
              type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    File:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
          description: 文件名
        alternativeText:
          type: string
          description: 替代文本
        caption:
          type: string
          description: 说明文字
        width:
          type: number
          description: 宽度（图片）
        height:
          type: number
          description: 高度（图片）
        formats:
          type: object
          description: 图片格式
        hash:
          type: string
          description: 文件哈希
        ext:
          type: string
          description: 文件扩展名
        mime:
          type: string
          description: MIME类型
        size:
          type: number
          description: 文件大小（KB）
        url:
          type: string
          description: 文件URL
        previewUrl:
          type: string
          description: 预览URL
        provider:
          type: string
          description: 存储提供商
        provider_metadata:
          type: object
          description: 提供商元数据
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
