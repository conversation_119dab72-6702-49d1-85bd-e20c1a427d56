const { <PERSON>urd<PERSON>out<PERSON> } = require('accel-utils')

const curdRouter = new (class extends CurdRouter {

  _appendBaseFilter (ctx) {
    ctx.request.query.provider = 'yxWeCom'
  }

  async find (ctx) {
    this._appendBaseFilter(ctx)
    return await super.find(ctx)
  }

  async count (ctx) {
    this._appendBaseFilter(ctx)
    return await super.count(ctx)
  }

})('qx-user')

module.exports = {
  ...curdRouter.createHandlers(),
}
