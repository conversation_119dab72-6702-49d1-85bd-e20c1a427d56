{"fieldsMap": {"学校/教育局": "2200000485593768", "学校ID": "2200000485593775", "经销商": "2200000485593769", "授权产品": "2200000485593770", "授权类型": "2200000485593771", "授权开始日期": "2200000485593772", "授权结束日期": "2200000485593773", "CRMID": "2200000485674631", "业务类型": "2200000485674632", "项目管理": "2200000485593774", "订单号/合同编号": "2200000485674633", "学校/教育局-学校名称": "1112001102000000", "学校/教育局-学校ID": "1112001101000000"}, "table_id": "2100000059578666", "name": "授权记录", "alias": "", "space_id": "4000000003570865", "created_on": "2024-10-15 11:34:33", "fields": [{"field_id": "2200000485593768", "name": "学校/教育局", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000485593775", "name": "学校ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000485593769", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000485593770", "name": "授权产品", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "阅卷"}, {"id": "4", "name": "分析"}, {"id": "2", "name": "360会员"}, {"id": "3", "name": "错题本"}, {"id": "5", "name": "校本题库"}, {"id": "6", "name": "题库"}, {"id": "7", "name": "排考"}, {"id": "8", "name": "作业系统"}, {"id": "9", "name": "阅卷业支"}]}}, {"field_id": "2200000485593771", "name": "授权类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "开拓授权"}, {"id": "2", "name": "运营授权"}, {"id": "3", "name": "直营合作"}, {"id": "4", "name": "项目合作"}, {"id": "5", "name": "区县独家"}, {"id": "6", "name": "产品分销"}, {"id": "7", "name": "服务授权"}, {"id": "8", "name": "学校独家"}, {"id": "9", "name": "独家运营授权"}]}}, {"field_id": "2200000485593772", "name": "授权开始日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000485593773", "name": "授权结束日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000485674631", "name": "CRMID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000485674632", "name": "业务类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "订单交付"}, {"id": "2", "name": "合同履约"}, {"id": "3", "name": "开拓立项"}, {"id": "4", "name": "其他"}]}}, {"field_id": "2200000485593774", "name": "项目管理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000485674633", "name": "订单号/合同编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "1112001102000000", "name": "学校名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000485593768}, "required": true, "description": "", "config": {}}, {"field_id": "1112001101000000", "name": "学校ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000485593768}, "required": true, "description": "", "config": {}}]}