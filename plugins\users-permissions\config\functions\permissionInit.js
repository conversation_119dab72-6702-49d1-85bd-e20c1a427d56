const _ = require('lodash')
const { passwordBcrypt } = require('accel-utils')

const { mergePermissionConfigs } = require('../../utils/utils')
const usersPermissionsConfig = require('../permission').usersPermissionsConfig

async function getRoutes () {
  const routes = await strapi.plugins['users-permissions'].services.userspermissions.getRoutes()
  return Object.keys(routes).map(key => {
    return routes[key].map(e => {
      return {
        group: key,
        ...e,
      }
    })
  }).flat()
}

async function fillApiPermissionRoute (apiPermissions) {
  if (!apiPermissions) return apiPermissions
  const routes = await getRoutes()
  return apiPermissions.map(api => {
    const matchRoute = routes.find(route => {
      const [, controller, action] = route.handler.match(/^([^.]+)\.([^.]+)$/) || [null, null, null]
      return api.type === route.group
        && api.controller.toLocaleLowerCase() === (controller || '').toLocaleLowerCase()
        && api.action === action
    })
    if (!matchRoute) return api
    return {
      ...api,
      path: matchRoute.path,
      method: matchRoute.method
    }
  })
}

// 根据视图配置初始化默认视图
module.exports = async () => {
  const baseConfig = usersPermissionsConfig
  const pluginConfigs = []
  const appConfig = strapi.config.permission || {}
  for (let pluginsKey in strapi.plugins) {
    const pluginConfig = strapi.plugins[pluginsKey].config.usersPermissionsConfig
    if (!pluginConfig || pluginsKey === 'users-permissions') continue
    pluginConfigs.push(pluginConfig)
  }
  const { apps: customApps, branches, pageGroups, functions, roles } = mergePermissionConfigs([
    baseConfig, ...pluginConfigs, appConfig
  ])

  // 插件数据存储
  const pluginStore = strapi.store({
    type: 'plugin',
    name: 'users-permissions',
  })

  // 初始化页面
  await (async function () {
    console.log('Users-Permissions Init Page...')
    console.time('Users-Permissions Init Page')
    // 对比数据库存储判断配置数据是否更新
    const storePageGroups = await pluginStore.get({ key: 'pageGroups' })
    if (_.isEqual(storePageGroups, pageGroups)) {
      console.timeEnd('Users-Permissions Init Page')
      return
    }
    const pageService = strapi.query('page', 'users-permissions')
    const currentPages = await pageService.find({ _limit: 0 })
    const matchPages = []
    for (let group of pageGroups) {
      for (const page of group.pages) {
        const baseFields = {
          sId: page.sId,
        }
        const fullFields = {
          ...baseFields,
          parent: group.sId,
          isPreset: true,
          name: page.name || '功能',
          icon: page.icon || 'table_chart',
          meta: page.meta || {},
          ...page
        }
        let currentPage = await currentPages.find(e => e.sId === baseFields.sId)
        if (currentPage) {
          matchPages.push(currentPage)
          const oldPage = _.pick(currentPage, ['sId', 'parent', 'name', 'icon', 'meta', 'isPreset'])
          const newPage = {
            ...fullFields,
            // meta 字段合并
            meta: {
              ...(oldPage.meta || {}),
              ...fullFields.meta,
            },
            // 非 meta parent name icon 字段以当前配置为准
            ..._.omit(oldPage, ['meta', 'parent', 'name', 'icon',]),
            // parent 字段以项目配置
            parent: fullFields.parent,
            isPreset: fullFields.isPreset
          }
          if (!_.eq(newPage, oldPage)) {
            await pageService.update(baseFields, newPage)
          }
        } else {
          await pageService.create(fullFields)
        }
      }
    }
    // 删除无效视图
    const removePages = currentPages.filter(e => !matchPages.find(j => j.sId === e.sId)).filter(e => e.isPreset)
    const deleteQuery = { isPreset_nin: [true, false] }
    const expireDeleteResult = await strapi.query('page', 'users-permissions').delete(deleteQuery)
    expireDeleteResult?.length > 0 && console.log('Users-Permissions Remove Expire Pages:', expireDeleteResult)
    if (removePages.length > 0) {
      const invalidDeleteResult = await strapi.query('page', 'users-permissions').delete({
        id_in: removePages.map(e => e.id),
        isPreset: true
      })
      console.log('Users-Permissions Remove Invalid Pages:', invalidDeleteResult)
    }
    // 更新数据库配置
    await pluginStore.set({ key: 'pageGroups', value: pageGroups })
    console.timeEnd('Users-Permissions Init Page')
  })()

  // 初始化菜单
  await (async function () {
    console.time('Users-Permissions Init Menu')
    await pluginStore.set({ key: 'menus', value: pageGroups })
    console.timeEnd('Users-Permissions Init Menu')
  })()

  // 初始化默认应用
  await (async function () {
    console.log('Users-Permissions Init Default App...')
    console.time('Users-Permissions Init Default App')
    const appQuery = strapi.query('app', 'users-permissions')
    // 默认创建管理后台
    const defaultApp = await appQuery.findOne({ id: '61b062c033f23d6a74933ec8' })
    if (!defaultApp) {
      await appQuery.create({
        id: '61b062c033f23d6a74933ec8',
        type: 'local',
        name: '管理后台',
        tokenSecret: 'vvb7YXTZhYZuWr2pvu75',
        isPreset: true
      })
    }
    for (let app of customApps) {
      const appFilter = {
        ...(app.id ? { id: app.id } : {}),
        ...(app.sId ? { sId: app.sId } : {}),
        // isPreset: true,
      }
      const existApp = await appQuery.findOne(appFilter, [])
      if (app.modules) {
        app.modules = await strapi.query('group', 'users-permissions').find({ sId_in: app.modules })
      }
      if (!existApp) {
        await appQuery.create({
          ...app,
          isPreset: true,
        })
      } else {
        await appQuery.update({
          id: existApp.id
        }, {
          ...app,
          isPreset: true,
        })
      }
    }
    console.timeEnd('Users-Permissions Init Default App')
  })()

  // 初始化租户
  await (async function () {
    console.time('Users-Permissions Init Branch')
    if (branches) {
      const branch = await strapi.query('branch', 'users-permissions')
        .findOne({ type: 'default' })
      if (!branch) {
        await strapi.query('branch', 'users-permissions')
          .create({ name: '默认租户', type: 'default' })
      }
    }
    console.timeEnd('Users-Permissions Init Branch')
  })()

  // 初始化功能
  await (async function () {
    console.log('Users-Permissions Init function...')
    console.time('Users-Permissions Init Function')
    // 对比数据库存储判断配置数据是否更新
    const storeFunctions = await pluginStore.get({ key: 'functions' })
    if (_.isEqual(storeFunctions, functions)) {
      console.timeEnd('Users-Permissions Init Function')
      return
    }
    const groupService = strapi.query('group', 'users-permissions')
    const pageService = strapi.query('page', 'users-permissions')
    for (const functionItem of functions) {
      const group = await groupService.findOne({ sId: functionItem.sId })
      const baseFields = {
        sId: functionItem.sId,
      }
      const pages = functionItem.pages ? await pageService.find({ sId_in: functionItem.pages }) : []
      const fullFields = {
        ...baseFields,
        isPreset: true,
        name: functionItem.name,
        apiPermissions: await fillApiPermissionRoute(functionItem.apiPermissions.map(e => {
          return {
            ...e,
            enabled: true,
            policy: '',
          }
        })),
        pages: pages.map(e => e.id)
      }
      if (group) {
        const oldPermissions = group.apiPermissions
        const newPermissions = _.differenceWith(fullFields.apiPermissions, group.apiPermissions, (a, b) => {
          return a.type === b.type
            && a.controller === b.controller
            && a.action === b.action
        })
        await groupService.update({ id: group.id }, {
          isPreset: true,
          pages: fullFields.pages
        })
        if (newPermissions.length > 0) {
          await groupService.update({ id: group.id }, {
            apiPermissions: [
              ...oldPermissions,
              ...newPermissions
            ],
          })
        }
      } else {
        await groupService.create(fullFields)
      }
    }
    console.timeEnd('Users-Permissions Init Function')
  })()

  // 初始化基础角色：超级管理员、普通用户、未登录角色
  await (async function () {
    // 超级管理员角色
    console.log('Users-Permissions Init Role...')
    console.time('Users-Permissions Init Role')
    // 对比数据库存储判断配置数据是否更新
    const storeRoles = await pluginStore.get({ key: 'roles' })
    const storeFunctions = await pluginStore.get({ key: 'functions' })
    if (_.isEqual(storeRoles, roles) && _.isEqual(storeFunctions, functions)) {
      console.timeEnd('Users-Permissions Init Role')
      return
    }
    const permissionService = strapi.plugins['users-permissions'].services.userspermissions
    let adminRole = await strapi.query('role', 'users-permissions').findOne({ type: 'SuperAdmin' })
    const allModules = await strapi.query('group', 'users-permissions').find({})
    if (adminRole) {
      await permissionService.updatePermissions(adminRole.id)
      await permissionService.updateRole(adminRole.id, {
        isPreset: true,
        modules: allModules.map(e => e.id)
      })
    } else {
      await permissionService.createRole({
        isPreset: true,
        name: '超级管理员',
        type: 'SuperAdmin',
        description: '系统默认超级管理员角色，会自动配置所有权限',
        modules: allModules.map(e => e.id)
      })
    }
    // 普通用户
    const authenticatedRole = await strapi.query('role', 'users-permissions')
      .findOne({ type: 'authenticated' })
    const authenticatedModules = allModules.filter(e => e.sId === 'AuthenticatedFunction').map(e => e.id)
    if (authenticatedRole) {
      const oldModules = authenticatedRole.modules.map(e => e.id)
      const newModules = _.differenceWith(authenticatedModules, oldModules)
      await permissionService.updatePermissions(authenticatedRole.id)
      await permissionService.updateRole(authenticatedRole.id, {
        isPreset: true,
        modules: _.uniq([
          ...oldModules,
          ...newModules,
        ])
      })
    } else {
      await permissionService.createRole({
        isPreset: true,
        name: '普通用户',
        type: 'authenticated',
        description: '系统默认的普通用户角色，拥有基础功能权限',
        modules: authenticatedModules
      })
    }
    // 未登录
    const publicRole = await strapi.query('role', 'users-permissions')
      .findOne({ type: 'public' })
    const publicModules = allModules.filter(e => e.sId === 'PublicFunction').map(e => e.id)
    if (publicRole) {
      const oldModules = publicRole.modules.map(e => e.id)
      const newModules = _.differenceWith(publicModules, oldModules)
      await permissionService.updatePermissions(publicRole.id)
      await permissionService.updateRole(publicRole.id, {
        isPreset: true,
        modules: _.uniq([
          ...oldModules,
          ...newModules,
        ])
      })
    } else {
      await permissionService.createRole({
        isPreset: true,
        name: '未登录',
        type: 'public',
        description: '公共功能，未登录状态下也可以使用，例如注册、登录功能',
        modules: publicModules
      })
    }
    // 初始化自定义角色
    for (let role of roles) {
      const updateRoleLog = `Users-Permissions Init Role Update [${role.type}] [${role.name}] `
      console.time(updateRoleLog)
      const oldRole = await strapi.query('role', 'users-permissions').findOne({ type: role.type })
      const modules = allModules.filter(e => role.modules.find(sId => sId === e.sId))
      if (oldRole) {
        const oldModules = oldRole.modules.map(e => e.id)
        const newModules = _.differenceWith(modules, oldModules).map(e => e.id)
        await permissionService.updatePermissions(oldRole.id)
        await permissionService.updateRole(oldRole.id, {
          isPreset: true,
          ...role,
          modules: _.uniq([
            ...oldModules,
            ...newModules,
          ])
        })
      } else {
        await permissionService.createRole({
          isPreset: true,
          name: role.name,
          type: role.type,
          description: role.description,
          ...role,
          modules: modules.map(e => e.id)
        })
      }
      console.timeEnd(updateRoleLog)
    }
    // 更新数据库配置数据
    await pluginStore.set({ key: 'roles', value: roles })
    await pluginStore.set({ key: 'functions', value: functions })
    console.timeEnd('Users-Permissions Init Role')
  })()

  // 初始化超管账号
  await (async function () {
    console.log('Users-Permissions Init Admin Account...')
    console.time('Users-Permissions Init Admin Account')
    // 仅当已更新角色的情况下初始化超管账号
    const adminRole = await strapi.query('role', 'users-permissions').findOne({ type: 'SuperAdmin' })
    const userPlugin = strapi.plugins['users-permissions']
    const adminUserConfig = strapi.config.get('plugins.usersPermissions.adminUser')
    const userInfo = {
      password: adminUserConfig?.password || 'wlyAdmin123!@#',
      username: 'SuperAdmin',
      email: adminUserConfig?.email || '<EMAIL>',
    }
    const encryptedPassword = await passwordBcrypt(userInfo.password)
    let adminUser = await userPlugin.services['user'].findOne({ email: userInfo.email })
    if (!adminUser) {
      adminUser = await userPlugin.services['user'].create({
        username: userInfo.username,
        email: userInfo.email,
        password: encryptedPassword,
        confirmed: true,
        provider: 'local',
      })
    }
    const oldRoles = (adminUser.roles || []).map(e => e.id)
    const newRoles = _.difference([adminRole.id], oldRoles)
    await userPlugin.services['user'].update({
      id: adminUser.id
    }, {
      role: adminRole.id,
      roles: [
        ...oldRoles,
        ...newRoles,
      ]
    })
    console.timeEnd('Users-Permissions Init Admin Account')
  })()

  // 初始化全局高级配置
  await (async function () {
    const storeSettings = await pluginStore.get({ key: 'advanced' })
    if (Object.keys(storeSettings).length === 0) {
      console.time('Users-Permissions Init Settings')
      await pluginStore.set({ key: 'advanced', value: usersPermissionsConfig.settings })
      console.timeEnd('Users-Permissions Init Settings')
    }
  })()
}
