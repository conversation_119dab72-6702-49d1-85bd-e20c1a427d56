const { customerTypes } = require('../utils/crmTypes')
module.exports = {
  collectionName: 'customer-service',
  duplicateCollection: true,
  info: {
    name: 'CustomerService',
    label: '客户服务',
    description: '客户服务'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    name: {
      label: '名称',
      editable: false,
      type: 'string',
      size: 3,
    },
    customerId: {
      label: '客户ID',
      editable: false,
      visible: false,
      unique: true,
      type: 'string'
    },
    schoolId: {
      label: '学校 ID',
      editable: false,
      type: 'integer',
      size: 3,
    },
    yjTag: {
      label: '阅卷签约标签',
      type: 'string',
      editable: false,
      size: 3,
      options: [
        {
          "label": "体验校",
          "value": "体验校"
        },
        {
          "label": "付费校",
          "value": "付费校"
        },
        {
          "label": "应续校",
          "value": "应续校"
        },
        {
          "label": "流失校",
          "value": "流失校"
        }
      ]
    },
    // @Customer 字段
    deleted: {
      label: '是否删除',
      type: 'boolean',
      editable: false,
      visible: false,
    },
    crm_type: {
      label: '类型',
      'type': 'string',
      editable: false,
      options: customerTypes,
      size: 3,
    },
    yjVersion: {
      label: '阅卷版本',
      type: 'string',
      editable: false,
      size: 4,
    },
    yjStartTime: {
      label: '阅卷开始时间',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 4,
    },
    yjEndTime: {
      label: '阅卷截止时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      editable: false,
      size: 4,
    },

    // 站点类型
    siteType: {
      label: '站点类型',
      type: 'string',
      default: 'normal',
      options: [
        { label: '普通站点', value: 'normal' },
        { label: '好分数精准教学', value: 'hfs' }
      ],
      size: 4,
    },
    siteStartTime: {
      label: '站点开始时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 4,
    },
    siteEndTime: {
      label: '站点到期时间',
      type: 'datetime',
      format: 'YYYY-MM-DD',
      size: 4,
    },
    // 站点主页
    siteHome: {
      label: '站点主页',
      type: 'string',
      visible: false,
    },

    // 阅卷授权
    authAgent: {
      label: '授权经销商',
      model: 'boss-agent',
      mainField: 'company',
      editable: false,
      size: 2,
    },
    authAgentId: {
      label: '授权经销商id',
      type: 'string',
      editable: false,
      size: 2,
    },
    authAgentNo: {
      label: '授权经销商no',
      type: 'string',
      editable: false,
      size: 2,
    },
    authAgentName: {
      label: '授权经销商名称',
      type: 'string',
      editable: false,
      size: 2,
    },

    authType: {
      label: '授权类型',
      type: 'string',
      editable: false,
      size: 2,
    },
    authEndTime: {
      label: '授权到期日期',
      type: 'datetime',
      editable: false,
      format: 'YYYY-MM-DD',
      size: 2,
    },
    authRemark: {
      label: '授权备注',
      type: 'string',
      editable: false,
      size: 2,
    },
  }
}
