'use strict'

const { CurdRouter } = require('accel-utils')
const { getModelService } = require('accel-utils/collection-utils')

class CollectionCurdRouter extends CurdRouter {
  constructor (name, config = {}) {
    super(name, config)
  }

  _getService (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    const { model } = this._parseCtx(ctx)
    return getModelService(model)
  }

  _getModel (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    const { model } = this._parseCtx(ctx)
    return strapi.getModel(model)
  }
}

const collectionCurdRouter = new CollectionCurdRouter()
const handlers = collectionCurdRouter.createHandlers()

module.exports = {
  find: handlers.find,
  count: handlers.count,
  findOne: handlers.findOne,
  create: handlers.create,
  update: handlers.update,
  delete: handlers.delete,
  export: handlers.export,
}
