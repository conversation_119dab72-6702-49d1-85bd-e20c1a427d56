'use strict'

/**
 * Module dependencies
 */

// Public node modules.
const uaParser = require('ua-parser-js')
const axios = require('axios')
const { isRegExp, isString } = require('lodash')

module.exports = strapi => {
  return {
    beforeInitialize () {
    },

    initialize () {
      // 中间件注册
      const config = {
        // Rule [ Method, UrlMatch ]
        ignoreRules: [],
        notIgnoreRules: [],
        notifyChannel: '',
        feishuWebhookUrl: '',
        ...(strapi.config.get('plugins.log') || {})
      }
      const defaultIgnoreRules = [
        ['*', /^\/system-request-logs/],
        ['*', /^\/system-error-logs/]
      ]

      strapi.app.use(async function (ctx, next) {
        let processError = null
        const requestAt = new Date()
        const ip = ctx.request.headers['x-real-ip'] ||
          ctx.request.headers['x-forwarded-for'] ||
          ctx.request.ip
        const userAgent = ctx.request.headers['user-agent']
        const uaInfo = uaParser(userAgent)
        // 用户请求分析数据附加到 ctx
        ctx._ip = ip
        ctx._uaInfo = uaInfo

        const requestData = {
          ip: ip,
          userAgent: userAgent,
          uaInfo: ctx._uaInfo,
          originalUrl: ctx.originalUrl,
          method: ctx.method,
          requestBody: ctx.request.body,
          requestAt,
          host: ctx.header.host,
          referer: ctx.header.referer
        }
        // 遇到腾讯云安全测试请求直接抛出 400 Bad Request
        if (ctx._uaInfo.ua && /Tencent Security Team/.test(ctx._uaInfo.ua)) {
          throw strapi.errors.badRequest(`Parameter Error`)
        }
        try {
          await next()
        } catch (err) {
          console.log(err)
          processError = err
          throw err
        } finally {
          // 异步记录请求日志与错误日志
          setTimeout(async () => {
            const user = ctx.state && ctx.state.user
            const responseAt = new Date()
            const responseTime = responseAt - requestAt
            if (user) {
              requestData.userId = user._id
              requestData.branchId = user?.pBranch?.id
              requestData.userName = user.name
            }
            requestData.responseAt = responseAt
            requestData.responseTime = responseTime
            requestData.requestBody = JSON.stringify(ctx.request.body)

            let isIgnore = false
            const ignoreRules = [...defaultIgnoreRules, ...config.ignoreRules]
            for (let ignoreRule of ignoreRules) {
              const [method, urlMatch] = ignoreRule
              const ignoreMethod = method === '*' || method.toUpperCase() === ctx.method
              let ignoreUrl = false
              if (urlMatch === '*') {
                ignoreUrl = true
              } else if (isRegExp(urlMatch) && urlMatch.test(ctx.originalUrl)) {
                ignoreUrl = true
              } else if (isString(urlMatch) && ctx.originalUrl.includes(urlMatch)) {
                ignoreUrl = true
              }
              if (ignoreMethod && ignoreUrl) isIgnore = true
            }
            const notIgnoreRules = [...config.notIgnoreRules]
            for (let notIgnoreRule of notIgnoreRules) {
              const [method, urlMatch] = notIgnoreRule
              const notIgnoreMethod = method === '*' || method.toUpperCase() === ctx.method
              let notIgnoreUrl = false
              if (urlMatch === '*') {
                notIgnoreUrl = true
              } else if (isRegExp(urlMatch) && urlMatch.test(ctx.originalUrl)) {
                notIgnoreUrl = true
              } else if (isString(urlMatch) && ctx.originalUrl.includes(urlMatch)) {
                notIgnoreUrl = true
              }
              if (notIgnoreMethod && notIgnoreUrl) isIgnore = false
            }

            if (!isIgnore) {
              strapi.query('system-request-log', 'log').create(requestData)
            }
            if (processError && processError?.message !== 'ValidationError') {
              const uaDeviceStr = Object.values(ctx._uaInfo.device || {}).filter(e => e).join(' ')
              const uaOsStr = Object.values(ctx._uaInfo.os || {}).filter(e => e).join(' | ')
              const uaBrowserStr = Object.values(ctx._uaInfo.browser || {}).filter(e => e).join(' | ')
              const uaEngineStr = Object.values(ctx._uaInfo.engine || {}).filter(e => e).join(' | ')
              if (ctx.response.type === 'application/json') {
                requestData.responseBody = ctx.response.body && (ctx.response.body.code || ctx.response.body.statusCode) ? ctx.response.body : null
              }
              strapi.query('system-error-log', 'log').create({
                name: processError.name,
                message: processError.message,
                stack: processError.stack,
                ...requestData,
              })
              try {
                if (config && config.notifyChannel === 'feishu') {
                  const webhookUrl = config.feishuWebhookUrl
                  await axios.post(webhookUrl, {
                      msg_type: 'interactive',
                      card: {
                        config: {
                          wide_screen_mode: true,
                          enable_forward: true
                        },
                        header: {
                          template: 'red',
                          title: {
                            content: `💥 接口报错 [${ctx._ip}] [${ctx.origin}]`,
                            tag: 'plain_text'
                          }
                        },
                        elements: [
                          {
                            tag: 'note',
                            elements: [
                              {
                                'tag': 'lark_md',
                                'content': `**[Request]**`
                              },
                              {
                                'tag': 'plain_text',
                                'content': `${ctx.method} ${ctx.originalUrl}`
                              }
                            ]
                          },

                          // 请求体信息
                          {
                            tag: 'note',
                            elements: [
                              {
                                'tag': 'lark_md',
                                'content': `**[RequestBody]**`
                              },
                              {
                                'tag': 'plain_text',
                                'content': `${JSON.stringify(ctx.request.body, null, 2)}`
                              }
                            ]
                          },
                          {
                            tag: 'note',
                            elements: [
                              {
                                'tag': 'lark_md',
                                'content': `**[ErrorMessage]**`
                              },
                              {
                                'tag': 'plain_text',
                                'content': `${processError.message}`
                              }
                            ]
                          },
                          // 用户信息
                          {
                            tag: 'note',
                            elements: [
                              {
                                'tag': 'lark_md',
                                'content': `**[User]**`
                              },
                              {
                                'tag': 'plain_text',
                                'content': (() => {
                                  if (user) return `🤦‍ ${user?.username} [${user?._id}]`
                                  return `👤 未登录`
                                })()
                              }
                            ]
                          },
                          // 换行符
                          { 'tag': 'hr' },
                          // UA 信息
                          ...(() => {
                            if (uaDeviceStr || uaOsStr || uaBrowserStr || uaEngineStr) {
                              return [
                                {
                                  'tag': 'div',
                                  'fields': [
                                    {
                                      'is_short': true,
                                      'text': {
                                        'content': `**👓 Browser: ** \n${uaBrowserStr || 'unknown'}`,
                                        'tag': 'lark_md'
                                      },
                                    },
                                    {
                                      'is_short': true,
                                      'text': {
                                        'content': `**👓 Engine: ** \n${uaEngineStr || 'unknown'}`,
                                        'tag': 'lark_md'
                                      },
                                    },
                                    {
                                      'is_short': true,
                                      'text': {
                                        'content': `**💻 OS: ** \n${uaOsStr || 'unknown'}`,
                                        'tag': 'lark_md'
                                      }
                                    },
                                    {
                                      'is_short': true,
                                      'text': {
                                        'content': `**📱 Device: ** \n${uaDeviceStr || 'unknown'}`,
                                        'tag': 'lark_md'
                                      }
                                    },
                                  ],
                                },
                              ]
                            }
                            return [
                              {
                                'tag': 'div',
                                'fields': [
                                  {
                                    'is_short': false,
                                    'text': {
                                      'content': `📜 UAInfo:  ${ctx._uaInfo.ua}`,
                                      'tag': 'plain_text'
                                    }
                                  },
                                  // 空行
                                  // { 'is_short': false, 'text': { 'content': '', 'tag': 'lark_md' } },
                                ],
                              },
                            ]
                          })(),
                        ]
                      }
                    },
                    {
                      headers: {
                        'content-type': 'application/json'
                      }
                    }
                  )
                }
              } catch (e) {
                console.error('Feishu Error Report Fail: ', e.toString())
              }
            }
          }, 10)
        }
      })
    },
  }
}
