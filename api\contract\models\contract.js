'use strict'
const { internalWorkflowExecute } = require('../../extend/controllers/workflow')
const { _addChangeHistory } = require('../../extend/controllers/change-history')
const { ObjectId } = require('mongodb')
let contract
module.exports = {
    kind: 'collectionType',     // 备注
    "collectionName": "contract",
    "info": {
        "name": "Contract",
        "label": "合同",
        "description": "合同管理"
    },
    // lifecycles: {
    //     async beforeUpdate(...args) {
    //         // console.log('beforeUpdate', ...args);
    //         contract = await strapi.query('contract').findOne({ id: ObjectId(args[0]._id) });
    //     },
    //     async afterUpdate(...args) {
    //         // console.log('afterUpdate', ...args);
    //         await internalWorkflowExecute({
    //             beforeData: contract,
    //             afterData: args[0],
    //             updateData: args[2],
    //             model: 'contract',
    //         });
    //         await _addChangeHistory({
    //             beforeData: contract,
    //             afterData: args[0],
    //             model: 'contract',
    //         });
    //     },
    // },
    "options": {
        "draftAndPublish": false,
        "timestamps": true,
        "groups": [
            {
                "title": "项目基础信息（同步自伙伴云）",
                "fields": [
                    "no",
                    "projectApproval",
                    "projectStatus",
                    "projectProperty", "\n",
                    "projectName", "\n",
                    "amoeba",
                    "partASalesName",
                    "cooperation",
                    "needMeeting"
                    // "schools",          //学校
                    // "agent"             //经销商
                ]
            },
            {
                "title": "项目方案与报价",
                "fields": [
                    "isStandard", "offerPriceLow", "meetingTime", "\n",
                    "offerDesc",
                    "offerReviewState",
                    "offerReviewNotes"
                ]
            },
            {
                "title": "合同条款",
                "fields": [
                    "isApplicationTarget", "\n",
                    "applicationTarget",            //SaaS权益开通列表
                    "isQuan", "\n",
                    "quan",                         //商品券采购清单
                    "isRightsSchool", "\n",
                    "rightsSchool",                   //经营授权学校列表
                    "isRights", "\n",
                    "rights",                       //区域经销授权范围
                    "isExemptSchool", "\n",
                    "exemptSchool",                 //区域独家豁免学校列表
                    "isExpansion", "\n",
                    "expansion",                    //膨胀金
                    "\n",
                    "terms",                        //特殊条款
                    "isMargin", "\n",
                    "margin", "\n",                  //保证金
                    "marginTerms",                  //保证金条款
                    "amount", "\n",                  //合同金额
                    "installments",                 //回款计划
                    "billingCategory", "\n",        //开票类型
                    "remark"                        //备注
                ]
            },
            {
                "title": "签约信息",
                "fields": [
                    "signTime",                 //签约日期
                    "count",                    //合同份数
                    "beginTime",                //合同开始日期
                    "endTime",                  //合同结束日期
                    "partAName",                //我方公司主体
                    // "partASalesName", "\n",      =//我方业务经理
                    "partBType",                //签约对象类型：经销、学校、教育局等
                    "partBName",                //签约对象名称
                    "partBNo", "\n",             //签约对象编号 ==> 自动带出，不需要填写
                    "partBCorporation",         //签约对象法人
                    "partBPhone",               //签约对象联系电话
                    "signAddress"               //合同填写地址
                ]
            },
            {
                "title": "合同文书",
                "fields": [
                    "template",                 //合同模版名称：好分数使用协议等
                    // "status",                 //合同状态：执行中、已终结、已中止 ==> 默认按照CRM逻辑
                    // "payment",               //收费方式：线上、线下
                    // "billingStatus",         //开票状态
                    // "payouts",               //支出列表
                    // "performances",          //绩效考核列表
                    // "category",              //合同类别：地面业务团队、大客户团队、门店加盟商 ==> 默认为“地面业务团队”
                    "attachments",              //合同文书（附件）
                    "contractFileRemarks",      //文书审批与变更记录
                ]
            },
            {
                "title": "用章与邮寄",
                "fields": [
                    "needMailing",              //是否需要邮寄
                    "mailingRemark",            //合同用章邮寄备注
                    "mailingName",              //合同收件人姓名"
                    "mailingPhone", "\n",        //合同收件人电话"
                    "mailingAddress",           //合同邮寄地址"
                    "isSealed"                  //盖章状态
                ]
            }
        ],
        "allOf": [
            {//流程控制状态：方案&报价部分
                "if": {
                    "properties": {
                        "offerReviewState": {
                            "const": "1" //label": "待发起"
                            // "const": "2" //label": "待上会审批"
                            // "const": "4" //label": "方案待完善"
                            // "const": "9" //label": "申请变更重审"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isStandard": { "editable": true },
                        "offerDesc": { "editable": true },
                        "offerPriceLow": { "editable": true }
                    },
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "1" //label": "待发起"
                            "const": "2" //label": "待上会审批"
                            // "const": "4" //label": "方案待完善"
                            // "const": "9" //label": "申请变更重审"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isStandard": { "editable": true },
                        "offerDesc": { "editable": true },
                        "offerPriceLow": { "editable": true }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "1" //label": "待发起"
                            // "const": "2" //label": "待上会审批"
                            "const": "4" //label": "方案待完善"
                            // "const": "9" //label": "申请变更重审"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isStandard": { "editable": true },
                        "offerDesc": { "editable": true },
                        "offerPriceLow": { "editable": true }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "1" //label": "待发起"
                            // "const": "2" //label": "待上会审批"
                            // "const": "4" //label": "方案待完善"
                            "const": "9" //label": "申请变更重审"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isStandard": { "editable": true },
                        "offerDesc": { "editable": true },
                        "offerPriceLow": { "editable": true }
                    }
                },
                "else": {}
            },
            {//流程控制状态：合同条款与核价部分
                "if": {
                    "properties": {
                        "offerReviewState": {
                            "const": "6" //label": "待核价"
                            // "const": "7" //label": "待客户反馈"
                            // "const": "8" //label": "待出合同"
                            // "const": "11" //label": "合同文书待审批"
                            // "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isApplicationTarget": { "editable": false },
                        "applicationTarget": { "editable": false },
                        "isQuan": { "editable": false },
                        "quan": { "editable": false },              //isQuan=true，则必填
                        "isRightsSchool": { "editable": false },
                        "rightsSchool": { "editable": false },      //isRightsSchool=true，则必填
                        "isRights": { "editable": false },
                        "rights": { "editable": false },            //isRights=true，则必填
                        "isExemptSchool": { "editable": false },
                        "exemptSchool": { "editable": false },      //isExemptSchool=true，则必填
                        "isExpansion": { "editable": false },
                        "expansion": { "editable": false },         //isExpansion=true，则必填
                        "amount": { "editable": false },            //必填
                        "terms": { "editable": false },
                        "isMargin": { "editable": false },
                        "margin": { "editable": false },            //isMargin=ture，则必填
                        "marginTerms": { "editable": false },
                        "installments": { "editable": false },      //必填
                        "billingCategory": { "editable": false },
                        "remark": { "editable": false }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "6" //label": "待核价"
                            "const": "7" //label": "待客户反馈"
                            // "const": "8" //label": "待出合同"
                            // "const": "11" //label": "合同文书待审批"
                            // "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isApplicationTarget": { "editable": false },
                        "applicationTarget": { "editable": false },
                        "isQuan": { "editable": false },
                        "quan": { "editable": false },
                        "isRightsSchool": { "editable": false },
                        "rightsSchool": { "editable": false },
                        "isRights": { "editable": false },
                        "rights": { "editable": false },
                        "isExemptSchool": { "editable": false },
                        "exemptSchool": { "editable": false },
                        "isExpansion": { "editable": false },
                        "expansion": { "editable": false },
                        "amount": { "editable": false },
                        "terms": { "editable": false },
                        "isMargin": { "editable": false },
                        "margin": { "editable": false },
                        "marginTerms": { "editable": false },
                        "installments": { "editable": false },
                        "billingCategory": { "editable": false },
                        "remark": { "editable": false }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "6" //label": "待核价"
                            // "const": "7" //label": "待客户反馈"
                            "const": "8" //label": "待出合同"
                            // "const": "11" //label": "合同文书待审批"
                            // "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isApplicationTarget": { "editable": false },
                        "applicationTarget": { "editable": false },
                        "isQuan": { "editable": false },
                        "quan": { "editable": false },
                        "isRightsSchool": { "editable": false },
                        "rightsSchool": { "editable": false },
                        "isRights": { "editable": false },
                        "rights": { "editable": false },
                        "isExemptSchool": { "editable": false },
                        "exemptSchool": { "editable": false },
                        "isExpansion": { "editable": false },
                        "expansion": { "editable": false },
                        "amount": { "editable": false },
                        "terms": { "editable": false },
                        "isMargin": { "editable": false },
                        "margin": { "editable": false },
                        "marginTerms": { "editable": false },
                        "installments": { "editable": false },
                        "billingCategory": { "editable": false },
                        "remark": { "editable": false }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "6" //label": "待核价"
                            // "const": "7" //label": "待客户反馈"
                            // "const": "8" //label": "待出合同"
                            "const": "11" //label": "合同文书待审批"
                            // "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isApplicationTarget": { "editable": false },
                        "applicationTarget": { "editable": false },
                        "isQuan": { "editable": false },
                        "quan": { "editable": false },
                        "isRightsSchool": { "editable": false },
                        "rightsSchool": { "editable": false },
                        "isRights": { "editable": false },
                        "rights": { "editable": false },
                        "isExemptSchool": { "editable": false },
                        "exemptSchool": { "editable": false },
                        "isExpansion": { "editable": false },
                        "expansion": { "editable": false },
                        "amount": { "editable": false },
                        "terms": { "editable": false },
                        "isMargin": { "editable": false },
                        "margin": { "editable": false },
                        "marginTerms": { "editable": false },
                        "installments": { "editable": false },
                        "billingCategory": { "editable": false },
                        "remark": { "editable": false }
                    }
                },
                "else": {}
            },
            {
                "if": {
                    "properties": {
                        "offerReviewState": {
                            // "const": "6" //label": "待核价"
                            // "const": "7" //label": "待客户反馈"
                            // "const": "8" //label": "待出合同"
                            // "const": "11" //label": "合同文书待审批"
                            "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "isApplicationTarget": { "editable": false },
                        "applicationTarget": { "editable": false },
                        "isQuan": { "editable": false },
                        "quan": { "editable": false },
                        "isRightsSchool": { "editable": false },
                        "rightsSchool": { "editable": false },
                        "isRights": { "editable": false },
                        "rights": { "editable": false },
                        "isExemptSchool": { "editable": false },
                        "exemptSchool": { "editable": false },
                        "isExpansion": { "editable": false },
                        "expansion": { "editable": false },
                        "amount": { "editable": false },
                        "terms": { "editable": false },
                        "isMargin": { "editable": false },
                        "margin": { "editable": false },
                        "marginTerms": { "editable": false },
                        "installments": { "editable": false },
                        "billingCategory": { "editable": false },
                        "remark": { "editable": false }
                    }
                },
                "else": {}
            },
            {//流程控制状态：合同文书部分
                "if": {
                    "properties": {
                        "offerReviewState": {
                            "const": "12" //label": "合同审批完毕"
                        }
                    }
                },
                "then": {
                    "properties": {
                        "attachments": { "editable": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否购买SaaS】
                "if": {
                    "properties": {
                        "isApplicationTarget": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "applicationTarget": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否购买商品券】
                "if": {
                    "properties": {
                        "isQuan": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "quan": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否购买单校经营权】
                "if": {
                    "properties": {
                        "isRightsSchool": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "rightsSchool": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否购买区域经营权】
                "if": {
                    "properties": {
                        "isRights": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "rights": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否有豁免学校】
                "if": {
                    "properties": {
                        "isExemptSchool": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "exemptSchool": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否使用膨胀金】
                "if": {
                    "properties": {
                        "isExpansion": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "expansion": { "visible": false }
                    }
                },
                "else": {}
            },
            {//字段显示控制：合同条款【是否支付保证金】
                "if": {
                    "properties": {
                        "isMargin": { "const": false }
                    }
                },
                "then": {
                    "properties": {
                        "margin": { "visible": false },
                        "marginTerms": { "visible": false }
                    }
                },
                "else": {}
            }
        ],
        "workflows": [
            {
                "id": "65a4f0083ceaef33d03573ad",
                "label": "方案&报价申请",
                "params": {//流程校验必填字段
                    "id": "id",
                    "projectName": "projectName",
                    "isStandard": "isStandard",
                    "no": "no",
                    "itemId": "itemId",
                    "projectProperty": "projectProperty",
                }
            },
            {
                "id": "65a4f05b3ceaef33d03573b2",
                "label": "合同用章流程",
                "params": {
                    "id": "id",
                    "projectName": "projectName",
                    "no": "no",
                    "itemId": "itemId",
                    "attachments": "attachments",
                    "signAddress": "signAddress",
                    "partASalesName": "partASalesName",
                    "partAName": "partAName",
                    "partBType": "partBType",
                    "partBName": "partBName",
                    "count": "count",
                    "needMailing": "needMailing",
                }
            }
        ]
    },
    "pluginOptions": {},
    "attributes": {
        "status": {
            "label": "合同状态",
            "type": "number",
            "size": 3,
            "options": [
                {
                    "label": "执行中",
                    "value": 1
                },
                {
                    "label": "已终结",
                    "value": 9
                },
                {
                    "label": "已中止",
                    "value": 10
                }
            ],
            "editable": false,
            "visible": false
        },
        "no": {
            "label": "立项编号",
            "type": "string",
            "size": 3,
            "unique": true,
            "editable": false
        },
        "projectProperty": {
            "label": "项目属性",
            "type": "string",
            "size": 3,
            "default": " ",
            "editable": false,
        },
        "template": {
            "label": "合同模版名称",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "100",
                    "label": "好分数使用协议"
                },
                {
                    "value": "101",
                    "label": "经销商合同"
                },
                {
                    "value": "102",
                    "label": "区县购买合同"
                },
                {
                    "value": "103",
                    "label": "考试服务协议"
                },
                {
                    "value": "104",
                    "label": "SaaS平台合作协议"
                },
                // {
                //     "value": "105",
                //     "label": "错题本合同"
                // },
                {
                    "value": "106",
                    "label": "保密协议"
                },
                {
                    "value": "107",
                    "label": "战略合作框架协议"
                },
                {
                    "value": "108",
                    "label": "设备采购合同"
                },
                {
                    "value": "109",
                    "label": "主体变更三方协议"
                },
                // {
                //     "value": "110",
                //     "label": "终止协议"
                // },
                // {
                //     "value": "111",
                //     "label": "区县授权合同"
                // },
                // {
                //     "value": "112",
                //     "label": "好分数代理合同"
                // },
                // {
                //     "value": "113",
                //     "label": "好分数产品代理合同"
                // },
                // {
                //     "value": "114",
                //     "label": "SaaS平台服务协议"
                // },
                {
                    "value": "115",
                    "label": "推广运营合同"
                },
                {
                    "value": "116",
                    "label": "好分数产品独家经销合同"
                },
                {
                    "value": "117",
                    "label": "好分数产品经销合同"
                },
                {
                    "value": "118",
                    "label": "错题本单品经销合同"
                },
                {
                    "value": "119",
                    "label": "360会员单品经销合同"
                },
                // {
                //     "value": "120",
                //     "label": "学情套餐"
                // },
                {
                    "value": "200",
                    "label": "产品采购协议"
                },
                {
                    "value": "201",
                    "label": "项目合作协议"
                },
                // {
                //     "value": "202",
                //     "label": "框架战略合作协议"
                // },
                // {
                //     "value": "203",
                //     "label": "授权经销商协议"
                // },
                // {
                //     "value": "204",
                //     "label": "采购合同"
                // },
                {
                    "value": "11012",
                    "label": "经营权膨胀金合同"
                },
                {
                    "value": "11013",
                    "label": "精准练联合运营"
                },
                {
                    "value": "11014",
                    "label": "渠道合作协议v2024"
                },
                {
                    "value": "999",
                    "label": "其他"
                }
            ]
        },
        "count": {
            "label": "合同份数",
            "type": "enumeration",
            "enum": [
                "一式两份",
                "一式三份",
                "一式四份",
                "一式五份",
            ],
            "size": 3,
            "default": "一式两份"
        },
        "amount": {
            "label": "合同金额",
            "type": "number",
            "format": "rmb",
            "size": 3
        },
        "isMargin": {
            "label": "是否支付保证金",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "margin": {
            "label": "保证金",
            "type": "number",
            "format": "rmb",
            "size": 3
        },
        "marginTerms": {
            "label": "保证金约定条款",
            "type": "richtext",
            "size": 12
        },
        "signTime": {
            "label": "签约日期",
            "type": "date",
            "size": 3,
            "format": "YYYY-MM-DD"
        },
        "beginTime": {
            "label": "开始日期",
            "type": "date",
            "size": 3
        },
        "endTime": {
            "label": "结束日期",
            "size": 3,
            "type": "date"
        },
        "partAName": {
            "label": "我方公司主体",
            "type": "enumeration",
            "size": 3,
            "enum": [
                "北京海西青科技有限责任公司",
                "北京爱云校教育科技研究院",
                "北京志远无限信息技术研究院",
                "北京中科校园科技有限公司",
                "北京中科校园科技有限公司郑州分公司",
                "好分数科技有限公司北京分公司",
                "好分数科技有限公司",
                "好分数科技有限公司郑州分公司",
                "好分数科技有限公司河南分公司",
                "郑州中科校园科技有限公司",
                "精准学（郑州）人工智能科技有限公司",
                "精准学（南京）人工智能科技有限公司",
                "精准学（广州）人工智能科技有限公司",
                "精准学（广州）人工智能科技有限公司郑州分公司",
                "郑州美美哒科技有限公司",
            ]
        },
        "amoeba": {
            "label": "阿米巴单元",
            "type": "string",
            "editable": false,
            "size": 3
        },
        "cooperation": {
            "label": "合作方式",
            "type": "string",
            "editable": false,
            "size": 3
        },
        "needMeeting": {
            "label": "申请制作合同",
            "type": "string",
            "editable": false,
            "enum": [
                "否",
                "非标方案需上会",
                "标准方案",
            ],
            "size": 3
        },
        "partASalesName": {
            "label": "公司业务经理",
            "type": "string",
            "editable": false,
            "size": 3
        },
        "partASalesWxId": {
            "label": "公司业务经理wxId",
            "type": "string",
            "visible": false,
            "size": 3
        },
        "partBType": {
            "label": "签约对象类型",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "agent",
                    "label": "经销商"
                },
                {
                    "value": "school",
                    "label": "学校"
                },
                {
                    "value": "jiaoyuju",
                    "label": "教育局"
                },
                {
                    "value": "jiaoyanshi",
                    "label": "教研室"
                },
                {
                    "value": "lianmeng",
                    "label": "联盟"
                },
                {
                    "value": "liankao",
                    "label": "联考"
                }
            ]
        },
        "partBNo": {
            "label": "签约对象编号",
            "size": 3,
            "type": "string"
        },
        "partBName": {
            "label": "签约对象名称",
            "size": 3,
            "type": "string"
        },
        "partBCorporation": {
            "label": "签约对象法人",
            "size": 3,
            "type": "string"
        },
        "partBPhone": {
            "label": "签约对象联系电话",
            "size": 3,
            "type": "string"
        },
        "isApplicationTarget": {
            "label": "是否购买SaaS",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "applicationTarget": {
            "label": "SaaS权益开通列表",
            "type": "component",
            "repeatable": true,
            "displayMode": "table",
            "component": "contract.target",
            "viewTableConfig": {
                "columns": ["school", "goods", "calculatedUnitPrice", "unitPrice", "quantity", "beginTime", "endTime", "totalPrice", "remark"]
            }
        },
        "isQuan": {
            "label": "是否购买商品券",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "quan": {
            "label": "商品券采购",
            "type": "component",
            "repeatable": true,
            "displayMode": "table",
            "component": "contract.quan",
            "viewTableConfig": {
                "columns": ["quanGoods", "limitType", "limits", "expTime", "unitPrice", "quantity", "totalPrice", "remark"]
            }
        },
        "isRightsSchool": {
            "label": "是否购买单校经营权",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "rightsSchool": {
            "label": "经营授权学校列表",
            "type": "component",
            "repeatable": true,
            "displayMode": "table",
            "component": "contract.rights-school",
            "viewTableConfig": {
                "columns": ["schools", "agentType", "beginTime", "endTime", "task", "remark",]
            }
        },
        "isRights": {
            "label": "是否购买区域经营权",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "rights": {
            "label": "区域经销授权范围",
            "type": "json",
            "jsonSchema": {
                "title": "区域经销授权范围",
                "type": "array",
                "items": {
                    "title": "经销商权益",
                    "type": "object",
                    "properties": {
                        "target": {
                            "title": "授权区县（省/市/区县）",
                            "type": "string"
                        },
                        "type": {
                            "title": "授权类型",
                            "type": "string",
                            "options": [
                                {
                                    "label": "基础独家（仅限SaaS）",
                                    "value": "basicExclusiveAuth"
                                },
                                {
                                    "label": "区域独家（SaaS+会员分成）",
                                    "value": "regionalExclusiveAuth"
                                }
                            ]
                        },
                        "beginTime": {
                            "title": "开始日期",
                            "type": "date"
                        },
                        "endTime": {
                            "title": "结束日期",
                            "type": "date"
                        },
                        "price": {
                            "title": "经销授权费",
                            "format": "rmb",
                            "type": "number"
                        },
                        "remark": {
                            "title": "备注",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "isExpansion": {
            "label": "是否使用膨胀金",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "expansion": {
            "label": "膨胀金",
            "size": 12,
            "type": "json",
            "jsonSchema": {
                "title": "膨胀金列表",
                "type": "array",
                "items": {
                    "title": "膨胀金",
                    "type": "object",
                    "properties": {
                        "face": {
                            "title": "面值",
                            "type": "number"
                        },
                        "cost": {
                            "title": "成本",
                            "type": "number"
                        },
                        "startTime": {
                            "title": "生效日期",
                            "type": "date"
                        },
                        "endTime": {
                            "title": "过期日期",
                            "type": "date"
                        },
                        "boundary": {
                            "title": "适用范围",
                            "type": "string"
                        },
                        "remark": {
                            "title": "备注",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "isExemptSchool": {
            "label": "是否有豁免学校",
            "type": "boolean",
            "size": 3,
            "default": false
        },
        "exemptSchool": {
            "label": "区域独家豁免学校列表",
            "size": 12,
            "type": "json",
            "jsonSchema": {
                "title": "区域独家豁免学校列表",
                "type": "array",
                "items": {
                    "title": "豁免学校列表",
                    "type": "object",
                    "properties": {
                        "sid": {
                            "title": "学校ID",
                            "type": "string"
                        },
                        "name": {
                            "title": "学校名称",
                            "type": "string"
                        },
                        "remark": {
                            "title": "备注",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "projectApproval": {
            "label": "立项审核",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "4",
                    "label": "新提交"
                },
                {
                    "value": "6",
                    "label": "待审批"
                },
                {
                    "value": "1",
                    "label": "立项成功"
                },
                {
                    "value": "3",
                    "label": "待定"
                },
                {
                    "value": "2",
                    "label": "立项失败"
                },
                {
                    "value": "5",
                    "label": "立项过期"
                }
            ],
            "editable": false
        },
        "projectStatus": {
            "label": "项目状态",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "1",
                    "label": "进行中"
                },
                {
                    "value": "2",
                    "label": "完成"
                },
                {
                    "value": "3",
                    "label": "中止"
                },
                {
                    "value": "4",
                    "label": "关闭"
                }
            ],
            "editable": false
        },
        "projectDate": {
            "label": "立项日期",
            "size": 3,
            "visible": false,
            "type": "date"
        },
        "isStandard": {
            "label": "是否为标准价格",
            "size": 3,
            "type": "enumeration",
            "enum": [
                "标准价格",
                "非标价格"
            ],
            "default": "非标价格",
            "editable": false // 只有少数状态下可以修改，默认值设为不可编辑
        },
        "offerDesc": {
            "label": "合作方案说明",
            "size": 12,
            "type": "richtext",
            "default": "<p>【项目背景】</p><p>【项目标价】</p><p>【申请方案】</p><p>【申请原因】</p>",
            "editable": false // 只有少数状态下可以修改，默认值设为不可编辑
        },
        "offerPriceLow": {
            "label": "报价（元）",
            "size": 3,
            "type": "number",
            "editable": false // 只有少数状态下可以修改，默认值设为不可编辑
        },
        "offerPriceHigh": { // 方案报销不需要提交上限，本字段不再使用，可删除，先做隐藏处理
            "label": "报价区间（高）",
            "size": 3,
            "type": "number",
            "visible": false,
            "editable": false
        },
        "offerReviewState": {
            "label": "方案审批状态",
            "size": 3,
            "type": "string",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "待发起"
                },
                {
                    "value": "2",
                    "label": "待上会审批"
                },
                {
                    "value": "3",
                    "label": "报价初审通过"
                },
                {
                    "value": "4",
                    "label": "方案待完善"
                },
                {
                    "value": "5",
                    "label": "报价确认，请完善合同条款"
                },
                {
                    "value": "6",
                    "label": "待出合同"
                },
                {
                    "value": "7",
                    "label": "待客户反馈"
                },
                // {
                //     "value": "8",
                //     "label": "待出合同"
                // },
                {
                    "value": "9",
                    "label": "申请变更重审"
                },
                {
                    "value": "10",
                    "label": "方案驳回"
                },
                {
                    "value": "11",
                    "label": "合同文书待审批"
                },
                {
                    "value": "12",
                    "label": "合同审批完毕"
                }
            ],
            "editable": false
        },
        "meetingTime": {
            "label": "计划上会日期",
            "type": "date",
            "size": 3,
            "format": "YYYY-MM-DD"
        },
        "offerReviewNotes": {
            "label": "方案审批备注",
            "size": 12,
            "type": "text"
        },
        "projectName": {
            "label": "项目名称",
            "size": 12,
            "type": "string",
            "editable": false
        },
        "signAddress": {
            "label": "合同填写地址",
            "size": 6,
            "type": "string"
        },
        "attachments": {
            "label": "合同文件（可上传多个文件）",
            "collection": "file",
            "allowedTypes": [
                "images",
                "files"
            ],
            "plugin": "upload",
            "multiple": true,
            "pluginOptions": {}
        },
        "terms": {
            "label": "特殊条款",
            "type": "richtext",
            "editable": false,
            "size": 12
        },
        "isContractApproved": { //字段弃用，复用offerReviewState
            "label": "文书审批状态",
            "size": 3,
            "editable": false,
            "type": "enumeration",
            "enum": [
                "文书待审批",
                "文书已审批",
                "驳回"
            ],
            "visible": false
        },
        "contractFileRemarks": {
            "label": "文书审批与变更记录",
            "size": 12,
            "type": "text"
        },
        "isSealed": {
            "label": "盖章状态",
            "type": "number",
            "size": 3,
            "options": [
                {
                    "label": "已盖章",
                    "value": 1
                },
                {
                    "label": "未盖章",
                    "value": 0
                },
                {
                    "label": "异常",
                    "value": -1
                }
            ],
        },
        "submitter": {
            "label": "提交人",
            "size": 3,
            "visible": false,
            "type": "json",
            "jsonSchema": {
                "title": "提交人",
                "type": "object",
                "properties": {
                    "userId": {
                        "title": "提交人id",
                        "type": "string"
                    },
                    "name": {
                        "title": "提交人名称",
                        "type": "string"
                    }
                }
            }
        },
        "remark": {
            "label": "备注",
            "type": "text",
            "size": 12
        },
        "installments": {
            "label": "回款计划",
            "type": "json",
            "size": 12,
            "jsonSchema": {
                "title": "回款计划",
                "type": "array",
                "items": {
                    "title": "分期",
                    "type": "object",
                    "properties": {
                        "planTime": {
                            "title": "计划回款日期",
                            "type": "date"
                        },
                        "planAmount": {
                            "title": "计划回款金额（元）（不含保证金）",
                            "format": "rmb",
                            "type": "number"
                        },
                        "remark": {
                            "title": "备注",
                            "type": "string"
                        }
                    }
                }
            }
        },
        "billingCategory": {
            "label": "开票类型",
            "size": 3,
            "type": "number",
            "options": [
                {
                    "label": "增值税普通发票",
                    "value": 10
                },
                {
                    "label": "增值税专用发票",
                    "value": 11
                }
            ]
        },
        "needMailing": {
            "label": "是否需要邮寄",
            "size": 3,
            "type": "boolean",
            "default": true
        },
        "mailingName": {
            "label": "合同收件人姓名",
            "size": 3,
            "type": "string"
        },
        "mailingPhone": {
            "label": "合同收件人电话",
            "size": 3,
            "type": "string"
        },
        "mailingAddress": {
            "label": "合同邮寄地址",
            "size": 12,
            "type": "string"
        },
        "mailingRemark": {
            "label": "用章与邮寄备注",
            "type": "string",
            "size": 9
        },
        "crmContractId": {
            "label": "关联crm合同id",
            "type": "string",
            "visible": false
        },
        "schools": {
            "label": "学校",
            "mainField": "showName",
            "collection": "boss-school",
            "size": 6,
            "editable": false
        },
        "agent": {
            "label": "经销商",
            "mainField": "company",
            "model": "boss-agent",
            "size": 6,
            "editable": false
        },
        "itemId": {
            "label": "伙伴itemId",
            "type": "string",
            "visible": false,
        },
        "isLocked": {
            "label": "是否锁定不允许修改",
            "type": "enumeration",
            "enum": [
                "是",
                "否"
            ],
            "size": 3,
            "default": "否",
            "visible": false
        },
        "processRecord": {
            "label": "关联流程记录",
            "collection": "process-record",
            "editable": false
        },
    }
}
