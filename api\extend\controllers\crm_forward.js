const { MongoClient, ObjectId } = require('mongodb')
const _ = require('lodash')
const axios = require('axios')

async function get_list_by_paging(ctx) {
  // 获取指定多个客户的应用使用情况列表 /crm/customer/app_usage/get_by_customer_ids
  // 分页获取客户授权列表 /crm/agent_auth/customer/get_list_by_paging
  const query = ctx.request.query
  let res = await axios.get(`${strapi.config.server.bossApi.url}/crm/agent_auth/customer/get_list_by_paging`, {
    params: query,
    headers: {
      cookie: 'BOSSID=' + ctx.cookies.get('BOSSID')
    }
  })

  let data = res.data
  try {
    if (data && data.list && data.list.length) {
      let customerIds = await axios.post(`${strapi.config.server.bossApi.url}/crm/customer/app_usage/get_by_customer_ids`, {
        customerIds: data.list.map(e => e.customer.id)
      }, {
        headers: {
          cookie: 'BOSSID=' + ctx.cookies.get('BOSSID')
        }
      })
      console.log('customerIds', customerIds.data)
      if (customerIds.data && customerIds.data.length) {
        data.list = data.list.map(e => {
          let usageRes = customerIds.data.find(c => c.customer_id === e.customer.id)
          if (usageRes && usageRes.usages && usageRes.usages.length) {
            let usage = usageRes.usages.find(u => u.productCategoryInfo.sign === e.product.sign)
            if (usage) {
              e.product['usage'] = usage
            }
          }
          return e
        })
      }
    }
  } catch (e) {
    console.log('get_by_customer_ids', e)
  }
  return data
}

module.exports = {
  get_list_by_paging,
}
