const axios = require('axios')

let go_tokens = {}
let lastUpdateTime = 0

async function getToken (domain = 'org', force = false) {
  // 最新时间不是当天
  let go_token = go_tokens[domain]?.token || null
  let lastUpdateTime = go_tokens[domain]?.lastUpdateTime || 0
  if (force || !go_token || (Date.now() - lastUpdateTime) > 60 * 60 * 1000) {
    const res = await axios.get(`http://${domain}.yunxiao.io/__get_token?id=wly&secret=wly%23$@%23$%23$@%23`)
    if (res.data && typeof res.data === 'string') {
      go_tokens[domain] = {
        token: res.data,
        lastUpdateTime: Date.now()
      }
    }
  }
  return go_tokens[domain]?.token || null
}

const noticeWhiteList = ['xuzhenzhou_0617', 'xushihao_68068', 'xijunpeng',]

async function sendQxNoticeBoss (userQxIds, notice) {
  try {
    let users = userQxIds
    if (process.env.SERVER !== 'prod') {
      users = userQxIds.filter(userQxId => noticeWhiteList.includes(userQxId))
      console.log('sendQxNoticeBoss test', users)
    }
    if (!users || users.length === 0) {
      return null
    }
    let noticeRes = await axios.post('http://org.yunxiao.io/notice/boss', {
      __go_token: await getToken('org'),
      users: userQxIds.join(','),
      notice: {
        title: notice.title,
        desc: notice.desc,
        url: notice.url
      }
    })
    let result = noticeRes.data
    if (result.errcode === 0) {
      console.log('sendQxNoticeBoss success', result.msgid)
      return result.msgid
    } else {
      console.log('sendQxNoticeBoss fail', result.errmsg)
    }
    return null
  } catch (err) {
    return null
  }
}

let userInfoCache = {}

async function getQxUserInfo (qxId) {
  try {
    if (userInfoCache[qxId] && Date.now() - userInfoCache[qxId].lastUpdateTime < 10 * 60 * 60 * 1000) {
      return userInfoCache[qxId]
    }
    if (process.env.SERVER === 'test') {
      // 测试环境访问不了org
      return {
        qxId: qxId,
        name: qxId,
        avatar: 'https://wwcdn.weixin.qq.com/node/wework/images/empty.png'
      }
    }

    const res = await axios.get(`http://ids.yunxiao.io/user/info?__go_token=${await getToken('ids')}&id=${qxId}`)
    if (res.data && res.data.length >= 3) {
      let userInfo = {
        qxId: res.data[0],
        name: res.data[1],
        avatar: res.data[2]
      }
      userInfoCache[qxId] = {
        ...userInfo,
        lastUpdateTime: Date.now()
      }
      return userInfo
    }
    return null
  } catch (err) {
    console.log('getQxUserAvatar err', err)
    return null
  }
}

module.exports = {
  getToken,
  sendQxNoticeBoss,
  getQxUserInfo
}
