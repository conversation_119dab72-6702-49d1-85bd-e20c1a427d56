'use strict'

const { UserCurdRouter } = require('accel-utils')
const { getModelService } = require('accel-utils/collection-utils')

class CollectionUserCurdRouter extends UserCurdRouter {
  constructor (name, config = {}) {
    super(name, config)
  }

  _getService (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    const { model } = this._parseUserCtx(ctx)
    return getModelService(model)
  }

  _getModel (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    const { model } = this._parseUserCtx(ctx)
    return strapi.getModel(model)
  }
}

const collectionUserCurdRouter = new CollectionUserCurdRouter()
const handlers = collectionUserCurdRouter.createHandlers()

module.exports = {
  find: handlers.selfFind,
  count: handlers.selfCount,
  findOne: handlers.selfFindOne,
  create: handlers.selfCreate,
  update: handlers.selfUpdate,
  delete: handlers.selfDelete,
  export: handlers.selfExport,
}
