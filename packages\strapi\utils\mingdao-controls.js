const timestampControls = [
  {
    'controlId': 'ctime',
    'controlName': '创建时间',
    'type': 16,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'utime',
    'controlName': '最近修改时间',
    'type': 16,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
]

const userControls = [{
  'controlId': 'rowid',
  'controlName': '记录ID',
  'type': 2,
  'attribute': 0,
  'row': 9999,
  'col': 0,
  'dot': 0,
  'enumDefault': 0,
  'enumDefault2': 0,
  'dataSource': '',
  'sourceControlType': 0,
  'noticeItem': 0,
  'userPermission': 0,
  'options': [],
  'unique': false,
  'fieldPermission': '100',
  'advancedSetting': {},
  'size': 0,
  'deleteTime': '0001-01-01 00:00:00',
  'lastEditTime': '0001-01-01 00:00:00',
  'disabled': false,
  'checked': false
},
  {
    'controlId': 'ownerid',
    'controlName': '拥有者',
    'type': 26,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'caid',
    'controlName': '创建人',
    'type': 26,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'uaid',
    'controlName': '最近修改人',
    'type': 26,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
]

const workflowControls = [
  {
    'controlId': 'wfname',
    'controlName': '流程名称',
    'type': 2,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfcuaids',
    'controlName': '节点负责人',
    'type': 26,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfcaid',
    'controlName': '发起人',
    'type': 26,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfctime',
    'controlName': '发起时间',
    'type': 16,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfrtime',
    'controlName': '节点开始时间',
    'type': 16,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfftime',
    'controlName': '剩余时间',
    'type': 38,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'unit': '1',
    'enumDefault': 3,
    'enumDefault2': 0,
    'dataSource': '',
    'sourceControlId': '$wfdtime$',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {
      'dateformulatype': '1',
      'autocarry': '1'
    },
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  },
  {
    'controlId': 'wfstatus',
    'controlName': '流程状态',
    'type': 11,
    'attribute': 0,
    'row': 9999,
    'col': 0,
    'dot': 0,
    'enumDefault': 0,
    'enumDefault2': 1,
    'dataSource': '',
    'sourceControlType': 0,
    'noticeItem': 0,
    'userPermission': 0,
    'options': [
      {
        'key': 'pass',
        'value': '通过',
        'index': 0,
        'isDeleted': false,
        'color': '#4caf50',
        'score': 0,
        'hide': false
      },
      {
        'key': 'refuse',
        'value': '否决',
        'index': 1,
        'isDeleted': false,
        'color': '#f44336',
        'score': 0,
        'hide': false
      },
      {
        'key': 'abort',
        'value': '中止',
        'index': 2,
        'isDeleted': false,
        'color': '#e8e8e8',
        'score': 0,
        'hide': false
      },
      {
        'key': 'other',
        'value': '进行中',
        'index': 3,
        'isDeleted': false,
        'color': '#e5f1fe',
        'score': 0,
        'hide': false
      }
    ],
    'unique': false,
    'fieldPermission': '100',
    'advancedSetting': {},
    'size': 0,
    'deleteTime': '0001-01-01 00:00:00',
    'lastEditTime': '0001-01-01 00:00:00',
    'disabled': false,
    'checked': false
  }
]

module.exports = {
  timestampControls,
  userControls,
  workflowControls,
}
