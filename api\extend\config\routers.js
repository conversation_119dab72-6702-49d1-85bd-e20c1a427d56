const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    {
      'method': 'GET',
      'path': '/apis',
      'handler': 'api.find',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/apis/count',
      'handler': 'api.count',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/apis',
      'handler': 'api.create',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/apis/export',
      'handler': 'api.delete',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/apis/import',
      'handler': 'api.delete',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'PUT',
      'path': '/apis/:id',
      'handler': 'api.update',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/apis/execute/:id',
      'handler': 'api.apiExecute',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'DELETE',
      'path': '/apis/:id',
      'handler': 'api.delete',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/process-records',
      controller: 'process-record'
    }),
    {
      'method': 'POST',
      'path': '/workflows/webhook/:id',
      'handler': 'workflow.webhookExecute',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/workflows/timer/:id',
      'handler': 'workflow.timerExecute',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/workflows',
      controller: 'workflow'
    }),
    {
      'method': 'PUT',
      'path': '/texts/:id',
      'handler': 'text.updateFile',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/data-syncs/execute/:id',
      'handler': 'data-sync.dataSyncExecute',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/texts/demo',
      'handler': 'demo.getJson',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/utils/dbQuery',
      'handler': 'utils.dbQuery',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/utils/qr-code',
      'handler': 'utils.getQrCode',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/utils/book-token',
      'handler': 'utils.bookToken',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/third-party-payments/wechat/pay/:type',
      'handler': 'third-party-payment.wxPay',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/third-party-payments/by-ids',
      'handler': 'third-party-payment.findByIds',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/third-party-payments/by-query',
      'handler': 'third-party-payment.findByQuery',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/third-party-payments/wechat/pay/notification/:mchid',
      'handler': 'third-party-payment.wxPayNotification',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/third-party-payments/wechat/refund',
      'handler': 'third-party-payment.wxRefund',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/third-party-payments/wechat/refund/notification/:mchid',
      'handler': 'third-party-payment.wxRefundNotification',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/third-party-payments',
      controller: 'third-party-payment'
    }),
    ...createDefaultRoutes({
      basePath: '/third-party-refunds',
      controller: 'third-party-refund'
    }),
    ...createDefaultRoutes({
      basePath: '/data-syncs',
      controller: 'data-sync'
    }),
    ...createDefaultRoutes({
      basePath: '/data-sources',
      controller: 'data-source'
    }),
    ...createDefaultRoutes({
      basePath: '/change-historys',
      controller: 'change-history'
    }),
    {
      'method': 'POST',
      'path': '/question-collections/:id',
      'handler': 'question-collection.updateQuestion',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/question-collections/del/:id',
      'handler': 'question-collection.deleteQuestion',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/question-collections',
      controller: 'question-collection'
    }),
    ...createDefaultRoutes({
      basePath: '/texts',
      controller: 'text'
    }),
    ...createDefaultRoutes({
      basePath: '/message-centre',
      controller: 'message-centre'
    }),
    ...createDefaultRoutes({
      controller: 'db-cache',
      basePath: '/db-cache',
    }),
    ...createDefaultRoutes({
      controller: 'huoban-queue',
      basePath: '/huoban-queue',
    }),
    ...createDefaultRoutes({
      controller: 'huoban-msg-queue',
      basePath: '/huoban-msg-queue',
    }),
    ...createDefaultRoutes({
      controller: 'mingdao-queue',
      basePath: '/mingdao-queue',
    }),
    ...createDefaultRoutes({
      controller: 'yj-school-setting',
      basePath: '/yj-school-setting',
    }),
    {
      'method': 'POST',
      'path': '/message-centre/mingdaoMsg',
      'handler': 'message-centre.mingdaoMsg',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/school/update',
      'handler': 'huoban.updateHuobanSchool',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/school/updateHuobanSchoolSalesFollow',
      'handler': 'huoban.updateHuobanSchoolSalesFollow',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/boss-school/create',
      'handler': 'huoban.createBossSchool',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/boss-school/update',
      'handler': 'huoban.updateBossSchool',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/boss-agent/create',
      'handler': 'huoban.createBossAgent',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/agent-auth/update',
      'handler': 'huoban.updateAgentAuthSchool',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/data-json/init',
      'handler': 'huoban.initHuobanDataJson',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/huoban/message',
      'handler': 'huoban.huobanMessage',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/action/syncByHuobanId',
      'handler': 'huoban.syncByHuobanId',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/huoban/action/createHuobanSalesProject',
      'handler': 'huoban.createHuobanSalesProject',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/huoban/action/getProjectBySchoolId',
      'handler': 'huoban.getProjectBySchoolId',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/huoban/action/getSalesProjectListByTime',
      'handler': 'huoban.getSalesProjectListByTime',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/huoban/createExpenditureByBoss',
      'handler': 'huoban.createExpenditureByBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/huoban/deleteExpenditureByBoss',
      'handler': 'huoban.deleteExpenditureByBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/huoban/createPayExpenditureByBoss',
      'handler': 'huoban.createPayExpenditureByBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/huoban/deletePayExpenditureByBoss',
      'handler': 'huoban.deletePayExpenditureByBoss',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/crm/agent_auth/customer/get_list_by_paging',
      'handler': 'crm_forward.get_list_by_paging',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'GET',
      'path': '/data-comments/listDetail',
      'handler': 'data-comment.listDetail',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/data-comments/addComment',
      'handler': 'data-comment.addComment',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/data-comments/deleteComment',
      'handler': 'data-comment.deleteComment',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/data-comments',
      controller: 'data-comment'
    })
  ]
}
