const { resolveFile } = require('./upload-utils')
const path = require('path')
const BosClient = require('@baiducloud/sdk').BosClient
const STS = require('@baiducloud/sdk').STS

function initBos (providerOptions) {
  const config = {
    endpoint: providerOptions.Endpoint,     // 传入Bucket所在区域域名
    credentials: {
      ak: providerOptions.AK,
      sk: providerOptions.SK
    }
  }
  const bucket = providerOptions.Bucket
  const bosClient = new BosClient(config)
  const stsClient = new STS({
    credentials: config.credentials,
    region: 'bj'
  })
  return {
    config: config,
    async putObject ({ Key, Body, FileName }) {
      const ext = path.extname(FileName).slice(1).toLowerCase()
      const options = {
        'x-bce-acl': 'public-read',
      }
      // 非 Web 图片文件设置下载名称
      if (!['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)) {
        options['Content-Disposition'] = `attachment; filename="${encodeURI(FileName)}"`
      }
      return await bosClient.putObjectFromFile(bucket, Key, Body, options)
    },
    async deleteObject ({ Key }) {
      return await bosClient.deleteObject(bucket, Key)
    },
    async getSecurityToken () {
      const response = await stsClient.getSessionToken(1800, {
        accessControlList: [{
          service: 'bce:bos',
          resource: ['ayx-pk'],
          region: '*',
          effect: 'Allow',
          permission: ['READ', 'WRITE']
        }]
      })
      const body = response.body
      return {
        AccessKeyId: body.accessKeyId,
        SecretAccessKey: body.secretAccessKey,
        SessionToken: body.sessionToken,
        Expiration: body.expiration
      }
    },
    async uploadFileToBucket (fileName, fileContent, dir = 'production/upload') {
      const {
        key, size, buffer
      } = await resolveFile(fileName, fileContent, dir)
      const result = await this.putObject({ Key: key, Body: fileContent, FileName: fileName })
      const Location = bosClient.config.endpoint + '/' + key
      return {
        key, size, buffer,
        Location,
        ...result
      }
    }
  }
}

module.exports = {
  initBos
}
