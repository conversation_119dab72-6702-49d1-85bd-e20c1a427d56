const swaggerJsdoc = require('swagger-jsdoc');
const Router = require('koa-router');
const path = require('path');
const fs = require('fs');
const marked = require('marked');
const { getSwaggerConfig, loadExternalConfig } = require('../../services/swagger');
const { extendSwaggerSpec } = require('../../services/auto-generator');

module.exports = strapi => {
  return {
    initialize() {
      // 获取所有可用的模块
      const getAvailableModules = () => {
        const modules = [];

        try {
          // 扫描 api 目录下的业务模块
          const apiDir = path.join(process.cwd(), 'api');
          if (fs.existsSync(apiDir)) {
            const apiModules = fs.readdirSync(apiDir).filter(file => {
              const stat = fs.statSync(path.join(apiDir, file));
              return stat.isDirectory();
            });

            apiModules.forEach(module => {
              modules.push({
                name: module,
                type: 'api',
                label: `📋 ${module}`,
                description: `${module} 模块接口`
              });
            });
          }

          // 扫描扩展模块
          const { getExtensions } = require('../../services/extensions');
          const extensions = getExtensions();

          Object.keys(extensions).forEach(moduleName => {
            if (moduleName !== 'components' && moduleName !== 'additionalTags' && extensions[moduleName].paths) {
              modules.push({
                name: moduleName,
                type: 'extension',
                label: `🔧 ${moduleName}`,
                description: `${moduleName} 系统模块`
              });
            }
          });

        } catch (error) {
          console.error('获取可用模块失败:', error);
        }

        return modules;
      };

      // 生成 Swagger 规范的函数
      const generateSwaggerSpec = (filterModule = null, filterController = null) => {
        // 清除 Node.js require 缓存
        Object.keys(require.cache).forEach(key => {
          if (key.includes('/api/') && key.includes('/controllers/')) {
            delete require.cache[key];
          }
        });

        // 获取配置并生成基础 Swagger 规范
        const swaggerOptions = getSwaggerConfig(true); // 强制重新加载配置

        // 如果指定了模块过滤，调整API扫描路径
        if (filterModule) {
          swaggerOptions.apis = [
            `./api/${filterModule}/**/config/routes.js`,
            `./api/${filterModule}/**/controllers/*.js`
          ];
          // 不要包含 extensions.yaml，因为扩展配置将通过 extendSwaggerSpec 单独处理
        }

        // 添加时间戳确保 swagger-jsdoc 重新解析
        swaggerOptions.definition.info.version = `${swaggerOptions.definition.info.version}-${Date.now()}`;

        let swaggerSpec = swaggerJsdoc(swaggerOptions);

        // 扩展自动生成的路径
        swaggerSpec = extendSwaggerSpec(swaggerSpec, filterModule, filterController);

        // 更新文档标题
        if (filterController) {
          swaggerSpec.info.title += ` - ${filterController} 控制器`;
        } else if (filterModule) {
          swaggerSpec.info.title += ` - ${filterModule} 模块`;
        }

        return swaggerSpec;
      };

      // 在开发环境下每次请求都重新生成（避免缓存）
      const isDevelopment = process.env.NODE_ENV !== 'production';
      let cachedSwaggerSpec = null;

      if (!isDevelopment) {
        // 生产环境缓存规范
        cachedSwaggerSpec = generateSwaggerSpec();
      }

      const router = new Router();

      // Swagger JSON endpoint - 支持模块和控制器过滤（路径参数方式）
      // 这个路由需要放在更通用的路由之前
      router.get('/api-docs/swagger.json/:module/:controller', (ctx) => {
        const { module, controller } = ctx.params;
        console.log(`JSON API 请求 - 模块: ${module}, 控制器: ${controller}`);
        // 获取最新的 swagger 规范
        const currentSpec = isDevelopment ?
          generateSwaggerSpec(module, controller) :
          generateSwaggerSpec(module, controller);
        ctx.body = currentSpec;
      });

      router.get('/api-docs/swagger.json/:module', (ctx) => {
        const { module } = ctx.params;
        console.log(`JSON API 请求 - 模块: ${module}`);
        // 获取最新的 swagger 规范
        const currentSpec = isDevelopment ?
          generateSwaggerSpec(module) :
          generateSwaggerSpec(module);
        ctx.body = currentSpec;
      });

      // Swagger JSON endpoint - 支持模块和控制器过滤（查询参数方式）
      router.get('/api-docs/swagger.json', (ctx) => {
        const { module, controller } = ctx.query;
        console.log(`JSON API 请求 - 查询参数 模块: ${module || '全部'}, 控制器: ${controller || '全部'}`);
        // 获取最新的 swagger 规范
        const currentSpec = isDevelopment ?
          generateSwaggerSpec(module, controller) :
          (module || controller ? generateSwaggerSpec(module, controller) : cachedSwaggerSpec);
        ctx.body = currentSpec;
      });

      // Swagger UI - 支持模块和控制器过滤
      router.get('/api-docs/:module?/:controller?', (ctx) => {
        const { module, controller } = ctx.params;
        // 获取最新的 swagger 规范
        const currentSpec = isDevelopment ?
          generateSwaggerSpec(module, controller) :
          (module || controller ? generateSwaggerSpec(module, controller) : cachedSwaggerSpec);

        // 获取外部配置
        const externalConfig = loadExternalConfig();
        const uiConfig = externalConfig.ui || {};
        const uiTitle = uiConfig.title || 'API Documentation';
        const uiOptions = uiConfig.options || {};

        // 获取所有可用的模块
        const availableModules = getAvailableModules();

        const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <title>${uiTitle}</title>
  <link rel="stylesheet" type="text/css" href="/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui.css" />
  <link rel="icon" type="image/png" href="/plugins/swagger/static/swagger-ui/5.0.0/favicon-32x32.png" sizes="32x32" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin: 0;
      background: #fafafa;
    }
    .topbar {
      background-color: #1b1b1b !important;
    }
    .swagger-ui .topbar .download-url-wrapper {
      display: none !important;
    }
    /* 自定义文档信息栏 - 吸附头部设计 */
    .custom-info {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      padding: 8px 20px;
      border-radius: 0;
      border: none;
      border-bottom: 1px solid #e2e8f0;
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 0;
      min-height: 50px;
      box-sizing: border-box;
      position: sticky;
      top: 0;
      z-index: 1000;
      backdrop-filter: blur(8px);
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    .custom-info .main-content {
      width: 350px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .custom-info h1 {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
      line-height: 1.2;
    }
    .custom-info .description {
      font-size: 12px;
      color: #64748b;
      margin: 4px 0 0 0;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
    .doc-links-container {
      display: flex;
      flex-direction: row;
      gap: 12px;
      align-items: center;
      flex: 1;
      min-width: 0;
      justify-content: flex-end;
      flex-wrap: wrap;
    }
    .doc-links-container .links-section {
      display: flex;
      gap: 6px;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
    }
    .doc-links-container a {
      display: inline-flex;
      align-items: center;
      gap: 3px;
      padding: 4px 8px;
      background: rgba(255, 255, 255, 0.8);
      color: #475569;
      text-decoration: none;
      border-radius: 4px;
      font-size: 12px;
      transition: all 0.15s;
      font-weight: 500;
      border: 1px solid rgba(226, 232, 240, 0.8);
      backdrop-filter: blur(2px);
    }
    .doc-links-container a:hover {
      background: rgba(255, 255, 255, 0.95);
      color: #334155;
      transform: translateY(-1px);
      box-shadow: 0 2px 6px rgba(0,0,0,0.1);
      border-color: rgba(203, 213, 225, 0.9);
    }
    .doc-links-container a.json-link {
      background: #dbeafe;
      color: #1d4ed8;
    }
    .doc-links-container a.json-link:hover {
      background: #bfdbfe;
      color: #1e40af;
      border-color: #93c5fd;
    }
    .doc-links-container a.external-link {
      background: #ecfdf5;
      color: #059669;
    }
    .doc-links-container a.external-link:hover {
      background: #d1fae5;
      color: #047857;
      border-color: #a7f3d0;
    }
    .doc-links-container a.ai-link {
      background: #fdf4ff;
      color: #7c3aed;
    }
    .doc-links-container a.ai-link:hover {
      background: #f3e8ff;
      color: #6d28d9;
      border-color: #c4b5fd;
    }
    .swagger-ui .info {
      display: none;
    }
    /* Swagger UI 内容样式 */
    #swagger-ui {
      padding-top: 0;
    }
    /* 状态信息样式 */
    .custom-info .links-section h4 {
      margin: 0 0 6px 0;
      font-size: 13px;
      color: #64748b;
      font-weight: 500;
    }
    .custom-info .links-section a {
      font-size: 13px;
      padding: 4px 8px;
      background: #f1f5f9;
      color: #475569;
      border-radius: 4px;
      text-decoration: none;
      transition: all 0.15s;
    }
    .custom-info .links-section a:hover {
      background: #e2e8f0;
      color: #334155;
    }

    /* 响应式设计 - 小窗口适配 */
    @media (max-width: 768px) {
      .custom-info {
        min-height: auto;
        padding: 6px 12px;
        flex-direction: column;
        gap: 6px;
        align-items: stretch;
      }
      .custom-info .main-content {
        width: 100%;
        text-align: center;
      }
      .custom-info h1 {
        font-size: 16px;
        margin-bottom: 2px;
      }
      .custom-info .description {
        font-size: 11px;
        margin-bottom: 4px;
      }
      .doc-links-container {
        min-width: unset;
        align-items: center;
        gap: 4px;
        flex-direction: column;
      }
      .doc-links-container .links-section {
        flex-direction: row;
        gap: 3px;
        justify-content: center;
        flex-wrap: wrap;
      }
      .doc-links-container a {
        font-size: 10px;
        padding: 2px 4px;
        gap: 2px;
      }
      .module-selector,
      .json-selector {
        justify-content: center;
        gap: 4px;
      }
      .module-selector select,
      .json-selector select {
        min-width: 120px;
        font-size: 10px;
        padding: 3px 6px;
      }
      .module-selector label,
      .json-selector label {
        font-size: 10px;
      }
    }
    /* 下拉框样式 - 行内自适应 */
    .module-selector,
    .json-selector {
      display: flex;
      gap: 6px;
      align-items: center;
      flex-wrap: wrap;
      margin: 0;
      padding: 0;
      background: transparent;
      border: none;
      flex-shrink: 1;
    }
    .module-selector label,
    .json-selector label {
      display: inline-flex;
      align-items: center;
      gap: 2px;
      margin: 0;
      font-weight: 500;
      color: #475569;
      font-size: 12px;
      white-space: nowrap;
    }
    .module-selector select,
    .json-selector select {
      min-width: 140px;
      padding: 4px 8px;
      border: 1px solid rgba(203, 213, 225, 0.8);
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.8);
      font-size: 12px;
      color: #475569;
      cursor: pointer;
      transition: all 0.15s;
      font-weight: 500;
      backdrop-filter: blur(2px);
    }
    .module-selector select:hover,
    .json-selector select:hover {
      background: #e2e8f0;
      border-color: #94a3b8;
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0,0,0,0.08);
    }
    .module-selector select:focus,
    .json-selector select:focus {
      outline: none;
      background: #dbeafe;
      border-color: #3b82f6;
      color: #1d4ed8;
      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
    }
    .module-selector select option,
    .json-selector select option {
      background: white;
      color: #374151;
      padding: 8px 12px;
    }
    .module-selector select option:hover,
    .json-selector select option:hover {
      background: #f3f4f6;
    }
    /* 响应式处理 */
    @media (max-width: 768px) {
      .module-selector,
      .json-selector {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
      }
      .module-selector label,
      .json-selector label {
        justify-content: center;
      }
      .module-selector select,
      .json-selector select {
        min-width: unset;
        width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="custom-info">
    <div class="main-content">
      <h1>${currentSpec.info.title}</h1>
      <div class="description">${currentSpec.info.description}</div>
      ${controller ? `
      <div class="links-section">
        <h4 style="margin: 8px 0; color: #374151; font-size: 14px;">🎯 当前控制器: ${controller}</h4>
        <a href="/api-docs${module ? `/${module}` : ''}">↩️ ${module ? `返回 ${module} 模块` : '查看全部接口'}</a>
      </div>
      ` : ''}
    </div>
    <div class="doc-links-container">
      <div class="links-section">
        <a href="https://github.com/OAI/OpenAPI-Specification/blob/3.0.1/versions/3.0.1.md" target="_blank" class="external-link">
          🌐 OpenAPI 3.0 规范
        </a>
        <a href="/api-doc/markdown/README.md" target="_blank">
          📚 Swagger 使用指南
        </a>
        <a href="/api-doc/markdown/前端AI接口开发约定.md" target="_blank" class="ai-link">
          🤖 前端 AI 接口开发约定
        </a>
      </div>
      <div class="links-section">
        <div class="json-selector">
          <label for="json-select">🔧 OpenAPI JSON：</label>
          <select id="json-select" onchange="openJsonUrl(this.value)">
            <option value="/api-docs/swagger.json">-- 选择模块 --</option>
            <option value="/api-docs/swagger.json">完整 API</option>
            ${availableModules.map(mod => `
              <option value="/api-docs/swagger.json/${mod.name}">${mod.label}</option>
            `).join('')}
          </select>
        </div>
        ${!module && !controller ? `
        <div class="module-selector">
          <label for="module-select">📂 选择模块浏览：</label>
          <select id="module-select" onchange="navigateToModule(this.value)">
            <option value="">-- 选择模块 --</option>
            ${availableModules.map(mod => `
              <option value="${mod.name}" title="${mod.description}">${mod.label}</option>
            `).join('')}
          </select>
        </div>
        ` : ''}
        ${module && !controller ? `
        <div class="module-selector">
          <label for="module-select">📂 切换到其他模块：</label>
          <select id="module-select" onchange="navigateToModule(this.value)">
            <option value="">-- 选择模块 --</option>
            <option value="">📄 完整接口文档</option>
            ${availableModules.map(mod => `
              <option value="${mod.name}" ${mod.name === module ? 'selected' : ''} title="${mod.description}">${mod.label}</option>
            `).join('')}
          </select>
        </div>
        ` : ''}
      </div>
    </div>
  </div>
  <div id="swagger-ui">
</div>
  <script src="/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui-bundle.js"></script>
  <script src="/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui-standalone-preset.js"></script>
  <script>
    // 模块导航函数
    function navigateToModule(moduleName) {
      if (moduleName) {
        window.location.href = '/api-docs/' + moduleName;
      } else {
        // 空值表示选择了"完整接口文档"
        window.location.href = '/api-docs';
      }
    }

    // JSON 选择器导航函数
    function openJsonUrl(jsonUrl) {
      if (jsonUrl) {
        window.open(jsonUrl, '_blank');
      }
    }

    window.onload = function() {
      // 构建带有过滤参数的 JSON URL
      let jsonUrl = "/api-docs/swagger.json";
      const params = new URLSearchParams();
      ${module ? `params.append('module', '${module}');` : ''}
      ${controller ? `params.append('controller', '${controller}');` : ''}
      if (params.toString()) {
        jsonUrl += '?' + params.toString();
      }

      window.ui = SwaggerUIBundle({
        url: jsonUrl,
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        docExpansion: '${uiOptions.docExpansion || 'list'}',
        defaultModelsExpandDepth: ${uiOptions.defaultModelsExpandDepth !== undefined ? uiOptions.defaultModelsExpandDepth : -1},
        persistAuthorization: ${uiOptions.persistAuthorization !== undefined ? uiOptions.persistAuthorization : true},
        tryItOutEnabled: ${uiOptions.tryItOutEnabled !== undefined ? uiOptions.tryItOutEnabled : true},
        displayRequestDuration: ${uiOptions.displayRequestDuration !== undefined ? uiOptions.displayRequestDuration : true},
        filter: ${uiOptions.filter !== undefined ? uiOptions.filter : true},
        tagsSorter: ${uiOptions.tagsSorter !== undefined ? `'${uiOptions.tagsSorter}'` : 'false'},
        operationsSorter: '${uiOptions.operationsSorter || 'alpha'}'
      });
    };
  </script>
</body>
</html>
        `;
        ctx.type = 'html';
        ctx.body = html;
      });

      // 静态资源路由
      router.get('/plugins/swagger/static/(.*)', async (ctx) => {
        const filePath = path.join(__dirname, '../..', 'static', ctx.params[0]);

        if (fs.existsSync(filePath)) {
          const ext = path.extname(filePath).toLowerCase();
          const contentTypes = {
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml'
          };

          ctx.type = contentTypes[ext] || 'application/octet-stream';
          ctx.body = fs.readFileSync(filePath);
        } else {
          ctx.status = 404;
        }
      });

      // Markdown 文档路由
      router.get('/api-doc/markdown/:filename', async (ctx) => {
        try {
          const filename = ctx.params.filename;
          const allowedFiles = ['README.md', '前端AI接口开发约定.md', '前端AI接口开发约定.md', '技术规范与实现.md', '自定义接口注释指南.md'];

          if (!allowedFiles.includes(filename)) {
            ctx.status = 404;
            ctx.body = 'File not found';
            return;
          }

          // 在插件根目录查找文档
          let filePath = path.join(__dirname, '../..', filename);

          if (!fs.existsSync(filePath)) {
            ctx.status = 404;
            ctx.body = 'File not found';
            return;
          }

          const markdown = fs.readFileSync(filePath, 'utf8');
          const html = marked.parse(markdown);

          const template = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${filename.replace('.md', '')}</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
      background: #f5f5f5;
    }
    .container {
      background: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    h1, h2, h3, h4, h5, h6 {
      color: #2c3e50;
      margin-top: 24px;
      margin-bottom: 16px;
    }
    h1 { border-bottom: 2px solid #e1e4e8; padding-bottom: 10px; }
    h2 { border-bottom: 1px solid #e1e4e8; padding-bottom: 8px; }
    code {
      background: #f6f8fa;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
      font-size: 0.9em;
    }
    pre {
      background: #f6f8fa;
      padding: 16px;
      border-radius: 6px;
      overflow-x: auto;
      line-height: 1.45;
      position: relative;
    }
    pre code {
      background: none;
      padding: 0;
    }
    .copy-button {
      position: absolute;
      top: 8px;
      right: 8px;
      padding: 4px 12px;
      background: #fff;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      color: #374151;
      transition: all 0.2s;
    }
    .copy-button:hover {
      background: #f3f4f6;
      border-color: #9ca3af;
    }
    .copy-button.copied {
      background: #10b981;
      color: white;
      border-color: #10b981;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin: 16px 0;
    }
    table th, table td {
      border: 1px solid #ddd;
      padding: 8px 12px;
      text-align: left;
    }
    table th {
      background: #f6f8fa;
      font-weight: 600;
    }
    table tr:nth-child(even) {
      background: #f9f9f9;
    }
    a {
      color: #0066cc;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    blockquote {
      border-left: 4px solid #ddd;
      margin: 16px 0;
      padding: 0 16px;
      color: #666;
    }
    .back-link {
      display: inline-block;
      margin-bottom: 20px;
      color: #0066cc;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
    ul, ol {
      padding-left: 30px;
      margin: 16px 0;
    }
    li {
      margin: 8px 0;
    }
  </style>
</head>
<body>
  <div class="container">
    <a href="/api-docs" class="back-link">← 返回 API 文档</a>
    ${html}
  </div>
  <script>
    // 为所有代码块添加复制按钮
    document.addEventListener('DOMContentLoaded', function() {
      const codeBlocks = document.querySelectorAll('pre');

      codeBlocks.forEach(function(block) {
        // 创建复制按钮
        const button = document.createElement('button');
        button.className = 'copy-button';
        button.textContent = '复制';

        // 添加点击事件
        button.addEventListener('click', function() {
          const code = block.querySelector('code');
          const text = code ? code.textContent : block.textContent;

          // 复制到剪贴板
          navigator.clipboard.writeText(text).then(function() {
            // 显示复制成功
            button.textContent = '已复制!';
            button.classList.add('copied');

            // 2秒后恢复
            setTimeout(function() {
              button.textContent = '复制';
              button.classList.remove('copied');
            }, 2000);
          }).catch(function(err) {
            console.error('复制失败:', err);
            button.textContent = '复制失败';
            setTimeout(function() {
              button.textContent = '复制';
            }, 2000);
          });
        });

        // 将按钮添加到代码块
        block.appendChild(button);
      });
    });
  </script>
</body>
</html>`;

          ctx.type = 'html';
          ctx.body = template;
        } catch (error) {
          ctx.status = 500;
          ctx.body = 'Internal server error';
          strapi.log.error('Error rendering markdown:', error);
        }
      });

      // 只在开发环境启用文档
      if (process.env.NODE_ENV !== 'production' || process.env.ENABLE_API_DOCS === 'true') {
        strapi.app.use(router.routes()).use(router.allowedMethods());

        strapi.log.info('📚 API 文档已启用');
        strapi.log.info(`📖 访问地址: http://localhost:${strapi.config.server.port}/api-docs`);
      }
    }
  };
};
