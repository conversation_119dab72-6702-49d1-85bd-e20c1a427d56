const { preparedBiddingProjectAnalysis } = require("../services/bidding-s1-handle");
module.exports = {
  kind: "collectionType",
  collectionName: strapi.config.server.mingdaoConfig.biddingProjectId,
  connection: "mingdao",
  info: {
    name: "BiddingProject",
    label: "招标信息",
    description: "",
  },
  options: {},
  pluginOptions: {},
  lifecycles: {
    async afterCreate(...args) {
      let resultArr = await strapi.query("bidding-project-analysis").find({
        _where: {
          biddingProjectId_in: args[0].id
        }
      })
      if (resultArr.length > 0) {
        return;
      }
      const analysisObj = preparedBiddingProjectAnalysis(args[0])
      const tmpResult = await strapi.query("bidding-project-analysis").create(analysisObj)
      console.log(tmpResult)
    },
    beforeCreate(...args) {
      console.log('Model Lifecycle beforeCreate', ...args)
    },
  },
  attributes: {
    manager: {
      // label: '业务经理',
      ref: "67693cb69e13a09bfff3bd2d",
    },
    facilitator: {
      // label: '售前/商务协同',
      ref: "67693cb69e13a09bfff3bd2f",
    },
    bidder: {
      // label: '投标人',
      ref: "677768759e13a09bfff71fcf",
    },
    title: {
      ref: "676938269e13a09bfff3ba98",
    },
    buyer: {
      ref: "676b8a9d9e13a09bfff40e51",
    },
    publishTime: {
      ref: "676b8a9d9e13a09bfff40e52",
    },
    budget: {
      ref: "67693cb69e13a09bfff3bd26",
    },
    type: {
      ref: "677f3e7c9e13a09bfff926ed",
    },
    subtype: {
      ref: "67693cb69e13a09bfff3bd25",
    },
    area: {
      ref: "676e06a99e13a09bfff48e02",
    },
    originalUrl: {
      ref: "676938269e13a09bfff3ba99",
    },
    jianyuUrl: {
      ref: "676e19b69e13a09bfff498f1",
    },
    jianyuPageId: {
      ref: "6771f88a9e13a09bfff4f10c",
    },
    applyDeadline: {
      ref: "67693cb69e13a09bfff3bd21",
    },
    applyVia: {
      ref: "67693cb69e13a09bfff3bd22",
    },
    caseRemark: {
      ref: "676bb4739e13a09bfff417f7",
    },
    filterTag: {
      ref: "677a4a009e13a09bfff77b2f",
    },
    no: {
      ref: "676bc1a39e13a09bfff4226a",
    },
    operator: {
      // label: '修改操作人',
      ref: "67809ab09e13a09bfff9e8c2",
    },
    bidAmount: {
      //中标金额
      ref: "676d2f749e13a09bfff4772a",
    },
    winner: {
      //中标单位
      ref: "6778dd869e13a09bfff76ac3",
    },
    keywords: {
      //关键词
      ref: "6781018d9e13a09bfffa2dbd",
    },
    concern: {
      // 关注
      ref: "6785bee19e13a09bfffb3403",
    },
    ignore: {
      // 忽略
      ref: "6785dfaf9e13a09bfffb4d54",
    },
    readTime: {
      // 已读时间
      ref: "67810aea9e13a09bfffa2f0a",
    },
    industry: {
      // 行业
      ref: "67889eb39e13a09bfffca98c",
    },
    preprocessed: {
      // 已预处理
      ref: "67ab047d9e13a09bff053a78"
    }
  },
};
