const axios = require('axios')
const _ = require('lodash');
const Payment = require('./wx-pay-kit')

// 缓存有效时长
const cacheExpirationTime = 60 * 60 * 1000
// 缓存数据
let configCachedData = null
// 缓存失效时间
let cacheExpiration = null

async function getPayment(mchid, key) {
    let paymentConfigList = await getPaymentConfigList(true);
    if (mchid && key && !paymentConfigList.find(e => e.mchid === mchid && e.accessKeys.find(ak => ak.status && ak.key === key))) {
        throw new Error('商户号不匹配');
    } else if (!paymentConfigList.find(e => e.mchid === mchid)) {
        throw new Error('商户号不存在');
    }
    const paymentConfig = paymentConfigList.find(e => e.mchid === mchid);
    return new Payment(paymentConfig)
}

async function getPaymentConfigList(needKey = false) {
    let paymentConfigList;
    if (configCachedData && cacheExpiration && cacheExpiration > Date.now()) {
        paymentConfigList = configCachedData
    } else {
        try {
            paymentConfigList = await _getAllPaymentConfig()
            configCachedData = paymentConfigList
            cacheExpiration = Date.now() + cacheExpirationTime
        } catch (e) {
            if (!configCachedData) throw e
            // 获取出错暂时使用缓存数据
            cacheExpiration = Date.now() + cacheExpirationTime
            paymentConfigList = configCachedData
            strapi.log.warn(`远程获取微信支付配置失败，使用缓存商户数据`)
        }
    }
    if (!needKey) paymentConfigList = paymentConfigList.map(e => _.omit(e, accessKeys));
    return paymentConfigList;
}

async function _getAllPaymentConfig() {
    let res = await axios.get(`${strapi.config.server.wlyConfig.url}/pay-wechat/publicFind`, {
        headers: {
            'access-key': strapi.config.server.wlyConfig.adminAccessKey
        }
    })
    let list = [];
    const wechatPayConfigList = res.data
    if (_.isEmpty(wechatPayConfigList)) {
        throw new Error('支付配置信息获取失败')
    }

    for (const config of wechatPayConfigList) {
        // 获取微信支付配置
        const mchid = config.merchantId
        const appid = config.appId
        const serial_no = config.serial_no
        const apiv3_private_key = config.apiKey
        const keyPemRes = await axios.get(config.keyPem.url)
        const notify_url = `${strapi.config.server.payment.wechatPay.pay_notify_url}/${mchid}`
        const private_key = keyPemRes.data
        list.push({
            merchantName: config.merchantName,
            mchid,
            appid,
            private_key,
            serial_no,
            notify_url,
            apiv3_private_key,
            accessKeys: config.accessKeys,
        })
    }
    return list;
}

module.exports = {
    getPayment,
    getPaymentConfigList,
}
