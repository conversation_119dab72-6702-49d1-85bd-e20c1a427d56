const { createDefaultRoutes } = require('accel-utils')

module.exports = {
    'routes': [
        ...createDefaultRoutes({
            basePath: '/cash-quans',
            controller: 'cash-quan'
        }),
        {
            'method': 'GET',
            'path': '/agent-payments/pay-list',
            'handler': 'agent-payment.getWxPayConfigList',
            'config': {
                'policies': [], 'prefix': '',
            }
        },
        {
            'method': 'POST',
            'path': '/pay-native',
            'handler': 'agent-payment.payNative',
            'config': {
                'policies': [], 'prefix': '',
            }
        },
        {
            'method': 'GET',
            'path': '/mock/forward',
            'handler': 'agent-payment.forwardBossApi',
            'config': {
                'policies': [], 'prefix': '',
            }
        },
        ...createDefaultRoutes({
            basePath: '/agent-payments',
            controller: 'agent-payment'
        }),
    ]
}
