module.exports = {
  collectionName: 'app-updates',
  info: {
    name: 'AppUpdate',
    label: '应用更新',
    description: '应用更新记录'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    app: {
      label: '应用',
      model: 'app',
      plugin: 'users-permissions',
      required: true
    },
    version: {
      label: '版本号',
      type: 'string',
      required: true
    },
    releaseDate: {
      label: '发布日期',
      type: 'datetime',
      required: true
    },
    description: {
      label: '更新描述',
      type: 'richtext',
      required: true
    },
    downloadUrl: {
      label: '下载地址',
      type: 'string',
      required: true
    },
    pageUrl: {
      label: '下载页面地址',
      type: 'string',
    },
    fileSize: {
      label: '文件大小',
      type: 'number',
      required: true
    },
    isMandatory: {
      label: '是否强制更新',
      type: 'boolean',
      default: false
    },
    platform: {
      label: '平台',
      type: 'string',
      required: true,
      options: [
        { label: 'iOS', value: 'ios' },
        { label: 'Android', value: 'android' },
        { label: 'Mac(Intel芯片)', value: 'mac' },
        { label: 'Mac(M1芯片)', value: 'mac-arm64' },
        { label: 'Windows', value: 'win' }
      ]
    },
    status: {
      label: '状态',
      type: 'string',
      required: true,
      options: [
        { label: '待发布', value: 'pending' },
        { label: '已发布', value: 'published' },
        { label: '已下架', value: 'unpublished' }
      ]
    }
  }
};
