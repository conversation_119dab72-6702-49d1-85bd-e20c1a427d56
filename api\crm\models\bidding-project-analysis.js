module.exports ={
  "collectionName": "bidding-project-analysis",
  "kind": "collectionType",
  "info": {
    "name": "bidding-project-analysis",
    "label": "招投标信息分析数据集"
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    "biddingProjectId": {
      "label": "明道云招标信息数据id",
      "type": "string"
    },
    "ctime": {
      "label": "创建时间",
      "type": "datetime"
    },
    "title": {
      "label": "招标项目名称",
      "type": "string"
    },
    "buyer": {
      "label": "招标单位",
      "type": "string"
    },
    "publishTime": {
      "label": "发布时间",
      "type": "datetime"
    },
    "budget": {
      "label": "预算(万)",
      "type": "number"
    },
    "type": {
      "label": "公告类型(大类)",
      "type": "string"
    },
    "subtype": {
      "label": "公告类型",
      "type": "string"
    },
    "bidAmount": {
      "label": "中标金额(万)",
      "type": "number"
    },
    "winner": {
      "label": "中标单位",
      "type": "string"
    },
    "area": {
      "label": "区域",
      "type": "string"
    }
  }
}
