# 服务端文档

快速开发平台服务端基于 strapi 开发。主要功能如下

- 数据模型与模型管理
- 基础 Web 开发框架：Model、Service、Controller、Middleware
- 插件机制、扩展机制

## Web 开发框架

- Model：`/*/models`
- Controller：`/*/controllers`
- Service：`/*/middlewares`
- Route：`/*/services`

## 基础插件 - 用户权限

### 视图、权限、功能

- Page：页面
  - 一个角色有多个页面
  - 一个页面包含一组视图配置
    - View：视图
      - 一个页面有多个视图
      - 一个角色有多个视图
- Permission：接口（控制器）权限
- Function：功能
  - 一个功能由一组 Page 与 Permission 组成

### 用户、角色、租户

- Role：角色
  - 一个角色有多个功能
    - 一个功能有多个 Page 与 Permission
  - 一个角色有一组 Permission
    - 自定义 Permission
    - 基于功能生成的 Permission
- Branch：租户
  - 租户配置
- User：用户
  - 一个用户有多个角色
  - 一个用户有一个当前角色
  - 一个用户可关联多个租户
  - 一个用户有一个当前租户
  - 针对每个租户，用户有不同的角色与当前角色

## **安装与启动**

### **初始化项目与 Git 仓库**

```bash
# 初始化
bash init-project.sh
# 初始化并推送到指定远程仓库
bash init-project.sh GIT_ORIGIN_URL
```

功能模块与插件默认使用 Git 子模块的机制进行管理。以子模块形式引入项目中，在维护依赖项目会相对便捷。传统的 npm 包发布管理机制在这种场景下操作非常繁琐。

### 安装依赖

```bash
npm install
```

子模块拉取使用 `npm script preinstall` 机制，会在每次安装依赖的时候自动执行

### 启动服务

```bash
# 本地开发模式启动 - 默认端口 3015
npm run develop-local-start

# 本地开发模式启动 - 指定端口
cross-env NODE_ENV=8000 npm run develop-local-start

# 测试模式启动
npm run develop-test-start
```

自动生成的超级管理员账号

- 账号: <EMAIL>
- 密码: WD123!@#

## 代码示例

具体代码实现可参考模板项目提供的 Demo

### 数据模型与接口  `api/blog/*`

- 配置模型 `api/blog/models/book.settings.json`

    ```json
    {
      "kind": "collectionType",
      "collectionName": "articles",
      "info": {
        "name": "Article",
        "description": "一本书",
        "label": "图书"
      },
      "options": {
        "increments": true,
        "timestamps": true,
        "draftAndPublish": true,
        "defaultMainField": "name"
      },
      "pluginOptions": {},
      "attributes": {
        "name": {
          "label": "名称",
          "type": "string",
          "required": true,
        },
        "description": {
          "label": "描述",
          "type": "string"
        },
        "createdBy": {
          "label": "创建用户",
          "plugin": "users-permissions",
          "model": "user"
        },
      }
    }
    ```

- 编写控制器代码 `api/book/controllers/book.js`

    ```jsx
    'use strict'
    const { CurdRouter } = require('accel-utils')
    
    const bookCurdRouter = new (class extends CurdRouter {
      constructor (modelName) {
        super(modelName)
      }
    })('book')
    
    module.exports = {
      ...bookCurdRouter.createHandlers()
    }
    ```

- 编写路由代码 `api/book/config/routes.js`

    ```jsx
    const { createDefaultRoutes } = require('accel-utils')
    
    // 图书
    const goodRoutes = [
      ...createDefaultRoutes({
        controller: 'book',
        basePath: '/books',
      }),
    ]
    ```


### 视图与权限配置 `config/*`

配置权限  `config/permission.js`
