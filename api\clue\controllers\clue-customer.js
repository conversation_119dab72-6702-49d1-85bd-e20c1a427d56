const { CurdRouter } = require('accel-utils')
const { getBaseQuery, } = require('../../crm/services/customer-service')
const axios = require('axios')
const curdRouter = new (class extends CurdRouter {

  _getQueryByUser(query, user) {
    // 公共资源
    // 1. 阅卷付费标签：体验校、应续校、流失校
    // 2. 阅卷产品的授权：无，或均已过期（旧版授权 + 新版授权的产品=阅卷）
    // 3. 归属单元为空 && 运营小组为空 && 直营小组为空 && 运营经理为空 && 直营经理为空
    // 4. 销售：仅限已分配的省份；运营：不限
    // 5. 学校类型 =/= 单次联考
    query.unit_null = true
    query.directServiceTeam_null = true
    query.directSalesTeam_null = true
    query.directServiceManager_null = true
    query.directSalesManager_null = true
    query.crm_type_ne = '13cc0c2a-c4a7-4f35-ad4e-08c1ecc430b7' // 单次联考
    query.yjTag_ne = 'eb35b0cb-3d12-4598-a7da-1c712fd05027' // 付费校
    query._where = {
      _or: [
        { authEndTime_lt: new Date() },
        { authEndTime_null: true }
      ]
    };
    if (['sales-manager', 'sales-group-leader', 'sales-group-member',].includes(user.role.type)) {
      query._where.province_in = user.provinces || []
    }

    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.find(ctx)
  }
})('clue-customer')

module.exports = {
  ...curdRouter.createHandlers(),
}
