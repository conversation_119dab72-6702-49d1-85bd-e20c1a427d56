{"collectionName": "workflow", "info": {"name": "Workflow", "label": "工作流", "description": "工作流管理"}, "options": {"draftAndPublish": true, "timestamps": true, "allOf": [{"if": {"properties": {"type": {"const": "event"}}}, "then": {"properties": {"url": {"visible": false}, "inputParam": {"visible": false}, "queryParam": {"visible": false}, "timerParam": {"visible": false}}}, "else": {}}, {"if": {"properties": {"type": {"const": "webhook"}}}, "then": {"properties": {"triggerParam": {"visible": false}, "timerParam": {"visible": false}}}, "else": {}}, {"if": {"properties": {"type": {"const": "time"}}}, "then": {"properties": {"url": {"visible": false}, "api": {"visible": false}, "inputParam": {"visible": false}, "queryParam": {"visible": false}}}, "else": {}}]}, "pluginOptions": {}, "attributes": {"title": {"label": "标题", "type": "string"}, "description": {"label": "描述", "type": "text"}, "url": {"label": "webhook url", "editable": false, "type": "string", "default": "/workflows/webhook/:id"}, "type": {"label": "类型", "type": "string", "options": [{"value": "time", "label": "日期触发"}, {"value": "event", "label": "字段触发"}, {"value": "webhook", "label": "webhook"}]}, "api": {"label": "关联api", "model": "api"}, "model": {"label": "关联model", "type": "string"}, "timerParam": {"label": "日期触发字段", "type": "string"}, "inputParam": {"label": "输入参数", "type": "json", "jsonSchema": {"title": "参数对象列表", "type": "array", "items": {"title": "参数对象", "type": "object", "properties": {"key": {"title": "key", "type": "string"}, "default": {"title": "默认值", "type": "string"}, "userValue": {"title": "使用当前用户值", "type": "string"}, "required": {"title": "是否必传", "type": "boolean", "default": false, "options": [{"value": false, "label": "否"}, {"value": true, "label": "是"}]}, "description": {"title": "描述", "type": "string"}}}}}, "triggerParam": {"label": "触发条件", "type": "json", "jsonSchema": {"title": "触发条件列表", "type": "array", "items": {"title": "触发条件", "type": "object", "properties": {"key": {"title": "key", "type": "string"}, "value": {"title": "value", "type": "string"}, "valueType": {"title": "字段值类型", "type": "string", "options": [{"value": "string", "label": "字符串类型"}, {"value": "number", "label": "数字类型"}, {"value": "boolean", "label": "布尔类型"}]}, "type": {"title": "类型", "type": "string", "options": [{"value": "$eq", "label": "等于"}, {"value": "$ne", "label": "不等于"}, {"value": "$gt", "label": "大于"}, {"value": "$gte", "label": "大于等于"}, {"value": "$lt", "label": "小于"}, {"value": "$lte", "label": "小于等于"}, {"value": "$in", "label": "包含"}, {"value": "$nin", "label": "不包含"}]}, "description": {"title": "描述", "type": "string"}}}}}, "queryParam": {"label": "关联模型数据查询条件", "type": "json", "jsonSchema": {"title": "关联模型数据查询条件列表", "type": "array", "items": {"title": "查询条件", "type": "object", "properties": {"key": {"title": "模型字段", "type": "string"}, "reqKey": {"title": "传入字段", "type": "string"}, "description": {"title": "描述", "type": "string"}}}}}, "updateParam": {"label": "更新字段", "type": "json", "jsonSchema": {"title": "更新字段列表", "type": "array", "items": {"title": "更新字段", "type": "object", "properties": {"type": {"title": "类型", "type": "string", "options": [{"value": "fixed", "label": "固定值"}, {"value": "pass", "label": "传入字段"}, {"value": "expression", "label": "表达式"}]}, "key": {"title": "模型字段", "type": "string"}, "value": {"title": "字段值", "type": "string"}, "valueType": {"title": "字段值类型", "type": "string", "options": [{"value": "string", "label": "字符串类型"}, {"value": "number", "label": "数字类型"}, {"value": "boolean", "label": "布尔类型"}]}, "reqKey": {"title": "传入字段", "type": "string"}, "description": {"title": "描述", "type": "string"}}}}}}}