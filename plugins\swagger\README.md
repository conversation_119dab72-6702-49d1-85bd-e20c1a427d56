# Swagger API 插件文档

基于 OpenAPI 3.0 规范的 Strapi 插件，自动生成 RESTful API 文档。

## 功能特性

- 🚀 **零配置使用** - 自动扫描标准 CRUD 接口并生成文档
- 🔐 **权限感知** - 自动识别公开接口和需认证接口
- 📝 **注释支持** - 支持 JSDoc @swagger 注释自定义文档
- 🏷️ **智能标签** - 自动分组和中文标签映射
- 🎨 **界面友好** - 内置 Swagger UI，支持在线测试
- 🔄 **热重载** - 开发环境下配置修改即时生效

## 快速开始

### 1. 集成插件

使用 Git 子模块方式集成插件：

```bash
git <NAME_EMAIL>:wly/strapi-swagger.git plugins/swagger
```

插件将被添加到 `/plugins/swagger` 目录。

### 2. 配置中间件

在主项目 `config/middleware.js` 中确保插件已启用：

```javascript
module.exports = {
  settings: {
    // 其他中间件配置...
    swagger: {
      enabled: true
    }
  }
};
```

### 3. 配置 Swagger

在项目根目录创建 `config/swagger.js` 配置文件：

```javascript
module.exports = {
  // Swagger 文档定义
  definition: {
    info: {
      title: '我的 API 文档',
      version: '1.0.0',
      description: '基于 Strapi 的 API 服务'
    },
    servers: [
      {
        url: 'http://localhost:3015',
        description: '开发环境'
      },
      {
        url: 'https://api.myapp.com',
        description: '生产环境'
      }
    ]
  },

  // API 模块标签映射（控制器名称 -> 中文名称）
  defaultTags: {
    'user': {
      name: '用户管理',
      description: '用户相关接口'
    },
    'product': {
      name: '产品管理',
      description: '产品相关接口'
    }
  },

  // 接口文档生成过滤配置
  ignore: {
    // 忽略的控制器列表（支持字符串精确匹配和正则表达式）
    controllers: [
      // 忽略部门相关的控制器
      "idsp-department",
      "db-cache",
      // 示例：忽略所有test开头的控制器
      // /^growth-.*/,

      // 示例：忽略特定控制器
      // 'internal-api',
      // 'debug-controller'
    ],

    // 忽略的路径列表（支持字符串精确匹配和正则表达式）
    paths: [
      // 示例：忽略所有内部接口
      // /^\/internal\/.*/,
      // 示例：忽略调试接口
      // /^\/debug\/.*/,
      // 示例：忽略特定路径
      // '/health-check',
      // '/metrics'
    ],
    // 忽略的模型列表（用于Schema生成，支持字符串精确匹配和正则表达式）
    models: [
      // 示例：忽略系统内部模型
      // 'core_store',
      // 'strapi_permission',
      // 'strapi_role',
      // "tags"

      // 示例：忽略测试模型
      // /^test-.*/,

      // 示例：忽略临时模型
      // /temp$/
    ]
  },
  // 特殊路径标签映射
  pathTags: {
    '/api/upload': {
      name: '文件上传',
      description: '文件上传相关接口'
    }
  },

  // Swagger UI 配置
  ui: {
    title: 'API 文档',
    options: {
      docExpansion: 'none',           // 默认折叠: 'list', 'full', 'none'
      defaultModelsExpandDepth: -1,   // 隐藏模型定义
      persistAuthorization: true,     // 记住认证信息
      tryItOutEnabled: true,          // 启用在线测试
      filter: true,                   // 启用搜索过滤
      tagsSorter: 'alpha',           // 标签排序方式
      operationsSorter: 'alpha'      // 接口排序方式
    }
  }
};
```

### 4. 启动并查看文档

启动项目后访问：

```bash
# 启动开发服务器
npm run dev

# 访问 Swagger UI
http://localhost:3015/api-docs

# 获取 OpenAPI JSON
http://localhost:3015/api-docs/swagger.json
```

## 使用指南

### 标准 CRUD 接口（自动生成）

使用 `createDefaultRoutes` 创建的标准接口会自动生成文档：

```javascript
// api/product/config/routes.js
const { createDefaultRoutes } = require('accel-utils');

module.exports = {
  routes: [
    ...createDefaultRoutes({
      basePath: '/products',
      controller: 'product'
    })
  ]
};
```

自动生成的接口包括：
- `GET /api/products` - 查询列表
- `GET /api/products/:id` - 查询详情
- `POST /api/products` - 创建记录
- `PUT /api/products/:id` - 更新记录
- `DELETE /api/products/:id` - 删除记录
- `GET /api/products/count` - 统计数量

### 自定义接口注释

对于非标准接口，在控制器方法上使用 `@swagger` 注释：

```javascript
/**
 * @swagger
 * /api/products/search:
 *   get:
 *     tags:
 *       - 产品管理
 *     summary: 搜索产品
 *     description: 根据关键词搜索产品
 *     parameters:
 *       - in: query
 *         name: keyword
 *         required: true
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 搜索成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 */
async search(ctx) {
  // 实现搜索逻辑
}
```

### 查询参数说明

系统自动识别的查询参数：

| 参数 | 类型 | 说明 | 示例 |
|------|------|------|------|
| `_limit` | integer | 每页数量 | `_limit=20` |
| `_start` | integer | 偏移量 | `_start=10` |
| `_sort` | string | 排序字段 | `_sort=name:DESC` |
| `_q` | string | 全文搜索 | `_q=关键词` |
| `field` | any | 精确查询 | `status=active` |
| `field_contains` | string | 模糊查询 | `name_contains=张` |
| `field_gte` | number | 大于等于 | `amount_gte=100` |
| `field_lte` | number | 小于等于 | `created_at_lte=2024-01-01` |

## 高级配置

### 扩展无法修改的模块

对于插件或核心模块的接口，使用扩展配置文件。

创建 `config/swagger-extensions.yaml`：

```yaml
# 为 users-permissions 插件添加文档
users-permissions:
  tags:
    - name: 用户认证
      description: 用户认证相关接口
  paths:
    /auth/local:
      post:
        tags: [用户认证]
        summary: 用户登录
        requestBody:
          required: true
          content:
            application/json:
              schema:
                type: object
                properties:
                  identifier:
                    type: string
                  password:
                    type: string
```

或使用 JavaScript 配置 `config/swagger-extensions.js`：

```javascript
module.exports = {
  'custom-plugin': {
    tags: [
      {
        name: '插件功能',
        description: '插件功能相关接口'
      }
    ],
    paths: {
      '/plugin/endpoint': {
        get: {
          tags: ['插件功能'],
          summary: '获取插件数据'
        }
      }
    }
  }
};
```

### 权限配置

在 `config/permission.js` 中未登录分类下的接口会自动识别为公开接口：

```javascript
{
  name: 'PublicFunction',
  apiPermissions: [
    // 单个接口
    { type: 'application', controller: 'product', action: 'search' },
    
    // 整个控制器
    ...createDefaultPermissions({ type: 'application', controller: 'news' })
  ]
}
```

### 环境变量

- `NODE_ENV=production` - 生产环境默认禁用文档
- `ENABLE_API_DOCS=true` - 强制在生产环境启用文档

## 最佳实践

### 1. 完善模型定义

```javascript
// api/product/models/product.js
module.exports = {
  attributes: {
    name: {
      type: 'string',
      required: true,
      label: '产品名称',      // 添加中文标签
      description: '产品的名称' // 添加字段描述
    },
    price: {
      type: 'decimal',
      required: true,
      label: '价格',
      min: 0
    },
    status: {
      type: 'enumeration',
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
      label: '状态'
    }
  }
};
```

### 2. 统一响应格式

```javascript
// 成功响应
ctx.body = {
  code: 0,
  message: '操作成功',
  data: result
};

// 错误响应
ctx.body = {
  code: 400,
  message: '参数错误',
  data: null
};
```

### 3. 合理组织接口

- 使用语义化的控制器名称
- 相关功能放在同一控制器下
- 在 swagger.js 中定义清晰的中文标签

## 接口过滤
  所有的接口文档全部展示，不容易查找，以及相应太多，影响效率，可进行过滤，只生成对应模块以及对应controller的接口文档

**实现的功能**：
- **UI 页面过滤**：`http://localhost:8108/api-docs/{module}` 和 `http://localhost:8108/api-docs/{module}/{controller}`
- **JSON API 过滤**：`http://localhost:8108/api-docs/swagger.json/{module}` 和 `http://localhost:8108/api-docs/swagger.json/{module}/{controller}`
- **动态模块发现**：自动扫描 `api/` 目录下的业务模块和扩展模块
- **下拉选择器**：在文档页面提供模块选择下拉框
- **扩展模块分离**：业务模块和扩展模块严格分离，避免混合显示

## 常见问题

**Q: 文档没有更新？**
1. 检查代码是否有语法错误
2. 重启服务器：`npm run dev`
3. 清除浏览器缓存

**Q: 接口没有显示？**
1. 确认路由使用了标准格式
2. 自定义接口需要添加 @swagger 注释
3. 检查控制器是否正确导出

**Q: 权限标记不正确？**
1. 检查 permission.js 配置
2. 确认控制器和动作名称正确
3. 重启服务生效

## 相关文档

- [技术规范与实现](./技术规范与实现.md) - 深入了解技术细节
- [自定义接口注释指南](./自定义接口注释指南.md) - 详细的注释模板和示例
- [前端 AI 接口开发约定](./前端AI接口开发约定.md) - 前端 AI 开发指南
- [OpenAPI 3.0 规范](https://swagger.io/specification/) - 官方规范文档

## 技术支持

如有问题或建议，请联系技术团队或查看项目 Wiki。
