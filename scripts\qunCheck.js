const { ObjectId } = require('mongodb')
const customerModel = require('../api/crm/models/customer-service')
const qunModel = require('../api/xiaoyun/models/xiaoyun-qun')
const { logger } = require('./utils')

// 更新qun-check
async function updateQunCheck(db) {
  const qunList = await db.collection('xiaoyun-qun').find({ isDeleted: { $ne: true } }).toArray()
  const customers = await db.collection('customer-service').find({ deleted: { $ne: true } }).toArray()
  let qunMap = {}
  for (const qun of qunList) {
    qunMap[qun._id.toString()] = qun
  }
  let insertList = []
  const syncTime = new Date()
  for (const customer of customers) {
    let qunInfo = null
    if (customer.qun) {
      qunInfo = qunMap[customer.qun.toString()]
    }

    let defaultFields = genQunCheckSchool(customer)

    if (qunInfo) {
      Object.assign(defaultFields, genQunCheckQun(qunInfo))
    }
    defaultFields['syncTime'] = syncTime
    insertList.push(defaultFields)
  }

  logger('---- 检查总数据量 ----', insertList.length)
  if (insertList.length > 0) {
    await db.collection('qun-check').deleteMany({})
    const results = await db.collection('qun-check').insertMany(insertList)
    console.log('写入数量', results.insertedCount, results.acknowledged)
  }
}

function genQunCheckSchool(customer) {
  const checkCustomer = {}
  for (const attr in customerModel.attributes) {
    checkCustomer['customer_' + attr] = customer[attr]
  }
  return checkCustomer
}

function genQunCheckQun(qunInfo) {
  const checkQun = {}

  for (const attr in qunModel.attributes) {
    checkQun['qun_' + attr] = qunInfo[attr]
  }
  return checkQun
}


module.exports = {
  updateQunCheck
}
