# 日志插件

提供日志相关功能

### 配置说明

插件配置文件位置 `/config/plugins.js`

```typescript
interface Config {
  // 日志记录排除规则
  ignoreRules?: [
    // Method 匹配
      '*' | 'GET' | 'POST' | 'PUT' | 'PATCH', 'OPTION', 'DELETE',
    // URL 匹配
      '*' | string | RegExp
  ],
    // 日志记录不排除规则
   notIgnoreRules?: [
    // Method 匹配
      '*' | 'GET' | 'POST' | 'PUT' | 'PATCH', 'OPTION', 'DELETE',
    // URL 匹配
      '*' | string | RegExp
  ],
  // 消息通知
  notifyChannel?: 'feishu' | 'wechat',
  feishuWebhookUrl?: string
}
```
