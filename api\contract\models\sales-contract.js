'use strict'
module.exports = {
    kind: 'collectionType',     // 备注
    "collectionName": "contract",
    "info": {
        "name": "Contract",
        "label": "合同",
        "description": "合同管理"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true,
        "groups": [
            {
                "title": "项目基础信息（同步自伙伴云）",
                "fields": [
                    "no",
                    "projectApproval",
                    "offerReviewState",
                    "amoeba", "\n",
                    "projectName", "\n",
                    "partASalesName",
                    "meetingTime",
                    "offerPriceLow"
                ]
            },
        ],
    },
    duplicateCollection: true,
    "pluginOptions": {},
    "attributes": {
        "no": {
            "label": "立项编号",
            "type": "string",
            "size": 3,
            "unique": true,
            "editable": false
        },
        "amoeba": {
            "label": "阿米巴单元",
            "type": "string",
            "editable": false,
            "size": 3
        },
        "projectApproval": {
            "label": "立项审核",
            "type": "string",
            "size": 3,
            "options": [
                {
                    "value": "4",
                    "label": "新提交"
                },
                {
                    "value": "6",
                    "label": "待审批"
                },
                {
                    "value": "1",
                    "label": "立项成功"
                },
                {
                    "value": "3",
                    "label": "待定"
                },
                {
                    "value": "2",
                    "label": "立项失败"
                },
                {
                    "value": "5",
                    "label": "立项过期"
                }
            ],
            "editable": false
        },
        "offerPriceLow": {
            "label": "报价（元）",
            "size": 3,
            "type": "number",
            "editable": false // 只有少数状态下可以修改，默认值设为不可编辑
        },
        "offerReviewState": {
            "label": "方案审批状态",
            "size": 3,
            "type": "string",
            "default": "1",
            "options": [
                {
                    "value": "1",
                    "label": "待发起"
                },
                {
                    "value": "2",
                    "label": "待上会审批"
                },
                {
                    "value": "3",
                    "label": "报价初审通过"
                },
                {
                    "value": "4",
                    "label": "方案待完善"
                },
                {
                    "value": "5",
                    "label": "报价确认，请完善合同条款"
                },
                {
                    "value": "6",
                    "label": "待出合同"
                },
                {
                    "value": "7",
                    "label": "待客户反馈"
                },
                // {
                //     "value": "8",
                //     "label": "待出合同"
                // },
                {
                    "value": "9",
                    "label": "申请变更重审"
                },
                {
                    "value": "10",
                    "label": "方案驳回"
                },
                {
                    "value": "11",
                    "label": "合同文书待审批"
                },
                {
                    "value": "12",
                    "label": "合同审批完毕"
                }
            ],
            "editable": false
        },
        "meetingTime": {
            "label": "计划上会日期",
            "type": "date",
            "size": 3,
            "format": "YYYY-MM-DD"
        },
        "projectName": {
            "label": "项目名称",
            "size": 12,
            "type": "string",
            "editable": false
        },

        "partASalesName": {
            "label": "公司业务经理",
            "type": "string",
            "editable": false,
            "size": 3
        },
        "partASalesWxId": {
            "label": "公司业务经理wxId",
            "type": "string",
            "editable": false,
            // "visible": false,
            "size": 3
        },
        "fileUrls": {
            "label": "crm合同文件",
            "type": "json",
            "jsonSchema": {
                "title": "crm合同文件",
                "type": "array",
                "items": {
                    "title": "crm合同文件",
                    "type": "object",
                    "properties": {
                        "id": {
                            "title": "crm合同文件url",
                            "type": "string"
                        },
                        "name": {
                            "title": "crm合同文件名",
                            "type": "string"
                        },
                    },
                }
            }
        }
    }
}
