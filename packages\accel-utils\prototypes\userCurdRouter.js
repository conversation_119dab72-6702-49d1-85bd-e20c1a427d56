const CurdRouter = require('./curdRouter')
const { parseCtxData } = require('../collection-utils')
const _ = require('lodash')

class UserCurdRouter extends CurdRouter {
  constructor (name, config = {}) {
    super(name, config)
    this.mode = config.mode || 'self'
  }

  _parseUserCtx (ctx) {
    const { model, query, params, data, files } = parseCtxData(ctx)
    let user = ctx.state.user
    if (!user) {
      throw Error('未登录状态无接口权限')
    }
    if (!query._sort && this.defaultSort) {
      query._sort = this.defaultSort
    }
    return {
      user,
      query: {
        ...query,
        user: user.id,
      },
      params: {
        ...params,
        user: user.id,
      },
      data, files,
      model
    }
  }

  async selfFind (ctx) {
    const { query } = this._parseUserCtx(ctx)
    ctx.query = query
    return super.find(ctx)
  }

  async selfCount (ctx) {
    const { query } = this._parseUserCtx(ctx)
    ctx.query = query
    return super.count(ctx)
  }

  async selfFindOne (ctx) {
    const { params } = this._parseUserCtx(ctx)
    ctx.params = params
    return super.findOne(ctx)
  }

  async selfCreate (ctx) {
    const { user, data, files } = this._parseUserCtx(ctx)
    let entity
    // 如果用户存在租户且模型存在 pBranch 字段则附加租户信息
    const model = this._getModel(ctx)
    let branchField = {}
    if (model.attributes.pBranch && user.pBranch?.id) {
      branchField = { pBranch: user.pBranch.id }
    }
    entity = await this._getService(ctx).create({
      data: {
        ...data,
        user: user.id,
        ...branchField
      },
      files
    })
    return this._sanitizeEntity(ctx, entity)
  }

  async selfUpdate (ctx) {
    const { params, data, files } = this._parseUserCtx(ctx)
    const entity = await this._getService(ctx).update({ params, data, files })
    return this._sanitizeEntity(ctx, entity)
  }

  async selfUpdateMany (ctx) {
    const { user } = this._parseUserCtx(ctx)
    const body = ctx.request.body
    if (!_.isObject(body.filter) || !_.isObject(body.data)) {
      return ctx.badRequest('filter is invalid')
    }
    body.filter.user = user.id
    return super.updateMany(ctx)
  }

  async selfDelete (ctx) {
    const { params } = this._parseUserCtx(ctx)
    const entity = await this._getService(ctx).delete({ params })
    return this._sanitizeEntity(ctx, entity)
  }

  async selfDeleteMany (ctx) {
    const { data, query } = this._parseUserCtx(ctx)
    if (!_.isArray(data.filter.id_in)) {
      return ctx.badRequest('filter.id_in is invalid')
    }
    const entity = await this._getService(ctx).deleteMany(
      {
        params: {
          ...data.filter,
          ...query,
        }
      }
    )
    return this._sanitizeEntity(ctx, entity)
  }

  async selfExport (ctx) {
    const { query } = this._parseUserCtx(ctx)
    ctx.query = query
    return super.export(ctx)
  }

  async selfImport (ctx) {
    const { user } = this._parseUserCtx(ctx)
    // 如果用户存在租户且模型存在 pBranch 字段则附加租户信息
    const model = this._getModel(ctx)
    let branchField = {}
    if (model.attributes.pBranch && user.pBranch?.id) {
      branchField = { pBranch: user.pBranch.id }
    }
    ctx._constFields = {
      ...branchField,
      user: user.id
    }
    return super.import(ctx)
  }

  /**
   * @param {string[]} [names=]
   */
  createHandlers (names) {
    if (names) return super.createHandlers(names)
    const defaultNames = [
      'selfFind', 'selfCount', 'selfFindOne',
      'selfCreate', 'selfUpdate', 'selfUpdateMany', 'selfDelete',
      'selfExport', 'selfImport', 'selfDeleteMany',
    ]
    const extendNames = this._getExtendMethodNames()
    return super.createHandlers([
      ...defaultNames,
      ...extendNames,
    ])
  }
}

module.exports = UserCurdRouter
