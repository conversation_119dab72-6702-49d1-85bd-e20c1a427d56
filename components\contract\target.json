{"collectionName": "components_contract_target", "info": {"name": "Target", "label": "SaaS对象", "icon": "comment", "description": ""}, "options": {}, "attributes": {"school": {"label": "学校", "type": "json", "mainField": "showName"}, "goods": {"label": "商品", "mainField": "name", "meta": {"query": {"parentCategory": "SaaS平台", "status": true, "_limit": 200}}, "model": "sale-sku-product"}, "productParam": {"label": "产品参数", "type": "json", "visible": false}, "calculatedUnitPrice": {"label": "原单价", "editable": false, "format": "rmb", "type": "number"}, "unitPrice": {"label": "实际单价", "format": "rmb", "type": "number"}, "quantity": {"label": "数量", "type": "number"}, "totalPrice": {"label": "总金额", "format": "rmb", "type": "number"}, "beginTime": {"label": "开始日期", "type": "date"}, "endTime": {"label": "结束日期", "type": "date"}, "status": {"label": "状态", "type": "string", "size": 3, "editable": false, "options": [{"label": "未开通", "value": "未开通"}, {"label": "开通中", "value": "开通中"}, {"label": "部分开通失败", "value": "部分开通失败"}, {"label": "已开通", "value": "已开通"}]}, "remark": {"label": "备注", "type": "string"}, "failReason": {"label": "开通失败原因", "visible": false, "type": "json"}}}