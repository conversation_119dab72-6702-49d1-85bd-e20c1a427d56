const { CurdRouter } = require('accel-utils')
const _ = require('lodash');
const BOSS_COOKIE = 'BOSSID=eyJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJpeXVueGlhby5jb20iLCJleHAiOjE2OTMzNDI4MDAwMDAsImlhdCI6MTY5MzI3MTc0Nzg2OSwiY2FzX2lkIjoiMjc1NDkyMDIzNDM4NTQwOCIsInVzZXJfaWQiOiI1YjMwNmViODAwMDAwNDZjMWFlYjhhYzIiLCJlbXBsb3llZV9ubyI6IjAzNTk4IiwibmFtZSI6IuW4reS_ium5jyIsInJvbGVzIjpbIuS4muWKoeS4reWPsO-8iOS4k-WRmO-8iSJdLCJkZXBhcnRtZW50IjoiQUnnsr7lh4bnu4MiLCJhY2NvdW50X2lkIjoiNWIzMDZlYjgwMDAwMDQzNjA2MjBmZWZjIiwidmVyc2lvbiI6IjUuMC4wIiwibG9naW5fdXNlcl9pZCI6IjViMzA2ZWI4MDAwMDA0NmMxYWViOGFjMiIsInNpdGUiOiJib3NzIn0.LoYIxUOnykopUgPKpLBy0JLrE_zkzOuc8RfvaYAwW18';
const axios = require('axios');

const curdRouter = new (class extends CurdRouter {
    // ...
    async _getQueryByUser(query, user) {
        // 新经理为本人 没有运营组或没有直营组 的学校
        let roles = user.roles.map(e => e.type);

        let allowRoles = ['admin', 'SuperAdmin', 'CEO', 'sales-admin', 'sales-manager', 'sales-group-leader', 'sales-observer']
        // 直营只能查看直营的数据
        if (_.intersection(roles, allowRoles).length > 0) {
            query.amoeba_contains = '2025~3#直营'
        }
        return query
    }

    async count(ctx) {
        const user = ctx.state.user
        let { query } = this._parseCtx(ctx)
        query = await this._getQueryByUser(query, user)
        return super.count(ctx)
    }

    async find(ctx) {
        const user = ctx.state.user
        let { query } = this._parseCtx(ctx)
        query = await this._getQueryByUser(query, user)
        let contracts = await super.find(ctx)
        const contractNos = contracts.filter(e => e.crmContractId).map(e => e.no)
        const crmContracts = await Promise.all(
          contractNos.map(no => this.getCrmContractByNo(no))
        )
        const fileIds = _.compact(_.flatten(crmContracts.map(e => e?.attachments?.filter(e_a => e_a.id).map(e_a => e_a.id ))))
        let crmFileMap = {}
        if (fileIds.length > 0) {
          crmFileMap = await this.getCrmFileUrlByIds(fileIds)
        }
        for (const contract of contracts) {
          const crmContract = crmContracts.find(e => e?.no === contract.no)
          let fileUrls = []
          crmContract?.attachments?.forEach(e => {
            if (e.id) {
              fileUrls.push({
                id: crmFileMap[e.id],
                name: e.name,
              })
            } else if (e.src) {
              fileUrls.push({
                id: e.src,
                name: e.name,
              })
            }
          })
          contract.fileUrls = fileUrls
        }
        return contracts
    }

    async update(ctx) {
        return ctx.wrapper.error('HANDLE_ERROR', '暂不支持修改')
        // const oldContract = await strapi.query('contract').findOne({ id: data.id })
        // if (data.offerReviewState && oldContract.offerReviewState !== data.offerReviewState) {
        //     return ctx.wrapper.error('HANDLE_ERROR', '源数据已更新，请刷新后重新编辑')
        // }
        // return super.update(ctx)
    }

    async getCrmContractByNo(contractNo) {
      try {
        const bossApiUrl = strapi.config.server.bossApi.url
        const response = await axios.get(
          `${bossApiUrl}/contract/v2/get_by_no?category=1&no=${contractNo}`,
          {
            // params: {
            //   no: contractNo,
            //   category: '1'
            // },
            headers: {
              'cookie': BOSS_COOKIE
            },
            timeout: 10000
          }
        )
    
        return response.data
      } catch (error) {
        console.error('获取合同失败:', error)
        return null
      }
    }
    
     async getCrmFileUrlByIds(ids) {
      try {
        const bossApiUrl = strapi.config.server.bossApi.url
        const response = await axios.post(
          `${bossApiUrl}/storage/get_file_download_url`,
          {
              fileIds: ids,
          },
          {
            headers: {
            'cookie': BOSS_COOKIE
            }
          },
        )
    
        return response.data
      } catch (error) {
        console.error('获取合同失败:', error)
        return null
      }
    }
})('sales-contract')

module.exports = {
    ...curdRouter.createHandlers(),
}

