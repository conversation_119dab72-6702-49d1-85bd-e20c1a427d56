const axios = require('axios');
const path = require('path');
const fs = require("fs");

const huobanConfig = strapi.config.server.huoban;

async function huobanHandler({ data: { code, message, data }, config }) {
    if (code === 0) {
        // console.log(data)
        return data
    }
    throw new Error(message)
}


function request(method, router, data) {
    let config = {
        method: method,
        url: `${huobanConfig.url}/${router}`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(huoban<PERSON>andler)
}


function getTableList() {
    let data = JSON.stringify({
        "space_id": huobanConfig.space_id
    });

    return request('post', 'v1/table/list', data)
}

function getTableConfig(table_id) {
    return request('post', `v1/table/${table_id}`)
}

function getTableItemList(table_id, filter, order = null, offset = 0, limit = 100) {
    let data = JSON.stringify({
        "table_id": table_id,
        "filter": filter,
        "order": order,
        "limit": limit,
        "offset": offset,
        "with_field_config": 0
    });
    return request('post', 'v1/item/list', data)
}


function getProjectList(nos) {
    return getTableItemList(
        huobanConfig.project_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.project_table.fieldsMap['立项编号'], // 订单状态，待发货
                    "query": {
                        "in": nos
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}

function getOneProjectByNo(no) {
    return getTableItemList(
        huobanConfig.project_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.project_table.fieldsMap['立项编号'], // 订单状态，待发货
                    "query": {
                        "eq": no
                    }
                }
            ]
        }
    ).then(function (data) {
        let items = data.items
        if (items && items.length > 0) {
            return items[0]
        }
        return null
    })
}

function getProjectListBySchool(itemIds) {
    return getTableItemList(
        huobanConfig.project_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.project_table.fieldsMap['学校/教育局'],
                    "query": {
                        "in": itemIds
                    }
                },
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}

function getSalesProjectListByTime(time) {
    return getTableItemList(
        huobanConfig.sales_project_table.table_id,
        {
            "or": [
                {
                    "field": huobanConfig.sales_project_table.fieldsMap['签约日期'],
                    "query": {
                        "gte": time
                    }
                },
                {
                    "field": huobanConfig.sales_project_table.fieldsMap['实际回款日期'],
                    "query": {
                        "gte": time
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return []
    })
}



function getTableItem(item_id) {
    return request('post', `v1/item/${item_id}`)
}

/**
 * 根据订单号获取子表信息
 * @param order_id
 * @returns {Promise<AxiosResponse<any>> | *}
 */
function getSubTableItemList(order_id) {
    let data = JSON.stringify({
        "table_id": huobanConfig.order_detail_table.table_id,
        "filter": {
            "and": [
                {
                    "field": huobanConfig.order_table.fieldsMap['发货订单'],
                    "query": {
                        "eq": [order_id]
                    }
                }
            ]
        },
        "limit": 100,
        "offset": 0,
        "with_field_config": 0
    });
    return request('post', 'v1/item/list', data)
}

function getAgentList(nos) {
    return getTableItemList(
        huobanConfig.agent_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.agent_table.fieldsMap['经销商编号'],
                    "query": {
                        "eqm": nos
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}


function getSchoolList(ids) {
    return getTableItemList(
        huobanConfig.school_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.school_table.fieldsMap['学校ID'],
                    "query": {
                        "eqm": ids
                    }
                }
            ]
        },
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}

function getSchoolById(schoolId) {
    return getTableItemList(
        huobanConfig.school_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.school_table.fieldsMap['学校ID'], // 订单状态，待发货
                    "query": {
                        "eq": schoolId
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items[0]
        }
        return null
    })
}

function getUserByQxId(qxId) {
    return getTableItemList(
        huobanConfig.staff_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.staff_table.fieldsMap['企信ID'], 
                    "query": {
                        "eq": qxId
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items[0]
        }
        return null
    })
}

function getUserByQxIds(qxIds) {
    return getTableItemList(
        huobanConfig.staff_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.staff_table.fieldsMap['企信ID'], 
                    "query": {
                        "in": qxIds
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}

function getAgentAuthList(ids) {
    return getTableItemList(
        huobanConfig.agent_auth_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.agent_auth_table.fieldsMap['CRMID'], // 订单状态，待发货
                    "query": {
                        "in": ids
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}

function getSaasSchoolRecodeList(itemId) {
    return getTableItemList(
        huobanConfig.saas_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.saas_table.fieldsMap['合作学校'], // 订单状态，待发货
                    "query": {
                        "in": [itemId]
                    }
                }
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items[0]
        }
        return null
    })
}

function getOneBusinessUnitByName(name) {
    return getTableItemList(
        huobanConfig.business_unit_table.table_id,
        {
            "and": [
                {
                    "field": huobanConfig.business_unit_table.fieldsMap['经营单元'], // 订单状态，待发货
                    "query": {
                        "eq": name
                    }
                }
            ]
        }
    ).then(function (data) {
        let items = data.items
        if (items && items.length > 0) {
            return items[0]
        }
        return null
    })
}

function getExpenditureListByNo(nos) {
    return getTableItemList(
        huobanConfig.expenditure_table.table_id,
        {
            "or": [
                {
                    "field": huobanConfig.expenditure_table.fieldsMap['报销流程单号'],
                    "query": {
                        "eq": nos
                    }
                },
                {
                    "field": huobanConfig.expenditure_table.fieldsMap['BOSS流程编号（付款单号）'],
                    "query": {
                        "eq": nos
                    }
                },
            ]
        }
    ).then(function (data) {
        if (data && data.items && data.items.length > 0) {
            return data.items
        }
        return null
    })
}


function updateSaasSchoolItem(itemId, record) {
    let fields = {}
    fields[huobanConfig.saas_table.fieldsMap['阅卷跟进记录(同步)']] = record
    let data = JSON.stringify({
        "fields": fields
    });

    console.log(`https://api.huoban.com/openapi/v1/item/${itemId}`)
    console.log(data)
    let config = {
        method: 'put',
        url: `https://api.huoban.com/openapi/v1/item/${itemId}`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            throw new Err(data.code, data.message, data.message)
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })

}

function createSchoolItem(item) {
    let data = JSON.stringify({
        "table_id": huobanConfig.school_table.table_id,
        "fields": item
    });

    console.log(data)
    let config = {
        method: 'post',
        url: `https://api.huoban.com/openapi/v1/item`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };
    // return;
    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            return { code: data.code, msg: data.code + ' ' + data.message }
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function updateSchoolItem(itemId, item) {
    let data = JSON.stringify({
        "fields": item
    });

    console.log(data)
    let config = {
        method: 'put',
        url: `https://api.huoban.com/openapi/v1/item/${itemId}`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };
    // return;
    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            return { code: data.code, msg: data.code + ' ' + data.message }
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function createAgentItem(fields) {
    let data = JSON.stringify({
        "table_id": huobanConfig.agent_table.table_id,
        "fields": fields
    });

    console.log(data)
    let config = {
        method: 'post',
        url: `https://api.huoban.com/openapi/v1/item`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            throw new Error(data.code, data.message, data.message)
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function deleteTableItem(itemId) {
    console.log(`deleteTableItem` + itemId)
    let config = {
        method: 'delete',
        url: `https://api.huoban.com/openapi/v1/item/${itemId}`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
    };
    // return;
    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            return { code: data.code, msg: data.code + ' ' + data.message }
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function updateTableItem(itemId, item) {
    let data = JSON.stringify({
        "fields": item
    });

    console.log(data)
    let config = {
        method: 'put',
        url: `https://api.huoban.com/openapi/v1/item/${itemId}`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };
    // return;
    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            return { code: data.code, msg: data.code + ' ' + data.message }
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function createTableItem(tableId, fields) {
    let data = JSON.stringify({
        "table_id": tableId,
        "fields": fields
    });

    console.log(data)
    let config = {
        method: 'post',
        url: `https://api.huoban.com/openapi/v1/item`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            throw new Error(data.code, data.message, data.message)
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function batchUpdateTableItems(tableId, items) {
    let data = JSON.stringify({
        "table_id": tableId,
        "update_type": "upsert",
        "update_by_fields": [
            "item_id"
        ],
        "items": items
    });

    console.log(data)
    let config = {
        method: 'put',
        url: `https://api.huoban.com/openapi/v1/items`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            throw new Error(data.code, data.message, data.message)
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function batchDeleteTableItems(tableId, item_ids) {
    let data = JSON.stringify({
        "table_id": tableId,
        "item_ids": item_ids
    });

    console.log(data)
    let config = {
        method: 'delete',
        url: `https://api.huoban.com/openapi/v1/items`,
        headers: {
            'Open-Authorization': huobanConfig.auth_header,
            'Content-Type': 'application/json'
        },
        data: data
    };

    return axios(config)
        .then(({ data, config }) => {
            if (data.code === 0) {
                // console.log(data)
                return { code: data.code, message: data.message }
            }
            throw new Error(data.code, data.message, data.message)
        })
        .catch((error) => {
            let msg = error.msg || (error.response && error.response.data && error.response.data.error_msg) || '接口异常'
            return { code: 2, msg: error.code + ' ' + msg }
        })
}

function initHuobanJson() {
    // huobanApi.getTableList()
    //     .then(function (data) {
    // let tables = data.tables
    let tables = [{
        "table_id": "2100000018475755",
        "name": "经销商档案",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-07 16:34:57"
    }, {
        "table_id": "2100000018480321",
        "name": "项目管理",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-08 08:31:35"
    }, {
        "table_id": "2100000018513284",
        "name": "员工信息",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 17:29:20"
    }, {
        "table_id": "2100000021897176",
        "name": "合作学校",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2022-06-24 17:46:42"
    }, {
        "table_id": "2100000052919044",
        "name": "应续SaaS列表",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2022-06-24 17:46:42"
    },
    {
        "table_id": "2100000018524704",
        "name": "省",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000018524705",
        "name": "市",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000018524706",
        "name": "区/县",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000054080177",
        "name": "直营项目管理",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000059498808",
        "name": "申请开拓授权",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000059578666",
        "name": "授权记录",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000059700201",
        "name": "项目跟进记录（直营）",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000059982481",
        "name": "直营SaaS项目特点",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000060066489",
        "name": "直营SaaS谈判目的",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000060996421",
        "name": "消息队列",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000055636966",
        "name": "经营单元",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000056577118",
        "name": "AMB成本类型",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    {
        "table_id": "2100000055614225",
        "name": "AMB支出明细",
        "alias": "",
        "space_id": "4000000003570865",
        "created_on": "2021-11-09 19:53:07"
    },
    ]
    for (let i = 0; i < tables.length; i++) {
        _getConfig(tables[i].table_id)
    }
    // })

}

function _getConfig(table_id) {
    getTableConfig(table_id)
        .then(function (data) {
            _createJson(data.table)
        })
}

function _createJson(table) {
    let { name, table_id, fields } = table
    let fieldsMap = {}
    for (let i = 0; i < fields.length; i++) {
        let { name, field_id, from_relation_field } = fields[i]
        if (from_relation_field?.field_id) {
            const relation = fields.find(e => e.field_id == from_relation_field?.field_id)
            name = `${relation?.name}-${name}`
        }
        fieldsMap[name] = field_id
    }
    let result = {
        fieldsMap: fieldsMap, // 添加转换的map，方便使用
        ...table
    }

    let jsonName = `${name.replaceAll('/', '-')}-${table_id}.json`

    let newArr = JSON.stringify(result, null, '\t')//将数组转成json格式
    // console.log(newArr);
    //3.写入需要的文件当中

    let dir = path.join(__dirname + '/../controllers/tables/业务管理/', jsonName);
    fs.writeFile(dir, newArr, 'utf8', (err) => {
        console.log('写入成功', err)
    });
}

module.exports = {
    initHuobanJson,
    getProjectList,
    getOneProjectByNo,
    getProjectListBySchool,
    getTableItem,
    getAgentAuthList,
    deleteTableItem,
    updateTableItem,
    createTableItem,
    batchUpdateTableItems,
    batchDeleteTableItems,
    getTableItemList,
    getAgentList,
    createAgentItem,
    getSchoolList,
    updateSchoolItem,
    createSchoolItem,
    getSchoolById,
    getUserByQxId,
    getUserByQxIds,
    getSaasSchoolRecodeList,
    getOneBusinessUnitByName,
    updateSaasSchoolItem,
    getSalesProjectListByTime,
    getExpenditureListByNo,
}
