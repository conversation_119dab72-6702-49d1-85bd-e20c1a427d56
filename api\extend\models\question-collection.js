'use strict';

module.exports = {
    "collectionName": "questionCollection",
    "info": {
        "name": "QuestionCollection",
        "label": "book问题收集",
        "description": "book问题收集"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true
    },
    "pluginOptions": {},
    "attributes": {
        "user": {
            "label": "提交人",
            "type": "string",
        },
        "content": {
            "label": "问题信息",
            "type": "text"
        },
        "status": {
            "label": "状态",
            "type": "string",
        },
        "result": {
            "label": "处理结果",
            "type": "text"
        },
        "operator": {
            "label": "处理人",
            "type": "string"
        },
        "type": {
            "label": "问题分类",
            "type": "string",
        }
    }
}
