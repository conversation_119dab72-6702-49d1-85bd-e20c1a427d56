{"collectionName": "users-permissions_role", "info": {"name": "role", "label": "角色", "description": "角色包含一组功能，并关联一组接口权限"}, "pluginOptions": {"content-manager": {"visible": false}}, "attributes": {"isPreset": {"label": "是否预置", "type": "boolean", "required": true, "configurable": false, "editable": false, "default": false}, "name": {"label": "角色名称", "type": "string", "minLength": 3, "required": true, "configurable": false}, "description": {"label": "角色描述", "type": "string", "configurable": false}, "type": {"label": "角色编号", "type": "string", "unique": true, "configurable": false}, "permissions": {"label": "角色接口权限", "collection": "permission", "via": "role", "plugin": "users-permissions", "configurable": false, "isVirtual": true}, "modules": {"label": "角色功能权限", "collection": "group", "via": "roles", "dominant": true, "plugin": "users-permissions", "configurable": false}, "menu": {"label": "角色菜单", "model": "menu", "dominant": true, "plugin": "users-permissions", "configurable": false}}}