const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    {
      'method': 'GET',
      'path': '/external/menus/huoban',
      'handler': 'brand-user-menu.getUserMenu',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    {
      'method': 'POST',
      'path': '/external/menus/huoban',
      'handler': 'brand-user-menu.setUserMenu',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/brand-huoban-menus',
      controller: 'brand-huoban-menu'
    }),
    ...createDefaultRoutes({
      basePath: '/brand-user-menus',
      controller: 'brand-user-menu'
    }),
  ]
}
