function createCacheFunction (dataFunction, cacheDuration) {
  let cache = null
  let cacheExpiry = null
  let isRefreshing = false

  const refreshCache = async () => {
    if (!isRefreshing) {
      isRefreshing = true
      try {
        cache = await dataFunction()
        cacheExpiry = Date.now() + cacheDuration
      } catch (error) {
        console.error('Failed to refresh cache:', error)
      } finally {
        isRefreshing = false
      }
    }
  }

  return async () => {
    const now = Date.now()
    if (cache !== null && (now < cacheExpiry || isRefreshing)) {
      // 如果缓存有效或者正在刷新缓存，立即返回当前缓存（即使过期也返回）
      return cache
    } else if (cache !== null && now >= cacheExpiry) {
      // 缓存过期，需要刷新，但异步进行，立即返回过期缓存
      refreshCache().catch((error) => console.error('Error refreshing cache in background:', error))
      return cache
    } else {
      // 没有缓存，同步刷新缓存并返回结果
      await refreshCache()
      return cache
    }
  }
}

module.exports = {
  createCacheFunction
}
