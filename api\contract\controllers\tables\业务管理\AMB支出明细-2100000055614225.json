{"fieldsMap": {"支出方单元": "2200000471094350", "归属联盟": "2200000471574654", "成本类型": "2200000464602495", "外采设备": "2200000511721533", "预算科目": "2200000463668574", "支出内容": "2200000457335824", "支出金额": "2200000457335825", "业绩扣减": "2200000516042165", "线索成本确认状态": "2200000464588651", "归属项目": "2200000457335822", "实施提供方": "2200000463354971", "实际付款日期": "2200000463402047", "成本核算月份": "2200000463402048", "月份备注": "2200000493070461", "BOSS流程编号（付款单号）": "2200000457335826", "付款状态": "2200000458777790", "报销流程单号": "2200000498083590", "直营会员项目登记": "2200000464259041", "关联经销商": "2200000464444819", "关联经销商收入属性": "2200000464444875", "备注": "2200000463325586", "外采金额": "2200000503173951", "已计算提成": "2200000517301592", "流程发起人": "2200000458790079", "流程状态": "2200000458790080", "流程进展": "2200000458790081", "流程启动时间": "2200000458790082", "流程结束时间": "2200000458790083", "支出方单元-经营单元": "1143001101000000", "支出方单元-归属联盟": "1143001153000000", "支出方单元-账户余额": "1143001168000000", "归属联盟-阿米巴联盟": "1144001101000000", "归属联盟-联盟盟主": "1144001113000000", "成本类型-成本类型": "1142001101000000", "成本类型-扣除方式": "1142001111000000", "外采设备-核算价": "1148001112000000", "归属项目-立项编号": "1112021155000000", "归属项目-业绩归属单元": "1112021237000000", "归属项目-项目经理": "1112021102000000", "归属项目-AMB项目类型": "1112021240000000", "归属项目-🛎24财年累计项目业绩（扣除收入前支出）": "1112021241000000", "归属项目-🛎25财年累计项目业绩（扣除收入前支出）": "1112021374000000", "归属项目-立项审核": "1112021151000000", "归属项目-项目状态": "1112021201000000", "成本核算月份-业绩月份": "1136001101000000", "直营会员项目登记-联盟": "1138001114000000", "直营会员项目登记-单元": "1138001112000000", "直营会员项目登记-学校": "1138001113000000", "直营会员项目登记-承接开始日期": "1138001115000000", "直营会员项目登记-承接结束日期": "1138001116000000", "直营会员项目登记-过去一年会员数量": "1138001117000000", "直营会员项目登记-基础量金额": "1138001118000000", "直营会员项目登记-应划转业绩金额（基础量*1.3）": "1138001119000000", "关联经销商-公司名称": "1139001103000000", "关联经销商-经销商编号": "1139001112000000"}, "table_id": "2100000055614225", "name": "AMB支出明细", "alias": "", "space_id": "4000000003570865", "created_on": "2024-01-29 17:41:59", "fields": [{"field_id": "2200000471094350", "name": "支出方单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000471574654", "name": "归属联盟", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056608623", "space_id": "4000000003570865"}}, {"field_id": "2200000464602495", "name": "成本类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000056577118", "space_id": "4000000003570865"}}, {"field_id": "2200000511721533", "name": "外采设备", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000063318252", "space_id": "4000000003570865"}}, {"field_id": "2200000463668574", "name": "预算科目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055679269", "space_id": "4000000003570865"}}, {"field_id": "2200000457335824", "name": "支出内容", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000457335825", "name": "支出金额", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": true, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000516042165", "name": "业绩扣减", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "业绩已扣减"}, {"id": "2", "name": "提成前已扣减"}]}}, {"field_id": "2200000464588651", "name": "线索成本确认状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "2", "name": "已确认"}, {"id": "3", "name": "有疑问"}, {"id": "4", "name": "待确认"}]}}, {"field_id": "2200000457335822", "name": "归属项目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000463354971", "name": "实施提供方", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000463402047", "name": "实际付款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000463402048", "name": "成本核算月份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000037344043", "space_id": "4000000003570865"}}, {"field_id": "2200000493070461", "name": "月份备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000457335826", "name": "BOSS流程编号（付款单号）", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000458777790", "name": "付款状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "待审批"}, {"id": "2", "name": "待付款"}, {"id": "3", "name": "已付款"}]}}, {"field_id": "2200000498083590", "name": "报销流程单号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000464259041", "name": "直营会员项目登记", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056514001", "space_id": "4000000003570865"}}, {"field_id": "2200000464444819", "name": "关联经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000464444875", "name": "关联经销商收入属性", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "增量属性"}, {"id": "2", "name": "存量属性"}]}}, {"field_id": "2200000463325586", "name": "备注", "alias": "", "field_type": "rich", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000503173951", "name": "外采金额", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000517301592", "name": "已计算提成", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已结算"}]}}, {"field_id": "2200000458790079", "name": "流程发起人", "alias": "process_executor_id", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000458790080", "name": "流程状态", "alias": "process_status", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "执行中"}, {"id": "2", "name": "已完成（同意）"}, {"id": "3", "name": "已完成（不同意）"}, {"id": "4", "name": "已撤销"}, {"id": "5", "name": "已关闭"}, {"id": "6", "name": "异常中"}]}}, {"field_id": "2200000458790081", "name": "流程进展", "alias": "process_current_log", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000458790082", "name": "流程启动时间", "alias": "process_created_on", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "datetime"}}, {"field_id": "2200000458790083", "name": "流程结束时间", "alias": "process_completed_on", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "datetime"}}, {"field_id": "1143001101000000", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471094350}, "required": false, "description": "", "config": {}}, {"field_id": "1143001153000000", "name": "归属联盟", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000471094350}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000056608623", "space_id": "4000000003570865"}}, {"field_id": "1143001168000000", "name": "账户余额", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000471094350}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1144001101000000", "name": "阿米巴联盟", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000471574654}, "required": false, "description": "", "config": {}}, {"field_id": "1144001113000000", "name": "联盟盟主", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000471574654}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1142001101000000", "name": "成本类型", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000464602495}, "required": false, "description": "", "config": {}}, {"field_id": "1142001111000000", "name": "扣除方式", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000464602495}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "收入前"}, {"id": "4", "name": "收入前（计提）"}, {"id": "2", "name": "收入后"}, {"id": "5", "name": "信用支出"}, {"id": "6", "name": "暂缓入账"}]}}, {"field_id": "1148001112000000", "name": "核算价", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000511721533}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1112021155000000", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "", "config": {}}, {"field_id": "1112021237000000", "name": "业绩归属单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000457335822}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1112021102000000", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000457335822}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1112021240000000", "name": "AMB项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636965", "space_id": "4000000003570865"}}, {"field_id": "1112021241000000", "name": "🛎24财年累计项目业绩（扣除收入前支出）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1112021374000000", "name": "🛎25财年累计项目业绩（扣除收入前支出）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1112021151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "直营待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1112021201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000457335822}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "1136001101000000", "name": "业绩月份", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {"field_id": 2200000463402048}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "1138001114000000", "name": "联盟", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000464259041}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "会员流量"}, {"id": "3", "name": "深耕联盟"}, {"id": "2", "name": "项目联盟"}, {"id": "1", "name": "渠道联盟"}]}}, {"field_id": "1138001112000000", "name": "单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000464259041}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1138001113000000", "name": "学校", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000464259041}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "1138001115000000", "name": "承接开始日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {"field_id": 2200000464259041}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "1138001116000000", "name": "承接结束日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {"field_id": 2200000464259041}, "required": true, "description": "", "config": {"precision": "date"}}, {"field_id": "1138001117000000", "name": "过去一年会员数量", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000464259041}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1138001118000000", "name": "基础量金额", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {"field_id": 2200000464259041}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1138001119000000", "name": "应划转业绩金额（基础量*1.3）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000464259041}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1139001103000000", "name": "公司名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000464444819}, "required": true, "description": "", "config": {}}, {"field_id": "1139001112000000", "name": "经销商编号", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {"field_id": 2200000464444819}, "required": true, "description": "", "config": {}}]}