module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.managerId,
  "connection": "mingdao",
  "info": {
    name: 'ManagerUser',
    label: '企业用户管理',
    description: ''
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    username: {
      // label: '用户名',
      "ref": "672d7718444eb62fc450ecbf"
    },
    showName: {
      // label: '显示名称',
      "ref": "6731bf7a1637ee6db9afdd51"
    },
    phone: {
      // label: '手机	',
      "ref": "6731bf7a1637ee6db9afdd52"
    },
    blocked: {
      // label: '是否禁用',
      "ref": "672d7718444eb62fc450ecc0"
    },
    leaved: {
      // label: '是否离职',
      "ref": "672d7718444eb62fc450ecc1"
    },
    provider: {
      // label: '用户来源',
      "ref": "672d7718444eb62fc450ecc2"
    },
    customId: {
      // label: '自定义Id',
      "ref": "672d7718444eb62fc450ecc3"
    },
    // role: {
    //   // label: '当前角色',
    //   "ref": "672d7718444eb62fc450ecc4"
    // },
    // roles: {
    //   // label: '可用角色',
    //   "ref": "672d7718444eb62fc450ecc5"
    // },
    // serviceManagerQrCode: {
    //   // label: '企微二维码',
    //   "ref": "6731bd351637ee6db9afdd33"
    // },
    groups: {
      // label: '所属团队',
      "ref": "672d7835b632a9103eb4496d"
    },
    units: {
      // label: '所属单元	',
      "ref": "6750436aba60f67ec34c1d60"
    },
    provinces: {
      // label: '所属省份	',
      mainField: 'name',
      "ref": "6760fcc29e13a09bfff17c8f"
    },
  }
}
