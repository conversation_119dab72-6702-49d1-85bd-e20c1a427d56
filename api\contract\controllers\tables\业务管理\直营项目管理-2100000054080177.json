{"fieldsMap": {"单元": "2200000482995401", "项目负责人": "2200000447441044", "立项记录": "2200000447425430", "学校名称": "2200000447425431", "当前所处阶段": "2200000474924746", "进展概述": "2200000478586398", "风险程度": "2200000447441047", "计划签约日期": "2200000447441045", "计划回款金额（万元）": "2200000447441046", "验收单进展": "2200000497582906", "产品版本": "2200000509861080", "签约日期": "2200000464979771", "已签约金额": "2200000459158445", "计划回款日期": "2200000483462555", "实际回款日期": "2200000447441049", "实际回款金额": "2200000447441050", "渠道标准价": "2200000458733412", "服务运营": "2200000495522158", "运营激励": "2200000461798127", "商务费": "2200000458733413", "单元收入": "2200000458611934", "预计提成": "2200000458733414", "项目阶段": "2200000447441048", "mark": "2200000464832826", "本年度签约": "2200000469749967", "项目负责人-外部用户": "1114001121000000", "项目负责人-直营小组": "1114001118000000", "项目负责人-外部用户-组长": "1114001122000000", "立项记录-业绩归属单元": "1112001237000000", "立项记录-项目收入": "1112001259000000", "立项记录-立项审核": "1112001151000000", "学校名称-有效项目数": "1113001105000000"}, "table_id": "2100000054080177", "name": "直营项目管理", "alias": "", "space_id": "4000000003570865", "created_on": "2023-10-11 14:38:17", "fields": [{"field_id": "2200000482995401", "name": "单元", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "SaaS-1"}, {"id": "2", "name": "SaaS-2"}, {"id": "3", "name": "SaaS-3"}, {"id": "4", "name": "SaaS-4"}]}}, {"field_id": "2200000447441044", "name": "项目负责人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000447425430", "name": "立项记录", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000447425431", "name": "学校名称", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000474924746", "name": "当前所处阶段", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "初次联系"}, {"id": "2", "name": "挖掘需求"}, {"id": "3", "name": "明确意向"}, {"id": "4", "name": "谈单"}, {"id": "5", "name": "签约"}]}}, {"field_id": "2200000478586398", "name": "进展概述", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000447441047", "name": "风险程度", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "基本确定"}, {"id": "4", "name": "低风险"}, {"id": "2", "name": "中风险"}, {"id": "3", "name": "高风险"}]}}, {"field_id": "2200000447441045", "name": "计划签约日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000447441046", "name": "计划回款金额（万元）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000497582906", "name": "验收单进展", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "已发送验收单"}, {"id": "2", "name": "验收单已盖章"}, {"id": "3", "name": "验收单已回收"}]}}, {"field_id": "2200000509861080", "name": "产品版本", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000464979771", "name": "签约日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000459158445", "name": "已签约金额", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000483462555", "name": "计划回款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000447441049", "name": "实际回款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000447441050", "name": "实际回款金额", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000458733412", "name": "渠道标准价", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000495522158", "name": "服务运营", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000461798127", "name": "运营激励", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000458733413", "name": "商务费", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000458611934", "name": "单元收入", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000458733414", "name": "预计提成", "alias": "", "field_type": "money", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "2200000447441048", "name": "项目阶段", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "2", "name": "已出学校方案"}, {"id": "4", "name": "已上会决策"}, {"id": "3", "name": "明确学校方案"}, {"id": "5", "name": "启动收费"}, {"id": "6", "name": "已回款"}, {"id": "7", "name": "已流失"}]}}, {"field_id": "2200000464832826", "name": "mark", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000469749967", "name": "本年度签约", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1114001121000000", "name": "外部用户", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000447441044}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1114001118000000", "name": "直营小组", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000447441044}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059956977", "space_id": "4000000003570865"}}, {"field_id": "1114001122000000", "name": "外部用户-组长", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000447441044}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "1112001237000000", "name": "业绩归属单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000447425430}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1112001259000000", "name": "项目收入", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000447425430}, "required": false, "description": "", "config": {"precision": 2, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1112001151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000447425430}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "直营待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1113001105000000", "name": "有效项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000447425431}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}]}