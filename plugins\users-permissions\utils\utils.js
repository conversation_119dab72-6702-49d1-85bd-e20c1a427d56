const _ = require('lodash')

function fmtNormalXML (xml) {
  let message = {}

  if (typeof xml === 'object') {
    const keys = Object.keys(xml)

    for (let i = 0; i < keys.length; i++) {
      let item = xml[keys[i]]
      let key = keys[i]

      if (!(item instanceof Array) || item.length === 0) {
        continue
      }

      if (item.length === 1) {
        let val = item[0]

        if (typeof val === 'object') {
          message[key] = fmtNormalXML(val)
        } else {
          message[key] = (val || '').trim()
        }
      } else {
        message[key] = []

        for (let j = 0; j < item.length; j++) {
          message[key].push(fmtNormalXML(item[j]))
        }
      }
    }
  }

  return message
}

// 合并配置
function mergePermissionConfigs (configs) {
  let apps = []
  let branches = []
  let pageGroups = []
  let functions = []
  let roles = []
  for (let config of configs) {
    apps = [...apps, ...(config.apps || [])]
    branches = [...branches, ...(config.branches || [])]
    pageGroups = [...pageGroups, ...(config.pageGroups || [])]
    functions = [...functions, ...(config.functions || [])]
    roles = [...roles, ...(config.roles || [])]
  }
  // 合并 sId 相同的项，其中相同项的 pages 也根据 sId 合并
  pageGroups = pageGroups.reduce((acc, cur) => {
    const matchGroup = acc.find(e => e.sId === cur.sId)
    if (matchGroup) {
      matchGroup.pages = _.uniqBy([...matchGroup.pages, ...cur.pages], 'sId')
    } else {
      acc.push(cur)
    }
    return acc
  }, [])
  return { apps, branches, pageGroups, functions, roles }
}

// gen random string
function genRandomString (len) {
  const chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'
  const maxPos = chars.length
  let pwd = ''
  for (let i = 0; i < len; i++) {
    pwd += chars.charAt(Math.floor(Math.random() * maxPos))
  }
  return pwd
}


module.exports = {
  fmtNormalXML,
  mergePermissionConfigs,
  genRandomString
}
