module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.recycleClueDetailId,
  "connection": "mingdao",
  "info": {
    name: 'RecycleClueDetail',
    label: '回收公共池明细',
    description: '回收公共池明细'
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    directServiceTeam: {
      // label: '运营小组',
      mainField: 'name',
      "ref": "67a568f19e13a09bff029ea0"
    },
    directServiceManager: {
      // label: '直服经理',
      mainField: 'username',
      "ref": "67a568f19e13a09bff029ea2"
    },
    directSalesTeam: {
      // label: '直营小组',
      mainField: 'name',
      "ref": "6790ace29e13a09bfffed237"
    },
    directSalesManager: {
      // label: '直营经理',
      mainField: 'username',
      "ref": "6790ace29e13a09bfffed239"
    },
  }
}
