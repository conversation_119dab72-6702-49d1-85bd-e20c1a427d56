
const { CurdRouter } = require('accel-utils')

const curdRouter = new (class extends CurdRouter {
    // ...
    _getIntersection(arr1, arr2) {
        const set = new Set(arr1)
        return arr2.filter(item => set.has(item))
    }

    async create(ctx) {
        const { data } = this._parseCtx(ctx)
        // if (data.customerService) {
        //     await strapi.query('customer-service').update({
        //         id: data.customerService[0]
        //     }, { lastSalesTrackTime: data.time ? new Date(data.time) : new Date() })
        // }
        data.operator = [ctx.state.user.mingdaoId]
        return super.create(ctx)
    }

    async update(ctx) {
        const { data } = this._parseCtx(ctx)
        data.operator = [ctx.state.user.mingdaoId]
        return super.update(ctx)
    }

    async updateMany(ctx) {
        const { data: { filter, data } } = this._parseCtx(ctx)
        data.operator = [ctx.state.user.mingdaoId]
        const result = await super.updateMany(ctx)
        return result
    }

    async count(ctx) {
        const user = ctx.state.user
        const query = ctx.request.query
        if (['sales-group-leader', 'sales-group-member'].includes(user.role.type)) {
            // const groups = await strapi.query('manager-group').find({ members: [user.mingdaoId] })
            // const memberIds = groups?.[0]?.members?.map(e => e.id) || [user.mingdaoId];
            // const users = await strapi.query('manager').find({ id: { $in: memberIds } })
            // query.salesQxId = users.map(e => e.customId)
            const users = await strapi.query('manager').find({ groups: user.mingdaoGroupIds, _limit: 9999 })
            query.salesQxId = users.map(e => e.customId)
        }
        return super.count(ctx)
    }

    async find(ctx) {
        const user = ctx.state.user
        const query = ctx.request.query
        if (['sales-group-leader', 'sales-group-member'].includes(user.role.type)) {
            const users = await strapi.query('manager').find({ groups: user.mingdaoGroupIds, _limit: 9999 })
            query.salesQxId = users.map(e => e.customId)
        }
        return super.find(ctx)
    }
})('customer-sales-follow-record')

module.exports = {
    ...curdRouter.createHandlers(),
}
