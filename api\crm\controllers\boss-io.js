const axios = require('axios')

module.exports = {
  async getUserAuthSchoolList(ctx) {
    const { userid, wxid } = ctx.request.query

    if (!userid || !wxid) {
      return ctx.badRequest('缺少必填参数：userid 和 wxid')
    }

    try {
      // 先查询符合条件的角色
      const roles = await strapi.query('role', 'users-permissions').find({
        type_in: ['sales-group-member', 'sales-group-leader'],
        _limit: -1
      })

      const roleIds = roles.map(role => role.id)

      // 再查询具有这些角色的用户
      const qxUser = await strapi.query('qx-user').findOne({
        customId: wxid,
        role_in: roleIds
      })

      if (qxUser) {
        const schools = await strapi.query('customer-service-mid').find({
          directSalesManager: qxUser.id,
          _limit: -1
        })

        const result = schools.map(school => ({
          schoolId: school.schoolId,
          name: school.name,
          location: {
            province: school.province || '',
            city: school.city || '',
            district: school.district || ''
          },
          type: '学校',
          linkMode: 1,
          linkStatus: 1
        }))

        return ctx.send({
          code: 1,
          msg: 'OK',
          data: result
        })
      } else {
        const bossConfig = strapi.config.server.bossApi
        const apiPath = `/external/api/customer/to_yz/get_auth_list_by_user_id?userId=${userid}`

        const response = await axios.get(`${bossConfig.url}${apiPath}`, {
          headers: {
            'apiKey': bossConfig.apikey,
            'token': bossConfig.token
          }
        })

        return ctx.send(response.data)
      }
    } catch (error) {
      strapi.log.error('获取用户授权学校列表失败', error)
      return ctx.badRequest('获取用户授权学校列表失败：' + error.message)
    }
  }
}
