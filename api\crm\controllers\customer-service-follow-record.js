const { CurdRouter } = require('accel-utils')
const moment = require('moment/moment')

strapi.eventHub.on('bootstrap', async () => {
  console.info('Model Mounted: ', Object.keys(strapi.models))
})

const curdRouter = new (class extends CurdRouter {

  // async count(ctx) {
  //   const user = ctx.state.user
  //   const query = ctx.request.query
  //   if (['service-group-leader', 'service-group-member'].includes(user.role.type)) {
  //     query['customerService_in'] = await getUserCustomerServiceIds(user)
  //   }
  //   return super.count(ctx)
  // }

  // async find(ctx) {
  //   const user = ctx.state.user
  //   const query = ctx.request.query
  //   if (['service-group-leader', 'service-group-member'].includes(user.role.type)) {
  //     query['customerService_in'] = await getUserCustomerServiceIds(user)
  //   }
  //   return super.find(ctx)
  // }

  // ...
  _getIntersection(arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }

  async count(ctx) {
    const user = ctx.state.user
    const query = ctx.request.query
    if (['service-group-leader', 'service-group-member'].includes(user.role.type)) {
      const users = await strapi.query('manager').find({ groups: user.mingdaoGroupIds, _limit: 9999 })
      query.serviceQxId = users.map(e => e.customId)
    }
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    const query = ctx.request.query
    if (['service-group-leader', 'service-group-member'].includes(user.role.type)) {
      const users = await strapi.query('manager').find({ groups: user.mingdaoGroupIds, _limit: 9999 })
      query.serviceQxId = users.map(e => e.customId)
    }
    return super.find(ctx)
  }

  async create(ctx) {
    // const { data } = this._parseCtx(ctx)
    // if (ctx.request.body.customerService) {
    //   let updateData = {
    //     lastServiceTrackTime: data.startedAt
    //   }
    //   const customerService = await strapi.query('customer-service').update({
    //     id: ctx.request.body.customerService[0]
    //   }, updateData)
    //   ctx.request.body.eduSystem = customerService.edu_system
    // }
    return super.create(ctx)
  }
  async update(ctx) {
    // const { data } = this._parseCtx(ctx)
    const result = await super.update(ctx)
    // if (data.isValid === true && data.customerService) {
    //   await strapi.query('customer-service').update({
    //     id: data.customerService[0]
    //   }, updateData)
    // }
    return result
  }
})('customer-service-follow-record')

module.exports = {
  ...curdRouter.createHandlers(),
}
