const { MongoClient, ObjectId } = require('mongodb')
const moment = require('moment')
const _ = require('lodash')
const axios = require('axios')
const { sleep, printExcel } = require('./utils')

const DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
  : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

let db, dbClient

let now = new Date()

const serverUrl = (process.env.NODE_ENV !== 'production') ? 'http://wly-boss-api-lan.iyunxiao.com' : 'http://wly-boss-api-lan.iyunxiao.com'
const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY1OWNmNTBmYjg1ZTExN2QwMTc1NDVlZSIsImlhdCI6MTc0MTYwNzQwNH0.WU2IgA0wqivXGHJqSAwq7MqCjvoDpLOA-gXplZb6uhc';

(async function () {
  dbClient = await MongoClient.connect(DB_URL)
  db = dbClient.db()
  try {
    logger('gen start')
    await main()
    logger('gen end')
  } catch (e) {
    console.log(e.stack || 'err')
  } finally {
    await dbClient.close()
    setTimeout(() => {
      process.exit(1)
    }, 5000)
  }
})()

function logger (msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg}`)
}

async function main () {
  // 根据 schoolCategory 获取 未立项的 临期校

  // {
  //   "key": "6445621b-d7bc-41bd-a2d8-8d550493e794",
  //   "value": "6445621b-d7bc-41bd-a2d8-8d550493e794",
  //   "index": 10,
  //   "isDeleted": false,
  //   "color": "#484848",
  //   "score": 0,
  //   "hide": false,
  //   "label": "临期2个月"
  // },
  // {
  //   "key": "8c08b028-a803-424f-8608-a2360f87484a",
  //   "value": "8c08b028-a803-424f-8608-a2360f87484a",
  //   "index": 11,
  //   "isDeleted": false,
  //   "color": "#C9E6FC",
  //   "score": 0,
  //   "hide": false,
  //   "label": "临期1个月"
  // },

  const params = {
    schoolCategory_eq: ['6445621b-d7bc-41bd-a2d8-8d550493e794', '8c08b028-a803-424f-8608-a2360f87484a'],
    projectNo_null: true
  }

  const query = Object.keys(params).map(k => {
    const value = params[k]
    if (Array.isArray(value)) {
      return value.map((v, index) => {
        return `${k}[${index}]=${v}`
      }).join('&')
    }
    return `${k}=${value}`
  }).join('&')

  console.log(query)

  const res = await axios.get(`${serverUrl}/customer-services/count?${query}`, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  })
  console.log(res.data)
  const count = res.data
  if (!count) return

  const result = []

  // 如果count 大于100. 则分批请求
  if (count > 100) {
    const pageSize = 100
    for (let i = 0; i < Math.ceil(count / pageSize); i++) {
      console.log(`${i * pageSize} - ${(i + 1) * pageSize}`)
      await createHuobanSalesProject(i * pageSize, pageSize, query, result)
    }
  } else {
    console.log(`0 - ${count}`)
    await createHuobanSalesProject(0, count, query, result)
  }

  if (result.length) {
    await printExcel('学校自动立项完成', result, true)
  }
}

async function createHuobanSalesProject (start, pageSize, query, result) {
  const res = await axios.get(`${serverUrl}/customer-services?${query}&_limit=${pageSize}&_start=${start}`, {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    }
  })
  console.log(res.data.length)
  if (!res.data.length) {
    return
  }

  const customers = res.data
  let index = 0
  for (const customer of customers) {
    //   // /huoban/action/createHuobanSalesProject
    index++
    // {
    //   "key": "a71d8af5-b78e-4196-b636-8a9c670c4a6a",
    //   "value": "a71d8af5-b78e-4196-b636-8a9c670c4a6a",
    //   "index": 5,
    //   "isDeleted": false,
    //   "color": "#FF9300",
    //   "score": 0,
    //   "hide": false,
    //   "label": "临期校"
    // }
    //

    // 正式 id: '13f50026-6034-46b9-b71f-2e3faf5b6e62', username: '黄燕斌',

    const item = {
      name: customer.name,
      schoolId: customer.schoolId,
    }

    result.push(item)
    const data = {}
    const remoteProjectTag = customer.remoteProjectTag || null
    const directSalesManager = customer.directSalesManager || []

    if (remoteProjectTag !== 'a71d8af5-b78e-4196-b636-8a9c670c4a6a') {
      // 远程项目标签 = 【临期校】
      item['修改学校信息'] = '【临期校】'
      data.remoteProjectTag = 'a71d8af5-b78e-4196-b636-8a9c670c4a6a'
    }

    if (!directSalesManager || !directSalesManager.length) {
      // 如果没有直营经理。新增直营经理、黄燕斌
      item['修改学校信息'] = (item['修改学校信息'] || '') + '【新增直营经理、黄燕斌】'
      data.directSalesManager = ['13f50026-6034-46b9-b71f-2e3faf5b6e62']
    }
    try {
      if (Object.keys(data).length) {
        console.log(customer.name, data)
        const res = await axios.put(`${serverUrl}/customer-services/${customer.id}`, data, {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        })
        console.log(customer.name, '修改学校档案完成', index + start)
      }

      const res = await axios.post(`${serverUrl}/huoban/action/createHuobanSalesProject`, {
        schoolId: customer.schoolId
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      })
      if (res.data.code === 0) {
        item['立项结果'] = '成功'
      } else {
        item['立项结果'] = '立项失败' + res.data.msg
      }

      console.log(customer.name, '立项完成', index + start)
      await sleep(1000)
    } catch (err) {
      console.log(err)
      item['立项失败'] = err.message
    }
  }
}
