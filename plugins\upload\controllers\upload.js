const axios = require('axios')

async function create (ctx) {
  const { files } = ctx.request.body
  ctx.body = await Promise.all(files.map(async (file) => {
    const objectStorageRes = await axios({
      method: 'get',
      url: file.url,
      responseType: 'stream',
    })
    return strapi.query('file', 'upload').create({
      mime: objectStorageRes.headers['content-type'],
      ...file,
      credit: 10,
    })
  }))
}

module.exports = {
  create
}
