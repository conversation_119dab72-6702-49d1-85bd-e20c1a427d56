module.exports = {
  collectionName: 'qun-user',
  info: {
    name: 'QunUser',
    label: '群用户',
    description: ''
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    userId: {
      label: '用户ID',
      type: 'string',
      unique: true,
      editable: false
    },
    name: {
      label: '姓名',
      type: 'string',
      editable: false
    },
    avatar: {
      label: '头像',
      type: 'string',
      editable: false
    },
    qunCount: {
      label: '所在群数量',
      type: 'number',
      editable: false
    },
    schoolQunCount: {
      label: '所在学校群数量',
      type: 'number',
      editable: false
    },
    // 家长、老师、学生
    role: {
      label: '角色',
      type: 'string',
      options: [
        {
          label: '家长',
          value: '家长'
        },
        {
          label: '老师',
          value: '老师'
        },
        {
          label: '学生',
          value: '学生'
        },
        {
          label: '经销商',
          value: '经销商'
        },
        {
          label: '内部人员',
          value: '内部人员'
        },
        {
          label: '其他',
          value: '其他'
        }
      ]
    },
    isSchoolBlack: {
      label: '学校群黑名单',
      type: 'boolean'
    },
    remark: {
      label: '备注',
      type: 'string',
    },
    isDeleted: {
      label: '是否删除',
      type: 'boolean',
      editable: false
    }
  }
}
