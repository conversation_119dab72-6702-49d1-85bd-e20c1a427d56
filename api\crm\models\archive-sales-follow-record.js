module.exports = {
  kind: 'collectionType',
  collectionName: 'archive-sales-follow-record',
  info: {
    name: 'ArchiveSalesFollowRecord',
    label: '直营经理归档跟进记录',
    description: '直营经理归档跟进记录'
  },
  options: {
    timestamps: true,
    groups: [
      {
        title: '合作学校',
        fields: [
          'customerService',
          '67345d171637ee6db9affcd4',
          '67345d171637ee6db9affcd5',
          '67345d171637ee6db9affcd6',
          '67345d171637ee6db9affcd7',
          '67345d171637ee6db9affcd8',
          'salesQxId',
          'serviceQxId',
          'follower',
          '673dabc0ba60f67ec3497a7e'
        ]
      },
      {
        title: '本次待办&跟进',
        fields: [
          '673461cc1637ee6db9affd18',
          '673461cc1637ee6db9affd1a',
          '67406a45ba60f67ec3498c96',
          'time',
          '673461cc1637ee6db9affd1c',
          '673461cc1637ee6db9affd1d',
          '6751b1b7ba60f67ec34c3b4c',
          'content',
          'contact',
          '673461cc1637ee6db9affd20',
          '673461cc1637ee6db9affd21',
          '673461cc1637ee6db9affd23',
          '674d8739ba60f67ec34bd980',
          '674d8739ba60f67ec34bd981',
          '674d8739ba60f67ec34bd982',
          '674d8739ba60f67ec34bd983',
          '674d8739ba60f67ec34bd984',
          '674d8739ba60f67ec34bd985',
          '674d8739ba60f67ec34bd986',
          '674d8739ba60f67ec34bd987',
          '674d8739ba60f67ec34bd988',
          '674d8739ba60f67ec34bd989',
          '674d8739ba60f67ec34bd98a',
          '674d8739ba60f67ec34bd98c',
          '674d8739ba60f67ec34bd98d',
          '674d8739ba60f67ec34bd98e',
          '674d8739ba60f67ec34bd98f',
          '674d8739ba60f67ec34bd990',
          '674d8739ba60f67ec34bd991'
        ]
      },
      {
        title: '下次计划',
        fields: [
          'nextContent',
          'nextTime',
          '673462791637ee6db9affd6c'
        ]
      },
      {
        title: '质检',
        fields: [
          'recordInvalid',
          '673462101637ee6db9affd57',
          '673462271637ee6db9affd61'
        ]
      },
      {
        title: '待删除字段',
        fields: [
          '673ff6fbba60f67ec349889d',
          '673462101637ee6db9affd58',
          '673462271637ee6db9affd62'
        ]
      }
    ],
    formStyle: {
      tabPosition: '1',
      defTabName: '详情'
    },
    allOf: [
      {
        name: '交互规则1',
        if: {
          properties: {
            '673461cc1637ee6db9affd20': {
              const: [
                '9ee00a13-2cab-45ae-883c-f49928c09c98'
              ]
            }
          }
        },
        then: {
          properties: {
            '673461cc1637ee6db9affd21': {
              visible: true
            },
            '673461cc1637ee6db9affd23': {
              visible: true
            },
            '673462791637ee6db9affd6c': {
              visible: true
            }
          }
        },
        else: {
          properties: {
            '673461cc1637ee6db9affd21': {
              visible: false
            },
            '673461cc1637ee6db9affd23': {
              visible: false
            },
            '673462791637ee6db9affd6c': {
              visible: false
            }
          }
        }
      },
      {
        name: '交互规则2',
        if: {
          properties: {
            '673462271637ee6db9affd61': {
              const: '{{ Array.isArray($value) ? $value.length > 0 : !!$value }}'
            }
          }
        },
        then: {
          properties: {
            nextTime: {
              editable: false
            },
            nextContent: {
              editable: false
            }
          }
        },
        else: {
          properties: {
            nextTime: {
              editable: true
            },
            nextContent: {
              editable: true
            }
          }
        }
      },
      {
        name: '交互规则3',
        if: {
          properties: {
            '674d8739ba60f67ec34bd980': {
              const: true
            }
          }
        },
        then: {
          properties: {
            '674d8739ba60f67ec34bd981': {
              visible: true
            },
            '674d8739ba60f67ec34bd982': {
              visible: true
            },
            '674d8739ba60f67ec34bd983': {
              visible: true
            },
            '674d8739ba60f67ec34bd984': {
              visible: true
            },
            '674d8739ba60f67ec34bd985': {
              visible: true
            },
            '674d8739ba60f67ec34bd986': {
              visible: true
            },
            '674d8739ba60f67ec34bd987': {
              visible: true
            },
            '674d8739ba60f67ec34bd988': {
              visible: true
            },
            '674d8739ba60f67ec34bd989': {
              visible: true
            },
            '674d8739ba60f67ec34bd98a': {
              visible: true
            },
            '674d8739ba60f67ec34bd98c': {
              visible: true
            },
            '674d8739ba60f67ec34bd98d': {
              visible: true
            },
            '674d8739ba60f67ec34bd98e': {
              visible: true
            },
            '674d8739ba60f67ec34bd98f': {
              visible: true
            },
            '674d8739ba60f67ec34bd990': {
              visible: true
            },
            '674d8739ba60f67ec34bd991': {
              visible: true
            }
          }
        },
        else: {
          properties: {
            '674d8739ba60f67ec34bd981': {
              visible: false
            },
            '674d8739ba60f67ec34bd982': {
              visible: false
            },
            '674d8739ba60f67ec34bd983': {
              visible: false
            },
            '674d8739ba60f67ec34bd984': {
              visible: false
            },
            '674d8739ba60f67ec34bd985': {
              visible: false
            },
            '674d8739ba60f67ec34bd986': {
              visible: false
            },
            '674d8739ba60f67ec34bd987': {
              visible: false
            },
            '674d8739ba60f67ec34bd988': {
              visible: false
            },
            '674d8739ba60f67ec34bd989': {
              visible: false
            },
            '674d8739ba60f67ec34bd98a': {
              visible: false
            },
            '674d8739ba60f67ec34bd98c': {
              visible: false
            },
            '674d8739ba60f67ec34bd98d': {
              visible: false
            },
            '674d8739ba60f67ec34bd98e': {
              visible: false
            },
            '674d8739ba60f67ec34bd98f': {
              visible: false
            },
            '674d8739ba60f67ec34bd990': {
              visible: false
            },
            '674d8739ba60f67ec34bd991': {
              visible: false
            }
          }
        }
      },
      {
        name: '交互规则4',
        if: {
          properties: {
            customerService: {
              const: '{{ Array.isArray($value) ? $value.length > 0 : !!$value }}'
            }
          }
        },
        then: {
          properties: {
            customerService: {
              editable: false
            }
          }
        },
        else: {
          properties: {
            customerService: {
              editable: true
            }
          }
        }
      }
    ]
  },
  attributes: {
    rowid: {
      label: '明道ID',
      size: 4,
      row: 16,
      col: 2,
      visible: false,
      type: 'string'
    },
    customerService: {
      type: 'json',
      label: '学校档案',
      size: 4,
      row: 1,
      col: 0,
      mainField: 'name'
    },
    follower: {
      type: 'json',
      label: '跟进人',
      size: 6,
      row: 4,
      col: 0,
      mainField: 'username'
    },
    contact: {
      type: 'json',
      label: '联系人',
      size: 3,
      row: 9,
      col: 0,
      visibleOnCreate: false,
      mainField: 'title'
    },
    time: {
      label: '跟进时间',
      size: 3,
      row: 7,
      col: 2,
      visibleOnCreate: false,
      type: 'datetime',
      format: 'YYYY-MM-DD'
    },
    content: {
      label: '沟通内容',
      size: 12,
      row: 8,
      col: 0,
      visibleOnCreate: false,
      type: 'richtext'
    },
    nextContent: {
      label: '下次跟进方向',
      size: 4,
      row: 16,
      col: 2,
      visibleOnCreate: false,
      type: 'string'
    },
    nextTime: {
      label: '下次跟进时间',
      size: 4,
      row: 16,
      col: 1,
      visibleOnCreate: false,
      placeholder: '请选择日期',
      type: 'datetime',
      format: 'YYYY-MM-DD'
    },
    recordInvalid: {
      label: '记录无效',
      size: 6,
      row: 18,
      col: 0,
      visibleOnCreate: false,
      type: 'boolean'
    },
    salesQxId: {
      label: '直营经理自定义Id',
      size: 6,
      row: 3,
      col: 0,
      visible: false,
      visibleOnCreate: false,
      type: 'string',
      editable: false
    },
    serviceQxId: {
      label: '运营经理自定义Id',
      size: 6,
      row: 3,
      col: 1,
      visible: false,
      type: 'string',
      editable: false
    },
    '67345d171637ee6db9affcd4': {
      label: '阅卷签约标签',
      size: 4,
      row: 1,
      col: 1,
      type: 'string',
      options: [
        {
          key: '100c2c91-b1f4-4179-82bf-d29840c45a5b',
          value: '100c2c91-b1f4-4179-82bf-d29840c45a5b',
          index: 0,
          isDeleted: false,
          color: '',
          score: 0,
          hide: false,
          label: '体验校'
        },
        {
          key: 'eb35b0cb-3d12-4598-a7da-1c712fd05027',
          value: 'eb35b0cb-3d12-4598-a7da-1c712fd05027',
          index: 0,
          isDeleted: false,
          color: '',
          score: 0,
          hide: false,
          label: '付费校'
        },
        {
          key: '206080e5-1fda-4a8d-b46a-3e546dfd397a',
          value: '206080e5-1fda-4a8d-b46a-3e546dfd397a',
          index: 0,
          isDeleted: false,
          color: '',
          score: 0,
          hide: false,
          label: '应续校'
        },
        {
          key: '9d8e1ad8-3a45-4440-9e4d-b7057d633b6e',
          value: '9d8e1ad8-3a45-4440-9e4d-b7057d633b6e',
          index: 0,
          isDeleted: false,
          color: '',
          score: 0,
          hide: false,
          label: '流失校'
        }
      ]
    },
    '67345d171637ee6db9affcd5': {
      label: '阅卷截止时间',
      size: 4,
      row: 1,
      col: 2,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      editable: false
    },
    '67345d171637ee6db9affcd6': {
      label: '项目标签',
      size: 4,
      row: 2,
      col: 0,
      type: 'string',
      options: [
        {
          key: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          value: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '持续运营'
        },
        {
          key: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          value: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '快攻'
        },
        {
          key: '6298725d-197f-43d6-aca0-7d0423699d07',
          value: '6298725d-197f-43d6-aca0-7d0423699d07',
          index: 3,
          isDeleted: false,
          color: '#00C345',
          score: 0,
          hide: false,
          label: '已签约'
        },
        {
          key: 'ae02fb5c-bed3-4b7a-b31e-41894ca8f6f7',
          value: 'ae02fb5c-bed3-4b7a-b31e-41894ca8f6f7',
          index: 4,
          isDeleted: false,
          color: '#FAD714',
          score: 0,
          hide: false,
          label: '暂缓跟进'
        }
      ]
    },
    '67345d171637ee6db9affcd7': {
      label: '沟通目的',
      size: 4,
      row: 2,
      col: 1,
      type: 'string',
      options: [
        {
          key: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          value: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '必须直签'
        },
        {
          key: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          value: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '以打促和'
        }
      ],
      editable: false
    },
    '67345d171637ee6db9affcd8': {
      label: '学校标签',
      size: 4,
      row: 2,
      col: 2,
      type: 'json',
      jsonSchema: {
        type: 'array',
        uniqueItems: true,
        items: {
          type: 'string',
          enum: [
            '好学校',
            '会员校',
            '商中止合作'
          ],
          minItems: 0,
          maxItems: 0,
          'x-options': [
            {
              key: '15704554-bc9e-431d-8fda-894b15cd305a',
              value: '15704554-bc9e-431d-8fda-894b15cd305a',
              index: 1,
              isDeleted: false,
              color: '#2196F3',
              score: 0,
              hide: false,
              label: '好学校'
            },
            {
              key: '9731f22e-0dc4-44ca-9371-0fedfe1f2cdd',
              value: '9731f22e-0dc4-44ca-9371-0fedfe1f2cdd',
              index: 2,
              isDeleted: false,
              color: '#08C9C9',
              score: 0,
              hide: false,
              label: '会员校'
            },
            {
              key: 'b52bf571-fea4-4561-b5a9-4b9e5d047824',
              value: 'b52bf571-fea4-4561-b5a9-4b9e5d047824',
              index: 3,
              isDeleted: false,
              color: '#00C345',
              score: 0,
              hide: false,
              label: '商中止合作'
            }
          ]
        }
      }
    },
    '673dabc0ba60f67ec3497a7e': {
      type: 'json',
      label: '直营分组',
      size: 6,
      row: 4,
      col: 1
    },
    '673461cc1637ee6db9affd18': {
      type: 'json',
      label: '项目类型',
      size: 4,
      row: 6,
      col: 0
    },
    '673461cc1637ee6db9affd1a': {
      type: 'json',
      label: '项目阶段',
      size: 4,
      row: 6,
      col: 1
    },
    '67406a45ba60f67ec3498c96': {
      label: '阶段提示',
      size: 4,
      row: 6,
      col: 2,
      type: 'richtext',
      editable: false
    },
    '673461cc1637ee6db9affd1c': {
      label: '计划跟进日期',
      size: 3,
      row: 7,
      col: 0,
      placeholder: '请选择日期',
      type: 'datetime',
      format: 'YYYY-MM-DD'
    },
    '673461cc1637ee6db9affd1d': {
      label: '计划跟进事项',
      size: 3,
      row: 7,
      col: 1,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '6751b1b7ba60f67ec34c3b4c': {
      label: '跟进方式',
      size: 3,
      row: 7,
      col: 3,
      visibleOnCreate: false,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: 'ee701cb2-c504-498b-a7ee-979ae2f2edc2',
          value: 'ee701cb2-c504-498b-a7ee-979ae2f2edc2',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '电话'
        },
        {
          key: '9ffb49a6-7a70-4328-8b6c-057dbc4ce3a6',
          value: '9ffb49a6-7a70-4328-8b6c-057dbc4ce3a6',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '微信'
        },
        {
          key: 'c89147b3-2228-4112-84ca-09d6796d53b9',
          value: 'c89147b3-2228-4112-84ca-09d6796d53b9',
          index: 3,
          isDeleted: false,
          color: '#00C345',
          score: 0,
          hide: false,
          label: '面谈'
        }
      ]
    },
    '673461cc1637ee6db9affd20': {
      label: '跟进成果',
      size: 3,
      row: 9,
      col: 1,
      visibleOnCreate: false,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          value: '9ee00a13-2cab-45ae-883c-f49928c09c98',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '已有阶段成果'
        },
        {
          key: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          value: '462e0af1-82cd-4e6f-ad64-32b9c5cbf5c2',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '暂无阶段成果'
        }
      ]
    },
    '673461cc1637ee6db9affd21': {
      type: 'json',
      label: '阶段成果',
      size: 3,
      row: 9,
      col: 2,
      visibleOnCreate: false
    },
    '673461cc1637ee6db9affd23': {
      type: 'json',
      label: '附件',
      size: 3,
      row: 9,
      col: 3,
      visibleOnCreate: false,
      placeholder: '添加附件',
      // type: 'media'
    },
    '674d8739ba60f67ec34bd980': {
      label: '新增联系人',
      size: 3,
      row: 10,
      col: 0,
      visibleOnCreate: false,
      type: 'boolean'
    },
    '674d8739ba60f67ec34bd981': {
      label: '姓名',
      size: 3,
      row: 10,
      col: 1,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '674d8739ba60f67ec34bd982': {
      label: '联系方式',
      size: 3,
      row: 10,
      col: 2,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '674d8739ba60f67ec34bd983': {
      label: '职务',
      size: 3,
      row: 10,
      col: 3,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '674d8739ba60f67ec34bd984': {
      label: '性别',
      size: 3,
      row: 11,
      col: 0,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          value: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '女'
        },
        {
          key: 'ae206887-118d-4adc-a626-7005372fffc6',
          value: 'ae206887-118d-4adc-a626-7005372fffc6',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '男'
        }
      ]
    },
    '674d8739ba60f67ec34bd985': {
      label: '状态',
      size: 3,
      row: 11,
      col: 1,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          value: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '正常'
        },
        {
          key: 'ae206887-118d-4adc-a626-7005372fffc6',
          value: 'ae206887-118d-4adc-a626-7005372fffc6',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '退休'
        },
        {
          key: 'db64bb58-30a6-4cf8-957c-b820047053b8',
          value: 'db64bb58-30a6-4cf8-957c-b820047053b8',
          index: 3,
          isDeleted: false,
          color: '#00C345',
          score: 0,
          hide: false,
          label: '调离'
        }
      ]
    },
    '674d8739ba60f67ec34bd986': {
      label: '是否关键人',
      size: 3,
      row: 11,
      col: 2,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          value: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '普通'
        },
        {
          key: 'ae206887-118d-4adc-a626-7005372fffc6',
          value: 'ae206887-118d-4adc-a626-7005372fffc6',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '关键'
        }
      ]
    },
    '674d8739ba60f67ec34bd987': {
      label: '微信状态',
      size: 3,
      row: 11,
      col: 3,
      placeholder: '请选择',
      type: 'string',
      options: [
        {
          key: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          value: 'dd1d522c-6a46-4b3c-9e3f-a5789786272f',
          index: 1,
          isDeleted: false,
          color: '#2196F3',
          score: 0,
          hide: false,
          label: '未加微信'
        },
        {
          key: 'ae206887-118d-4adc-a626-7005372fffc6',
          value: 'ae206887-118d-4adc-a626-7005372fffc6',
          index: 2,
          isDeleted: false,
          color: '#08C9C9',
          score: 0,
          hide: false,
          label: '已加微信'
        }
      ]
    },
    '674d8739ba60f67ec34bd988': {
      label: '微信号',
      size: 3,
      row: 12,
      col: 0,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '674d8739ba60f67ec34bd989': {
      label: '是否已进群',
      size: 3,
      row: 12,
      col: 1,
      type: 'boolean',
    },
    '674d8739ba60f67ec34bd98a': {
      type: 'json',
      label: '微信添加人',
      size: 3,
      row: 12,
      col: 2
    },
    '674d8739ba60f67ec34bd98c': {
      label: '微信添加时间',
      size: 3,
      row: 12,
      col: 3,
      placeholder: '请选择日期',
      type: 'datetime',
      format: 'YYYY-MM-DD'
    },
    '674d8739ba60f67ec34bd98d': {
      label: '直营是否已加微信',
      size: 4,
      row: 13,
      col: 0,
      type: 'boolean'
    },
    '674d8739ba60f67ec34bd98e': {
      label: '直服是否已加微信',
      size: 4,
      row: 13,
      col: 1,
      type: 'boolean'
    },
    '674d8739ba60f67ec34bd98f': {
      label: '是否已赠送礼包',
      size: 4,
      row: 13,
      col: 2,
      type: 'boolean'
    },
    '674d8739ba60f67ec34bd990': {
      label: '感兴趣的产品/功能',
      size: 6,
      row: 14,
      col: 0,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '674d8739ba60f67ec34bd991': {
      label: '备注',
      size: 6,
      row: 14,
      col: 1,
      placeholder: '请填写文本内容',
      type: 'string'
    },
    '673462791637ee6db9affd6c': {
      type: 'json',
      label: '修改项目阶段',
      size: 4,
      row: 16,
      col: 0,
      visibleOnCreate: false
    },
    '673462101637ee6db9affd57': {
      visible: false,
      type: 'json',
      // model: 'archive-sales-follow-record',
      label: '上次跟进',
      size: 6,
      row: 19,
      col: 0,
      visibleOnCreate: false,
      editable: false
    },
    '673462271637ee6db9affd61': {
      visible: false,
      type: 'json',
      // model: 'archive-sales-follow-record',
      label: '下次跟进',
      size: 6,
      row: 19,
      col: 1,
      visibleOnCreate: false,
      editable: false
    },
    '673ff6fbba60f67ec349889d': {
      label: '文本组合',
      size: 12,
      row: 21,
      col: 0,
      visible: false,
      type: 'string',
      editable: false
    },
    // '673462101637ee6db9affd58': {
    //   type: 'json',
    //   label: '子',
    //   size: 12,
    //   row: 22,
    //   col: 0,
    //   visible: false
    // },
    // '673462271637ee6db9affd62': {
    //   type: 'json',
    //   label: '子',
    //   size: 12,
    //   row: 23,
    //   col: 0,
    //   visible: false,
    // },
  }
}
