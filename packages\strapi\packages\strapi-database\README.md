# Strapi database layer

[![npm version](https://img.shields.io/npm/v/strapi-database.svg)](https://www.npmjs.org/package/strapi-database)
[![npm downloads](https://img.shields.io/npm/dm/strapi-database.svg)](https://www.npmjs.org/package/strapi-database)
[![npm dependencies](https://david-dm.org/strapi/strapi-database.svg)](https://david-dm.org/strapi/strapi-database)
[![Build status](https://travis-ci.org/strapi/strapi-database.svg?branch=master)](https://travis-ci.org/strapi/strapi-database)
[![Slack status](https://slack.strapi.io/badge.svg)](https://slack.strapi.io)

This package is strapi's database handling layer. It is responsible for orchestrating database connectors and implementing the logic of strapi's data structures.

This package is not meant to be used as a standalone module.

## Resources

- [License](LICENSE)

## Links

- [Strapi documentation](https://strapi.io/documentation)
- [Strapi website](https://strapi.io/)
- [Strapi community on Slack](https://slack.strapi.io)
- [Strapi news on Twitter](https://twitter.com/strapijs)
