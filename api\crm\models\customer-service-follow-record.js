module.exports = {
    "kind": "collectionType",
    "collectionName": strapi.config.server.mingdaoConfig.customerServiceFollowRecordId,
    "connection": "mingdao",
    info: {
        name: 'CustomerServiceFollowRecord',
        label: '直服经理跟进记录',
        description: '直服经理跟进记录'
    },
    "options": {},
    "pluginOptions": {},
    "attributes": {
        customerService: {
            // label: '客户',
            "ref": "6731c6391637ee6db9afde35"
        },
        productTag: {
            // label: '服务产品',
            "ref": "672db04463106d1d595a529b"
        },
        categoryTag: {
            // label: '服务类别',
            "ref": "672db04463106d1d595a529c"
        },
        startedAt: {
            // label: '服务时间',
            "ref": "672db04463106d1d595a529d"
        },
        follower: {
            // label: '跟进人',
            mainField: 'username',
            "ref": "672db04463106d1d595a529e"
        },
        contact: {
            // label: '联系人',
            mainField: 'title',
            "ref": "672db04463106d1d595a529f"
        },
        contactWay: {
            // label: '沟通方式',
            "ref": "672db04463106d1d595a52a0"
        },
        isConnect: {
            // label: '是否接通',
            "ref": "674464afba60f67ec349b10d"
        },
        isValid: {
            // label: '本次沟通是否有效',
            "ref": "672db04463106d1d595a52a2"
        },
        // eduSystem: {
        //     // label: '学制',
        //     "ref": "672db04463106d1d595a52a3"
        // },
        description: {
            // label: '沟通记录描述',
            "ref": "672db04463106d1d595a52a4"
        },
        // comment: {
        //     // label: '备注',
        //     "ref": "672db04463106d1d595a52a5"
        // },
        recordInvalid: {
            // label: '记录无效',
            "ref": "6732d8e21637ee6db9afede0"
        },
        // prevTrack: {
        //     // label: '上次服务',
        //     "ref": "672db04463106d1d595a52a7"
        // },
        // nextTrack: {
        //     // label: '下次服务',
        //     "ref": "672db04463106d1d595a52a8"
        // },
        // yjSchoolBusinessProcess: {
        //     // label: '阅卷数据源',
        //     "ref": "672db04463106d1d595a52a9"
        // },
        // salesId: {
        //     // label: '客户直营经理Id',
        //     "ref": "67357ca09b0246655126abe7"
        // },
        // serviceId: {
        //     // label: '客户直服经理Id',
        //     "ref": "67357ca09b0246655126abe8"
        // },
        salesQxId: {
            // label: '直营经理自定义Id',
            "ref": "6764f0eb9e13a09bfff366ef"
        },
        serviceQxId: {
            // label: '运营经理自定义Id',
            "ref": "6764f0019e13a09bfff366a7"
        },
    }
}