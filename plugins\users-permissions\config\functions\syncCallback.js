const axios = require('axios')
const { isString } = require('lodash')

// 同步微信消息回调机制
module.exports = async (dbConfig, originUrlRegex) => {
  let settings = dbConfig
  if (isString(dbConfig)) {
    const strapiDatabaseConfig = require('../../../../config/database')({
      env: (key, defaultValue) => {
        return {
          DATABASE: dbConfig,
          DATABASE_SSL: false,
          DATABASE_DEBUG: false,
        }[key] || defaultValue
      }
    })
    settings = strapiDatabaseConfig.connections.default.settings
  }

  const connectUrl = settings.username
    ? `mongodb://${settings.username}:${settings.password}@${settings.host}:${settings.port}/${settings.database}`
    : `mongodb://${settings.host}:${settings.port}/${settings.database}`

  // 创建正式库数据库远程连接
  const MongodbConnector = require('../../../../assist/utils/MongodbConnector')
  const mongodbConnector = new MongodbConnector()
  await mongodbConnector.init({
    main: {
      dbName: settings.database,
      url: connectUrl,
    },
  })

  const db = mongodbConnector.use('main')

  const requests = []

  async function syncPayCallbackRequest () {
    // wait server init
    if (!strapi.isLoaded) return

    const docs = await db.collection('SystemRequestLog').find({
      originalUrl: {
        $regex: originUrlRegex,
      },
      ip: { $ne: '127.0.0.1' },
      requestAt: {
        $gte: new Date(new Date() - 4000)
      }
    }).sort({
      requestAt: -1
    }).limit(10).toArray()

    for (let doc of docs) {
      const match = requests.find(e => e._id.toString() === doc._id.toString())
      if (!match) {
        // 创建请求
        try {
          console.info(`[${requests.length}] Callback Request Sync :` + doc.originalUrl)
          const contentType = /^\/wechat\/event/.test(doc.originalUrl) ? 'text/plain' : 'application/json'
          await axios({
            method: 'post',
            url: `http://localhost:${process.env.PORT}${doc.originalUrl}`,
            headers: { 'Content-Type': contentType },
            data: doc.requestBody
          })
        } catch (e) {
          console.info(`[${requests.length}] Wechat Callback Handle Error: `, e.toString())
        }
        // 保存请求
        requests.push(doc)
      }
    }
  }

  setInterval(syncPayCallbackRequest, 3000)
}
