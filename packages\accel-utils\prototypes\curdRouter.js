const { sanitizeEntity } = require('../lib')
const ExcelJS = require('exceljs')
const lodash = require('lodash')
const { parseCtxData, dataFieldProcess, getModelService, hasDraftAndPublish, modelDataExport } = require('../collection-utils')
const _ = require('lodash')

class CurdRouter {
  constructor (name, config = {}) {
    this.modelName = config.modelName || name
    // Plugin - eg. plugins::users-permissions.user
    if (config.pluginName) {
      this.modelName = `plugins::${config.pluginName}.${this.modelName}`
    }
    // Scope - eg. mingdao::tag
    if (config.scope) {
      this.modelName = `${config.scope}::${this.modelName}`
    }
    this.defaultSort = config.sort || ''
  }

  _getService (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    return getModelService(this.modelName)
  }

  _getModel (ctx) {
    // ctx 参数为扩展预留
    if (!ctx) throw new Error('ctx is required')
    return strapi.getModel(this.modelName)
  }

  _sanitizeEntity (ctx, entities) {
    if (Array.isArray(entities)) {
      return entities.map(
        entity => sanitizeEntity(entity, { model: this._getModel(ctx) }))
    }
    return sanitizeEntity(entities, { model: this._getModel(ctx) })
  }

  _parseCtx (ctx) {
    const { model, params, query, data, files, populate } = parseCtxData(ctx)
    if (!query._sort && this.defaultSort) {
      query._sort = this.defaultSort
    }
    return {
      model, params, query, data, files, populate
    }
  }

  _getExtendMethodNames () {
    const extendNames = []
    Object.getOwnPropertyNames(this.constructor.prototype).forEach(key => {
      if (
        key !== 'constructor'
        && !/^_/.test(key)
        && this[key].bind) {
        extendNames.push(key)
      }
    })
    return extendNames
  }

  async find (ctx) {
    const { query, populate } = this._parseCtx(ctx)
    let entities
    if (hasDraftAndPublish(this._getModel(ctx))) {
      query._publicationState = query._publicationState || 'live'
    }
    // 分页默认 50 最大为 100
    if (!query._limit) {
      query._limit = 50
    }
    if (+query._limit > 1000) {
      query._limit = 1000
    }
    if (lodash.has(query, '_q')) {
      entities = await this._getService(ctx).search({ params: query, populate })
    } else {
      entities = await this._getService(ctx).find({ params: query, populate })
    }
    return this._sanitizeEntity(ctx, entities)
  }

  async count (ctx) {
    const { query } = this._parseCtx(ctx)
    if (hasDraftAndPublish(this._getModel(ctx))) {
      query._publicationState = query._publicationState || 'live'
    }
    // 分页默认 50 最大为 100
    if (!query._limit) {
      query._limit = 50
    }
    if (+query._limit > 1000) {
      query._limit = 1000
    }
    if (lodash.has(query, '_q')) {
      return await this._getService(ctx).countSearch({ params: query })
    }
    return await this._getService(ctx).count({ params: query })
  }

  async findOne (ctx) {
    const { params } = this._parseCtx(ctx)
    if (hasDraftAndPublish(this._getModel(ctx))) {
      params._publicationState = params._publicationState || 'live'
    }
    const entity = await this._getService(ctx).findOne({ params })
    return this._sanitizeEntity(ctx, entity)
  }

  async create (ctx) {
    const { data, files } = this._parseCtx(ctx)
    try {
      await dataFieldProcess(data, this._getModel(ctx))
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const entity = await this._getService(ctx).create({ data, files })
    return this._sanitizeEntity(ctx, entity)
  }

  async update (ctx) {
    const { params, data, files } = this._parseCtx(ctx)
    try {
      await dataFieldProcess(data, this._getModel(ctx))
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const entity = await this._getService(ctx).update({ params, data, files })
    return this._sanitizeEntity(ctx, entity)
  }

  async updateMany (ctx) {
    const { data, files } = this._parseCtx(ctx)
    const model =  this._getModel(ctx);
    try {
      await dataFieldProcess(data, this._getModel(ctx))
    } catch (e) {
      return ctx.badRequest(e.toString())
    }
    const updateFilter = data.filter
    const updateData = data.data
    // 当前仅支持基于 id_in 批量更新数据
    if (!_.isArray(updateFilter.id_in)) {
      return ctx.badRequest('id_in is invalid')
    }
    const ids = updateFilter.id_in
    if (model.orm === 'mingdao') {
      return await this._getService(ctx).updateMany({ params: { ids: ids }, data: updateData, files })
    }
    const entities = await Promise.all(ids.map((id, index) => {
      const data = Array.isArray(updateData) ? updateData[index] : updateData
      const params = {
        ..._.omit(updateFilter, 'id_in'),
        id,
      }
      return this._getService(ctx).update({ params, data: data, files })
    }))
    return entities.length
  }

  async delete (ctx) {
    const { params } = this._parseCtx(ctx)
    const entity = await this._getService(ctx).delete({ params })
    return this._sanitizeEntity(ctx, entity)
  }

  async deleteMany (ctx) {
    const { data } = this._parseCtx(ctx)
    if (!_.isArray(data.filter.id_in)) {
      return ctx.badRequest('filter.id_in is invalid')
    }
    const entity = await this._getService(ctx).deleteMany({
      params: {
        ...data.filter
      }
    })
    return this._sanitizeEntity(ctx, entity)
  }

  // 导出数据
  async export (ctx) {
    const { query } = this._parseCtx(ctx)
    let entities
    // 导出表格列
    let includeAttributes
    if (query['_attributes']) {
      includeAttributes = query['_attributes']
      delete query['_attributes']
    }
    // 查询数据
    if (query._q) {
      entities = await this._getService(ctx).search({ params: query })
    } else {
      entities = await this._getService(ctx).find({ params: query })
    }
    // 生成 Excel
    const model = this._getModel(ctx)
    const modelName = model.info.label || model.info.name
    const workbook = modelDataExport(model, entities, { includeAttributes })
    const res = ctx.res
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    res.setHeader(
      'Content-Disposition',
      'attachment; filename=' + `${encodeURIComponent(modelName)}.xlsx`
    )
    ctx.body = await workbook.xlsx.writeBuffer()
  }

  // 导入数据覆盖更新接口
  async import (ctx) {
    const { files } = ctx.request
    const file = files.file
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.readFile(file.path)
    const worksheet = workbook.getWorksheet(1)
    const rows = []
    const attributeMap = {}
    worksheet.eachRow({ includeEmpty: true }, function (row, rowNumber) {
      if (rowNumber === 1) {
        row.values.forEach((value, index) => {
          attributeMap[index] = value
        })
      } else {
        // 排除空行
        if (row.values.join('')) {
          const obj = {}
          Object.keys(attributeMap).forEach(key => {
            obj[attributeMap[key]] = row.values[key]
          })
          rows.push(obj)
        }
      }
    })
    // 校验转换数据
    const model = this._getModel(ctx)
    const errorRows = []
    const entities = rows.map((row, index) => {
      const attributes = model.attributes
      let entity = {}
      Object.keys(attributes).forEach(field => {
        const filedName = attributes[field].label || field
        const value = row[field] || row[attributes[field].label]
        try {
          if (typeof value !== 'undefined') {
            if (['string', 'text', 'richtext', 'enumeration'].includes(attributes[field].type)) {
              if (!_.isString(value)) {
                errorRows.push({
                  index: index,
                  message: `【${filedName}】字段类型错误:  ${JSON.stringify(value)}`
                })
              }
              entity[field] = value
            } else if (['integer', 'biginteger', 'float', 'decimal'].includes(attributes[field].type)) {
              if (_.isNaN(+value)) {
                errorRows.push({
                  index: index,
                  message: `【${filedName}】字段类型错误: ${JSON.stringify(value)}`
                })
              }
              entity[field] = +value
            } else {
              entity[field] = JSON.parse(value)
            }
          }
        } catch (e) {
          console.info('Import Row Error: ', attributes[field], value)
        }
      })
      // 附加固定字段
      entity = {
        ...entity,
        ...(ctx._constFields || {})
      }
      return entity
    })
    if (errorRows.length > 0) {
      return {
        status: 'error',
        errorRows,
      }
    }
    // 更新数据
    const modifiedRows = []
    const upsertedRows = []
    const matchedRows = []
    await Promise.all(entities.map(async entity => {
      if (!entity.id) {
        await model.create(entity)
        upsertedRows.push(entity)
      } else {
        const count = await model.count({ id: entity.id })
        if (count > 0) {
          await model.updateOne({ id: entity.id }, _.omit(entity, ['id']))
          modifiedRows.push(entity)
          matchedRows.push(entity)
        }
      }
      return entity
    }))
    const modifiedCount = modifiedRows.length
    const upsertedCount = upsertedRows.length
    const matchedCount = matchedRows.length
    return {
      status: 'success',
      modifiedCount,
      modifiedRows,
      upsertedCount,
      upsertedRows,
      matchedCount,
      matchedRows,
    }
  }

  /**
   * @param {string[]} [names=]
   */
  createHandlers (names) {
    let routerMethodNames = []
    if (names) {
      routerMethodNames = names
    } else {
      const defaultNames = [
        'find', 'count', 'findOne',
        'create', 'update', 'updateMany', 'delete',
        'export', 'import', 'deleteMany',
      ]
      const extendNames = this._getExtendMethodNames()
      routerMethodNames = [
        ...defaultNames,
        ...extendNames,
      ]
    }
    const handlers = {}
    for (let name of routerMethodNames) {
      handlers[name] = this[name].bind(this)
    }
    return handlers
  }
}

module.exports = CurdRouter
