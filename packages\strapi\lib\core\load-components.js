'use strict';

const { join } = require('path');
const _ = require('lodash');
const { exists } = require('fs-extra');
const loadFiles = require('../load/load-files');

module.exports = async strapi => {
  const componentsDir = join(strapi.dir, 'components');

  const map = await loadFiles(componentsDir, '*/*.*(js|json)');

  const apiComponents =  Object.keys(map).reduce((acc, category) => {
    Object.keys(map[category]).forEach(key => {
      const schema = map[category][key];

      const filePath = join(componentsDir, category, schema.__filename__);

      if (!schema.collectionName) {
        return strapi.stopWithError(
          `Component ${key} is missing a "collectionName" property.\nVerify file ${filePath}.`
        );
      }

      const uid = `${category}.${key}`;

      acc[uid] = Object.assign(schema, {
        uid,
        category,
        modelType: 'component',
        modelName: key,
        globalId: schema.globalId || _.upperFirst(_.camelCase(`component_${uid}`)),
      });
    });

    return acc;
  }, {});

  const pluginsDir = join(strapi.dir, 'plugins');
  const pluginsComponents = await loadPluginComponents(pluginsDir)

  return {
    ...apiComponents,
    ...pluginsComponents
  }
};


const loadPluginComponents = async (dir, pattern = '*/components/**/*.+(js|json)') => {
  const componentsDir = dir;

  if (!(await exists(componentsDir))) {
    return {};
  }

  const allMap = await loadFiles(dir, pattern);
  const pluginComponents = {}
  Object.keys(allMap).reduce((pluginMap, plugin) => {
    Object.keys(allMap[plugin]).forEach(key => {
      const map = allMap[plugin][key];
      return Object.keys(map).forEach((category) => {
        Object.keys(map[category]).forEach(key => {
          const schema = map[category][key];
          const filePath = join(componentsDir, category, schema.__filename__);
          if (!schema.collectionName) {
            return strapi.stopWithError(
              `Component ${key} is missing a "collectionName" property.\nVerify file ${filePath}.`
            );
          }
          const uid = `${category}.${key}`;
          pluginComponents[uid] = Object.assign(schema, {
            uid,
            category,
            modelType: 'component',
            modelName: key,
            globalId: schema.globalId || _.upperFirst(_.camelCase(`component_${uid}`)),
          });
        });
      });
    });
    return pluginMap;
  }, {});

  return pluginComponents
};

