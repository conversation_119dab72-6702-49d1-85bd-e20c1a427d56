const _ = require('lodash')
const userRole = require('../../xiaoyun/utils/userRole')
const { ObjectId } = require('mongodb')

function _getIntersection(arr1, arr2) {
  const set = new Set(arr1)
  return arr2.filter(item => set.has(item))
}

function getBaseQuery(query) {
  if (!query.hasOwnProperty('deleted_ne') && !query.hasOwnProperty('deleted_eq') && !query.hasOwnProperty('deleted')) {
    query.deleted_ne = true
  }
  if (!query.hasOwnProperty('obsolete_ne') && !query.hasOwnProperty('obsolete_eq') && !query.hasOwnProperty('obsolete')) {
    query.obsolete_ne = true
  }
}

function getQueryFollowerChange(query, groups) {
  if (query.directServiceTeam_in || query.directServiceTeam_eq) {

    let teamsQuery = query.directServiceTeam_in || query.directServiceTeam_eq
    let teams = typeof teamsQuery === 'string' ? [teamsQuery] : teamsQuery
    const curGroups = groups.filter(e => teams.includes(e.id))
    const ids = _.flatten(curGroups.map(e => e.members))
    query.directServiceManager_in = query.directServiceManager_in
      ? query.directServiceManager_in.concat(ids)
      : ids
    delete query.directServiceTeam_in
    delete query.directServiceTeam_eq
  }

  if (query.directSalesTeam_in || query.directSalesTeam_eq) {
    let teamsQuery = query.directSalesTeam_in || query.directSalesTeam_eq
    let teams = typeof teamsQuery === 'string' ? [teamsQuery] : teamsQuery
    const curGroups = groups.filter(e => teams.includes(e.id))
    const ids = _.flatten(curGroups.map(e => e.members))
    query.directSalesManager_in = query.directSalesManager_in
      ? query.directSalesManager_in.concat(ids)
      : ids
    delete query.directSalesTeam_in
    delete query.directSalesTeam_eq
  }

  if (query.qunGroup_in || query.qunGroup_eq) {
    const curGroups = groups.filter(e => (query.directServiceTeam_in || [query.directServiceTeam_eq]).includes(e.id))
    const ids = _.flatten(curGroups.map(e => e.members))
    query.qunCreator_in = query.qunCreator_in
      ? query.qunCreator_in.concat(ids)
      : ids
    delete query.qunGroup_in
    delete query.qunGroup_eq
  }
}

function getUserQueryByRole(query, user, groups) {
  const curGroups = groups.filter(e => e.leader?.id === user.id)
  if (user.role.type === 'service-group-leader') {
    const ids = _.flatten(curGroups.map(e => e.members))
    query.directServiceManager_in = query.directServiceManager_in
      ? _getIntersection(query.directServiceManager_in, ids)
      : ids
  } else if (user.role.type === 'service-group-member') {
    query.directServiceManager_in = query.directServiceManager_in
      ? _getIntersection(query.directServiceManager_in, [user.id])
      : [user.id]
  } else if (user.role.type === 'sales-group-leader') {
    const ids = _.flatten(curGroups.map(e => e.members))
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, ids)
      : ids
  } else if (user.role.type === 'sales-group-member') {
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, [user.id])
      : [user.id]
  } else if (user.role.type === 'sales-manager') {
    const managerGroups = groups.filter(e => e.manager?.id === user.id)
    const ids = _.flatten(managerGroups.map(e => e.members))
    query.directSalesManager_in = query.directSalesManager_in
      ? _getIntersection(query.directSalesManager_in, ids)
      : ids
  }
}

// 查询条件转换
function getQueryKeyChange(query) {
  function buildNameKeyRegex(key) {
    if (_.isEmpty(key)) {
      return key
    }

    // 长度小于3
    if (key.length < 3) {
      return key
    }

    // 数字
    let num = Number(key)
    if (_.isFinite(num)) {
      return key
    }

    let nameRegex = _buildFullWordRegex(key)

    function _buildFullWordRegex(key) {
      if (_.isEmpty(key)) {
        return key
      }

      let orgKey = key

      let re = /省|市|县|乡|镇|区|学校/mg
      key = key.replace(re, '') // 过滤掉不需要的字符

      if (_.isEmpty(key)) {
        key = orgKey
      }
      // return key;

      let regex = ''
      let words = key.split('')
      words.forEach((word) => {
        regex = regex + '.*' + word
      })

      regex += '.*'

      return regex
    }

    return nameRegex
  }

  if (query._sch) {
    if (_.isFinite(+query._sch)) {
      query.schoolId_eq = +query._sch
    } else {
      query.name = { $regex: buildNameKeyRegex(query._sch) }
    }
    delete query._sch
  }
}

function getFollowerNowGroup(customerService, groups) {
  // 填充服务数据
  if (customerService['interactContacts']?.length) {
    customerService['interactContacts'] = customerService['interactContacts'].filter(e => e.wxStatus === 'finished')
  }
  if (customerService['directServiceManager']) {
    const curGroups = groups.filter(e => e.members.map(member => member.id).includes(customerService['directServiceManager'].id))
    customerService['directServiceTeam'] = curGroups
  }
  if (customerService['directSalesManager']) {
    const curGroups = groups.filter(e => e.members.map(member => member.id).includes(customerService['directSalesManager'].id))
    customerService['directSalesTeam'] = curGroups
  }
  if (customerService['qunCreator']) {
    const curGroups = groups.filter(e => e.members.map(member => member.id).includes(customerService['qunCreator'].id))
    customerService['qunGroup'] = curGroups
  }
  return customerService
}

async function getCustomersByUser(user) {
  let list = []
  if (userRole.isSales(user)) {
    list = await strapi.query('customer-service-mid').find({ directSalesManager: ObjectId(user.id), _limit: 10000 }, [])
  } else if (userRole.isService(user)) {
    list = await strapi.query('customer-service-mid').find({ directServiceManager: ObjectId(user.id), _limit: 10000 }, [])
  }
  return list.map(item => item.schoolId)
}

async function getCustomerBySchoolId(schoolId, populate) {
  if (typeof schoolId !== 'string' || !+schoolId > 0) {
    return null
  }
  return await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId }, populate)
}

async function getManagerBySchoolId(schoolId) {
  if (typeof schoolId !== 'string' || !(+schoolId > 0)) {
    return null
  }
  const customerService = await strapi.query('customer-service-mid').findOne({ schoolId: +schoolId }, ['directServiceManager', 'qun'])
  if (customerService) {
    let directServiceManager = null
    if (customerService.directServiceManager) {
      directServiceManager = {
        id: customerService.directServiceManager.id,
        qxId: customerService.directServiceManager.customId,
        name: customerService.directServiceManager.username,
        qrCodeUrl: customerService.directServiceManager.serviceManagerQrCode?.url
      }
    }
    let qun = null
    if (customerService.qun && !customerService.hideQunQrCode) {
      qun = {
        qrCodeUrl: `${strapi.config.server.serverUrl}/qun/qr_code/${customerService.qun.qunId}`,
        name: customerService.qun.name,
        id: customerService.qun.qunId
      }
    }

    return {
      customerId: customerService.customerId,
      schoolId: customerService.schoolId,
      name: customerService.name,
      id: customerService.id,
      directServiceManager: directServiceManager,
      qun: qun
    }
  }
  return null
}

module.exports = {
  getBaseQuery,
  getQueryFollowerChange,
  getUserQueryByRole,
  getQueryKeyChange,
  getFollowerNowGroup,
  getCustomersByUser,
  getManagerBySchoolId,
  getCustomerBySchoolId
}
