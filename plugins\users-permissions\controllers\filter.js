'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const { CurdRouter } = require('accel-utils')

const curdRouter = new CurdRouter('filter', { pluginName: 'users-permissions' })

async function addFilter (ctx) {
  const { pageId, name, filters, public: isPublic } = ctx.request.body
  if (!filters || !pageId || !name) {
    return ctx.badRequest('参数错误')
  }
  // 新增
  return strapi.query('filter', 'users-permissions').create({
    page: pageId,
    name,
    filters,
    public: !!isPublic,
    user: ctx.state.user.id,
    operator: ctx.state.user.id
  })
}

async function changeFilter (ctx) {
  const { id } = ctx.params
  const { name, filters, public: isPublic } = ctx.request.body
  if (!filters && !name && isPublic !== true && isPublic !== false) {
    return ctx.badRequest('参数错误')
  }
  const filter = await strapi.query('filter', 'users-permissions').findOne({ id }, [])
  if (!filter) {
    return ctx.badRequest('筛选器不存在')
  }
  if (filter.user !== ctx.state.user.id) {
    return ctx.badRequest('没有权限修改该筛选器')
  }
  let data = {
    operator: ctx.state.user.id
  }
  if (name) {
    data.name = name
  }
  if (filters) {
    data.filters = filters
  }
  if (isPublic === true || isPublic === false) {
    data.public = isPublic
  }

  // 修改
  return strapi.query('filter', 'users-permissions').update({ id }, data)
}

async function getFilters (ctx) {
  const { pageId } = ctx.request.query
  if (!pageId) {
    return ctx.badRequest('pageId 不能为空')
  }
  return await strapi.query('filter', 'users-permissions').find({
    _limit: -1,
    _where: {
      _or: [
        {
          page_eq: pageId,
          user_eq: ctx.state.user.id,
        },
        {
          page_eq: pageId,
          public_eq: true
        },
      ],
    }
  }, [])
}

async function deleteFilter (ctx) {
  const { id } = ctx.params
  const filter = await strapi.query('filter', 'users-permissions').findOne({ id }, [])
  if (!filter) {
    return ctx.badRequest('筛选器不存在')
  }
  return await strapi.query('filter', 'users-permissions').delete({ id })
}

module.exports = {
  addFilter,
  changeFilter,
  getFilters,
  deleteFilter,
  ...curdRouter.createHandlers()
}
