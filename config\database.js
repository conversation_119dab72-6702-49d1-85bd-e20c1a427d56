// 使用远程 mongo 数据库
module.exports = ({ env }) => {
  const dbConfig = {
    prod: {
      // host: '************',
      // port: 6010,
      // database: 'wly_boss',
      // username: 'wly_write',
      // password: 'fefzmXkBbGllR057',
      uri: 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary&slaveOk=true'
    },
    prodDev: {
      host: '************',
      port: 6010,
      database: 'wly_boss',
      username: 'wly_write',
      password: 'fefzmXkBbGllR057',
    },
    test: {
      uri: 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    },
    local: {
      // uri: 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
      uri: 'mongodb://localhost:27017/testwly-boss'
      // host: 'localhost',
      // port: 27017,
      // database: 'template-dev',
    },
  }[env('DATABASE', 'prod')]
  const yjAmsDbConfig = {
    prod: {
      uri: '*********************************************************************************************************************************************'
    },
    prodDev: {
      uri: '*********************************************************************************************************************************************'
    },
    test: {
      uri: 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    },
    local: {
      uri: 'mongodb://localhost:27017/ams'
    },
  }[env('DATABASE', 'prod')]
  return {
    defaultConnection: 'default',
    connections: {
      default: {
        connector: 'mongoose',
        settings: {
          client: 'mongo',
          ...dbConfig
        },
        options: {
          authenticationDatabase: dbConfig.database,
          ssl: env('DATABASE_SSL', false),
          debug: env('DATABASE_DEBUG', false),
        },
      },
      yjAms: {
        connector: 'mongoose',
        settings: {
          client: 'mongo',
          ...yjAmsDbConfig
        },
        options: {
          authenticationDatabase: yjAmsDbConfig.database,
          ssl: env('DATABASE_SSL', false),
          debug: env('DATABASE_DEBUG', false),
        },
      },
      mingdao: {
        connector: 'mingdao',
        settings: {
          // App Mode
          host: 'https://app.p0.cn',
          sign: 'ZGVkZTE2MjQ4MWU3ZDYxODg1N2Y3OGQxNzRhZjU5MzU1NTJlZWFhNWIwNWY2OWM0OGFiZTVlZDc3MmNkNDI2YQ==',
          appKey: 'd42f7e55640a90d1',
          // Web Mode
          projectId: '9922dfeb-4eba-4dea-a566-486ac53dd6fc',
          account: '<EMAIL>',
          password: 'meimeida2023',
          publicKey: `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC1xzCYtdu8bZEinh6Oh7/p+6xc
ilHgV/ChU3bZXyezLQqf6mzOnLH6GVZMMDafMw3uMtljWyECCqnECy2UhZPa5BFc
qA2xbYH8/WyKTraCRJT3Hn61UrI4Eac4YVxa1CJ8KaTQtIeZBoXHIW0r5XyhBwYe
NkSun+OFN+YBoJvCXwIDAQAB
-----END PUBLIC KEY-----`
          // account: '<EMAIL>',
          // password: 'tzmm.0920',
        },
        options: {},
      },
    },
  }
}
