const { omit, isUndefined } = require('lodash')

function _genPrefixHandlers (prefix) {
  return {
    find: prefix ? `${prefix}Find` : `find`,
    count: prefix ? `${prefix}Count` : `count`,
    export: prefix ? `${prefix}Export` : `export`,
    import: prefix ? `${prefix}Import` : `import`,
    findOne: prefix ? `${prefix}FindOne` : `findOne`,
    create: prefix ? `${prefix}Create` : `create`,
    update: prefix ? `${prefix}Update` : `update`,
    updateMany: prefix ? `${prefix}UpdateMany` : `updateMany`,
    delete: prefix ? `${prefix}Delete` : `delete`,
    deleteMany: prefix ? `${prefix}DeleteMany` : `deleteMany`,
  }
}

// 路由工具
function createDefaultRoutes ({
  basePath = '',
  controller,
  config = {},
  mode = '',
  methods = ['find', 'count', 'export', 'import', 'findOne', 'create', 'update', 'updateMany', 'delete', 'deleteMany'],
}) {
  const handlers = _genPrefixHandlers(mode)
  // Config Example
  // 'config': {
  //   'policies': [],
  //      'prefix': '',
  //      'description': 'Update an existing user',
  //      'tag': {
  //      'plugin': 'users-permissions',
  //        'name': 'User',
  //        'actionType': 'update'
  //      }
  // }
  const routeMap = {
    find: {
      path: `${basePath}`,
      method: 'GET',
      handler: `${controller}.${handlers.find}`,
      config: {
        policies: [],
        ...config,
      }
    },
    count: {
      path: `${basePath}/count`,
      method: 'GET',
      handler: `${controller}.${handlers.count}`,
      config: {
        policies: [],
        ...config,
      }
    },
    export: {
      path: `${basePath}/export`,
      method: 'GET',
      handler: `${controller}.${handlers.export}`,
      config: {
        policies: [],
        ...config,
      }
    },
    import: {
      path: `${basePath}/import`,
      method: 'POST',
      handler: `${controller}.${handlers.import}`,
      config: {
        policies: [],
        ...config,
      }
    },
    findOne: {
      path: `${basePath}/:id`,
      method: 'GET',
      handler: `${controller}.${handlers.findOne}`,
      config: {
        policies: [],
        ...config,
      }
    },
    create: {
      path: `${basePath}`,
      method: 'POST',
      handler: `${controller}.${handlers.create}`,
      config: {
        policies: [],
        ...config,
      }
    },
    update: {
      path: `${basePath}/:id`,
      method: 'PUT',
      handler: `${controller}.${handlers.update}`,
      config: {
        policies: [],
        ...config,
      }
    },
    updateMany: {
      path: `${basePath}`,
      method: 'PUT',
      handler: `${controller}.${handlers.updateMany}`,
      config: {
        policies: [],
        ...config,
      }
    },
    delete: {
      path: `${basePath}/:id`,
      method: 'DELETE',
      handler: `${controller}.${handlers.delete}`,
      config: {
        policies: [],
        ...config,
      }
    },
    deleteMany: {
      path: `${basePath}/actions/deleteMany`,
      method: 'POST',
      handler: `${controller}.${handlers.deleteMany}`,
      config: {
        policies: [],
        ...config,
      }
    },
  }
  return methods.map(name => {
    const route = routeMap[name]
    if (!route) throw new Error(`Route not found: ${name}`)
    return route
  })
}

// 权限配置工具
function createDefaultPermissions ({
  type = '',
  controller = '',
  mode = '',
  role = 'root',
}) {
  let handlers = _genPrefixHandlers(mode)
  // root - 全部权限
  // read - 读取数据权限
  // readWrite - 读取与修改数据权限、不包含批量操作
  if (role === 'read') {
    handlers = omit(handlers, ['create', 'update', 'delete', 'updateMany', 'deleteMany', 'export', 'import'])
  }
  if (role === 'readWrite') {
    handlers = omit(handlers, ['create', 'update', 'delete', 'updateMany', 'deleteMany'])
  }
  return [
    { 'type': type, 'controller': controller, 'action': handlers.find },
    { 'type': type, 'controller': controller, 'action': handlers.count },
    { 'type': type, 'controller': controller, 'action': handlers.export },
    { 'type': type, 'controller': controller, 'action': handlers.import },
    { 'type': type, 'controller': controller, 'action': handlers.findOne },
    { 'type': type, 'controller': controller, 'action': handlers.create },
    { 'type': type, 'controller': controller, 'action': handlers.update },
    { 'type': type, 'controller': controller, 'action': handlers.updateMany },
    { 'type': type, 'controller': controller, 'action': handlers.delete },
    { 'type': type, 'controller': controller, 'action': handlers.deleteMany },
  ].filter(e => !isUndefined(e.action))
}

module.exports = {
  createDefaultPermissions,
  createDefaultRoutes,
}
