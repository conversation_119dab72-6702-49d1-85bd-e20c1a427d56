const { MongoClient, ObjectId } = require('mongodb')
const moment = require('moment')
const _ = require('lodash')
const axios = require('axios')

const DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://localhost:27017/testwly-boss-back'
  // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
  : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const BOSS_DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://boss:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testboss?replicaSet=ReplsetTest&readPreference=secondaryPreferred'
  : '**********************************************************'

let db, dbClient
let bossDb, bossDbClient

const yjApiServer = typeof strapi !== 'undefined' ? strapi.config.server.yjApiServer : { yezhiUrl: 'https://yezhi.haofenshu.com' }
let now = new Date();

(async function () {
  dbClient = await MongoClient.connect(DB_URL)
  db = dbClient.db()
  bossDbClient = await MongoClient.connect(BOSS_DB_URL)
  bossDb = bossDbClient.db()
  try {
    logger('gen start')
    await main()
    logger('gen end')
  } catch (e) {
    console.log(e.stack || 'err')
  } finally {
    await dbClient.close()
    await bossDbClient.close()
    setTimeout(() => {
      process.exit(1)
    }, 5000)
  }
})()

function logger (msg) {
  const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
  console.log(`${dateStr}: ${msg}`)
}

async function main () {
  // eslint-disable-next-line no-constant-condition
  let lastSyncObjectId
  const limit = 5000
  let curCount = 0

  const newAuthTypes = ['运营授权', '项目合作', '直营合作', '开拓授权']
  while (true) {
    const conditions = {
      school_id_20: { $gt: 0 }
      // school_id_20: 28379
    }
    if (lastSyncObjectId) {
      conditions._id = { '$lt': ObjectId(lastSyncObjectId) }
    }

    let customers = await bossDb.collection('@Customer').find(conditions).sort({ _id: -1 }).limit(limit).toArray()
    // let customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //   coll: '@Customer',
    //   filter: conditions,
    //   sort: { _id: -1 },
    //   limit: limit
    // }).then(({ data }) => { return data });
    let customerIds = customers.map(e => e._id.toString())
    let customerServices = await db.collection('customer-service').find({ customerId: { $in: customerIds } }).toArray()
    // let customerServices = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
    //   db: 'wly_boss',
    //   coll: 'customer-service',
    //   filter: { customerId: { $in: customerIds } },
    //   sort: { _id: -1 },
    //   limit: limit
    // }).then(({ data }) => { return data });
    let customerMap = {}, customerServiceMap = {}
    for (const customer of customers) {
      if (customerMap[customer.school_id_20]) {
        console.log('重复school_id_20', customer.school_id_20)
        continue
      }
      customerMap[customer.school_id_20] = customer
    }
    for (const customerService of customerServices) {
      customerServiceMap[customerService.schoolId] = customerService
    }

    await _fillYjData(customerMap)
    customerMap = await _getAppVersions(customerMap)

    let bulkWriteArray = []
    let insertArray = []
    for (const schoolId in customerMap) {
      let customer = customerMap[schoolId]
      // let newAuth = customer?.['[proxy]']?.find(e => e.disabled !== 1 && newAuthTypes.includes(e.authorisation_type) && e?.product?.sign === 'saas' && new Date(e.end_time) > now)
      // let oldAuth = customer?.['[proxy]']?.find(e => e.disabled !== 1 && !newAuthTypes.includes(e.authorisation_type) && new Date(e.end_time) > now)
      // let auth = newAuth || oldAuth
      let curProxyList = _.orderBy(customer?.['[proxy]'], ['update_time'], ['desc'])
      let newAuths = curProxyList?.filter(e => newAuthTypes.includes(e.authorisation_type) && e?.product?.sign === 'saas')
      let oldAuths = curProxyList?.filter(e => !newAuthTypes.includes(e.authorisation_type))
      let auth = newAuths?.[0] || oldAuths?.[0]
      let customerService = customerServiceMap[schoolId]
      let yjTag = getYjTag(customer, customerService?.yjTag)

      // console.log(customer['[proxy]'])
      let defaultFields = {
        name: customer.name,
        customerId: customer._id.toString(),
        schoolId: customer.school_id_20,
        schoolCategory: getCustomerCategory(customer.yjUsage, yjTag),
        yjTag: yjTag,
        lastExamTime: customer?.lastExamTime,
        province: customer?.location?.province,
        city: customer?.location?.city,
        district: customer?.location?.district,
        edu_system: customer?.edu_system,
        system: customer?.system,
        mingyou_school_type: customer?.mingyou_school_type,
        total_number: customer?.total_number,
        school_size: customer?.school_size,
        crm_type: customer?.type,
        // s_count: customer?.s_count,
        // t_count: customer?.t_count,
        usages: customer?.usages,
        yjVersion: customer?.yjVersion,
        yjStartTime: customer?.yjStartTime,
        yjEndTime: customer?.yjEndTime,
        fxVersion: customer?.fxVersion,
        fxStartTime: customer?.fxStartTime,
        fxEndTime: customer?.fxEndTime,
        tkVersion: customer?.tkVersion,
        tkStartTime: customer?.tkStartTime,
        tkEndTime: customer?.tkEndTime,
        '[sales_manager]': customer && customer['[sales_manager]'],
        '[proxy]': customer && customer['[proxy]'],
        'deleted': customer.deleted === 1 ? true : false,

        authAgent: auth?.id ? ObjectId(auth?.id) : null,
        authAgentId: auth?.id,
        // authAgentNo: auth?.agent?.name,
        authAgentName: auth?.name,
        authType: newAuthTypes.includes(auth?.authorisation_type) ? auth?.authorisation_type : (auth?.authorisation_type ? '旧授权' : auth?.authorisation_type),
        authEndTime: auth?.end_time,
        authRemark: auth?.remark,
      }

      let customerServiceId = customerService?._id
      if (customerServiceId
        && defaultFields?.name == customerService?.name
        && defaultFields?.schoolCategory == customerService?.schoolCategory
        && defaultFields?.yjTag == customerService?.yjTag
        && new Date(defaultFields?.lastExamTime || null)?.getTime() == new Date(customerService?.lastExamTime || null)?.getTime()
        && defaultFields?.province == customerService?.province
        && defaultFields?.city == customerService?.city
        && defaultFields?.district == customerService?.district
        && defaultFields?.edu_system == customerService?.edu_system
        && defaultFields?.system == customerService?.system
        && defaultFields?.mingyou_school_type == customerService?.mingyou_school_type
        && defaultFields?.total_number?.toString() == customerService?.total_number?.toString()
        && defaultFields?.school_size == customerService?.school_size
        && defaultFields?.crm_type == customerService?.crm_type
        // && defaultFields?.usages == customerService?.usages
        && defaultFields?.yjVersion == customerService?.yjVersion
        && defaultFields?.yjStartTime?.getTime() == customerService?.yjStartTime?.getTime()
        && defaultFields?.yjEndTime?.getTime() == customerService?.yjEndTime?.getTime()
        && defaultFields?.fxVersion == customerService?.fxVersion
        && defaultFields?.fxStartTime?.getTime() == customerService?.fxStartTime?.getTime()
        && defaultFields?.fxEndTime?.getTime() == customerService?.fxEndTime?.getTime()
        && defaultFields?.tkVersion == customerService?.tkVersion
        && defaultFields?.tkStartTime?.getTime() == customerService?.tkStartTime?.getTime()
        && defaultFields?.tkEndTime?.getTime() == customerService?.tkEndTime?.getTime()
        && defaultFields?.authAgentId == customerService?.authAgentId
        && defaultFields?.authType == customerService?.authType
        && new Date(defaultFields?.authEndTime || null)?.getTime() == new Date(customerService?.authEndTime || null)?.getTime()
        && defaultFields?.authRemark == customerService?.authRemark
      ) {
        continue
        // } else {
        //   console.log(`name: ${customerService?.name} id: ${customer._id.toString()} schoolId: ${customer.school_id_20}`)
        //   console.log(`name:${defaultFields?.name == customerService?.name}`)
        //   console.log(`schoolCategory:${defaultFields?.schoolCategory == customerService?.schoolCategory}`)
        //   console.log(`yjTag:${defaultFields?.yjTag == customerService?.yjTag}`)
        //   console.log(`lastExamTime:${new Date(defaultFields?.lastExamTime || null)?.getTime() == new Date(customerService?.lastExamTime || null)?.getTime()}`)
        //   console.log(`defaultFields lastExamTime:${defaultFields?.lastExamTime}`)
        //   console.log(`customerService lastExamTime:${customerService?.lastExamTime}`)
        //   console.log(`province:${defaultFields?.province == customerService?.province}`)
        //   console.log(`city:${defaultFields?.city == customerService?.city}`)
        //   console.log(`district:${defaultFields?.district == customerService?.district}`)
        //   console.log(`edu_system:${defaultFields?.edu_system == customerService?.edu_system}`)
        //   console.log(`system:${defaultFields?.system == customerService?.system}`)
        //   console.log(`mingyou_school_type:${defaultFields?.mingyou_school_type == customerService?.mingyou_school_type}`)
        //   console.log(`total_number:${defaultFields?.total_number?.toString() == customerService?.total_number?.toString()}`)
        //   console.log(`school_size:${defaultFields?.school_size == customerService?.school_size}`)
        //   console.log(`crm_type:${defaultFields?.crm_type == customerService?.crm_type}`)
        //   // console.log(`usages:${defaultFields?.usages == customerService?.usages}`)
        //   console.log(`yjVersion:${defaultFields?.yjVersion == customerService?.yjVersion}`)
        //   console.log(`yjStartTime:${defaultFields?.yjStartTime?.getTime() == customerService?.yjStartTime?.getTime()}`)
        //   console.log(`yjEndTime:${defaultFields?.yjEndTime?.getTime() == customerService?.yjEndTime?.getTime()}`)
        //   console.log(`fxVersion:${defaultFields?.fxVersion == customerService?.fxVersion}`)
        //   console.log(`fxStartTime:${defaultFields?.fxStartTime?.getTime() == customerService?.fxStartTime?.getTime()}`)
        //   console.log(`fxEndTime:${defaultFields?.fxEndTime?.getTime() == customerService?.fxEndTime?.getTime()}`)
        //   console.log(`tkVersion:${defaultFields?.tkVersion == customerService?.tkVersion}`)
        //   console.log(`tkStartTime:${defaultFields?.tkStartTime?.getTime() == customerService?.tkStartTime?.getTime()}`)
        //   console.log(`tkEndTime:${defaultFields?.tkEndTime?.getTime() == customerService?.tkEndTime?.getTime()}`)
        //   console.log(`authAgentId:${defaultFields?.authAgentId == customerService?.authAgentId}`)
        //   console.log(`authType:${defaultFields?.authType == customerService?.authType}`)
        //   console.log(`authEndTime:${new Date(defaultFields?.authEndTime || null)?.getTime() == new Date(customerService?.authEndTime || null)?.getTime()}`)
        //   console.log(`authRemark:${defaultFields?.authRemark == customerService?.authRemark}`)
        // console.log(`authType:${defaultFields?.authAgentName} ${customerService?.authAgentName}`)
        // console.log(`authType:${defaultFields?.authType} ${customerService?.authType}`)
      }

      if (customerService) {
        if (yjTag && yjTag !== customerService.yjTag) {
          await db.collection('customer-op-log').insertOne({
            customerService: customerService._id,
            type: 'yjTag',
            content: {
              oldYjTag: customerService.yjTag,
              newYjTag: yjTag
            },
            createdAt: new Date(),
            updatedAt: new Date()
          })
        }
        bulkWriteArray.push({
          updateOne: {
            filter: { customerId: customer._id.toString() },
            update: { $set: defaultFields }
          }
        })
      } else {
        let result = await db.collection('customer-service').insertOne(defaultFields)
        customerServiceId = result.insertedId
        if (yjTag) {
          await db.collection('customer-op-log').insertOne({
            customerService: result.insertedId,
            type: 'yjTag',
            content: {
              oldYjTag: null,
              newYjTag: yjTag
            },
            createdAt: new Date(),
            updatedAt: new Date()
          })
        }
        // bulkWriteArray.push({ insertOne: { document: defaultFields } })
      }

      // 过滤明道和boss删除的学校
      if (customer.deleted !== 1) {
        insertArray.push({
          type: 'school',
          customerId: customer._id.toString(),
          customer: customerServiceId,
          status: 'prepared',
          createdAt: now,
          updatedAt: now,
        })
      }
    }

    if (insertArray.length > 0) {
      console.log(`待更新数量：${insertArray.length}`)
      const results = await db.collection('mingdao-queue').insertMany(insertArray)
      // console.log(results)
    }

    if (bulkWriteArray.length > 0) {
      const results = await db.collection('customer-service').bulkWrite(bulkWriteArray)
      // console.log(results)
    }

    if (customers.length === 0) { break }
    lastSyncObjectId = customers[customers.length - 1]._id.toString()
    if (customers.length < limit) { break }
    curCount = curCount + limit
    // console.log(customerServices.length)
  }
}

async function _fillYjData (customerMap) {
  const yjClueUrl = `${yjApiServer.yezhiUrl}/api/schoolClue/crm/query/list`
  const res = await axios.post(yjClueUrl, {
    'p': 1,
    's': 20000,
    'schoolIds': Object.values(customerMap).filter(e => e.school_id_20).map(e => e.school_id_20)
  }, {
    headers: {
      'clue-api-key': 'MTc2MjAwMzAyNjU5NzU2OjE3NjIwMDMwMjY1OmN1c3RvbVNlcnZpY2U6Q1JN5a+55o6l5LiT55SoOjE6NlFYRk1XOTBkOHBHdEw0aDoxNzYyMDAzMDI2NTo6ZmFsc2U='
    }
  })
  const yjClues = res.data?.data?.item
  // console.log(yjClues)
  for (let yjClue of yjClues) {
    if (customerMap[yjClue.schoolId]) {
      customerMap[yjClue.schoolId].lastExamTime = yjClue.lastExamTime && yjClue.lastExamTime !== '' && new Date(yjClue.lastExamTime) || null
      customerMap[yjClue.schoolId].liankaoExamTimes = yjClue.liankaoExamTimes
    }
  }
}

async function _getAppVersions (customerMap) {
  let appVersions = await bossDb.collection('@CustomerAppVersion').find({ deleted: { $ne: 1 }, }).toArray()
  let customerAppsUsages = await bossDb.collection('@CustomerAppsUsage').find({ customer_id: { $in: Object.values(customerMap).map(e => e._id.toString()) } }).toArray()
  let productSettings = await bossDb.collection('@CustomerAppSettings').find({ type: 'productCategory' }).toArray()
  let appSettings = await bossDb.collection('@CustomerAppSettings').find({ type: 'appCategory' }).toArray()
  // let appVersions = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
  //   coll: '@CustomerAppVersion',
  //   filter: { deleted: { $ne: 1 } },
  //   limit: 10000
  // }).then(({ data }) => { return data });
  // let customerAppsUsages = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
  //   coll: '@CustomerAppsUsage',
  //   filter: { customer_id: { $in: Object.values(customerMap).map(e => e._id.toString()) } },
  //   limit: 10000
  // }).then(({ data }) => { return data });
  // let productSettings = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
  //   coll: '@CustomerAppSettings',
  //   filter: { type: 'productCategory' },
  //   limit: 10000
  // }).then(({ data }) => { return data });
  // let appSettings = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
  //   coll: '@CustomerAppSettings',
  //   filter: { type: 'appCategory' },
  //   limit: 10000
  // }).then(({ data }) => { return data });

  let appVersionMap = {}, customerAppsUsageMap = {}, productSettingMap = {}, appSettingMap = {}
  for (const appVersion of appVersions) {
    appVersionMap[appVersion.type] = appVersion
  }
  for (const customerAppsUsage of customerAppsUsages) {
    customerAppsUsageMap[customerAppsUsage.customer_id] = customerAppsUsage
  }
  for (const productSetting of productSettings) {
    productSettingMap[productSetting.sign] = productSetting
  }
  for (const appSetting of appSettings) {
    appSettingMap[appSetting.sign] = appSetting
  }

  for (const schoolId in customerMap) {
    const customerAppsUsage = customerAppsUsageMap[customerMap[schoolId]._id.toString()]
    let usages = [], yjUsage
    let yjVersion, yjStartTime, yjEndTime, fxVersion, fxStartTime, fxEndTime, tkVersion, tkStartTime, tkEndTime
    for (const usage of (customerAppsUsage?.usages || [])) {
      const appVersion = appVersionMap[usage.type]

      let curUsage = {
        type: usage.type,
        status: usage.status,
        begin_time: new Date(usage.begin_time),
        end_time: new Date(usage.end_time),
        enabled: usage.enabled,
        is_trial: usage.is_trial,
      }
      if (appVersion) {
        curUsage.appVersion = {
          version_name: appVersion.name,
          type: appVersion.type,
          app_category: appVersion.app_category,
          app_category_name: appSettingMap[appVersion.app_category]?.name || appVersion.app_category,
          product_category: appVersion.product_category,
          product_category_name: productSettingMap[appVersion.product_category]?.name || appVersion.product_category,
        }
      }
      usages.push(curUsage)

      if (appVersion?.product_category === 'saas') {
        yjVersion = appVersion.name
        yjStartTime = new Date(usage.begin_time)
        yjEndTime = new Date(usage.end_time)
        yjUsage = curUsage
      }
      if (appVersion?.product_category === 'fenxi') {
        fxVersion = appVersion.name
        fxStartTime = new Date(usage.begin_time)
        fxEndTime = new Date(usage.end_time)
      }
      if (appVersion?.product_category === 'p_2') {
        tkVersion = appVersion.name
        tkStartTime = new Date(usage.begin_time)
        tkEndTime = new Date(usage.end_time)
      }

    }
    customerMap[schoolId].usages = usages
    customerMap[schoolId].yjUsage = yjUsage
    customerMap[schoolId].yjVersion = yjVersion
    customerMap[schoolId].yjStartTime = yjStartTime
    customerMap[schoolId].yjEndTime = yjEndTime
    customerMap[schoolId].fxVersion = fxVersion
    customerMap[schoolId].fxStartTime = fxStartTime
    customerMap[schoolId].fxEndTime = fxEndTime
    customerMap[schoolId].tkVersion = tkVersion
    customerMap[schoolId].tkStartTime = tkStartTime
    customerMap[schoolId].tkEndTime = tkEndTime
  }
  return customerMap
}

function getCustomerCategory (yjUsage, yjTag) {
  if (!yjTag || yjTag === '体验校' || yjTag === '流失校') return yjTag

  let yjEndTime = moment(yjUsage.end_time)
  let today = moment().startOf('days')
  let diff = yjEndTime.diff(today, 'days')
  if (yjTag === '付费校') {
    if (diff <= 30) {
      return '临期1个月'
    } else if (diff <= 60) {
      return '临期2个月'
    } else if (diff <= 90) {
      return '临期3个月'
    } else if (diff <= 180) {
      return '临期半年'
    } else {
      return '付费校'
    }
  } else if (yjTag === '应续校') {
    if (diff >= -30) {
      return '应续1个月内'
    } else if (diff >= -60) {
      return '应续2个月内'
    } else if (diff >= -90) {
      return '应续3个月内'
    } else if (diff >= -180) {
      return '应续半年'
    } else {
      return '应续超过半年'
    }
  } else {
    return yjTag
  }
}

let flowMap = {
  '付费校': ['付费校', '应续校', '流失校'],
  '应续校': ['应续校', '流失校'],
  '流失校': ['流失校']
}

// 学校标签初始化后， 标签流转
function getYjTag (customer, oldYjTag) {
  if (customer.type === '联考') {
    return oldYjTag
  }
  if (!oldYjTag) {
    return getInitYjTag(customer.yjUsage)
  }
  if (oldYjTag === '体验校') return oldYjTag

  let vipYjTag = getVipYjTag(customer.yjUsage)
  if (flowMap[oldYjTag].includes(vipYjTag)) {
    // 符合流转规则。返回流转数据
    return vipYjTag
  } else {
    // 不符合流转规则。返回原数据
    return oldYjTag
  }
}

function getInitYjTag (yjUsage) {

  if (!yjUsage) {
    return null
  }

  let yjVersion = yjUsage.appVersion.version_name
  let isTrial = yjUsage.is_trial

  if (yjVersion === '体验版' || isTrial) {
    return '体验校'
  }
  return null
}

function getVipYjTag (yjUsage) {
  let type = null
  if (!yjUsage) {
    return ''
  }
  let yjVersion = yjUsage.appVersion.version_name
  let today = moment()
  let yjStartTime = moment(yjUsage.begin_time)
  let yjEndTime = moment(yjUsage.end_time)

  let diff = yjEndTime.diff(today, 'days')

  if (yjEndTime.isSameOrBefore(today) && diff >= -365) {
    return '应续校'
  } else if (diff < -365) {
    return '流失校'
  } else {
    return '付费校'
  }
}
