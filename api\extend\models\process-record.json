{"collectionName": "processRecord", "info": {"name": "ProcessRecord", "label": "流程记录", "description": "流程记录"}, "options": {"draftAndPublish": false, "timestamps": true}, "pluginOptions": {}, "attributes": {"bossId": {"label": "关联bossId", "type": "string"}, "model": {"label": "关联model", "type": "string"}, "modelId": {"label": "关联modelId", "type": "string"}, "api": {"label": "关联api", "model": "api"}, "workflow": {"label": "关联工作流", "model": "workflow"}}}