module.exports = {
  collectionName: 'customer-op-log',
  info: {
    name: 'CustomerOpLog',
    label: '客户档案操作日志',
    description: '客户档案操作日志'
  },
  options: {
    draftAndPublish: false,
    timestamps: true,
  },
  pluginOptions: {},
  attributes: {
    customerService: {
      label: '客户',
      model: 'customer-service-mid',
      required: true,
      visible: false
    },
    operator: {
      label: '操作人',
      model: 'user',
      plugin: 'users-permissions',
      visible: false
    },
    type: {
      label: '类型',
      type: 'string',
      options: [
        { label: '联系人加微信', value: 'wx' },
        { label: '高意向标记', value: 'required' },
        { label: '学校标签变更', value: 'yjTag' },
      ],
      visible: false,
      size: 4
    },
    content: {
      label: '内容',
      type: 'json',
    },
  }
}
