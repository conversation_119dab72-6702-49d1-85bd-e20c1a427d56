const _ = require('lodash')
const axios = require('axios')

const { CurdRouter } = require('accel-utils')
const { createCacheFunction } = require('../../crm-mid/utils/cache')
const { syncQxUserByDepartment, syncBusinessUser } = require('../services/manger')
const curdRouter = new (class extends CurdRouter {

  // _appendBaseFilter (ctx) {
  //   ctx.request.query.provider = 'yxWeCom'
  // }

  // async find (ctx) {
  //   this._appendBaseFilter(ctx)
  //   return await super.find(ctx)
  // }

  // async count (ctx) {
  //   this._appendBaseFilter(ctx)
  //   return await super.count(ctx)
  // }

})('manager')

// async function getUserGroupUnit() {
//   let units = await strapi.query('manager-unit').find({ _limit: 99999 })
//   let groups = await strapi.query('manager-group').find({ _limit: 99999 })
//   let managers = await strapi.query('manager').find({ _limit: 99999 })
//   return {
//     units,
//     groups,
//     managers,
//   }
// }

async function relationMangerUser(ctx) {
  let managers = await strapi.query('manager').find({ _limit: 9999 })
  const customIds = managers.map(item => item.customId)

  const users = await strapi.query('user', 'users-permissions').find({
    customId: { $in: customIds },
    _limit: 9999,
    _projection: { customId: 1, id: 1 }
  }, [])

  for (const user of users) {
    const curManger = managers.find(e => e.customId === user.customId)
    await strapi.query('user', 'users-permissions').update({
      id: user.id,
    }, {
      mingdaoId: curManger?.id,
    });
  }

  return ctx.wrapper.succ({})
}

async function syncBusinessQxUser(ctx) {
  let { departments } = ctx.request.body
  await syncBusinessUser(ctx, departments);
  return ctx.wrapper.succ({})
}

async function syncQxUser(ctx) {
  let { departments } = ctx.request.body
  await syncQxUserByDepartment(ctx, departments);
  return ctx.wrapper.succ({})
}

module.exports = {
  relationMangerUser,
  syncBusinessQxUser,
  syncQxUser,
  ...curdRouter.createHandlers(),
}
