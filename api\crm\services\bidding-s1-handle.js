const { similar } = require("../utils/stringUtil");
const _ = require("lodash");
const { isUndefinedOrEmpty } = require("../utils/lodashUplevel");
const { changeDateToBeijingTime } = require("../utils/datetime");
const { retry } = require("../utils/requestRetry");

const projectStatusLabelMap = {
  '待处理': 'f976d579-78fa-45ee-9855-93a1a06d7f84',
  '已忽略': '5e12105c-89fe-4ea5-bc54-707d88cc7a5a',
  '已关注': '205eddca-2cdb-4d92-9af5-718208986f04',
  '已立项': 'b14c29dc-06b5-4a7a-b2d4-6c4edf369c0f'
}
async function hasCreatedBiddingS1Project(project) {
  // 如果参数不符合条件直接返回
  if (isUndefinedOrEmpty(project.buyer) || isUndefinedOrEmpty(project.title)) {
    return true;
  }
  const ownedBiddingS1 = await retry(strapi.query('bidding-s1-project').find, 3, 0, {
    title: project.title,
    buyer: project.buyer,
    budget: project.buyer
  })
  return !!(ownedBiddingS1 && ownedBiddingS1.length > 0);
}

function compareBiddingProject(project, oldProject) {
  if (isUndefinedOrEmpty(oldProject.buyer) || isUndefinedOrEmpty(oldProject.budget)) {
    return similar(project.title, oldProject.title, 2) >= 85
  }
  return (
    project.buyer === oldProject.buyer &&
    project.budget === oldProject.budget &&
    similar(project.title, oldProject.title, 2) >= 85
  );
}

async function updateBiddingProjectPreprocessed(project) {
  await strapi.query('bidding-project').update({
    id: project.id
  }, {
    preprocessed: true
  })
}

async function saveToMingdaoDB(project) {
  let checkResult = await hasCreatedBiddingS1Project(project);
  // 幂等性校验，判重
  if (checkResult) {
    await retry(updateBiddingProjectPreprocessed, 3, 0, project);
    return;
  }
  let result = await retry(strapi.query('bidding-s1-project').create, 3, 0, {
    title: project.title,
    buyer: project.buyer,
    status: projectStatusLabelMap['待处理'],
    budget: project.budget,
    publishedAt: project.publishTime,
    mainBiddingProjects: [project.id],
  });
  await retry(updateBiddingProjectPreprocessed, 3, 0, project);
  return result;
}

async function transferToPreprocessed(project) {
  // 验证是否已经合并过了
  if (project.preprocessed) {
    return
  }
  let totalMergedArr = []
  let allCount = await retry(strapi.query('bidding-project-analysis').count, 3, 0, {
    id_ne: null,
  })

  for (let j = 1; j <= Math.ceil(allCount / 100); j++) {
    let preprocessedArr = await retry(strapi.query("bidding-project-analysis").find, 3, 0, {
      _where: {
        buyer: project.buyer,
        budget: project.budget,
      },
      _sort: "ctime:ASC",
      _start: (j - 1) * 100,
      _limit: 100,
    })
    if (preprocessedArr.length === 0) {
      continue;
    }
    let mergedArr = preprocessedArr.filter(proProject => {
      return compareBiddingProject(project, proProject)
    })
    if (!_.isEmpty(mergedArr)) {
      totalMergedArr.push(...mergedArr)
    }
  }
  if (totalMergedArr.length > 1) {
    totalMergedArr.reverse()
    let projects = await retry(strapi.query('bidding-s1-project').find, 3, 0, {
      mainBiddingProjects_in: totalMergedArr.map(mergedArr => mergedArr.biddingProjectId)
    })
    if (_.isEmpty(projects)) {
      await saveToMingdaoDB(project)
      return;
    }
    let projectResult = projects[0];
    await retry(strapi.query('bidding-s1-project').update, 3, 0,
      {
        id: projectResult.id
      }, {
      mainBiddingProjects: [totalMergedArr[0]?.biddingProjectId],
      relationBiddingProjects: totalMergedArr.map(mergedArr => mergedArr.biddingProjectId).slice(1),
    }
    )
    await retry(strapi.query('bidding-project').update, 3, 0, {
      id: project.id
    }, {
      preprocessed: true
    })
  } else {
    await saveToMingdaoDB(project)
  }
  totalMergedArr = []
}

function preparedBiddingProjectAnalysis(item) {
  let obj = {}
  obj.biddingProjectId = item.id
  obj.ctime = new Date()
  obj.title = item.title
  obj.buyer = item.buyer
  obj.budget = item.budget
  obj.winner = item.winner
  obj.bidAmount = item.bidAmount
  obj.type = item.type
  obj.subtype = item.subtype
  obj.publishTime = item.publishTime
  obj.area = item.area
  return obj;
}

async function scanBiddingProject() {
  const sixMonthsAgo = new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)
  for (let j = 1; j < 40000; j++) {
    let arr = await retry(strapi.query("bidding-project").find, 3, 0, {
      ctime_gt: sixMonthsAgo,
      preprocessed_ne: true,
      ignore_ne: true,
      _limit: 100,
      _start: (j - 1) * 100,
      _sort: "ctime:ASC"
    })
    if (arr.length === 0) {
      break;
    }
    for (let i = 0; i < arr.length; i++) {
      await transferToPreprocessed(arr[i])
    }
    arr = []
  }
}

module.exports = {
  preparedBiddingProjectAnalysis,
  scanBiddingProject,
}
