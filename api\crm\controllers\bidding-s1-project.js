const { CurdRouter } = require('accel-utils')
const path = require('path')
const fs = require('fs')
const _ = require('lodash')

const projectStatusMap = {
  'f976d579-78fa-45ee-9855-93a1a06d7f84': '待处理',
  '5e12105c-89fe-4ea5-bc54-707d88cc7a5a': '已忽略',
  '205eddca-2cdb-4d92-9af5-718208986f04': '已关注',
  'b14c29dc-06b5-4a7a-b2d4-6c4edf369c0f': '已立项',
}

const logTypeMapReverse = {
  '忽略': 'f976d579-78fa-45ee-9855-93a1a06d7f84',
  '关注': '5e12105c-89fe-4ea5-bc54-707d88cc7a5a',
  '跟进': '205eddca-2cdb-4d92-9af5-718208986f04',
  '提醒注意': 'a00ccb1a-71a0-4809-b41a-e0d43430c4db',
}

const statusLogTypeMap = {
  '已忽略': '忽略',
  '已关注': '关注',
  '已立项': '跟进',
}

const curdRouter = new (class extends CurdRouter {
  async find (ctx) {
    const rows = await super.find(ctx)
    const relateProjectIds = rows.map(e => {
      return [
        ...e.mainBiddingProjects.map(e => e.id),
        ...e.relationBiddingProjects.map(e => e.id),
      ]
    }).flat()
    const relateProjects = await strapi.query('bidding-project').find({
      id: relateProjectIds,
      _limit: 9999
    }, [])
    const biddingS2Projects = await strapi.query('bidding-s2-project').find({
      biddingS1Project_eq: rows.map(e => e.id),
      _limit: 9999
    }, [])
    const midBiddingProjectIds = _.map(biddingS2Projects, item => ({ ...item, biddingS1ProjectId: _.map(item.biddingS1Project, 'id') }))
    rows.forEach(async e => {
      e.mainBiddingProjects = relateProjects.filter(j => e.mainBiddingProjects.map(v => v.id).includes(j.id))
      e.type = e.mainBiddingProjects[0].type
      e.relationBiddingProjects = relateProjects.filter(j => e.relationBiddingProjects.map(v => v.id).includes(j.id))
      e.biddingS2Projects = midBiddingProjectIds.filter(j => _.includes(j.biddingS1ProjectId, e.id))
    })
    return rows
  }

  // 操作记录 忽略/跟进/关注
  async _logStatusChange (user, projectIds, updateData) {
    if (!updateData.status) return
    const statusName = projectStatusMap[updateData.status]
    const logTypeName = statusLogTypeMap[statusName]
    const logTypeValue = logTypeMapReverse[logTypeName]
    await Promise.all(projectIds.map(id => {
      return strapi.query('bidding-s1-project-log').create({
        type: logTypeValue,
        ...(user.mingdaoId ? { operator: user.mingdaoId } : {}),
        s1Project: [id],
        info: JSON.stringify({
          userId: user.id,
          username: user.username,
          updateData: updateData,
        }),
      })
    }))
  }

  async update (ctx) {
    const { params, data } = this._parseCtx(ctx)
    const user = ctx.state.user
    const result = await super.update(ctx)
    await this._logStatusChange(user, [params.id], data)
    return result
  }

  async updateMany (ctx) {
    const { data } = this._parseCtx(ctx)
    const result = await super.updateMany(ctx)
    const user = ctx.state.user
    const updateData = data.data
    const ids = data.filter.id_in
    await this._logStatusChange(user, ids, updateData)
    return result
  }


})('bidding-s1-project')

// 处理关联项目数据
async function exportRelationData(ctx) {
  try {
    const batchSize = 100;
    let hasMore = true;
    let start = 0;
    let totalProcessed = 0;

    console.log('开始处理bidding-s1-project数据...');

    while (hasMore) {
      // 分批查询数据
      const records = await strapi.query('bidding-s1-project').find({
        _start: start,
        _limit: batchSize,
        relationBiddingProjects_null: false
      });

      if (records.length === 0) {
        hasMore = false;
        break;
      }

      // 处理每条记录
      const updatePromises = records.map(record => {
        if (record.relationBiddingProjects && record.relationBiddingProjects.length > 0) {
          // 获取当前的mainBiddingProjects的id
          const mainProjectIds = record.mainBiddingProjects.map(p => p.id);
          
          // 获取relationBiddingProjects的id数组
          let relationProjectIds = record.relationBiddingProjects.map(p => p.id);
          
          // 将mainBiddingProjects的id添加到relationBiddingProjects末尾
          relationProjectIds = [...relationProjectIds, ...mainProjectIds];
          
          // 取relationBiddingProjects的第一个id作为新的mainBiddingProjects
          const newMainProjectId = relationProjectIds.shift();
          
          // 更新数据
          return strapi.query('bidding-s1-project').update(
            { id: record.id },
            {
              mainBiddingProjects: [newMainProjectId],
              relationBiddingProjects: relationProjectIds
            }
          );
        }
        return Promise.resolve();
      });

      // 批量执行更新
      await Promise.all(updatePromises);

      totalProcessed += records.length;
      console.log(`已处理 ${totalProcessed} 条记录`);
      start += batchSize;
    }

    return {
      message: '处理完成',
      totalProcessed
    };

  } catch (error) {
    console.error('处理过程中发生错误:', error);
    throw error;
  }
}

module.exports = {
  ...curdRouter.createHandlers(),
  exportRelationData
};
