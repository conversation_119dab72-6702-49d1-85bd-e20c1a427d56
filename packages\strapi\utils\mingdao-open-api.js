const axios = require('axios')
const { isNil, omit } = require('lodash')

/**
 * 创建明道云 Open API 连接
 * @param {object} settings
 * @param {string} settings.host
 * @param {string} settings.appKey
 * @param {string} settings.sign
 */
function createOpenApiConnection (settings) {
  const host = settings.host
  const appKey = settings.appKey
  const sign = settings.sign

  // API Connection Info
  createOpenApiConnection.connectionCount = createOpenApiConnection.connectionCount || 0
  const connectionId = createOpenApiConnection.connectionCount++

  let mingdaoRequestCounter = 0

  /**
   * 请求明道云 Open API
   * @param {AxiosRequestConfig} config
   * @returns {Promise<AxiosResponse<any>> | *}
   */
  async function mingdaoOpenApi (config) {
    // BaseURL
    config.baseURL = host
    // Authorization
    config.method = config.method || 'GET'
    const method = config.method.toUpperCase()
    if (method === 'GET') {
      config.params = {
        ...config.params,
        appKey: appKey,
        sign: sign
      }
    }
    if (method === 'POST') {
      config.data = {
        ...config.data,
        appKey: appKey,
        sign: sign
      }
    }
    let requestTag = `[OpenApi:${connectionId}] [${mingdaoRequestCounter++}] ${config.method.toUpperCase()} ${config.url}`
    if (config.params) {
      requestTag += ' ' + JSON.stringify(omit(config.params, ['appKey', 'sign']))
    }
    if (config.data) {
      requestTag += ' ' + JSON.stringify(omit(config.data, ['appKey', 'sign']))
    }
    console.time(requestTag)
    let res
    try {
      res = await axios(config)
    } catch (e) {
      console.warn(requestTag, e)
      throw e
    } finally {
      console.timeEnd(requestTag)
    }
    if (res.data.success === false) {
      throw Error(JSON.stringify(res.data))
    }
    return res.data.data
  }

  // 获取应用信息
  async function getApp () {
    return await mingdaoOpenApi({ url: '/api/v1/open/app/get' })
  }

  // // 获取工作表信息
  // async function getWorksheetInfo (worksheetId) {
  //   return await mingdaoOpenApi({
  //     url: '/api/v2/open/worksheet/getWorksheetInfo',
  //     method: 'post',
  //     data: {
  //       worksheetId: worksheetId
  //     }
  //   })
  // }

  /**
   * 列表获取
   * @param { MingdaoApiFilterRowParams } params
   * @returns {Promise<MingdaoApiGetFilterRowsData>}
   */
  async function getFilterRows (params) {
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/getFilterRows',
      method: 'post',
      data: {
        pageSize: 50,
        pageIndex: 1,
        filters: [],
        listType: 0,
        controls: [],
        getSystemControl: false,
        ...params
      }
    })
  }

  // 获取关联记录
  async function getRowRelations (params) {
    const { worksheetId, rowId, controlId, pageSize, pageIndex } = params
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/getRowRelations',
      method: 'post',
      data: {
        worksheetId: worksheetId,
        rowId: rowId,
        controlId: controlId,
        pageSize: pageSize,
        pageIndex: pageIndex,
      }
    })
  }

  // 行记录详情获取
  async function getRowByIdPost (worksheetId, rowId, { getSystemControl } = { getSystemControl: false }) {
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/getRowByIdPost',
      method: 'post',
      data: {
        worksheetId: worksheetId,
        rowId: rowId,
        getSystemControl: getSystemControl
      }
    })
  }

  /**
   * 行记录新增
   * @param {MingdaoApiAddRowParams} params
   * @returns {Promise<string>}
   */
  async function addRow (params) {
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/addRow',
      method: 'post',
      data: {
        worksheetId: params.worksheetId,
        controls: params.controls,
        triggerWorkflow: isNil(params.triggerWorkflow) ? true : params.triggerWorkflow,
      }
    })
  }

  /**
   * 行记录更新
   * @param {MingdaoApiEditRowParams} params
   * @returns {Promise<string>}
   */
  async function editRow (params) {
    console.info('editRow', params)
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/editRow',
      method: 'post',
      data: {
        worksheetId: params.worksheetId,
        controls: params.controls,
        rowId: params.rowId,
        triggerWorkflow: isNil(params.triggerWorkflow) ? true : params.triggerWorkflow,
      }
    })
  }

  /**
   * 批量更新行记录
   * @param {MingdaoApiEditRowsParams} params
   * @returns {Promise<string>}
   */
  async function editRows (params) {
    console.info('editRow', params)
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/editRows',
      method: 'post',
      data: {
        worksheetId: params.worksheetId,
        controls: params.controls,
        rowIds: params.rowIds,
        triggerWorkflow: isNil(params.triggerWorkflow) ? true : params.triggerWorkflow,
      }
    })
  }

  /**
   * 行记录删除
   * @param {MingdaoApiDeleteRowParams} params
   * @returns {Promise<any>}
   */
  async function deleteRow (params) {
    return await mingdaoOpenApi({
      url: '/api/v2/open/worksheet/deleteRow',
      method: 'post',
      data: {
        worksheetId: params.worksheetId,
        rowId: params.rowId,
        triggerWorkflow: isNil(params.triggerWorkflow) ? true : params.triggerWorkflow,
      }
    })
  }

  return {
    getApp,
    // getWorksheetInfo,
    getFilterRows,
    getRowRelations,
    getRowByIdPost,
    addRow,
    editRow,
    editRows,
    deleteRow,
  }
}

module.exports = {
  createOpenApiConnection
}
