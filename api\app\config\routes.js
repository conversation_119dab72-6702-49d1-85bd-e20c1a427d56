const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    {
      method: 'POST',
      path: '/record-app/loginByQxId',
      handler: 'record-app.loginByQxId',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/record-app/loginByQxCode',
      handler: 'record-app.loginByQxCode',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/record-app/logout',
      handler: 'record-app.logout',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/record-app/upload',
      handler: 'record-app.upload',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/record-app/list',
      handler: 'record-app.getRecords',
      config: {
        policies: [], prefix: '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/telephone-records',
      controller: 'telephone-record'
    }),
    {
      method: 'GET',
      path: '/app-updates/latest',
      handler: 'app-update.getLatest',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/app-updates/downloadUrl',
      handler: 'app-update.getDownloadUrl',
      config: {
        policies: [], prefix: '',
      }
    },
    ...createDefaultRoutes({
      basePath: '/app-updates',
      controller: 'app-update'
    }),
  ]
}
