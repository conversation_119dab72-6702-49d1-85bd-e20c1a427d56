const { createDefaultRoutes } = require('accel-utils')

module.exports = {
  'routes': [
    ...createDefaultRoutes({
      basePath: '/customer-service-mids',
      controller: 'customer-service-mid'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-service-hfs',
      controller: 'customer-service-hfs'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-service-tags',
      controller: 'customer-service-tag'
    }),
    ...createDefaultRoutes({
      basePath: '/xkw-service-applies',
      controller: 'xkw-service-apply'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-op-logs',
      controller: 'customer-op-log'
    }),
    ...createDefaultRoutes({
      basePath: '/customer-problems',
      controller: 'customer-problem'
    }),
    ...createDefaultRoutes({
      basePath: '/qx-users',
      controller: 'qx-user'
    }),
    ...createDefaultRoutes({
      basePath: '/manager-group-mids',
      controller: 'manager-group-mid'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-monthly-process',
      controller: 'sales-monthly-process'
    }),
    ...createDefaultRoutes({
      basePath: '/sales-daily-process',
      controller: 'sales-daily-process'
    }),
    {
      method: 'POST',
      path: '/sales-monthly-process/updateSalesProcessData',
      handler: 'sales-monthly-process.updateSalesProcessData',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/sales-monthly-process/initSalesMonthlyProcess',
      handler: 'sales-monthly-process.initSalesMonthlyProcess',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/customer/actions/statistic',
      handler: 'customer-service-mid.statistic',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/customer/actions/getYjSupportToken',
      handler: 'customer.getYjSupportToken',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/customer-problem/actions/statistic',
      handler: 'customer-problem.statistic',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/customer-problem/feedbackByTeacher',
      handler: 'customer-problem.feedbackByTeacher',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/external/customer-services/service-manager',
      handler: 'customer-service-mid.getCustomerManager',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/external/customer-services/listByWxId',
      handler: 'customer-service-mid.getCustomerListByWxId',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'GET',
      path: '/external/customer-services/getSchoolTag',
      handler: 'customer-service-mid.getSchoolTag',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/external/customer-services/changeSchoolTag',
      handler: 'customer-service-mid.changeSchoolTag',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/customer-service-hfs/action/openAppUsages',
      handler: 'customer-service-hfs.openAppUsages',
      config: {
        policies: [], prefix: '',
      }
    },
    {
      method: 'POST',
      path: '/customer-service-mids/bindQunInfo',
      handler: 'customer-service-mid.bindQunInfo',
      config: {
        policies: [], prefix: '',
      }
    },
  ]
}
