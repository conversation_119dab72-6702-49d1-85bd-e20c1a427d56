// COS
const COS = require('cos-nodejs-sdk-v5')
const STS = require('qcloud-cos-sts')
const { resolveFile } = require('./upload-utils')

const syncGetCredential = async (config, policy) => {
  return new Promise((resolve, reject) => {
    STS.getCredential({
      secretId: config.SecretId,
      secretKey: config.SecretKey,
      proxy: config.Proxy,
      durationSeconds: config.DurationSeconds,
      policy: policy,
    }, function (err, tempKeys) {
      if (err) {
        reject(err)
      } else {
        resolve(JSON.stringify(err || tempKeys) || '')
      }
    })
  })
}

// // COS Config
// SecretId: '',
// SecretKey: '',
// Bucket: '',
// Region: '',
// Proxy: '',
// // 有效期
// DurationSeconds: 1800,
// AllowPrefix: '*',
// AllowActions: [
//   '*'
//   // 简单上传
//   // 'name/cos:PutObject',
//   // 'name/cos:PostObject',
//   // 分片上传
//   // 'name/cos:InitiateMultipartUpload',
//   // 'name/cos:ListMultipartUploads',
//   // 'name/cos:ListParts',
//   // 'name/cos:UploadPart',
//   // 'name/cos:CompleteMultipartUpload'
// ],

function initCos (cosOptions) {
  const cos = new COS({
    SecretId: cosOptions.SecretId,
    SecretKey: cosOptions.SecretKey,
  })
  return {
    async putObject ({ Key, Body }) {
      return await cos.putObject({
        Bucket: cosOptions.Bucket,
        Region: cosOptions.Region,
        Key: Key,
        Body: Body,
      })
    },
    async deleteObject ({ Key }) {
      return await cos.deleteObject({
        Bucket: cosOptions.Bucket,
        Region: cosOptions.Region,
        Key: Key,
      })
    },
    async getObjectUrl ({ Key, Expires }) {
      return await cos.getObjectUrl({
        Bucket: cosOptions.Bucket,
        Region: cosOptions.Region,
        Key: Key,
        Sign: true,
        Expires: Expires || 3600, // 单位秒
      })
    },
    async getSecurityToken () {
      const shortBucketName = cosOptions.Bucket.substr(0,
        cosOptions.Bucket.lastIndexOf('-'))
      const appId = cosOptions.Bucket.substr(1 + cosOptions.Bucket.lastIndexOf('-'))
      const policy = {
        'version': '2.0',
        'statement': [
          {
            'action': cosOptions.AllowActions,
            'effect': 'allow',
            'principal': { 'qcs': ['*'] },
            'resource': [
              'qcs::cos:' + cosOptions.Region + ':uid/' + appId + ':prefix//' +
              appId + '/' + shortBucketName + '/' + cosOptions.AllowPrefix,
            ],
          }],
      }
      /** @type {string} */
      const credential = await syncGetCredential(cosOptions, policy)
      return {
        ...JSON.parse(credential),
        bucket: cosOptions.Bucket,
        region: cosOptions.Region,
      }
    },
    // 上传文件
    async uploadFileToBucket (fileName, fileContent, dir = 'production/upload') {
      const {
        key, size, buffer
      } = await resolveFile(fileName, fileContent, dir)
      const result = await this.putObject({ Key: key, Body: buffer })
      return {
        key, size, buffer,
        ...result
      }
    },
    // 删除文件
    async deleteFileFromBucket (key) {
      return await this.deleteObject({ Key: key })
    },
    // 获取文件签名链接
    async getBucketSignedUrl (key, expires) {
      return await this.getObjectUrl({
        Key: key,
        Expires: expires || 3600, // 单位秒
      })
    },
  }
}

module.exports = {
  initCos
}
