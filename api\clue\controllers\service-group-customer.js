const { CurdRouter } = require('accel-utils')
const { getBaseQuery, } = require('../../crm/services/customer-service')

const curdRouter = new (class extends CurdRouter {

  // ...
  _getIntersection(arr1, arr2) {
    const set = new Set(arr1)
    return arr2.filter(item => set.has(item))
  }

  _getQueryByUser(query, user) {
    query._where = {
      unit_null: false,
      directServiceTeam_null: false,
      directServiceManager_null: true,
    };
    // 有单元 有运营组 没有运营跟进人的学校
    // 除了admin 外只有只看本组资源
    if (user.role.type !== 'admin'
      && user.role.type !== 'SuperAdmin'
      // && user.role.type !== 'sales-admin'
    ) {
      if (query.directServiceTeam_in) {
        query.directServiceTeam_in = query.directServiceTeam_in
          ? this._getIntersection(query.directServiceTeam_in, user.mingdaoGroupIds || [])
          : user.mingdaoGroupIds || []
        delete query.directServiceTeam_in
      } else {
        query.directServiceTeam_in = user.mingdaoGroupIds || []
      }
    }
    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.find(ctx)
  }
})('service-group-customer')

module.exports = {
  ...curdRouter.createHandlers(),
}
