const { isUndefined, isArray } = require('lodash');

function convertValueToRefValue(value, attribute) {
  const optionsMap = attribute?.options || []
    if (!isUndefined(value) && optionsMap.length > 0 ) {
        if (!isArray(value)) {
            // 先检查 optionsMap 中是否有匹配的 label
            const matchOptionMap = optionsMap.find(opt => opt.refValue === value)
            if (matchOptionMap) {
                return matchOptionMap.value
            }
            // 如果 optionsMap 中没找到，返回原值
            return value
        }

        if (isArray(value)) {
            return value.map(item => {
                // 检查 optionsMap 中是否有匹配的 label
                const matchOptionMap = optionsMap.find(opt => opt.refValue === item)
                return matchOptionMap ? matchOptionMap.value : item
            })
        }
    }
    return value;
}

function revertRefAttributeFromSourceAttr(refAttribute, sourceAttr) {
    // 单选的下拉和平铺， model配置的选项，不要覆盖从control得来的选项
    if(refAttribute?.controlType == 9 || refAttribute.controlType == 11) {
      refAttribute.options = sourceAttr.options || []
    }
}

// 提取model中配置的某单选属性的options
function getOptionsMap(model, control) {
  let optionsMap = []
  if (model.options?.attributes) {
    Object.entries(model.options.attributes).forEach(([key, attr]) => {
      if (attr.options && attr.ref && attr.ref === control.controlId) {
        optionsMap = attr.options
      }
    })
  }
  return optionsMap;
}

module.exports = {
  convertValueToRefValue,
  revertRefAttributeFromSourceAttr,
  getOptionsMap
}
