'use strict'

const _ = require('lodash')
const axios = require('axios')
const jwt = require('jsonwebtoken')
const { inRange } = require('lodash')

module.exports = async (ctx, next) => {
  let role

  if (ctx.state.user) {
    // request is already authenticated in a different way
    return next()
  }

  // 从 Header、URL Query、RequestBody 中获取 token
  const token = ctx.request?.header?.authorization
    || ctx.request?.query?.token
    || ctx.request?.body?.token

  // 从 Header中获取 bossCookie huobanBossCookie 允许使用 bossCookie huobanBossCookie 直接调用本服务接口
  const bossCookie = ctx.cookies.get('BOSSID')
  const huobanBossCookie = ctx.cookies.get('HUOBAN_BOSSID')
  const site = ctx.request.header['site']

  const goToken = ctx.cookies.get('GO_TOKEN') || ctx.request?.query?.go_token || ctx.request?.body?.go_token

  if (token || bossCookie || (huobanBossCookie && site === 'huoban') || goToken) {
    try {
      if (token) {
        const { id, tokenId } = await strapi.plugins['users-permissions'].services.jwt.getToken(ctx)
        if (tokenId) {
          ctx.state.token = await strapi.query('token', 'users-permissions').findOne({ id: tokenId }, [])
        }
        if (id === undefined) {
          throw new Error('Invalid token: Token did not contain required fields')
        }
        // fetch authenticated user
        ctx.state.user = await strapi.plugins[
          'users-permissions'
        ].services.user.fetchAuthenticatedUser(id)
      } else if (huobanBossCookie && site === 'huoban') {
        let userWxId = null
        let userName = null
        const cookieData = jwt.verify(huobanBossCookie, strapi.config.server.bossSecret)
        // 获取wxid
        let response = await axios.post(`${strapi.config.server.bossApi.url}/user/get_user_by_id`, {
          id: cookieData.login_user_id,
          fields: ['weixin_id', 'name']
        })
        let instanceData
        if (response.status === 200) {
          instanceData = response.data
          userWxId = instanceData.weixin_id
          userName = instanceData.name
        }

        // 同步 book 用户
        const iPlugin = strapi.plugins['users-permissions']

        if (userWxId) {
          let user = await iPlugin.services['user'].findOne({ customId: userWxId })
          // 用户不存在，先创建一个
          if (!user) {
            const bossRole = await strapi.query('role', 'users-permissions').findOne({ type: 'bossAgent' })

            const branch = await iPlugin.services['branch'].getCtxDefaultBranch(ctx)

            user = await iPlugin.services['user'].createNewUser(ctx, {
              username: userName,
              pBranch: branch.id,
              // avatar: res.data.avatar || '',
              customId: userWxId,
              provider: 'yxWeCom',
              role: bossRole.id
            }, null)
          }
          ctx.state.user = user
        }
      } else if (bossCookie || goToken) {
        let userWxId = null
        let userName = null
        if (goToken) {
          const res = await axios.get('https://a.iyunxiao.com/org:my/profile', {
            headers: {
              cookie: 'GO_TOKEN=' + goToken
            },
          })
          userWxId = res.data?.userid
          userName = res.data?.name
        } else {
          const cookieData = jwt.verify(bossCookie, strapi.config.server.bossSecret)
          // 获取wxid
          let response = await axios.post(`${strapi.config.server.bossApi.url}/user/get_user_by_id`, {
            id: cookieData.login_user_id,
            fields: ['weixin_id', 'name']
          })
          let instanceData
          if (response.status === 200) {
            instanceData = response.data
            userWxId = instanceData.weixin_id
            userName = instanceData.name
          }
        }

        // 同步 book 用户
        const iPlugin = strapi.plugins['users-permissions']

        if (userWxId) {
          let user = await iPlugin.services['user'].findOne({ customId: userWxId })
          // 用户不存在，先创建一个
          if (!user) {
            const bossRole = await strapi.query('role', 'users-permissions').findOne({ type: 'boss' })

            const branch = await iPlugin.services['branch'].getCtxDefaultBranch(ctx)

            user = await iPlugin.services['user'].createNewUser(ctx, {
              username: userName,
              pBranch: branch.id,
              // avatar: res.data.avatar || '',
              customId: userWxId,
              provider: 'yxWeCom',
              role: bossRole.id
            }, null)
          }
          ctx.state.user = user
        }
      }
    } catch (err) {
      if (err.message === 'Invalid token.' || err.message === 'invalid signature') {
        // bossCookie 和 Token 无效状态允许请求 public 接口
        const role = await strapi.query('role', 'users-permissions').findOne({ type: 'public' }, [])
        const route = ctx.request.route
        const permission = await strapi.query('permission', 'users-permissions').findOne(
          {
            role: role.id,
            type: route.plugin || 'application',
            controller: route.controller,
            action: route.action,
            enabled: true,
          },
          []
        )
        if (!permission) {
          return handleErrors(ctx, err, 'unauthorized')
        }
        // Execute the policies.
        if (permission.policy) {
          return await strapi.plugins['users-permissions'].config.policies[permission.policy](ctx, next)
        }
        return await next()
      }
      return handleErrors(ctx, err, 'unauthorized')
    }

    if (!ctx.state.user) {
      return handleErrors(ctx, 'User Not Found', 'unauthorized')
    }

    role = ctx.state.user.role
    if (role?.type === 'root') {
      return await next()
    }

    const store = await strapi.store({
      environment: '',
      type: 'plugin',
      name: 'users-permissions',
    })

    if (
      _.get(await store.get({ key: 'advanced' }), 'email_confirmation') &&
      !ctx.state.user.confirmed
    ) {
      return handleErrors(ctx, 'Your account email is not confirmed.', 'unauthorized')
    }
    // 平台屏蔽
    if (ctx.state.user.blocked) {
      return handleErrors(
        ctx,
        '您的账号已经被平台管理员屏蔽，如有疑问请联系平台管理员。',
        'unauthorized'
      )
    }
    // 租户内屏蔽
    const branch = ctx.state.user.pBranch
    if (ctx.state.user.pBranch) {
      const branchConfig = ctx.state.user.pBranchConfigs?.find(e => e.branchId === branch.id)
      if (branchConfig && branchConfig.blocked) {
        return handleErrors(
          ctx,
          '您的账号已经被企业管理员屏蔽，如有疑问请联系企业管理员。',
          'unauthorized'
        )
      }
    }
  }

  if (ctx.request.route.controller !== 'api') {
    // Retrieve `public` role.
    if (!role) {
      role = await strapi.query('role', 'users-permissions').findOne({ type: 'public' }, [])
    }

    const route = ctx.request.route
    const permission = await strapi.query('permission', 'users-permissions').findOne(
      {
        role: role.id,
        type: route.plugin || 'application',
        controller: route.controller,
        action: route.action,
        enabled: true,
      },
      []
    )

    if (!permission) {
      return handleErrors(ctx, undefined, 'forbidden')
    }

    // Execute the policies.
    if (permission.policy) {
      return await strapi.plugins['users-permissions'].config.policies[permission.policy](ctx, next)
    }
  }

  // 附加 Query Filter
  const queryFilters = strapi.config.permission.queryFilters
  const route = ctx.request.route
  if (queryFilters) {
    for (let queryFilter of queryFilters) {
      const type = route.plugin || 'application'
      const controller = route.controller
      const action = route.action
      if (
        (queryFilter.type === type || (_.isRegExp(queryFilter.type) && queryFilter.type?.test(type)))
        && (queryFilter.controller === controller || (_.isArray(queryFilter.controller) && queryFilter.controller.includes(controller)))
        && (queryFilter.action === action || (_.isRegExp(queryFilter.action) && queryFilter.action?.test(action)))
      ) {
        await queryFilter.callback(ctx)
      }
    }
  }
  // Execute the action.
  await next()
}

const handleErrors = (ctx, err = undefined, type) => {
  throw strapi.errors[type](err)
}
