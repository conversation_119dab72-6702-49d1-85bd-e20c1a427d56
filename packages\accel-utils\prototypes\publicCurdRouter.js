const UserCurdRouter = require('./userCurdRouter')
const _ = require('lodash')

class PublicCurdRouter extends UserCurdRouter {
  constructor (name, config = {}) {
    super(name, config)
    this.mode = config.mode
  }

  async publicFind (ctx) {
    return super.find(ctx)
  }

  async publicCount (ctx) {
    return super.count(ctx)
  }

  async publicFindOne (ctx) {
    return super.findOne(ctx)
  }

  async publicCreate (ctx) {
    return super.selfCreate(ctx)
  }

  async publicUpdate (ctx) {
    return super.update(ctx)
  }

  async publicUpdateMany (ctx) {
    return super.updateMany(ctx)
  }

  async publicDelete (ctx) {
    return super.delete(ctx)
  }

  async publicDeleteMany (ctx) {
    return super.deleteMany(ctx)
  }

  async publicExport (ctx) {
    return super.export(ctx)
  }

  async publicImport (ctx) {
    return super.import(ctx)
  }

  /**
   * @param {string[]} [names=]
   */
  createHandlers (names) {
    if (names) return super.createHandlers(names)
    const defaultNames = [
      'publicFind', 'publicCount', 'publicFindOne',
      'publicCreate', 'publicUpdate', 'publicUpdateMany', 'publicDelete',
      'publicExport', 'publicImport', 'publicDeleteMany',
    ]
    const extendNames = this._getExtendMethodNames()
    return super.createHandlers([
      ...defaultNames,
      ...extendNames,
    ])
  }
}

module.exports = PublicCurdRouter
