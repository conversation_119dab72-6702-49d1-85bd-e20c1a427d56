{"fieldsMap": {"员工姓名": "2200000174352823", "企信ID": "2200000453852092", "系统账号": "2200000174352824", "外部用户": "2200000490677013", "所属阿米巴单元": "2200000458417626", "阿米巴负责人": "2200000458452250", "部门负责人": "2200000449792462", "电话号码": "2200000174352825", "性别": "2200000174352826", "直营小组": "2200000488098177", "直营小组长": "2200000488255282", "外部用户-组长": "2200000492820905", "状态": "2200000174352830", "业务线（弃用）": "2200000448573779", "部门（弃用）": "2200000178130086", "岗位（弃用）": "2200000174352829", "岗位名称（弃用）": "2200000449305470", "外部用户-昵称": "1121001102000000", "外部用户-手机号": "1121001103000000", "所属阿米巴单元-经营单元": "1116001101000000", "所属阿米巴单元-单元负责人": "1116001114000000", "所属阿米巴单元-团队成员": "1116001115000000"}, "table_id": "2100000018513284", "name": "员工信息", "alias": "", "space_id": "4000000003570865", "created_on": "2021-11-09 17:29:20", "fields": [{"field_id": "2200000174352823", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000453852092", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000174352824", "name": "系统账号", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000490677013", "name": "外部用户", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "2200000458417626", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "2200000458452250", "name": "阿米巴负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000449792462", "name": "部门负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "2200000174352825", "name": "电话号码", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000174352826", "name": "性别", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "男"}, {"id": "2", "name": "女"}]}}, {"field_id": "2200000488098177", "name": "直营小组", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059956977", "space_id": "4000000003570865"}}, {"field_id": "2200000488255282", "name": "直营小组长", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "2200000492820905", "name": "外部用户-组长", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000060332234", "space_id": "4000000003570865"}}, {"field_id": "2200000174352830", "name": "状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "在职"}, {"id": "2", "name": "离职"}]}}, {"field_id": "2200000448573779", "name": "业务线（弃用）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "1#渠道"}, {"id": "2", "name": "2#项目"}, {"id": "3", "name": "3#精准练"}, {"id": "4", "name": "4#会员流量"}, {"id": "5", "name": "5#自营"}, {"id": "6", "name": "总部"}, {"id": "7", "name": "其他"}, {"id": "8", "name": "1#5 王元元"}, {"id": "9", "name": "1#3 冯新征"}, {"id": "10", "name": "1#4 陈林"}, {"id": "11", "name": "1#6 霍兵"}, {"id": "12", "name": "1#7 李鹏展"}]}}, {"field_id": "2200000178130086", "name": "部门（弃用）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "19", "name": "精准练单元"}, {"id": "22", "name": "项目单元"}, {"id": "14", "name": "项目一部"}, {"id": "15", "name": "项目二部"}, {"id": "17", "name": "项目三部"}, {"id": "26", "name": "项目标杆部"}, {"id": "9", "name": "综合支持"}, {"id": "8", "name": "渠道单元"}, {"id": "11", "name": "渠道一部"}, {"id": "12", "name": "渠道二部"}, {"id": "21", "name": "渠道三部"}, {"id": "23", "name": "渠道四部"}, {"id": "24", "name": "渠道五部"}, {"id": "25", "name": "渠道六部"}, {"id": "30", "name": "服务团队"}, {"id": "29", "name": "直营团队"}, {"id": "20", "name": "先锋[1]队"}, {"id": "27", "name": "先锋[2]队"}, {"id": "28", "name": "先锋[3]队"}, {"id": "31", "name": "会员流量"}, {"id": "18", "name": "销管团队"}]}}, {"field_id": "2200000174352829", "name": "岗位（弃用）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "1", "name": "销售"}, {"id": "2", "name": "业务管理"}, {"id": "11", "name": "渠道总监"}, {"id": "10", "name": "渠道经理"}, {"id": "3", "name": "中台"}, {"id": "4", "name": "产研"}, {"id": "5", "name": "项目管理"}, {"id": "7", "name": "售前"}, {"id": "8", "name": "实施服务"}, {"id": "9", "name": "课程规划"}]}}, {"field_id": "2200000449305470", "name": "岗位名称（弃用）", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "区域经理"}, {"id": "2", "name": "大客户经理"}]}}, {"field_id": "1121001102000000", "name": "昵称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000490677013}, "required": false, "description": "", "config": {}}, {"field_id": "1121001103000000", "name": "手机号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000490677013}, "required": false, "description": "", "config": {}}, {"field_id": "1116001101000000", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000458417626}, "required": false, "description": "", "config": {}}, {"field_id": "1116001114000000", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000458417626}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1116001115000000", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000458417626}, "required": false, "description": "", "config": {"is_multi": 1}}]}