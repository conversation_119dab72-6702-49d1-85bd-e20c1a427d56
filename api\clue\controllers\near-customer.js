const { CurdRouter } = require('accel-utils')
const { getBaseQuery, } = require('../../crm/services/customer-service')
const curdRouter = new (class extends CurdRouter {

  _getQueryByUser(query, user) {
    // 临期校
    // authAgentName
    // schoolCategory
    // 临期标签：{ 临期半年、3个月、2个月、1个月 } 包含其中之一
    // 授权经销商名称：已填写
    query.unit_null = true
    query.directServiceTeam_null = true
    query.directSalesTeam_null = true
    query.directServiceManager_null = true
    query.directSalesManager_null = true
    query.crm_type_ne = '13cc0c2a-c4a7-4f35-ad4e-08c1ecc430b7' // 单次联考

    // query.authAgentName_null = false
    // query.authEndTime_gte = new Date()

    const options = strapi.query('clue-customer').model.attributes['schoolCategory'].options
    query.schoolCategory_in = options.filter(v => ['临期半年','临期3个月', '临期2个月', '临期1个月'].includes(v.label)).map(v => v.value)

    return query
  }

  async count(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.count(ctx)
  }

  async find(ctx) {
    const user = ctx.state.user
    let { query } = this._parseCtx(ctx)
    query = this._getQueryByUser(query, user)
    getBaseQuery(query)
    return super.find(ctx)
  }
})('near-customer')

module.exports = {
  ...curdRouter.createHandlers(),
}
