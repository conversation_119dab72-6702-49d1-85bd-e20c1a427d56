const { ObjectId } = require('mongodb')
const backTagId = '66a89e708f4f7973ccc6a513'
const paikeTagId = '66a89e708f4f7973ccc6a514'
const dupTagId = '66a89e708f4f7973ccc6a515' // 重复群
const todoTagId = '66a89e708f4f7973ccc6a516' // 待处理群
const yxzxTagId = '67ef5b4a458de42080da0638'

// "1688854321410952" "好分数客服" "https://wework.qpic.cn/wwpic3az/945528_oJDK5ghPR0WPlgO_1714449514/0"
// "1688855828587333" "客户服务助手" "https://wwcdn.weixin.qq.com/node/wework/images/empty.png"
// "1688854305554904" "好分数服务总监" "https://wework.qpic.cn/wwpic3az/949896_pDPJNyFYQyuaBO6_1721370370/0"
// "1688857577566284" "客户服务🛎︎" "https://wework.qpic.cn/wwpic3az/470690_y1SdL3K4TKKZYPp_1709364140/0"
//     [
//     "1688856746625841",
//       "机器人(只做群管理，无法回答问题)",
//       "https://wework.qpic.cn/wwpic3az/550605_crwJGGCXR5K8yQG_1722679345/0"
//     ],
//
const schoolCreatorMap = {
  '1688855828587333': '客户服务助手',
  '1688854305554904': '好分数服务总监',
  '1688857577566284': '客户服务🛎︎',
}

const qunCreatorMap = {
  ...schoolCreatorMap,
  '1688856746625841': '机器人(只做群管理，无法回答问题)',
}

function parseQunInfo(info, originInfo, userList) {
  const id = info._id
  const name = info.name
  const owner = info.owner
  const cTime = info.create_time
  const qunType = info.is_external ? 1 : 0
  const userCount = Object.keys(info.members).length
  const users = Object.keys(info.members)

  let qunTags = getQunTags(info, originInfo?.qunTags)
  const qunGroup = originInfo?.qunGroup || getQunGroup(info)
  const matchUsers = users.filter(user => userList && userList.find(e => e.userId === user)).map(user => userList && userList.find(e => e.userId === user))
  let customer = null
  if (originInfo && originInfo.customer) {
    if (originInfo.customer instanceof ObjectId) {
      customer = originInfo.customer
    } else if (typeof originInfo.customer === 'object' && originInfo.customer.id) {
      customer = originInfo.customer.id
    }
  }
  return {
    qunId: id,
    name: name,
    qunCreatorId: owner,
    qunCreatorName: qunCreatorMap[owner] || owner,
    qunCreatorAvatar: null,
    qunCreateTime: cTime,
    qunType: qunType,
    userCount: userCount,
    qunUsers: matchUsers.map(e => e._id),
    qunGroup: qunGroup,
    qunTags: qunTags,
    qunExternalUserCount: matchUsers.filter(user => user.role !== '内部人员' && user.role !== '经销商').length || 0,
    parseCustomerName: qunGroup === 1 ? parseCustomerName(info) : null,
    customer: customer,
    forceTime: originInfo?.forceTime || null,
    syncTime: new Date(),
    isDeleted: false
  }
}

function getQunGroup(info) {
  const id = info._id
  const name = info.name
  const qunType = info.is_external ? 1 : 0
  if (isBackQun(info)) {
    return null
  }

  const agentMark = ['考试服务支撑-', '业务对接群', '区域专属服务群']
  const schoolMark = ['专属服务群', '云校智学服务群']
  if (+qunType === 0) {
    return 3 // 其他
  }
  if (name.startsWith('【') && name.endsWith('】对接群')) {
    return 2 // 经销商
  }
  if (agentMark.some(e => name.indexOf(e) !== -1)) {
    return 3 // 其他
  } else if (schoolMark.some(e => name.indexOf(e) !== -1)) {
    return 1 // 学校
  }
  return 3
}

function getQunTags(info, originTags) {
  let name = info.name || ''
  let tags = originTags || []
  if (tags.some(e => e.toString() === todoTagId)) {
    return [ObjectId(todoTagId)]
  }

  if (isBackQun(info)) {
    // 备用群，增加备用标签
    if (!tags.some(e => e.toString() === backTagId)) {
      tags.push(ObjectId(backTagId))
    }
  } else {
    // 非备用群。 去除备用标签。
    if (tags.some(e => e.toString() === backTagId)) {
      tags = tags.filter(e => e.toString() !== backTagId)
    }
  }

  if (name.trim().startsWith('📚')) {
    // 专属群，增加专属标签
    if (!tags.some(e => e.toString() === paikeTagId)) {
      tags.push(ObjectId(paikeTagId))
    }
  } else {
    // 去除排课标签
    if (tags.some(e => e.toString() === paikeTagId)) {
      tags = tags.filter(e => e.toString() !== paikeTagId)
    }
  }

  if (name.trim().endsWith('云校智学服务群')){
    // 专属群，增加专属标签
    if (!tags.some(e => e.toString() === yxzxTagId)) {
      tags.push(ObjectId(yxzxTagId))
    }
  } else {
    // 去除云校智学标签
    if (tags.some(e => e.toString() === yxzxTagId)) {
      tags = tags.filter(e => e.toString() !== yxzxTagId)
    }
  }

  tags = tags.filter(e => e.toString() !== dupTagId)

  return tags
}

const backMark = ['【】专属服务群', '【】好分数专属服务群', '【备用', '备用群', '【专属']

function isBackQun(info) {
  let name = info.name || ''
  if ((name === '群聊' && Object.keys(info.members).length === 1)
    || (name === '机器人(只做群管理，无法回答问题)' && Object.keys(info.members).length === 1)
    || (!name && Object.keys(info.members).length === 1)
    || name === '客户服务助手'
    || name === '客户服务🛎︎'
    || name === '好分数服务总监'
    || name === '业务服务台'
    || name.startsWith('好分数专属服务群')
    || name.startsWith('专属服务群')) {
    return true
  }

  return backMark.some(e => name.indexOf(e) !== -1)
}

function parseCustomerName(info) {
  const groupName = info.name
  let name = groupName.trim()
    .replace('📚', '')
    .replace('🛎︎', '')
  if (!name.endsWith('专属服务群') && !name.endsWith('云校智学服务群')) {
    return null
  }
  name = name
    .replace('好分数专属服务群', '')
    .replace('专属服务群', '')
    .replace('云校智学服务群', '')
    .trim()
  if (name.startsWith('【') && name.endsWith('】')) {
    name = name.substring(1, name.length - 1).trim()
  }

  if (name.startsWith('《') && name.endsWith('》')) {
    name = name.substring(1, name.length - 1).trim()
  }

  if (!name) return null

  return name
}

module.exports = {
  parseQunInfo,
  getQunGroup,
  parseCustomerName,
  backTagId,
  paikeTagId,
  dupTagId,
  todoTagId,
  yxzxTagId
}
