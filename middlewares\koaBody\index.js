const koaBody = require('koa-body')
const path = require('path')

module.exports = strapi => {
  return {
    initialize () {
      strapi.app.use(/** @type {*} */koaBody({
        multipart: true,
        formidable: {
          uploadDir: path.resolve(__dirname, '../../public/uploads'),
          keepExtensions: true,
          maxFieldsSize: 10 * 1024 * 1024, // 10mb
          maxFileSize: 10 * 1024 * 1024 * 1024,
        },
      }))
    },
  }
}
