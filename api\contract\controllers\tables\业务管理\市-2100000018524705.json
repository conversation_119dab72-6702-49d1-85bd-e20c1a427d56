{"fieldsMap": {"市名": "2200000174426675", "省": "2200000174426676", "市编": "2200000174426677", "省-省名": "1102001101000000", "省-权限一组": "1102001109000000", "省-权限二组": "1102001110000000", "省-权限三组": "1102001111000000"}, "table_id": "2100000018524705", "name": "市", "alias": "", "space_id": "4000000003570865", "created_on": "2021-11-09 19:53:07", "fields": [{"field_id": "2200000174426675", "name": "市名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000174426676", "name": "省", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000174426677", "name": "市编", "alias": "", "field_type": "number", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "1102001101000000", "name": "省名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000174426676}, "required": true, "description": "", "config": {}}, {"field_id": "1102001109000000", "name": "权限一组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174426676}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1102001110000000", "name": "权限二组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174426676}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1102001111000000", "name": "权限三组", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000174426676}, "required": false, "description": "", "config": {"is_multi": 1}}]}