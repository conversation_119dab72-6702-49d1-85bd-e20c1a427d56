'use strict'

const _ = require('lodash')
const { omit } = require('lodash')
const {handlingGroup, handlingPermission} = require("./utils/role/roleProcess");

/**
 * UsersPermissions.js service
 *
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 */

const DEFAULT_PERMISSIONS = [
  {
    action: 'callback',
    controller: 'auth',
    type: 'users-permissions',
    roleType: 'public'
  },
  {
    action: 'connect',
    controller: 'auth',
    type: 'users-permissions',
    roleType: null
  },
  {
    action: 'forgotpassword',
    controller: 'auth',
    type: 'users-permissions',
    roleType: 'public'
  },
  {
    action: 'register',
    controller: 'auth',
    type: 'users-permissions',
    roleType: 'public'
  },
  {
    action: 'emailconfirmation',
    controller: 'auth',
    type: 'users-permissions',
    roleType: 'public',
  },
  {
    action: 'resetpassword',
    controller: 'auth',
    type: 'users-permissions',
    roleType: 'public'
  },
  {
    action: 'me',
    controller: 'user',
    type: 'users-permissions',
    roleType: null
  }
]

const isPermissionEnabled = (permission, role) =>
  DEFAULT_PERMISSIONS.some(
    defaultPerm =>
      (defaultPerm.action === null || permission.action === defaultPerm.action) &&
      (defaultPerm.controller === null || permission.controller === defaultPerm.controller) &&
      (defaultPerm.type === null || permission.type === defaultPerm.type) &&
      (defaultPerm.roleType === null || role.type === defaultPerm.roleType)
  )

module.exports = {
  async createRole (params) {
    if (!params.type) {
      params.type = _.snakeCase(_.deburr(_.toLower(params.name)))
    }

    const role = await strapi.query('role', 'users-permissions')
      .create(_.omit(params, ['users', 'permissions']))

    // 当前项目文件 Action
    let actionsFoundInFiles = this.getActionsFoundInFiles().map(e => {
      const [type, controller, action] = e.split('.')
      return { type, controller, action }
    })

    // 角色关联功能的权限
    const enabledPermissions = []
    if (params.modules) {
      const groups = await strapi.query('group', 'users-permissions')
        .find({ id_in: params.modules })
      for (let group of groups) {
        if (!group.apiPermissions) continue
        for (let apiPermission of group.apiPermissions) {
          if (!apiPermission.enabled) continue
          enabledPermissions.push({
            type: apiPermission.type,
            controller: apiPermission.controller,
            action: apiPermission.action.toLowerCase(),
          })
        }
      }
    }

    const paramsPermissionObj = params.permissions
    const paramsPermissions = Object.keys(paramsPermissionObj || {}).reduce((acc, type) => {
      Object.keys(paramsPermissionObj[type].controllers).forEach(controller => {
        Object.keys(paramsPermissionObj[type].controllers[controller]).forEach(action => {
          acc.push({
            role: role.id,
            type,
            controller,
            action: action.toLowerCase(),
            ...paramsPermissionObj[type].controllers[controller][action]
          })
        })
      })
      return acc
    }, [])

    const arrayOfPromises = actionsFoundInFiles.map(actionItem => {
      const type = actionItem.type
      const controller = actionItem.controller
      const action = actionItem.action.toLowerCase()
      return strapi.query('permission', 'users-permissions').create({
        role: role.id,
        type: type,
        controller: controller,
        action: action,
        enabled:
          !!paramsPermissions.find(permission => {
            if (permission.type === type
              && permission.controller === controller
              && permission.action === action
              && permission.enabled
            ) return true
          })
          || !!enabledPermissions.find(permission => {
            if (permission.type === type
              && permission.controller === controller
              && permission.action === action
            ) return true
          })
      })
    })
    await Promise.all(arrayOfPromises)
  },

  async deleteRole (roleID, publicRoleID) {
    const role = await strapi.query('role', 'users-permissions').findOne({ id: roleID }, ['users', 'permissions'])

    if (!role) {
      throw new Error('Cannot find this role')
    }

    const users = await strapi.query('user', 'users-permissions').find({
      _where: {
        _or: [
          {
            role: roleID,
          },
          {
            roles: roleID,
          },
        ],
      },
    })

    // Move users to guest role.
    const arrayOfPromises = users.reduce((acc, user) => {
      const invalidRoles = user.roles?.filter(role => role.id !== roleID) || []
      acc.push(
        strapi.query('user', 'users-permissions').update(
          {
            id: user.id,
          },
          {
            role: invalidRoles.length > 0 ? invalidRoles[0] : publicRoleID,
            roles: invalidRoles,
          }
        )
      )
      return acc
    }, [])

    // Remove permissions related to this role.
    role.permissions.forEach(permission => {
      arrayOfPromises.push(
        strapi.query('permission', 'users-permissions').delete({
          id: permission.id,
        })
      )
    })

    // Delete the role.
    arrayOfPromises.push(
      strapi.query('role', 'users-permissions').delete({ id: roleID }))

    return await Promise.all(arrayOfPromises)
  },

  getActions () {
    const generateActions = data =>
      Object.keys(data).reduce((acc, key) => {
        if (_.isFunction(data[key])) {
          acc[key] = { enabled: false, policy: '' }
        }

        return acc
      }, {})

    const appControllers = Object.keys(strapi.api || {}).filter(key => !!strapi.api[key].controllers).reduce(
      (acc, key) => {
        Object.keys(strapi.api[key].controllers).forEach(controller => {
          acc.controllers[controller] = generateActions(
            strapi.api[key].controllers[controller])
        })

        return acc
      },
      { controllers: {} }
    )

    const pluginsPermissions = Object.keys(strapi.plugins).reduce((acc, key) => {
      const initialState = {
        controllers: {},
      }

      acc[key] = Object.keys(strapi.plugins[key].controllers).reduce((obj, k) => {
        obj.controllers[k] = generateActions(
          strapi.plugins[key].controllers[k])

        return obj
      }, initialState)

      return acc
    }, {})

    const permissions = {
      application: {
        controllers: appControllers.controllers,
      },
    }

    return _.merge(permissions, pluginsPermissions)
  },

  async getRole (roleID) {
    const role = await strapi.query('role', 'users-permissions').findOne({ id: roleID }, ['permissions'])

    if (!role) {
      throw new Error('Cannot find this role')
    }

    // Group by `type`.
    const permissions = role.permissions.reduce((acc, permission) => {
      _.set(acc,
        `${permission.type}.controllers.${permission.controller}.${permission.action}`,
        {
          enabled: _.toNumber(permission.enabled) == true,
          policy: permission.policy,
        })

      return acc
    }, {})

    return {
      ...role,
      permissions,
    }
  },

  async getRoles () {
    const roles = await strapi.query('role', 'users-permissions').find({ _sort: 'name' }, ['modules'])

    for (let i = 0; i < roles.length; ++i) {
      roles[i].nb_users = await strapi.query('user', 'users-permissions').count({ role: roles[i].id })
    }

    return roles
  },

  async getRoutes () {
    const routes = Object.keys(strapi.api || {}).reduce((acc, current) => {
      return acc.concat(_.get(strapi.api[current].config, 'routes', []))
    }, [])
    const clonedPlugins = _.cloneDeep(strapi.plugins)
    const pluginsRoutes = Object.keys(clonedPlugins || {}).reduce((acc, current) => {
      const routes = _.get(clonedPlugins, [current, 'config', 'routes'], []).reduce((acc, curr) => {
        const prefix = curr.config.prefix
        const path = prefix !== undefined
          ? `${prefix}${curr.path}`
          : `/${current}${curr.path}`
        _.set(curr, 'path', path)

        return acc.concat(curr)
      }, [])

      acc[current] = routes

      return acc
    }, {})

    return _.merge({ application: routes }, pluginsRoutes)
  },

  getActionsFoundInFiles () {
    // Aggregate first level actions.
    const appActions = Object.keys(strapi.api || {}).reduce((acc, api) => {
      Object.keys(_.get(strapi.api[api], 'controllers', {})).forEach(controller => {
        const actions = Object.keys(strapi.api[api].controllers[controller]).filter(action => _.isFunction(
          strapi.api[api].controllers[controller][action])).map(action => `application.${controller}.${action.toLowerCase()}`)
        acc = acc.concat(actions)
      })
      return acc
    }, [])
    // Aggregate plugins' actions.
    const pluginsActions = Object.keys(strapi.plugins).reduce((acc, plugin) => {
      Object.keys(strapi.plugins[plugin].controllers).forEach(controller => {
        const actions = Object.keys(
          strapi.plugins[plugin].controllers[controller]).filter(action => _.isFunction(
          strapi.plugins[plugin].controllers[controller][action])).map(action => `${plugin}.${controller}.${action.toLowerCase()}`)

        acc = acc.concat(actions)
      })
      return acc
    }, [])
    return appActions.concat(pluginsActions)
  },
  // 初始化角色对应接口权限配置
  // - 默认公开权限配置创建
  // - 新增的接口权限配置创建
  // - 移除的接口权限配置删除
  async updatePermissions (roleId) {
    const { primaryKey } = strapi.query('permission', 'users-permissions');

    const role = await strapi.query('role', 'users-permissions').findOne({ id: roleId });

    const dbPermissions = await strapi.query('permission', 'users-permissions').find({
      _limit: -1,
      role: roleId
    });

    let permissionsFoundInDB = dbPermissions.map(
      p => `${p.type}.${p.controller}.${p.action}.${p.role[primaryKey]}`
    );
    permissionsFoundInDB = _.uniq(permissionsFoundInDB);

    // Aggregate first level actions.
    const actionsFoundInFiles = this.getActionsFoundInFiles();

    // Create permissions for the role
    let permissionsFoundInFiles = actionsFoundInFiles.map(
      action => `${action}.${role[primaryKey]}`
    );
    permissionsFoundInFiles = _.uniq(permissionsFoundInFiles);

    // Compare to know if actions have been added or removed from controllers.
    if (!_.isEqual(permissionsFoundInDB.sort(), permissionsFoundInFiles.sort())) {
      const splitted = str => {
        const [type, controller, action, roleId] = str.split('.');
        return { type, controller, action, roleId };
      };

      // We have to know the difference to add or remove the permissions entries in the database.
      const toRemove = _.difference(permissionsFoundInDB, permissionsFoundInFiles).map(splitted);
      const toAdd = _.difference(permissionsFoundInFiles, permissionsFoundInDB).map(splitted);

      const query = strapi.query('permission', 'users-permissions');

      // Execute request to update entries in database for the role.
      await Promise.all(
        toAdd.map(permission =>
          query.create({
            type: permission.type,
            controller: permission.controller,
            action: permission.action,
            enabled: isPermissionEnabled(permission, role),
            policy: '',
            role: permission.roleId,
          })
        )
      );

      await Promise.all(
        toRemove.map(permission => {
          const { type, controller, action, roleId: role } = permission;
          return query.delete({ type, controller, action, role });
        })
      );
    }
  },

  async initialize () {
    // 移除已删除角色的遗留权限记录
    console.log('Users-Permissions Delete Invalid Permission ...')
    console.time('Users-Permissions Delete Invalid Permission')
    const roles = await strapi.query('role', 'users-permissions').find({}, [])
    const result = await strapi.query('permission', 'users-permissions').model.deleteMany({
      role: {
        $nin: roles.map(e => e._id)
      }
    })
    console.log(`Users-Permissions Delete Invalid Permission Count ${result.deletedCount}`)
    console.timeEnd('Users-Permissions Delete Invalid Permission')
  },

  async updateRole (roleId, newRoleData) {
    const role = await this.getRole(roleId)
    // 更新角色基础配置
    await strapi.query('role', 'users-permissions').update({ id: roleId }, omit(newRoleData, 'permissions'))

    // 更新参数中的权限列表
    const newPermissions = newRoleData.permissions
    await handlingPermission(newPermissions, role, roleId)

    // 更新角色关联功能的接口权限
    await handlingGroup(roleId, newRoleData,role);
  },

  template (layout, data) {
    const compiledObject = _.template(layout)
    return compiledObject(data)
  },
}

