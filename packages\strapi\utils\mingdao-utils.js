const { convertRestQueryParams } = require('accel-utils')
const { isUndefined, isString, omit, isFunction, cloneDeep, pick, isArray, isObject, isNil, flatten } = require('lodash')
const { timestampControls, workflowControls, userControls } = require('./mingdao-controls')

const { asyncCache } = require('./promise-utils')
const _ = require('lodash');
const fs = require('fs')
const path = require('path')
const { convertValueToRefValue } = require('./convert/convertToRef.js')
const {revertRefAttributeFromSourceAttr, getOptionsMap} = require("./convert/convertToRef");

const MingdaoModelPrefix = 'mingdao::'
const MingdaoModelTemplate = () => {
  return {
    orm: 'mingdao',
    modelType: 'contentType',
    kind: 'collectionType',
    primaryKey: 'id',
    info: {
      name: '',
      description: '',
    },
    options: {
      timestamps: true,
      groups: []
    },
    associations: [],
    attributes: {},
  }
}

const assocModelFields = [
  'orm',
  'modelType',
  'kind',
  'primaryKey',
  'info',
  'options',
  'associations',
  'attributes',
  'modelName',
  'uid',
  'apiID',
  'globalId',
  'allAttributes',
  'modelExt',
]

function getAppSectionWorksheets (sections, rootSectionId) {
  let worksheets = []
  for (const section of sections) {
    let sectionId = rootSectionId || section.sectionId
    for (let item of section.items) {
      if (item.type === 0) {
        worksheets.push({
          ...item,
          sectionId: sectionId
        })
      }
    }
    if (section.childSections) {
      worksheets = worksheets.concat(getAppSectionWorksheets(
        section.childSections, sectionId
      ))
    }
  }
  return worksheets
}

function getModelAttrRefKey (model, field) {
  if (!model.options?.attributes) return field
  for (let key of Object.keys(model.options?.attributes)) {
    if (model.options?.attributes[key].ref === field) {
      return key
    }
  }
  return field
}

// 构建模型关联列表字段
function defineAssociations (model, control) {
  const modeUid = `${MingdaoModelPrefix}${control.dataSource}`
  const modelOptions = strapi.mingdaoModels.find(e => e.collectionName === control.dataSource)
  const associationModel = {
    ...MingdaoModelTemplate(),
    uid: modeUid,
    apiID: control.dataSource,
    // // 子表父模型跟踪
    // parentUid: model.uid,
    info: {
      name: control.controlName,
    },
    options: {
      ...(modelOptions || {})
    }
  }
  const alias = getModelAttrRefKey(model, control.controlId)
  associationModel.attributes = controlToAttributes(control.relationControls, associationModel)
  for (let key of Object.keys(associationModel.attributes)) {
    const attribute = associationModel.attributes[key]
    const visible = control.showControls.includes(key)
    attribute.list = {
      visible: visible
    }
    attribute.edit = {
      visible: attribute.visible || true
    }
  }
  return {
    alias: alias,
    controlName: control.controlName,
    type: 'collection',
    targetUid: modeUid,
    model: control.dataSource,
    nature: 'manyWay',
    autoPopulate: true,
    dominant: true,
    associationModel,
  }
}

function getControlRule (controlType) {
  const controlMaps = {
    2: null,  // 文本 - 单行多行
    3: null,  // 电话 - 手机
    4: null,  // 电话 - 座机
    5: null,  // 邮箱
    7: null,  // 证件
    19: null,  // 地区 - 省
    21: null,  // 自由连接
    22: null,  // 分段
    23: null,  // 地区 - 省/市
    24: null,  // 地区 - 省/市/县
    25: null,  // 大写金额

    28: null,  // 等级
    40: null,  // 定位
    42: null,  // 签名
    45: null,  // 嵌入
    47: null,  // 条码
    48: null,  // 组织角色
    49: null,  // API查询
    50: null,  // API查询
    51: null,  // 查询记录
    10010: null,  // 备注

    // 数值
    6: {
      fAttribute: () => ({ type: 'number' })
    },
    // 金额
    8: {
      fAttribute: () => ({ type: 'number' }),
      fValue: (key, rawData) => {
        if (!rawData) return null
        return +rawData
      },
      fControl: (key, value, model, mode = 'update') => {
        return {
          controlId: key,
          value: '' + value,
          ...(mode === 'create' ? { valueType: 1, } : null)
        }
      }
    },
    // 成员
    26: {
      fAttribute: () => ({ type: 'json' })
    },
    // 部门
    27: {
      fAttribute: () => ({ type: 'json' })
    },
    // 文本组合
    32: {
      fAttribute: () => ({ type: 'string', editable: false }),
    },
    // 自动编号
    33: {
      fAttribute: () => ({ type: 'string', editable: false }),
    },
    // 检查框
    36: {
      fAttribute: () => ({ type: 'boolean' }),
      fValue: (key, rawData, attribute, model) => {
        return {
          '0': false,
          '1': true
        }[rawData]
      },
      fControl: (key, value) => {
        return {
          controlId: key,
          dot: 0,
          type: 36,
          value: value ? '1' : '0'
        }
      }
    },
    // 富文本
    41: {
      fAttribute: () => ({ type: 'richtext' }),
    },
    // 选项卡
    52: {
      fAttribute: (control, { model }) => {
        const options = {}
        if (control.advancedSetting.icon) {
          options.icon = JSON.parse(control.advancedSetting.icon)
        }
        return {
          type: 'tabs',
          editable: false,
          ...options,
        }
      }
    }
  }

  // 单选
  const getSelectDropDownAttribute = (control, model, configs = {}) => {
    const validationOptions = {}
    if (control.advancedSetting) {
      if (!isNil(control.advancedSetting.min)) {
        validationOptions.minItems = +control.advancedSetting.min
      }
      if (!isNil(control.advancedSetting.max)) {
        validationOptions.maxItems = +control.advancedSetting.max
      }
    }
    let optionsMap = getOptionsMap(model, control)
    const attribute = {
      type: 'string',
      options: control.options?.filter(e => !e.isDeleted)?.map(e => {

        let refValue = optionsMap.find(opt => opt.value == e.key)?.refValue
        return {
          ...e,
          label: e.value,
          value: e.key,
          refValue
        }
      }),
      ...validationOptions,
      ...configs
    }
    // 允许用户增加选项
    if (control.advancedSetting?.allowadd === '1') {
      attribute.allowAdd = true
    }
    return attribute
  }
  const getSelectControl = (key, value, model) => {

    // 替换选项
    // [
    //   {
    //     "controlId": "676e2a559e13a09bfff4a71c",
    //     "type": 11,
    //     "value": "[\"153a01e6-678b-4065-9947-adce4852a128\"]",
    //     "controlName": "动态标签",
    //     "dot": 0
    //   }
    // ]

    // 新增选项
    // [
    //   {
    //     "controlId": "676e2a559e13a09bfff4a71c",
    //     "type": 11,
    //     "value": "[\"{\\\"color\\\":\\\"#2196f3\\\",\\\"value\\\":\\\"新选项3\\\"}\"]",
    //     "controlName": "动态标签",
    //     "dot": 0
    //   }
    // ]

    const attribute = model.attributes[key]
    value = convertValueToRefValue(value,attribute)
    let controlValue = value
    if (!isArray(value)) {
      controlValue = value ? [value] : []
    }

    controlValue = controlValue.map(e => {
      if (isString(e)) return e
      return JSON.stringify({
        value: e?.label || e?.value,
        color: e?.color || '#2196f3'
      })
    })

    return {
      controlId: key,
      value: JSON.stringify(controlValue)
    }
  }
  controlMaps[9] = controlMaps[11] = {
    // 9:平铺  11:下拉/进度
    name: '单选',
    fAttribute: (control, {model}) => {
      const attribute = getSelectDropDownAttribute(control, model)
      // 下拉菜单
      if (control.advancedSetting?.showtype === '0') {
        attribute.controlComponent = 'select'
        if (control.advancedSetting?.allowadd === '1') {
          attribute.allowAdd = true
        }
      }
      // 平铺
      else if (control.advancedSetting?.showtype === '1') {
        attribute.controlComponent = 'radio'
      }
      // 进度
      else if (control.advancedSetting?.showtype === '2') {
        attribute.controlComponent = 'slider'
      }
      return attribute
    },
    fValue: (key, rawData, attribute) => {
      const value = rawData
      if (!value) return null
      return attribute.control.options.find(e => value === e.value)?.key
        || attribute.control.options.find(e => value === e.key)?.key
    },
    fControl: (key, value, model) => {
      return getSelectControl(key, value, model)
    },
  }


// 多选
  const getSelectTileAttribute = (control, model, configs = {}) => {
    const validationOptions = {}
    if (control.advancedSetting) {
      if (!isNil(control.advancedSetting.min)) {
        validationOptions.minItems = +control.advancedSetting.min
      }
      if (!isNil(control.advancedSetting.max)) {
        validationOptions.maxItems = +control.advancedSetting.max
      }
    }
    return {
      type: 'json',
      jsonSchema: {
        type: 'array',
        uniqueItems: true,
        items: {
          type: 'string',
          'enum': control.options?.filter(e => !e.isDeleted)?.map(e => e.value),
          ...validationOptions,
          'x-options': control.options?.filter(e => !e.isDeleted)?.map(e => {
            return {
              ...e,
              label: e.value,
              value: e.key,
            }
          }),
          ...configs
        },
      },
    }
  }
  controlMaps[10] = {
    name: '多选',
    fAttribute: (control,{model}) => {
      let attribute
      // 前端未对接 - 临时强制设置为平铺
      if (control.advancedSetting) {
        control.advancedSetting.checktype = '0'
      }
      // 平铺
      if (control.advancedSetting?.checktype === '0') {
        attribute = getSelectTileAttribute(control,model)
        attribute.controlComponent = 'checkbox'
      }
      // 下拉菜单
      else if (control.advancedSetting?.checktype === '1') {
        attribute = getSelectDropDownAttribute(control, model)
        attribute.controlComponent = 'select'
        if (control.advancedSetting?.allowadd === '1') {
          attribute.allowAdd = true
        }
      }
      return attribute
    },
    fValue: (key, rawData, attribute) => {
      const values = rawData
      return attribute.control.options.filter(e => values?.includes(e.value))?.map(e => e.key)
    },
    fControl: (key, value, model) => {
      return getSelectControl(key, value, model)
    },
  }

  // 附件
  controlMaps[14] = {
    fAttribute: (control) => {
      return {
        type: 'media',
        multiple: true,
        allowedTypes: [
          'images'
        ],
      }
    },
    fValue (key, rawData, attribute) {
      // Example rawData JSON
      // [
      //   {
      //     "attachmentType": 2,
      //     "fromType": 9,
      //     "fileID": "7fe61e52-6970-43e7-b8e9-d814042f70aa",
      //     "sourceID": "c5e2a2ee-e55d-4e3f-aa57-f1caac0d3a98",
      //     "commentID": "6787753a9e13a09bfffc3747",
      //     "docVersionID": "7fe61e52-6970-43e7-b8e9-d814042f70aa",
      //     "accountId": "a8517894-3d82-4105-9c9c-8bc564689275",
      //     "createUserName": "系统管理员",
      //     "createUserAvatar": "https://app.p0.cn/file/mdpic/UserAvatar/591D1baqezeX7e885I1o2wdH0rdY063C41end7e3aZ9fbW9i9A6D4xaqaj8b3T9L.png?imageView2/1/w/48/h/48/q/90",
      //     "originalFilename": "为多个企业购买接口许可模版",
      //     "ext": ".xlsx",
      //     "filesize": 9480,
      //     "appID": "",
      //     "createTime": "2025-01-15 16:44:57",
      //     "updateTime": "2025-01-15 16:44:57",
      //     "filename": "7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s",
      //     "viewUrl": "https://app.p0.cn/wwwapi/file/owa?id=7fe61e52-6970-43e7-b8e9-d814042f70aa&pst=4",
      //     "previewUrl": "",
      //     "viewType": 2,
      //     "allowDown": "ok",
      //     "fileRealPath": "https://app.p0.cn/file/mdoc/9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s.xlsx",
      //     "downloadUrl": "https://app.p0.cn/wwwapi/file/downDocument?fileID=7fe61e52-6970-43e7-b8e9-d814042f70aa&worksheetId=6715b9f4ae4fd9d1ba4f1b11",
      //     "waterMarkInfo": ""
      //   }
      // ]
      const records = JSON.parse(rawData || '[]')

      const mimeTypes = {
        // 图片类型
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'bmp': 'image/bmp',
        'webp': 'image/webp',
        // 视频类型
        'mp4': 'video/mp4',
        'avi': 'video/x-msvideo',
        'mkv': 'video/x-matroska',
        // 音频类型
        'mp3': 'audio/mpeg',
        'wav': 'audio/wav',
        'ogg': 'audio/ogg',
      };

      return records.map(e => {
        const pathname = new URL(e.fileUrl || e.DownloadUrl).pathname
        const ext = path.extname(pathname).slice(1).toLowerCase()
        const mime = mimeTypes[ext] || 'application/*'
        return {
          id: e['file_id'] || e['fileId'],
          url: e['fileUrl'] || e['DownloadUrl'] || e['original_file_full_path'] || e['previewUrl'] || e['preview_url'],
          mime: mime,
          name: e['original_file_name'] || e['originalFileName'] || e['file_name'] || e['fileName'],
          ext: ext ? `.${ext}` : '',
          size: (e['file_size'] || e['filesize'] || 0) / 1024
        }
      })
    },
    fControl: (key, value, model, mode = 'update') => {
      const files = Array.isArray(value) ? value : [value];
      const attachments = files.map((file, index) => {
        // Example Value
        // {
        //   "id": "71c38556-be05-4198-ac5b-a768c84e2827",
        //   "url": "https://ayx-wly.bj.bcebos.com/test/upload/6eb988b3b960c8dcb3c94b794410c7eb.png?e=1736934352&token=mdstorage:gXsT15s5g4MTJkueb2j7C33i4pM=",
        //   "mime": "image/png",
        //   "name": ".png"
        // }

        // Example Control
        // [
        //   {
        //     "controlId": "6787753a9e13a09bfffc3747",
        //     "type": 14,
        //     "value": "{\"attachmentData\":[],\"attachments\":[{\"fileID\":\"o_1ihki5ujkkrqe4nsaob2l1kfkk\",\"fileSize\":9480,\"serverName\":\"https://app.p0.cn/file/mdoc/\",\"filePath\":\"9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/\",\"fileName\":\"7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s\",\"fileExt\":\".xlsx\",\"originalFileName\":\"为多个企业购买接口许可模版\",\"key\":\"9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s.xlsx\",\"url\":\"https://app.p0.cn/file/mdoc/9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s.xlsx?e=1736934287&token=mdstorage:jLFkxoqomh4E3fsHGzG4jJamC1U=\",\"oldOriginalFileName\":\"%E4%B8%BA%E5%A4%9A%E4%B8%AA%E4%BC%81%E4%B8%9A%E8%B4%AD%E4%B9%B0%E6%8E%A5%E5%8F%A3%E8%AE%B8%E5%8F%AF%E6%A8%A1%E7%89%88\",\"allowDown\":true,\"docVersionID\":\"\",\"index\":0,\"isEdit\":false}],\"knowledgeAtts\":[]}",
        //     "controlName": "附件（缩略图）",
        //     "dot": 0
        //   }
        // ]
        // {
        //   "attachmentData": [],
        //   "attachments": [
        //     {
        //       "fileID": "o_1ihki5ujkkrqe4nsaob2l1kfkk",
        //       "fileSize": 9480,
        //       "serverName": "https://app.p0.cn/file/mdoc/",
        //       "filePath": "9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/",
        //       "fileName": "7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s",
        //       "fileExt": ".xlsx",
        //       "originalFileName": "为多个企业购买接口许可模版",
        //       "key": "9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s.xlsx",
        //       "url": "https://app.p0.cn/file/mdoc/9922dfeb-4eba-4dea-a566-486ac53dd6fc/f3808f22-82d5-439d-8ebd-b9ee58a34908/6715b9f4ae4fd9d1ba4f1b11/********/7XcY1b8X7obB6s3rew793b852E3d0Z77fU0LfcaE1KeT0EaKcBf41Ibf3cd18W6s.xlsx?e=1736934287&token=mdstorage:jLFkxoqomh4E3fsHGzG4jJamC1U=",
        //       "oldOriginalFileName": "%E4%B8%BA%E5%A4%9A%E4%B8%AA%E4%BC%81%E4%B8%9A%E8%B4%AD%E4%B9%B0%E6%8E%A5%E5%8F%A3%E8%AE%B8%E5%8F%AF%E6%A8%A1%E7%89%88",
        //       "allowDown": true,
        //       "docVersionID": "",
        //       "index": 0,
        //       "isEdit": false
        //     }
        //   ],
        //   "knowledgeAtts": []
        // }
        const originalFileName = file.name?.replace(file.ext || '', '') || ''
        try {
          const url = new URL(file.url)
          const filePath = url.pathname.replace(/[^/]+$/, '')
          const fileName = url.pathname.replace(filePath, '').replace(file.ext || '', '')
          return {
            fileID: file.id || '',
            fileSize: (file.size || 0) * 1024, // Convert size to bytes
            serverName: url.origin,
            filePath: filePath,
            fileName: fileName,
            fileExt: file.ext || '',
            originalFileName: originalFileName,
            key: url.pathname,
            url: file.url || '',
            allowDown: true,
            index: index,
            isEdit: false,
          }
        } catch (error) {
          console.error('Invalid file URL:', file.url, error)
          return null
        }
      }).filter(e => e)
      return {
        controlId: key,
        type: 14,
        value: JSON.stringify({
          attachmentData: [],
          attachments: attachments,
          knowledgeAtts: []
        }),
        dot: 0
      }
    }
  }

  // 日期与时间
  controlMaps[15] = {
    // 年-月-日
    name: '日期',
    fAttribute: () => {
      return {
        type: 'datetime',
        format: 'YYYY-MM-DD',
      }
    },
    fValue: (key, rawData, attribute, model) => {
      if (!rawData) return null
      return rawData
    },
  }
  controlMaps[16] = {
    name: '日期时间',
    fAttribute: () => {
      return {
        type: 'datetime',
        format: 'YYYY-MM-DD HH:mm',
      }
    },
    fValue: (key, rawData, attribute, model) => {
      if (!rawData) return null
      return rawData
    },
  }
  controlMaps[46] = {
    name: '时间',
    fAttribute: () => {
      return {
        type: 'string',
        format: 'time',
      }
    },
    fValue: (key, rawData, attribute, model) => {
      if (!rawData) return null
      return rawData
    },
  }

  // 公式 - 计算结果为数字
  controlMaps[31] = {
    name: '数字(公式)',
    fAttribute: (control) => {
      // 公式类型关联记录计算规则待处理
      if (control.dataSource) {
        // control.dataSource
        // eg. $6715cc9aae4fd9d1ba4f1c43$
        // eg. $6715cc9aae4fd9d1ba4f1c43$/$6715cc9aae4fd9d1ba4f1c42$
        // ...
      }
      return { type: 'string', editable: false }
    },
  }
  // 公式 - 汇总
  controlMaps[37] = {
    name: '数字(公式-汇总)',
    fAttribute: (control) => {
      // 公式类型关联记录计算规则待处理
      // ...
      // 汇总 - 数值型 6
      if (control.enumDefault2 === 6) {
        return { type: 'number', editable: false }
      }
      // 汇总 - 日期 15
      if (control.enumDefault2 === 15) {
        return {
          type: 'datetime',
          format: 'YYYY-MM-DD',
          editable: false,
        }
      }
      // 汇总 - 日期时间 16
      if (control.enumDefault2 === 16) {
        return {
          type: 'date',
          format: 'YYYY-MM-DD hh:mm',
          editable: false,
        }
      }
      // 默认类型
      return { type: 'string', editable: false }
    },
  }
  // 公式 - 计算结果为日期
  controlMaps[38] = {
    name: '日期(公式)',
    fAttribute: () => {
      // 公式类型关联记录计算规则待处理
      return {
        type: 'date',
        format: 'YYYY-MM-DD hh:mm',
        editable: false,
      }
    },
    fValue: (key, rawData, attribute, model) => {
      if (!rawData) return null
      return rawData
    },
  }
  // 关联记录
  const getRelationOptions = (control, { model }) => {
    let source = control.dataSource
    const refFieldRegExp = /^\$(\w+)\$$/
    const [, refField] = refFieldRegExp.exec(source || '') || []
    const refFieldKey = refField && getModelAttrRefKey(model, refField)
    return {
      sourceType: refFieldKey ? 'field' : 'model',
      source: refFieldKey || control.dataSource,
      sourceField: control.sourceControlId,
      sourceControlType: control.sourceControlType,
      sourceTitleControlId: control.sourceTitleControlId,
      sourceEntityName: control.sourceEntityName,
    }
  }
  const getRelationSort = (control, { model }) => {
    if (control.advancedSetting?.sorts) {
      const mingdaoSorts = JSON.parse(control.advancedSetting?.sorts)
      return mingdaoSorts.map(e => {
        const refFieldKey = e.controlId && getModelAttrRefKey(model, e.controlId)
        return {
          field: refFieldKey,
          order: e.isAsc ? 'asc' : 'desc',
        }
      })
    }
  }

  const fRelationAttribute = (control, { model }) => {
    const association = defineAssociations(model, control)
    association.alias = getModelAttrRefKey(model, control.controlId)
    model.associations.push(association)

    const modeUid = `${MingdaoModelPrefix}${control.dataSource}`
    const attribute = {
      collection: modeUid,
      // ContentType Fields
      type: 'relation',
      targetModel: modeUid,
      relationType: association.nature,
    }
    // 1 单选 2 多选
    if (control.enumDefault === 1) {
      attribute.maxItems = 1
    }
    // attribute.mainField = 'rowid'
    if (control.sourceTitleControlId || control.showControls[0]) {
      attribute.mainField = control.sourceTitleControlId || control.showControls[0]
    }
    // Ref 映射处理
    if (attribute.mainField) {
      attribute.mainField = getModelAttrRefKey(association.associationModel, attribute.mainField)
    }
    const relationOptions = getRelationOptions(control, { model })
    // 排序配置
    const sort = getRelationSort(control, { model })
    if (sort) attribute.sort = sort
    // 展示字段
    if (isArray(control.showControls)) {
      attribute.showFields = control.showControls.map(field => {
        return getModelAttrRefKey(association.associationModel, field)
      })
    }
    return {
      ...attribute,
      relationOptions
    }
  }
  controlMaps[29] = {
    name: '关联记录',
    fAttribute: fRelationAttribute,
    fControl: (key, value, model, mode = 'update') => {
      if (mode === 'updateMany') {
        if (!isArray(value)) {
          console.warn(`[Warning] 关联记录字段类型异常，已临时兼容处理 ${key} ${value}`)
        }
        return {
          controlId: key,
          value: JSON.stringify(value || [])
        }
      }
      if (!isArray(value)) {
        console.warn(`[Warning] 关联记录字段类型异常，已临时兼容处理 ${key} ${value}`)
      }
      let controlValue = isArray(value) ? value.map(e => {
        return {
          sid: e.id || e
        }
      }) : []
      return {
        controlId: key,
        value: JSON.stringify(controlValue)
      }
    }
  }
  // 他表字段
  controlMaps[30] = {
    fAttribute: (control, { model }) => {
      // 关联他表字段控件类型正常不为他表字段
      if (control.sourceControlType === 30) {
        return { type: 'string', editable: false }
      }
      const controlRule = getControlRule(control.sourceControlType)
      const attribute = controlRule.fAttribute(control, { model })
      const relationOptions = getRelationOptions(control, { model })
      return {
        ...attribute,
        editable: false,
        relationOptions,
      }
    },
    fValue: (key, rawData, attribute, model) => {
      if (attribute.control.sourceControlType === 30) {
        return null
      }
      const controlRule = getControlRule(attribute.control.sourceControlType)
      return controlRule.fValue(key, rawData, attribute, model)
    }
  }
  // 级联选择
  controlMaps[35] = {
    name: '级联选择',
    fAttribute: fRelationAttribute,
    fControl: (key, value, model, mode = 'update') => {
      const newOldControlValue = [{ sid: value[0] }]
      return {
        controlId: key,
        type: 35,
        value: JSON.stringify(newOldControlValue)
      }
    }
  }

  // 子表
  controlMaps[34] = {
    name: '子表',
    fAttribute: (control, { model }) => {
      const association = defineAssociations(model, control)
      association.alias = getModelAttrRefKey(model, control.controlId)
      // 隐藏工作表子表关联字段
      for (let key of Object.keys(association.associationModel.attributes)) {
        const attr = association.associationModel.attributes[key]
        if (attr.targetModel === model.uid) {
          attr.edit.editable = false
          attr.editable = false
        }
      }
      model.associations.push(association)
      const attribute = {
        type: 'component',
        component: control.dataSource,
        repeatable: true,
        displayMode: 'table',
      }
      // 关联配置
      attribute.relationOptions = getRelationOptions(control, { model })
      // 排序配置
      const sort = getRelationSort(control, { model })
      if (sort) attribute.sort = sort
      return attribute
    },
    fValue: (key, rawData, attribute) => {
      return JSON.parse(rawData === '' ? [] : rawData)
    },
    fControl: (key, value, model, mode = 'update', currentRow) => {
      if (mode === 'updateMany') {
        console.warn(`[Warning] Model[${model.info?.name}] Control[${key}] - Sub table updateMany not allow`)
        return
      }
      const assocModel = model.associations.find(e => e.alias === key)?.associationModel
      if (!assocModel) {
        console.warn(`[Warning] Model[${model.info?.name}] Control[${key}] - Association model not found`)
        return
      }
      if (mode === 'create') {
        const controls = value.map(record => {
          return valuesToControls(assocModel, record, mode)
        })
        return {
          controlId: key,
          type: 34,
          editType: 9,
          value: JSON.stringify(controls)
        }
      }
      const createControls = value.map(record => {
        const controls = valuesToControls(assocModel, record, mode)
        return {
          ...(record.id ? { rowid: record.id } : {}),
          editType: 0,
          newOldControl: controls,
        }
      })
      const currentRecordIds = currentRow ? currentRow[key] : []
      const deleteControls = currentRecordIds.filter(rowId => !value.find(e => e.id === rowId)).map(e => {
        return {
          editType: 2,
          rowid: e
        }
      })
      const newOldControlValue = [
        ...createControls,
        ...deleteControls,
      ]
      return {
        controlId: key,
        type: 34,
        editType: 9,
        value: JSON.stringify(newOldControlValue)
      }
    }
  }

  return {
    // Default
    name: '控件',
    fAttribute: () => ({ type: 'string' }),
    fValue: (key, rawData, attribute, model) => {
      if (attribute.type === 'json' && isString(rawData)) {
        return JSON.parse(rawData || 'null')
      }
      if (attribute.type === 'relation' && rawData) {
        const assocModel = model.associations.find(e => e.alias === key)?.associationModel
        // 关联记录类型 - 空数组处理为 null - 临时解决非空 allOf 判定问题
        // if (rawData === '[]' && attribute.controlType === 29) return null
        const data = JSON.parse(rawData)
        if (Array.isArray(data)) {
          // ID字符串数组形式
          if (data.some(e => isString(e))) return data
          return data
            // 取消处理非空数据
            .filter(row => row['sourcevalue'])
            .map(row => {
              if (isString(row)) return row
              const sourceValue = JSON.parse(row['sourcevalue'] || '{}')
              sourceValue.id = sourceValue['rowid']
              for (let sKey of Object.keys(sourceValue)) {
                const refKey = getModelAttrRefKey(assocModel, sKey)
                if (refKey) {
                  const attribute = assocModel.attributes[refKey]
                  if (attribute && attribute.type === 'relation'
                    && isString(sourceValue[sKey]) && sourceValue[sKey] !== ''
                  ) {
                    sourceValue[refKey] = JSON.parse(sourceValue[sKey])
                  } else {
                    sourceValue[refKey] = sourceValue[sKey]
                  }
                }
              }
              return sourceValue
            })
        }
      }
      return rawData
    },
    fControl: (key, value, model, mode = 'update', currentRow) => {
      const attribute = model.attributes[key]
      // 只读类型的字段
      if ([22, 30, 31, 37, 45, 51].includes(attribute.control.type)) {
        return
      }
      if (attribute.type === 'relation') {
        return {
          controlId: key,
          editType: 9,
          value: JSON.stringify(value)
        }
      }
      return {
        controlId: key,
        value: value,
        // Create Mode
        ...(mode === 'create' ? {
          valueType: 1,
        } : null)
      }
    },
    // Type Custom
    ...(controlMaps[controlType] || {}),
  }
}

function valuesToControls (model, values, mode = 'update', currentRow) {
  const controls = []
  for (let key of Object.keys(values)) {
    const attribute = model.attributes[key]
    if (!attribute) continue
    // values[key] = convertValueToRefValue(values[key], attribute)
    if (['ctime', 'utime'].includes(key)) continue
    const controlRule = getControlRule(attribute.control.type)
    const control = controlRule.fControl(key, values[key], model, mode, currentRow)
    if (control) {
      if (attribute.ref) {
        control.controlId = attribute.ref
      }
      control.controlType = attribute.control.type
      controls.push(control)
    }
  }
  return controls
}

function controlToAttributes (controls, model) {
  return controls
    // 过滤分段字段
    .filter(e => e.type !== 22)
    .reduce((acc, control) => {
      const commonFields = {
        label: control.controlName,
      }
      /* 表单设置 */
      // 默认值
      if (control.default) {
        commonFields.default = control.default
      }
      // 字段验证机制
      if (control.required) {
        commonFields.required = true
      }
      // 字段布局 尺寸/行/列/标签页
      if (control.size || control.row || control.col) {
        commonFields.size = control.size
        commonFields.row = control.row
        commonFields.col = control.col
      }
      if (control.sectionId) {
        commonFields.sectionId = control.sectionId
      }
      // 字段权限控制
      if (isString(control.fieldPermission)) {
        const hiddenAlways = control.fieldPermission[0] === '0'
        const readOnly = control.fieldPermission[1] === '0'
        const hiddenCreate = control.fieldPermission[2] === '0'
        const isSystemField = control.row === 9999
        if (hiddenAlways || isSystemField) {
          commonFields.visible = false
        }
        if (hiddenCreate) {
          commonFields.visibleOnCreate = false
        }
        if (readOnly) {
          commonFields.editable = false
        }
      }
      /* 表单说明 */
      if (control.hint) {
        commonFields.placeholder = control.hint
      }
      if (control.desc) {
        commonFields.description = control.desc
      }
      /* 字段样式 */
      const pickFields = (obj, key, newKey) => {
        if (obj[key]) return { [newKey || key]: obj[key] }
        return {}
      }
      const controlStyle = {
        ...pickFields(control.advancedSetting, 'titlecolor', 'titleColor'),
        ...pickFields(control.advancedSetting, 'titlestyle', 'titleStyle'),
        ...pickFields(control.advancedSetting, 'titlesize', 'titleSize'),
        ...pickFields(control.advancedSetting, 'showtype', 'showType'),
      }
      /* 字段排序控制 */
      const fieldCanSort = (type, control = {}) => {
        const canSortTypes = [
          2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 15, 16, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 42, 46, 48, 50,
        ]
        if (control.type === 30 && control.strDefault === '10') {
          return false
        }
        return _.includes(canSortTypes, type)
      }
      commonFields.sortable = fieldCanSort(control.type, control)
      /* 字段配置 */
      if (!isNil(control.enumDefault)) {
        commonFields.controlOptionVar = control.enumDefault
      }
      if (!isNil(control.enumDefault)) {
        commonFields.controlOptionVar2 = control.enumDefault2
      }
      // 字段类型处理
      const controlRule = getControlRule(control.type)
      const attribute = controlRule.fAttribute(control, { model })
      acc[control.controlId] = {
        ...commonFields,
        ...attribute,
        controlType: control.type,
        control: control,
        ...(Object.keys(controlStyle).length > 0 ? { controlStyle } : {}),
      }
      return acc
    }, {})
}

function worksheetToModel (worksheet, options) {
  let controls = worksheet.template?.controls
  if (!controls) {
    console.warn('[Warning] Worksheet not controls.', worksheet)
    controls = []
  }

  const customControls = controls.filter(e => e.row !== 9999)
  // 根据 row 升序排列
  const sortedControls = customControls.sort((a, b) => a.row - b.row)
  const groups = []
  let currentGroup
  for (let control of sortedControls) {
    if (control.type === 22) {
      currentGroup = {
        title: control.controlName,
        fields: []
      }
      groups.push(currentGroup)
    } else if (currentGroup) {
      currentGroup.fields.push(control.controlId)
    }
  }
  const rawModel = {
    ...MingdaoModelTemplate(),
    modelName: options.modelName || `${worksheet.alias || worksheet.worksheetId}`,
    uid: `${MingdaoModelPrefix}${worksheet.worksheetId}`,
    globalId: worksheet.worksheetId,
    apiID: options.modelName,
    info: {
      name: worksheet.name,
      description: worksheet.notes,
    },
    lifecycles: options.lifecycles,
    options: {
      timestamps: true,
      groups: groups,
      formStyle: {
        title: worksheet.advancedSetting?.title,
        sectionShow: worksheet.advancedSetting?.sectionshow,
        tabPosition: worksheet.advancedSetting?.tabposition,
        defTabName: worksheet.advancedSetting?.deftabname || '详情',
      },
      ...options.options,
      // 映射模型配置临时存放位置
      ...pick(options || {},
        [
          'uid',
          'apiID',
          'collectionName',
          'modelName',
          'modelType',
          'kind',
          'info',
          'options',
          'attributes',
        ]
      )
    }
  }
  const customAttributes = controlToAttributes(sortedControls, rawModel)
  const timestampAttributes = controlToAttributes(timestampControls, rawModel)
  const userAttributes = controlToAttributes(userControls, rawModel)
  const workflowAttributes = controlToAttributes(workflowControls, rawModel)
  const refAttributes = cloneDeep(options.attributes) || {}
  if (refAttributes) {
    for (let key of Object.keys(refAttributes)) {
      const ref = refAttributes[key].ref
      const sourceAttr = customAttributes[ref] || timestampAttributes[ref]
      refAttributes[key] = {
        ...sourceAttr,
        ...refAttributes[key],
      }
      revertRefAttributeFromSourceAttr(refAttributes[key],sourceAttr)
      delete customAttributes[ref]
      // Group Ref 映射
      for (let group of groups) {
        for (let i = 0; i < group.fields.length; i++) {
          if (group.fields[i] === ref) {
            group.fields[i] = key
          }
        }
      }
    }
  }

  rawModel.attributes = {
    ...refAttributes,
    ...customAttributes,
    ...timestampAttributes
  }
  rawModel.allAttributes = {
    ...rawModel.attributes,
    ...userAttributes,
    ...workflowAttributes,
    id: {
      ref: 'rowid'
    }
  }

  // 业务规则转换为条件子模式
  const buildAllOf = () => {
    const allOf = []
    for (let rule of worksheet.rules) {
      const clueTip = `[Model:${rawModel.info.name}]`
      try {
        const patternRule = {
          name: rule.name,
          if: {
            properties: {}
          },
          then: {
            properties: {}
          },
          else: {
            properties: {}
          }
        }
        const lv0Filters = rule.filters[0].groupFilters
        for (let filter of lv0Filters) {
          const key = getModelAttrRefKey(rawModel, filter.controlId)
          const attribute = rawModel.attributes[key]
          if (!attribute) continue
          const controlRule = getControlRule(filter.dataType)

          // 筛选条件 - 为空
          if (filter.filterType === 7) {
            patternRule.if.properties[key] = {
              const: null
            }
            continue
          }
          // 筛选条件 - 不为空
          if (filter.filterType === 8) {
            patternRule.if.properties[key] = {
              const: `{{ Array.isArray($value) ? $value.length > 0 : !!$value }}`
            }
            continue
          }

          // 特殊情况待验证 - 已知[8-不为空]值会如下
          // filter.value = ''  filter.values = []
          const rawValue = filter.value || filter.values
          let value
          if (isArray(rawValue) && !rawValue.some(e => !isString(e))) {
            value = rawValue
          } else {
            value = controlRule.fValue(key, rawValue, attribute, rawModel)
          }

          if (isArray(value)) {
            value = value.map(e => e.id || e)
          } else if (isObject(value)) {
            value = value.id || value
          }
          patternRule.if.properties[key] = {
            const: value
          }
        }
        const ruleItems = rule.ruleItems
        for (let ruleItem of ruleItems) {
          // 显示/隐藏字段
          if ([1, 2].includes(ruleItem.type)) {
            for (let control of ruleItem.controls) {
              const key = getModelAttrRefKey(rawModel, control.controlId)
              if (patternRule.then.properties[key]) {
                patternRule.then.properties[key].visible = !!ruleItem.type
                patternRule.else.properties[key].visible = !ruleItem.type
              } else {
                patternRule.then.properties[key] = { visible: !!ruleItem.type }
                patternRule.else.properties[key] = { visible: !ruleItem.type }
              }
            }
          }
          // 可编辑/只读字段
          if ([3, 4].includes(ruleItem.type)) {
            for (let control of ruleItem.controls) {
              const key = getModelAttrRefKey(rawModel, control.controlId)
              if (patternRule.then.properties[key]) {
                patternRule.then.properties[key].editable = ruleItem.type === 3
                patternRule.else.properties[key].editable = ruleItem.type !== 3
              } else {
                patternRule.then.properties[key] = { editable: ruleItem.type === 3 }
                patternRule.else.properties[key] = { editable: ruleItem.type !== 3 }
              }
            }
          }
          // 必填字段
          if ([5].includes(ruleItem.type)) {
            for (let control of ruleItem.controls) {
              const key = getModelAttrRefKey(rawModel, control.controlId)
              if (patternRule.then.properties[key]) {
                patternRule.then.properties[key].required = true
                patternRule.else.properties[key].required = false
              } else {
                patternRule.then.properties[key] = { required: true }
                patternRule.else.properties[key] = { required: false }
              }
            }
          }
        }
        allOf.push(patternRule)
      } catch (e) {
        console.warn(`[Warning] ${clueTip} 模型业务规则解析失败 ${JSON.stringify(rule)}`)
      }
    }
    rawModel.options.allOf = allOf
  }

  if (worksheet.rules.length > 0) {
    try {
      buildAllOf()
    } catch (e) {
      console.error(`[Error] Worksheet ${worksheet.name} all of build error: `, e)
    }
  }

  const connectFields = {
    modelExt: omit(worksheet, 'template'),
  }
  return {
    ...rawModel,
    ...connectFields
  }
}

function formatPagination (filters) {
  const pagination = {}
  if (!isUndefined(filters.limit)) {
    pagination.pageSize = filters.limit
  }
  if (!isUndefined(filters.start)) {
    pagination.pageIndex = (filters.start / filters.limit) + 1
  }
  return pagination
}

function paramsToMingdaoFilters (params, model) {
  // Search Query _q
  const rawParams = { ...params }
  const baseQuery = {}
  if (isString(rawParams._q) && rawParams._q !== '') {
    baseQuery.keyWords = params._q
  }
  delete rawParams._q

  // Rest Query
  const filters = convertRestQueryParams(rawParams)
  const pagination = formatPagination(filters)
  const mingdaoQuery = {}
  if (filters.where) {
    mingdaoQuery.filters = []
    // 明道云筛选组，只支持两层筛选组(外层groupFilters，内层value)
    // 如果查询条件最外层只有 or 则将or放在第一层
    // 若 or 和其他查询条件同层级，则 or 数组对象只可有一个key
    if (filters.where.length === 1 && ['or', 'and'].includes(filters.where[0].operator)) {
      for (let conditions of filters.where[0].value) {
        const curGroupFilters = getGroupFilters(conditions, model)
        mingdaoQuery.filters.push({
          isGroup: true,
          spliceType: filters.where[0].operator === 'or' ? 2 :1,
          groupFilters: curGroupFilters
        })
      }
    } else {
      for (let conditions of filters.where) {
        const curGroupFilters = getGroupFilters(conditions, model)
        mingdaoQuery.filters.push({
          isGroup: true,
          spliceType: 1,
          groupFilters: curGroupFilters
        })
      }
    }
  }
  // Field Projection
  if (filters.projection) {
    baseQuery.controls = []
    // Include Fields eg. { field1: 1 ,filed2: 1 }
    if (Object.keys(filters.projection).some(key => filters.projection[key] === 1)) {
      for (let key of Object.keys(filters.projection)) {
        const attribute = model.allAttributes[key]
        if (filters.projection[key] === 1) {
          baseQuery.controls.push(attribute.ref || key)
        }
      }
    }
    // Exclude Fields eg. { field1: 0 ,filed2: 0 }
    else {
      const includeKeys = Object.keys(model.allAttributes).filter(key => filters.projection[key] !== 0)
      baseQuery.controls = includeKeys.map(key => {
        const attribute = model.attributes[key]
        return attribute.ref || key
      })
    }
  }
  // Field Sort
  const mingdaoSort = {}
  if (filters.sort) {
    const firstSort = filters.sort[0]
    if (firstSort) {
      const attribute = model.allAttributes[firstSort.field]
      if (attribute) {
        mingdaoSort.sortId = attribute.ref || firstSort.field
        mingdaoSort.isAsc = 'asc' === firstSort.order
      }
    }
  }
  return {
    ...baseQuery,
    ...pagination,
    ...mingdaoQuery,
    ...mingdaoSort,
  }
}


function getGroupFilters(conditions, model, spliceType = 1) {
  let groupFilters = []
  if (['or', 'and'].includes(conditions.operator) && Array.isArray(conditions?.value)) {
    const spliceType = conditions.operator === 'or' ? 2 : 1
    groupFilters = getGroupFilters(flatten(conditions?.value), model, spliceType)
    return groupFilters
  }
  const curConditions = Array.isArray(conditions) ? conditions : [conditions]
  for (let condition of curConditions) {
    const attribute = model.allAttributes[condition.field]
    if (!attribute) continue

    // 处理对象类型的 condition.value，查找真实的选项值
    condition.value = convertValueToRefValue(condition.value, attribute);

    let controlId = attribute.ref || condition.field
    if (condition.field === 'id') {
      controlId = 'rowid'
    }
    const filter = {
      controlId: controlId,
      dataType: attribute.control?.type,
      spliceType: spliceType,
    }

    /* 筛选类型 - FilterType */
    if (condition.operator === 'null') {
      if (condition.value === 'false' || condition.value === false) {
        filter.filterType = 8
      } else {
        filter.filterType = 7
      }
    } else {
      if (condition.field === 'id') {
        filter.filterType = {
          'eq': 2,
          'in': 2,
          'nin': 6,
        }[condition.operator] || 2
      } else {
        filter.filterType = {
          'eq': 2,
          'ne': 6,
          'contains': 1,
          'ncontains': 5,
          'gt': 13,
          'gte': 14,
          'lt': 15,
          'lte': 16,
          'in': 24,
          'nin': 25,
        }[condition.operator] || 2
      }
    }
    // 关联记录(51)模式查询类型
    if (filter.filterType === 2 && [29, 35].includes(attribute.control?.type)) {
      filter.filterType = 51
    }
    // 文本单选(24)多选(25) 没有 in nin,转变为 eq ne
    if ([24, 25].includes(filter.filterType) && [2, 9, 10, 11].includes(attribute.control?.type)) {
      filter.filterType = {
        24: 2,
        25: 6,
      }[filter.filterType]
    }

    /* 筛选值 - Value & Values */
    // 关联记录(29)/级联选择(35)
    if ([7, 8].includes(filter.filterType) && [29, 35].includes(attribute.control?.type)) {
      filter.values = []
    }
    // 筛选值为数组对应 Values
    else if (Array.isArray(condition.value)) {
      filter.values = condition.value
    }
    // 文本类型 - 等于 不等于 包含 不包含 使用数组格式
    else if ([1, 2, 5, 6].includes(filter.filterType) && [2].includes(attribute.controlType)) {
      filter.values = [condition.value]
    } else {
      // 检查框(36) [选中:_eq='1'] [未选中:_ne='1']
      if (attribute.controlType === 36) {
        filter.value = '1'
        if (condition.operator === 'eq' && ![true, '1', 'true'].includes(condition.value)) {
          filter.filterType = 6
        }
      } else {
        filter.value = condition.value
      }
    }
    groupFilters.push(filter)
  }

  return groupFilters
}

function formatMingdaoRow (rowData, options) {
  const { model } = options
  if (Array.isArray(rowData)) {
    return rowData.map(row => formatMingdaoRow(row, options))
  }
  // Ref 映射转换
  const refAttributes = model.options.attributes
  if (refAttributes) {
    for (let key of Object.keys(refAttributes)) {
      const attribute = refAttributes[key]
      if (isUndefined(rowData[attribute.ref])) continue
      rowData[key] = rowData[attribute.ref]
      delete rowData[attribute.ref]
    }
  }
  // 字段格式化
  const formated = {}
  for (let key of Object.keys(rowData)) {
    const attribute = model.attributes[key]
    if (!attribute) continue
    const rawData = rowData[key]
    const controlRule = getControlRule(attribute.control.type)
    formated[key] = controlRule.fValue(key, rawData, attribute, model)
  }
  formated.id = rowData['rowid']
  formated._id = rowData['_id']
  return formated
}

function mingdaoModelQuery (model) {
  const modelId = model.modelExt.id

  async function mingdaoFind (params) {
    const query = paramsToMingdaoFilters(params, model)
    const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    const data = await openApi.getFilterRows({
      worksheetId: modelId,
      useControlId: true,
      notGetTotal: true,
      ...query,
    })
    return formatMingdaoRow(data.rows, { model })
  }

  async function mingdaoCount (params) {
    const query = paramsToMingdaoFilters(params, model)
    const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    const data = await openApi.getFilterRows({
      ...query,
      worksheetId: modelId,
      pageSize: 1,
      pageIndex: 1,
      controls: ['rowid']
    })
    return data.total
  }

  async function mingdaoCreate (values) {
    const controls = valuesToControls(model, values, 'create')
    const createdResult = await model.db.addWorksheetRow({
      appId: model.modelExt.appId,
      worksheetId: model.modelExt.worksheetId,
      controls: controls
    })
    return await mingdaoFindOne({ id: createdResult.data.rowid })
  }

  async function mingdaoUpdate (params, values) {
    const rowId = params.id
    const currentRow = await mingdaoFindOne({ id: rowId })
    const controls = valuesToControls(model, values, 'update', currentRow)
    const updatedRow =  await model.db.updateWorksheetRow({
      appId: model.modelExt.appId,
      worksheetId: model.modelExt.worksheetId,
      controls: controls,
      rowId: rowId
    })
    return await mingdaoFindOne({ id: updatedRow.data.rowid })
  }

  async function mingdaoUpdateMany (params, values) {
    const rowIds = params.ids
    const controls = valuesToControls(model, values, 'updateMany')
    // 使用 OpenAPI 进行更新字段转换与 WebAPI 不一致，非文本/数值字段无法适用 valuesToControls 转换规则
    // const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    // return await openApi.editRows({
    //   appId: model.modelExt.appId,
    //   worksheetId: model.modelExt.worksheetId,
    //   controls: controls,
    //   rowIds: rowIds
    // })
    // 使用 WebAPI 每次仅支持更新单个字段，转换为多次调用模拟多字段批量更新
    return await Promise.all(controls.map(control => {
      return model.db.updateWorksheetRows({
        appId: model.modelExt.appId,
        worksheetId: model.modelExt.worksheetId,
        controlId: control.controlId,
        controlType: control.controlType,
        value: control.value,
        rowIds: rowIds
      })
    }))
  }

  async function mingdaoFindOne (params) {
    const rowId = params.id
    const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    const data = await openApi.getRowByIdPost(modelId, rowId)
    return formatMingdaoRow(data, { model })
  }

  async function mingdaoDeleteMany (params) {
    const rowId = params.id
    const rowIds = params.id_in ? params.id_in : [rowId]
    const result = {
      deleteCount: 0
    }
    const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    for (let rowId of rowIds) {
      await openApi.deleteRow({
        worksheetId: modelId,
        rowId: rowId
      })
      result.deleteCount++
    }
    return result
  }

  async function mingdaoGetRowRelationRows (params) {
    const { rowId, controlId } = params
    const pagination = formatPagination(params)
    const attribute = model.attributes[controlId]
    const originControlId = attribute.ref || controlId
    const openApi = await model.db.getAppOpenApi(model.modelExt.appId)
    const data = await openApi.getRowRelations({
      worksheetId: modelId,
      rowId: rowId,
      controlId: originControlId,
      ...pagination,
    })
    const associationModel = model.associations.find(e => e.alias === controlId)?.associationModel
    return formatMingdaoRow(data.rows, { model: associationModel })
  }

  return {
    findOne: mingdaoFindOne,
    find: mingdaoFind,
    search: mingdaoFind,
    count: mingdaoCount,
    countSearch: mingdaoCount,
    getRowRelationRows: mingdaoGetRowRelationRows,
    create: mingdaoCreate,
    update: mingdaoUpdate,
    updateMany: mingdaoUpdateMany,
    deleteMany: mingdaoDeleteMany
  }
}

function mingdaoModelInstance (model, options) {
  model.db = options.instance

  const mountModel = (strapi, model) => {
    const connectionFields = [
      'db', 'find', 'search', 'findOne', 'count', 'countSearch', 'getRowRelationRows',
      'create', 'update', 'updateMany', 'deleteMany'
    ]
    // 创建或更新 model 实例
    // 运行中 database-manager 会创建引用原始 model 对象的 db query
    strapi.models[model.modelExt.id] = Object.assign(strapi.models[model.modelExt.id] || {}, model)
    const modelContentType = Object.assign(strapi.contentTypes[model.uid] || {}, omit(model, connectionFields))
    strapi.contentTypes[model.uid] = modelContentType
    if (process.env.SERVER === 'local') {
      generatedModelJsonFile(modelContentType)
    }
    return modelInstance
  }

  let pendingSync = null
  const syncModel = function (syncOptions = {manualRefresh: false}) {
    if (pendingSync && !syncOptions.manualRefresh) return pendingSync

    let syncPromise = null
    try {
      syncPromise = (async () => {
        const modelId = model.modelExt.id
        const worksheetInfo = await model.db.getWorksheetInfo(modelId)
        const worksheet = {
          ...model.modelExt,
          ...worksheetInfo,
        }
        options = _.assign({}, options, {manualRefresh: syncOptions.manualRefresh})
        const newModelInstance = await mountWorksheet(worksheet, options)
        mountModel(strapi, newModelInstance)
        return newModelInstance
      })()

      pendingSync = syncPromise
      return syncPromise
    } catch (error) {
      console.error('Sync model error:', error)
      throw error
    } finally {
      // 确保同步完成后清理pendingSync
      syncPromise.finally(() => {
        if (pendingSync === syncPromise) {
          pendingSync = null
        }
      })
    }
  }

  const modelQuery = mingdaoModelQuery(model)
  const modelInstance = Object.assign(modelQuery, model, { syncModel })
  mountModel(strapi, modelInstance)

  return modelInstance
}

function getMingdaoFilterQueryValue (filter, model) {
  const controlRule = getControlRule(filter.dataType)
  const key = filter.controlId
  const attribute = model.attributes[key]

  const dynamicSource = filter.dynamicSource[0]
  if (dynamicSource && dynamicSource.cid) {
    const controlId = dynamicSource.cid
    const key = getModelAttrRefKey(model, controlId)
    return `{{ $formData[${JSON.stringify(key)}] }}`
  }
  // 筛选条件 - 不为空
  if (filter.filterType === 8) {
    return null
  }

  const rawValue = filter.value || filter.values
  // 关联记录与级联筛选
  if ([29, 35].includes(filter.dataType) && isArray(rawValue)) {
    return rawValue.map(e => JSON.parse(e)).map(e => e.id)
  }
  let value
  if (isArray(rawValue) && !rawValue.some(e => !isString(e))) {
    value = rawValue
  } else {
    value = controlRule.fValue(key, rawValue, attribute, model)
  }
  return value
}

function generatedModelJsonFile (modelContentType) {
  try {
    // 在项目根目录下创建文件夹 mingdao
    // 创建文件 mingdao/model.json
    const modelsDir = path.join(process.cwd(), 'build/mingdao')
    // 创建文件夹
    if (!fs.existsSync(modelsDir)) {
      fs.mkdirSync(modelsDir, { recursive: true })
    }

    // 定义文件名
    let filename = modelContentType.modelName + '.json'

    const modelFilePath = path.join(modelsDir, filename)
    const options = modelContentType.options

    const attributes = cloneDeep(modelContentType.attributes)
    Object.keys(attributes).forEach(key => {
      const attribute = attributes[key]
      delete attribute.control
    })
    fs.writeFileSync(modelFilePath, JSON.stringify({
      'kind': options.kind,
      'collectionName': options.collectionName,
      'connection': options.connection,
      'info': options.info,
      'options': {
        timestamps: options.timestamps,
        groups : options.groups,
        formStyle: options.formStyle,
        allOf: options.allOf,
      },
      'attributes': attributes
    }, null, 2), 'utf8')
  } catch (error) {
    console.log(error)
  }
}

async function mountWorksheet (worksheet, options) {
  const model = worksheetToModel(worksheet, options)
  const modelInstance = mingdaoModelInstance(model, options)
  // 装载并填充模型关联关系模型
  for (let association of modelInstance.associations) {
    const attribute = model.attributes[association.alias]
    const clueTip = `[Model:${model.info.name}] [Attribute:${attribute.label}.${attribute.control?.controlId}]`

    let originAssocModel = strapi.models[association.model]
    if (!originAssocModel) {
      const refModel = strapi.mingdaoModels?.find(e => e.collectionName === association.model)
      originAssocModel = await mountMingdaoModel(association.model, {
        instance: options.instance,
        manualRefresh: options.manualRefresh,
        ...(refModel || {})
      })
    }
    const associationModel = pick(originAssocModel, assocModelFields)
    const controlModel = association.associationModel
    const attributes = cloneDeep(originAssocModel.attributes)
    associationModel.attributes = attributes
    const showControls = attribute.control.showControls
    if (showControls?.length > 0) {
      associationModel.attributes = {}
      const showAttrRefs = showControls.map(controlId => getModelAttrRefKey(associationModel, controlId))
      for (let key of showAttrRefs) {
        if (attributes[key]) {
          associationModel.attributes[key] = attributes[key]
        }
      }
      Object.keys(attributes).forEach(key => {
        if (!associationModel.attributes[key]) {
          associationModel.attributes[key] = attributes[key]
        }
      })
    }
    // 关联模型属性字段处理
    for (let key of Object.keys(associationModel.attributes)) {
      const attr = associationModel.attributes[key]
      const controlAttr = controlModel.attributes[key]
      const clueTip2 = `[Assoc:${associationModel.info.name}.${attr.label}]`
      Object.assign(attr, controlAttr || {})
      // 处理列表、编辑模式配置 list edit
      const optionAttr = attr.ref
        ? association.associationModel.attributes[attr.ref]
        : association.associationModel.attributes[key]
      if (optionAttr) {
        attr.list = optionAttr.list
        attr.edit = optionAttr.edit
        if (optionAttr.visible === false) {
          attr.visible = optionAttr.visible
        }
        if (optionAttr.editable === false) {
          attr.editable = optionAttr.editable
        }
      }
      // 他表字段规则
      if (attr.control?.type === 30) {
        const relationOptions = attr.relationOptions
        const refModel = associationModel.associations.find(e => e.alias === relationOptions.source)?.associationModel
        const sourceField = relationOptions.sourceField
        const sourceFieldKey = sourceField && getModelAttrRefKey(refModel, sourceField)
        attr.relationOptions.sourceField = sourceFieldKey
        const sourceModel = strapi.models[refModel.apiID] || strapi.models[refModel.globalId]
        if (sourceModel){
          const sourceAttr = sourceModel.attributes[sourceFieldKey]
          if (sourceAttr) {
            attr.options = sourceAttr.options || attr.options
          } else {
            console.error(`[Error] ${clueTip} ${clueTip2} 他表字段规则规则解析失败 [AssocModel:${refModel.info?.name}] ${JSON.stringify(relationOptions)}`)
          }
        }
      }
    }
    association.associationModel = associationModel
  }
  // 装配字段规则
  Object.keys(modelInstance.attributes).forEach((key) => {
    const attribute = modelInstance.attributes[key]
    const clueTip = `[Model:${model.info.name}] [Attribute:${attribute.label}.${attribute.control?.controlId}]`
    // 字段过滤器规则
    if (attribute.control?.advancedSetting?.filters) {
      const query = {}
      // 公式/汇总控件字段过滤器
      if ([31, 37, 38].includes(attribute.control.type)) {
        // 暂不转换
      } else {
        const filters = JSON.parse(attribute.control.advancedSetting?.filters)
        for (let filter of filters) {
          try {
            const association = model.associations.find(e => e.alias === key)
            let queryKey = getModelAttrRefKey(association.associationModel, filter.controlId)
            if (filter.filterType === 1) {
              queryKey = `${queryKey}_contains`
            }
            if (filter.filterType === 5) {
              queryKey = `${queryKey}_ncontains`
            }
            if (filter.filterType === 6) {
              queryKey = `${queryKey}_ne`
            }
            if (filter.filterType === 13) {
              queryKey = `${queryKey}_gt`
            }
            if (filter.filterType === 14) {
              queryKey = `${queryKey}_gte`
            }
            query[queryKey] = getMingdaoFilterQueryValue(filter, modelInstance)
          } catch (e) {
            console.error(`[Error] ${clueTip} 字段过滤器规则解析失败 ${JSON.stringify(filter)} ${e.toString()}`)
          }
        }
        attribute.meta = { query }
      }
    }
    // 字段默认值规则
    if (attribute.control?.advancedSetting?.defsource) {
      const defaultSources = JSON.parse(attribute.control.advancedSetting?.defsource)
      for (let defaultSource of defaultSources) {
        try {
          if (!defaultSource.cid || !defaultSource.rcid) continue
          const mainControlId = defaultSource.rcid
          const mainKey = getModelAttrRefKey(model, mainControlId)
          const association = model.associations.find(e => e.alias === mainKey)
          const controlId = defaultSource.cid
          const key = getModelAttrRefKey(association.associationModel, controlId)
          attribute.defaultValueOptions = {
            source: mainKey,      // 关联字段ID
            sourceType: 'field',  // 来源模式
            sourceField: key,     // 来源字段ID
          }
        } catch (e) {
          console.warn(`[Warning] ${clueTip} 字段默认值规则解析失败`, JSON.stringify(defaultSources), '\n', e)
        }
      }
    }
    // 他表字段规则
    if (attribute.control?.type === 30) {
      const relationOptions = attribute.relationOptions
      const assocModel = model.associations.find(e => e.alias === relationOptions.source)?.associationModel
      const sourceField = relationOptions.sourceField
      attribute.relationOptions.sourceField = sourceField && getModelAttrRefKey(assocModel, sourceField)
    }
  })
  return modelInstance
}

async function getMingdaoWorksheet (worksheetId, instance) {
  const worksheetInfo = await instance.getWorksheetInfo(worksheetId)
  if (!worksheetInfo.worksheetId) {
    throw Error(`Worksheet not found, worksheet id: ${worksheetId}`)
  }
  return {
    type: 'worksheet',
    appId: worksheetInfo.appId,
    appName: worksheetInfo.appName,
    groupId: worksheetInfo.groupId,
    worksheetId: worksheetId,
    name: worksheetInfo.name,
    alias: worksheetInfo.alias,
    desc: worksheetInfo.desc,
    template: worksheetInfo.template,
    advancedSetting: worksheetInfo.advancedSetting,
    views: worksheetInfo.views.map(e => {
      return {
        viewId: e.viewId,
        viewType: e.viewType,
        name: e.name,
        controls: e.controls,
        displayControls: e.displayControls,
        filters: e.filters,
        viewControl: e.viewControl,
        sortType: e.sortType,
        viewControls: e.viewControls,
        controlsSorts: e.controlsSorts,
        moreSort: e.moreSort
      }
    }),
    rules: worksheetInfo.rules
  }
}

async function mountMingdaoModel (modelId, options = {}) {
  const instance = options.instance || strapi['connections'].mingdao
  // 已有模型同步更新
  const modelInstance = strapi.models[modelId]
  if (modelInstance && modelInstance.syncModel) {
    // 根据外部参数,决定是否重新请求明道云的接口
    const syncOptions = {manualRefresh: options.manualRefresh ? options.manualRefresh : false}
    return await modelInstance.syncModel(syncOptions)
  }
  // 新增模型创建挂载
  const mingdaoWorksheet = await getMingdaoWorksheet(modelId, instance)
  const worksheet = {
    ...mingdaoWorksheet,
    id: mingdaoWorksheet.worksheetId
  }
  return await mountWorksheet(worksheet, {
    ...options,
    instance: instance
  })
}

module.exports = {
  getAppSectionWorksheets,
  getMingdaoWorksheet,
  mountMingdaoModel: asyncCache(mountMingdaoModel),
}


