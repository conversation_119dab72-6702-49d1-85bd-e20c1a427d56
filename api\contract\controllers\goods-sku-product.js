'use strict'

const { CurdRouter } = require('accel-utils')

const curdRouter = new CurdRouter('goods-sku-product')

/**
 * 批量查询商品
 * @param {Object} ctx - 请求上下文
 */
async function findMany(ctx) {
    let { ids } = ctx.request.body
    if (!ids || !Array.isArray(ids)) {
        return ctx.wrapper.error('PARAMETERS_ERROR', 'ids 参数必须是数组')
    }
    const productList = await strapi.query('goods-sku-product').find({ id: { $in: ids } })
    return ctx.wrapper.succ(productList)
}

module.exports = {
    findMany,
    ...curdRouter.createHandlers(),
}