'use strict';

const { testa } = require("../role/roleProcess");

describe('Users Permissions', () => {

  test('first',() => {
    expect(1).toBe(1)
    const mockCallback = jest.fn(testa)
    mockCallback()
    expect(mockCallback.mock.results[0].value).toBe(11111)
  })

  test("permissons froms role or group handling", ( ) => {})
})

  // let stub = null
  // let spy = null
  // let test2 = null
  // beforeEach(function () {
  //
  //   stub = sinon.stub
  //   spy = sinon.spy
  //   test2 = new Test3()
  // })
  //
  // afterEach(function () {
  //   stub.restore()
  //   spy.restore()
  // })
  // it('stub', function (done) {
  //  spy =  sinon.spy(test2, 'cloud3')
  //  test2.cloud3()
  //
  // })
