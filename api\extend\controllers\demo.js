const _ = require('lodash');

async function getJson(ctx) {
    try {
        const { type } = _.assign({}, ctx.request.body, ctx.request.query);

        let resObj;
        switch (type) {
            case 'obj':
                resObj = {
                    "status": "processing",
                    "payThrough": "wechat-pay-qr",
                    "id": "660a877978f4f52cbe9bea6e",
                    "amount": 1,
                    "tradeNo": "69ee97fcba138b",
                };
                break
            case 'arr':
                resObj = [
                    "GET",
                    "PUT",
                    "PATCH",
                    "POST",
                    "DELETE"
                ];
                break
            case 'obj-obj':
                resObj = {
                    "status": "processing",
                    "payThrough": "wechat-pay-qr",
                    "id": "660a877978f4f52cbe9bea6e",
                    "createdAt": "2024-04-01T10:07:53.238Z",
                    "amount": 1,
                    "tradeNo": "69ee97fcba138b",
                    "info": {
                        "merchantId": "1654035650",
                        "merchantName": "爱云校教育科技研究院"
                    }
                };
                break
            case 'obj-arr':
                resObj = {
                    "amount": 1,
                    "tradeNo": "69ee97fcba138b",
                    "arr": [
                        "GET",
                        "PUT",
                        "PATCH",
                        "POST",
                        "DELETE"
                    ]
                };
                break
            case 'arr-obj':
                resObj = [{
                    "status": "processing",
                    "payThrough": "wechat-pay-qr",
                    "id": "660a877978f4f52cbe9bea6e",
                    "createdAt": "2024-04-01T10:07:53.238Z",
                    "amount": 1,
                    "tradeNo": "69ee97fcba138b",
                },
                {
                    "status": "processing",
                    "payThrough": "wechat-pay-qr",
                    "amount": 1,
                    "tradeNo": "1a9dc801ac747e5a138b",
                    "createdAt": "2024-04-01T10:07:53.238Z",
                    "id": "660a877978f4f52cbe9bea6e"
                },
                {
                    "status": "success",
                    "payThrough": "wechat-pay-qr",
                    "id": "660a88e202f40e37dfef67aa",
                    "amount": 1,
                    "tradeNo": "b724f908d47b6509810ae",
                    "createdAt": "2024-04-01T10:13:54.047Z",
                }];
                break
            case 'arr-arr':
                resObj = [[
                    "GET",
                    "PUT",
                    "PATCH",
                ],[
                    "POST",
                    "DELETE"
                ]];
                break
            default:
                resObj = {
                    "status": "processing",
                    "payThrough": "wechat-pay-qr",
                    "id": "660a877978f4f52cbe9bea6e",
                    "amount": 1,
                    "tradeNo": "69ee97fcba138b",
                };
                break
        }
        return resObj;
    } catch (e) {
        console.log(e)
        return ctx.wrapper.error('HANDLE_ERROR', e.message || e)
    }
}

module.exports = {
    getJson,
}
