module.exports = {
  collectionName: 'customer-problem',
  info: {
    name: 'CustomerProblem',
    label: '客户问题',
    description: '客户问题'
  },
  options: {
    draftAndPublish: false,
    timestamps: true
  },
  pluginOptions: {},
  attributes: {
    customerService: {
      label: '客户',
      model: 'customer-service-mid',
      visible: false,
    },
    productTag: {
      label: '服务产品',
      model: 'customer-service-tag',
      meta: {
        query: {
          type: 'product'
        }
      },
      size: 6
    },
    categoryTag: {
      label: '问题类别',
      model: 'customer-service-tag',
      meta: {
        query: {
          type: 'category'
        }
      },
      size: 6
    },
    submittedAt: {
      label: '提交时间',
      type: 'datetime',
      size: 3
    },
    resolvedAt: {
      label: '解决时间',
      type: 'datetime',
      size: 3
    },
    status: {
      label: '状态',
      type: 'string',
      options: [
        { label: '待解决', value: 'wait-solve' },
        { label: '已解决', value: 'solved' },
        { label: '暂缓', value: 'defer' },
        { label: '废弃', value: 'abandon' },
      ],
      default: '待解决',
      size: 3
    },
    timeSpent: {
      label: '耗时',
      type: 'number',
      size: 3,
    },
    // contact: {
    //   label: '联系人',
    //   model: 'customer-contact',
    //   meta: {
    //     query: {
    //       customerService: '{{ $formData.customerService.id || $formData.customerService }}'
    //     }
    //   },
    //   size: 6
    // },
    submitter: {
      label: '登记人',
      plugin: 'users-permissions',
      model: 'user',
      meta: {
        query: {
          ['role.type']: ['service-admin', 'service-group-leader', 'service-group-member']
        }
      },
      size: 6
    },
    description: {
      label: '描述',
      type: 'richtext',
    },
    teacherName: {
      label: '反馈人姓名',
      type: 'string',
      size: 6
    },
    contactDetails: {
      label: '联系方式',
      type: 'string',
      size: 6
    },
    comment: {
      label: '备注',
      type: 'string',
      size: 12
    }
  }
}
