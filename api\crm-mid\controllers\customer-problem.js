const jwt = require('jsonwebtoken')
const axios = require('axios')

const { CurdRouter } = require('accel-utils')
const { getYjAppUsageStatisticByLocal } = require('../services/customer')

const curdRouter = new (class extends CurdRouter {
  async statistic() {
    const { totalCount } = await getYjAppUsageStatisticByLocal()
    const customerProblemTotal = await strapi.query('customer-problem').count({})
    const customerProblem30 = await strapi.query('customer-problem').count({
      submittedAt_gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    })
    const customerProblemWaitSolve = await strapi.query('customer-problem').count({
      status: 'wait-solve'
    })
    const customerProblemSolved = await strapi.query('customer-problem').count({
      status: 'solved'
    })
    return {
      // 客户总数
      customerTotal: totalCount,
      // 待分配
      customerProblemTotal: customerProblemTotal,
      // 近30天登记问题
      customerProblem30: customerProblem30,
      // 待解决
      customerProblemWaitSolve: customerProblemWaitSolve,
      // 已解决
      customerProblemSolved: customerProblemSolved
    }
  }

  async create(ctx) {
    if (!ctx.request.body.submitter) {
      ctx.request.body.submitter = ctx.state.user.id
    }
    return super.create(ctx)
  }

  async count(ctx) {
    return super.count(ctx)
  }

  async find(ctx) {
    // if (['service-group-leader', 'service-group-member'].includes(user.role.type)) {
    //   query['customerService_in'] = await getUserCustomerServiceIds(user)
    // }
    return super.find(ctx)
  }

})('customer-problem')


async function feedbackByTeacher(ctx) {
  try {
    const { type, content, remark, name, contactDetails, } = ctx.request.body;
    const user = ctx.state.user;

    if (!content || !type || !remark) return ctx.wrapper.error('PARAMETERS_ERROR', '参数缺失')

    let categoryTag = await strapi.query('customer-service-tag').findOne({ name: type, type: 'category' })
    let productTag = await strapi.query('customer-service-tag').findOne({ name: '新工作台', type: 'product' })

    const userInfo = await strapi.query('user', 'users-permissions').findOne({ id: user.id })
    const yjUserInfo = await _getYjUserInfo(userInfo.portalId)

    const customer = await strapi.query('customer').findOne({ school_id_20: yjUserInfo.schoolId })
    let customerService = await strapi.query('customer-service-mid').findOne({ customerId: customer.id })
    if (!customerService) {
      customerService = await strapi.query('customer-service-mid').create({
        name: customer.name,
        customerId: customer.id,
      })
    }

    let customerServiceManager = await strapi.query('customer-service-manager').findOne({ customerService: customerService.id, status: 'normal'})
    const problemInfo = await strapi.query('customer-problem').create({
      productTag: productTag?.id,
      categoryTag: categoryTag?.id,
      customerService: customerService.id,
      description: content,
      comment: remark,
      teacherName: name || yjUserInfo.name,
      contactDetails: contactDetails || yjUserInfo.phone,
      submittedAt: new Date(),
      customerServiceManager: customerServiceManager?.id,
      status: 'wait-solve',
    })
    return ctx.wrapper.succ(problemInfo);
  } catch (e) {
    return ctx.wrapper.error('HANDLE_ERROR', e.message || e)
  }
}

async function _getYjUserInfo(portalUserId) {
  let tokenKey = Buffer.from(strapi.config.server.yjApiServer.gwJwtSecret, 'base64')
  let token = jwt.sign({ 'removed': true }, tokenKey, {
    algorithm: 'HS512',
    jwtid: portalUserId,
    noTimestamp: false
  })
  const res = await axios.get(`${strapi.config.server.yjApiServer.gwUrl}/v353/anno/user/profile/ForAppcenter`, {
    params: { token: token }
  })
  return res.data?.data
}

module.exports = {
  feedbackByTeacher,
  ...curdRouter.createHandlers(),
}
