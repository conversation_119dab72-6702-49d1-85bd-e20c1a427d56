const { getToken } = require('../utils/goToken')
const axios = require('axios')
const { getCustomerBySchoolId } = require('../../crm-mid/services/customer-service')
const { getQunInfo } = require('./qun')

async function addAdmin(ctx) {
  const { qid, uids } = ctx.request.body
  if (!qid || !uids || !uids.length) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'add_admin', {
    qid,
    uids
  })
}

async function delAdmin(ctx) {
  const { qid, uids } = ctx.request.body
  if (!qid || !uids || !uids.length) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'del_admin', {
    qid,
    uids
  })
}

async function changeName(ctx) {
  const { qid, name } = ctx.request.body
  if (!qid || !name) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'change_name', {
    qid,
    name
  })
}

async function notice(ctx) {
  const { qid, notice } = ctx.request.body
  if (!qid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'notice', {
    qid,
    notice: notice || ''
  })
}

async function configModName(ctx) {
  const { qid, allow } = ctx.request.body
  if (!qid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'config_mod_name', {
    qid,
    allow: allow ? 1 : 0
  })
}

async function configInvite(ctx) {
  const { qid, allow } = ctx.request.body
  if (!qid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }
  return await request(ctx, 'config_invite', {
    qid,
    allow: allow ? 1 : 0
  })
}

async function inviteUser(ctx) {
  const { qid, uids } = ctx.request.body
  if (!qid || !uids || !uids.length) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  return await request(ctx, 'invite', {
    qid,
    uids
  })
}

async function kickUser(ctx) {
  const { qid, uids } = ctx.request.body
  if (!qid || !uids || !uids.length) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  return await request(ctx, 'kick', {
    qid,
    uids
  })
}

async function transferOwner(ctx) {
  const { qid, uid } = ctx.request.body
  if (!qid || !uid) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  return await request(ctx, 'transfer_owner', {
    qid,
    uid
  })
}

async function request(ctx, action, data) {
  let token = await getToken('ask')
  if (!token) {
    return ctx.wrapper.error('AUTH_ERROR', 'token 无效')
  }

  const user = ctx.state.user

  let oldQunInfo = await getQunInfo(data.qid)
  const actionLog = await strapi.query('qun-action-log').create({
    qunId: data.qid,
    operator: user.id,
    operatedAt: new Date().toISOString(),
    action: action,
    oldInfo: oldQunInfo,
    actionInfo: data
  })

  let result = null
  try {
    const res = await axios.post(`http://ask.yunxiao.io/qun/${action}?__go_token=${token}`, data)
    result = res.data
  } catch (error) {
    console.log(error)
    result = error.response.data
  } finally {
    await strapi.query('qun-action-log').update({ id: actionLog.id }, { result })
  }

  return result
}

async function sendMsg(ctx) {
  let token = await getToken('ids')
  if (!token) {
    return ctx.wrapper.error('AUTH_ERROR', 'token 无效')
  }
  const { msg, schoolId } = ctx.request.body
  if (!msg || !schoolId) {
    return ctx.wrapper.error('PARAMETERS_ERROR', '参数错误')
  }

  const customer = getCustomerBySchoolId(schoolId)
  if (!customer) {
    return ctx.wrapper.error('NULL_ERROR', '未查询到对应学校')
  }
  if (!customer.qun) {
    return ctx.wrapper.error('NULL_ERROR', '学校未匹配群，无法发送消息')
  }
  const res = await axios.post(`http://ids.yunxiao.io/xiaoyun/send?__go_token=${token}`, {
    tid: customer.qun,
    msg
  })
  return res.data
}

module.exports = {
  addAdmin,
  delAdmin,
  changeName,
  notice,
  configModName,
  configInvite,
  inviteUser,
  kickUser,
  transferOwner,
  sendMsg
}
