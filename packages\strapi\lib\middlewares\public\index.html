<!doctype html>
<html>
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title>Welcome to your Yunxiao app</title>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <meta name="robots" content="noindex, nofollow">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/meyer-reset/2.0/reset.min.css" rel="stylesheet"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.11.2/css/all.min.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/css?family=Lato:400,700&display=swap" rel="stylesheet"/>
    <style>
        * {
            -webkit-box-sizing: border-box;
            text-decoration: none
        }

        body, html {
            margin: 0;
            padding: 0;
            font-size: 62.5%;
            -webkit-font-smoothing: antialiased
        }

        body {
            font-size: 1.3rem;
            font-family: Lato, Helvetica, Arial, Verdana, sans-serif;
            background: #fafafb;
            margin: 0;
            padding: 80px 0;
            color: #333740;
            line-height: 1.8rem
        }

        strong {
            font-weight: 700
        }

        .wrapper {
            width: 684px;
            margin: auto
        }

        h1 {
            text-align: center
        }

        h2 {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 1px
        }

        .logo {
            height: 40px;
            margin-bottom: 74px
        }

        .informations {
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            width: 100%;
            height: 126px;
            margin-top: 18px;
            padding: 20px 30px;
            background: #fff;
            border-radius: 2px;
            box-shadow: 0 2px 4px 0 #e3e9f3;
            font-size: 16px;
        }

        .informations:before {
            position: absolute;
            top: 0;
            left: 0;
            content: '';
            display: block;
            width: 100%;
            height: 2px;
            background: #007eff
        }

        .environment {
            display: inline-block;
            padding: 0 10px;
            height: 20px;
            margin-bottom: 36px;
            background: #e6f0fb;
            border: 1px solid #aed4fb;
            border-radius: 2px;
            text-transform: uppercase;
            color: #007eff;
            font-size: 1.2rem;
            font-weight: 700;
            line-height: 20px;
            letter-spacing: .05rem
        }

        .cta i {
            position: relative;
            display: inline-block;
            height: 100%;
            vertical-align: middle;
            font-size: 1rem;
            margin-right: 20px
        }

        .cta i:before {
            position: absolute;
            top: 8px
        }

        .text-align-right {
            text-align: right
        }

        .people-saying-hello img {
            position: absolute;
            max-width: 100%;
            opacity: 0;
            transition: opacity .2s ease-out
        }

        @media only screen and (max-width: 768px) {
            .wrapper {
                width: auto !important;
                margin: 0 20px
            }

            .informations {
                flex-direction: column;
                height: auto
            }

            .environment {
                width: 100%;
                text-align: center;
                margin-bottom: 18px
            }

            .text-align-right {
                margin-top: 18px;
                text-align: center
            }
        }
    </style>
</head>
<body lang="en">
<section class="wrapper">
    <h1>
        <img class="logo" src="<%= strapi.config.server.url %>/assets/images/logo_login.png"/>
    </h1>
    <div class="informations">
        <div>
            <span class="environment"><%= strapi.config.environment %></span>
            <p>
                The server is running successfully
            </p>
        </div>
        <div class="text-align-right">
            <p style="margin-bottom: 36px;"><%= serverTime %></p>
            <p>
                <strong><%= strapi.config.info.name %>  v<%= strapi.config.info.version %></strong>
            </p>
        </div>
    </div>
</section>
</body>
<script>
  // if (location.hostname === 'localhost') {
  //   location.href = 'http://localhost:8000/admin/'
  // } else {
  //   location.href = '/admin'
  // }
</script>
</html>
