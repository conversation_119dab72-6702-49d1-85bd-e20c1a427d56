// 通用 JavaScript 功能 - Strapi Swagger Plugin Documentation


// 代码块复制功能
function copyCode(button) {
    let codeBlock;
    let text;

    // 尝试多种方式查找代码块
    // 方式1：查找同级的下一个兄弟元素（原有方式）
    codeBlock = button.nextSibling;
    if (codeBlock && codeBlock.textContent) {
        text = codeBlock.textContent.trim();
    } 
    // 方式2：查找父容器中的 .example-code 元素（新的代码容器结构）
    else {
        const container = button.closest('.example-container') || button.closest('.code-block');
        if (container) {
            codeBlock = container.querySelector('.example-code') || container.querySelector('pre');
            if (codeBlock) {
                text = codeBlock.textContent.trim();
            }
        }
    }
    
    // 方式3：查找父容器的下一个兄弟元素中的代码
    if (!text) {
        const parentContainer = button.parentElement;
        if (parentContainer) {
            const nextElement = parentContainer.nextElementSibling;
            if (nextElement && (nextElement.classList.contains('example-code') || nextElement.tagName === 'PRE')) {
                text = nextElement.textContent.trim();
            }
        }
    }

    if (text) {
        navigator.clipboard.writeText(text).then(function() {
            const originalText = button.textContent;
            button.textContent = '成功!';
            button.classList.add('copied');

            setTimeout(function() {
                button.textContent = originalText;
                button.classList.remove('copied');
            }, 2000);
        }).catch(function(err) {
            console.error('复制失败:', err);
            // 显示错误提示
            button.textContent = '复制失败';
            setTimeout(function() {
                button.textContent = '复制';
            }, 2000);
        });
    } else {
        console.error('找不到要复制的代码内容');
        button.textContent = '无内容';
        setTimeout(function() {
            button.textContent = '复制';
        }, 2000);
    }
}

// 页面内锚点平滑滚动
function initSmoothScroll() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });

                // 更新活动状态
                document.querySelectorAll('.nav-item').forEach(item => {
                    item.classList.remove('active');
                });
                this.classList.add('active');
            }
        });
    });
}

// 设置当前页面的导航项为活动状态
function setActiveNavItem() {
    const currentPath = window.location.pathname;
    const fileName = currentPath.split('/').pop() || 'index.html';

    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
        if (item.getAttribute('href') === fileName) {
            item.classList.add('active');
        }
    });
}

// 移动端交互
function initMobileInteraction() {
    // 移动端点击内容区域关闭侧边栏
    const content = document.querySelector('.content');
    if (content) {
        content.addEventListener('click', function() {
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('open');
            }
        });
    }

    // 监听窗口大小变化
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            const sidebar = document.getElementById('sidebar');
            if (sidebar) {
                sidebar.classList.remove('open');
            }
        }
    });
}

// 初始化所有功能
function initDocumentation() {
    initSmoothScroll();
    setActiveNavItem();
    initMobileInteraction();
}

// 页面加载完成后初始化
if(document) {
  document.addEventListener('DOMContentLoaded', initDocumentation);
}

// 导出函数供其他脚本使用
window.DocUtils = {
    copyCode: copyCode,
    initDocumentation: initDocumentation
};
