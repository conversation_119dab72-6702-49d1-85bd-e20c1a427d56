'use strict';

module.exports = {
    "collectionName": "customerAppVersion",
    "info": {
        "name": "CustomerAppVersion",
        "label": "crm产品",
        "description": "crm产品"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": false
    },
    "pluginOptions": {},
    "attributes": {
        "name": {
            "label": "商品名",
            "type": "string"
        },
        "appCategory": {
            "label": "产品",
            "visible": false,
            "type": "string"
        },
        "productCategory": {
            "label": "产品组",
            "visible": false,
            "type": "string"
        },
        "productName": {
            "label": "组名称",
            "visible": false,
            "type": "string"
        },
        "appName": {
            "label": "产品名称",
            "visible": false,
            "type": "string"
        },
        "versionName": {
            "label": "版本名称",
            "visible": false,
            "type": "string"
        },
        "type": {
            "label": "版本号",
            "visible": false,
            "type": "number"
        },
        "usageParams": {
            "label": "产品参数",
            "type": "json",
            "visible": false
        },
        "customerTypes": {
            "label": "客户类型",
            "type": "json",
            "visible": false
        }
    }
}