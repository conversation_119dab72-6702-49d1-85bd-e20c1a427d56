{"name": "strapi-database", "version": "3.6.8", "description": "Strapi's database layer", "homepage": "https://strapi.io", "main": "./lib/index.js", "scripts": {"test": "echo \"no tests yet\""}, "directories": {"lib": "./lib"}, "author": {"name": "Strapi team", "email": "<EMAIL>", "url": "https://strapi.io"}, "repository": {"type": "git", "url": "git://github.com/strapi/strapi.git"}, "bugs": {"url": "https://github.com/strapi/strapi/issues"}, "engines": {"node": ">=16.20.0", "npm": ">=6.0.0"}, "license": "SEE LICENSE IN LICENSE", "dependencies": {"debug": "4.3.1", "lodash": "4.17.21", "p-map": "4.0.0", "verror": "^1.10.0"}, "gitHead": "8972d78e8d1783ded6089d0318bfb62aff8e6962"}