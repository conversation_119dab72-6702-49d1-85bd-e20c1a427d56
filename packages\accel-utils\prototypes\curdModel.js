const { sanitizeEntity } = require('../lib')
const _ = require('lodash')
const { getNonWritableAttributes } = require('../lib/content-types')


class CurdModel {
  constructor (name, config = {}) {
    this.modelName = config.modelName || name
    this.defaultSort = config.sort || {}
    this.mode = config.mode || 'self'
    this.plugin = config.plugin || ''
  }

  get model () {
    return this._getModel()
  }

  // 获取的 Mongoose 的 Model
  _getModel () {
    if (this.plugin) return strapi[this.plugin][this.modelName]
    return strapi.models[this.modelName]
  }

  _sanitizeEntity (entities) {
    if (Array.isArray(entities)) {
      return entities.map(
        entity => sanitizeEntity(entity, { model: this._getModel() }))
    }
    return sanitizeEntity(entities, { model: this._getModel() })
  }

  // make sure to keep the call to getNonWritableAttributes dynamic
  _sanitizeInput (data) {
    return _.omit(data, getNonWritableAttributes(this._getModel()))
  }

  async find (params) {
    const docs = await this.model.find(params)
    return docs.map(e => {
      return e.toObject()
    })
  }

  async findOne (params) {
    const doc = await this.model.findOne(params)
    return doc.toObject()
  }

  async count (params) {
    return await this.model.find(params)
  }

  async create (data) {
    const sanitizedData = this._sanitizeInput(data)
    const doc = await this.model.create(sanitizedData)
    return doc.toObject()
  }

  async update (params, data) {
    const sanitizedData = this._sanitizeInput(data)
    const doc = await this.model.update(params, sanitizedData)
    return doc.toObject()
  }

  async deleteOne (params) {
    const doc = await this.model.deleteOne(params)
    return doc.toObject()
  }

}

module.exports = CurdModel
