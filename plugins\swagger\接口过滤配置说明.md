# 接口文档生成过滤配置说明

## 功能概述

新增的过滤配置功能允许用户在 `config/swagger.js` 中配置需要忽略的控制器和路径，这些被忽略的接口不会出现在 Swagger 文档中。

## 配置格式

在 `config/swagger.js` 中添加 `ignore` 配置节：

```javascript
module.exports = {
  // ... 其他配置
  
  // 接口文档生成过滤配置
  ignore: {
    // 忽略的控制器列表（支持字符串精确匹配和正则表达式）
    controllers: [
      // 字符串精确匹配
      'internal-api',
      'debug-controller',
      
      // 正则表达式匹配
      /^test-.*/, // 忽略所有 test- 开头的控制器
      /.*-internal$/ // 忽略所有 -internal 结尾的控制器
    ],
    
    // 忽略的路径列表（支持字符串精确匹配和正则表达式）
    paths: [
      // 字符串精确匹配
      '/health-check',
      '/metrics',
      
      // 正则表达式匹配
      /^\/internal\/.*/, // 忽略所有 /internal/ 开头的路径
      /^\/debug\/.*/ // 忽略所有 /debug/ 开头的路径
    ]
  }
};
```

## 支持的匹配方式

### 1. 字符串精确匹配
```javascript
controllers: ['growth-activity', 'test-controller']
paths: ['/debug', '/health-check']
```

### 2. 正则表达式匹配
```javascript
controllers: [/^growth-.*/, /.*-test$/]
paths: [/^\/internal\/.*/, /^\/debug\/.*/]
```

### 3. 混合使用
```javascript
{
  controllers: [
    'specific-controller', // 精确匹配
    /^temp-.*/ // 正则匹配
  ], paths: [
    '/exact-path', // 精确匹配
    /^\/api\/v\d+\/internal\/.*/  // 正则匹配
  ]
}
```

## 常用配置示例

### 忽略所有测试相关接口
```javascript
ignore: {
  controllers: [/^test-.*/, /.*-test$/],
  paths: [/^\/test\/.*/, '/mock-data']
}
```

### 忽略内部管理接口
```javascript
ignore: {
  controllers: ['admin-internal', 'system-monitor'],
  paths: [/^\/internal\/.*/, /^\/admin\/system\/.*/]
}
```

### 忽略调试和监控接口
```javascript
ignore: {
  controllers: [/^debug-.*/, 'health-check'],
  paths: ['/debug', '/metrics', '/health', /^\/monitoring\/.*/]
}
```

## 过滤作用范围

过滤配置会影响：

1. **createDefaultRoutes 生成的 CRUD 接口**
   - 如果控制器被忽略，整个控制器的所有 CRUD 接口都不会生成文档
   - 如果路径被忽略，对应的接口路径不会生成文档

2. **自定义路由接口**
   - 检查控制器名称和路径，符合忽略规则的不会生成文档

## 配置生效

- 修改配置后需要重启服务才能生效
- 被忽略的接口完全不会出现在 Swagger 文档中
- 过滤不影响接口的实际功能，只影响文档生成

## 注意事项

1. **正则表达式语法**：使用 JavaScript 正则表达式语法
2. **路径匹配**：路径匹配时会同时检查原始路径和标准化路径（如 `:id` 转换为 `{id}` 后的路径）
3. **大小写敏感**：字符串匹配区分大小写
4. **性能影响**：过滤检查在文档生成时进行，对运行时性能无影响

## 验证配置

可以通过控制台输出验证过滤配置是否生效：

```
忽略控制器: test-controller (根据过滤配置)
忽略路径: /debug/info (根据过滤配置)
```

当看到这样的日志输出时，说明过滤配置已生效。
