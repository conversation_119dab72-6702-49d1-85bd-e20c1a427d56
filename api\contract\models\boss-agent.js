'use strict';

module.exports = {
    "collectionName": "bossAgent",
    "info": {
        "name": "BossAgent",
        "label": "boss经销商",
        "description": "boss经销商"
    },
    "options": {
        "draftAndPublish": false,
        "timestamps": true
    },
    "pluginOptions": {},
    "attributes": {
        "company": {
            "label": "公司名",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "no": {
            "label": "编号",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "userId": {
            "label": "userId",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "type": {
            "label": "类型",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "dealerType": {
            "label": "经销商类型",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "address": {
            "label": "地址",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "province": {
            "label": "省",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "city": {
            "label": "市",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "district": {
            "label": "区",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "corporationName": {
            "label": "公司法人",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "corporationPhone": {
            "label": "公司法人电话",
            "editable": false,
            "size": 3,
            "type": "string"
        },
        "corporationEmail": {
            "label": "公司法人邮箱",
            "editable": false,
            "size": 3,
            "type": "string"
        }
    }
}
