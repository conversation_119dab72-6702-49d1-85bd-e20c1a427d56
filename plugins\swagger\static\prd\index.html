<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Strapi Swagger 插件产品文档</title>
    <link rel="stylesheet" href="styles.css">
    <meta name="description" content="基于 OpenAPI 3.0 规范的智能 API 文档生成插件">
    <meta name="keywords" content="Strapi, Swagger, OpenAPI, API文档, 插件">
    <meta name="author" content="Strapi Swagger Plugin Team">
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3e%3ctext y='.9em' font-size='90'%3e📚%3c/text%3e%3c/svg%3e">
</head>
<body>

    <div class="layout">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1>📚 Swagger Plugin</h1>
                <p>产品文档中心</p>
            </div>

            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">概览</div>
                    <a class="nav-item active" data-page="overview">
                        <i>🏠</i>产品概览
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">文档指南</div>
                    <a class="nav-item" data-page="installation">
                        <i>🚀</i>快速开始
                    </a>
                    <a class="nav-item" data-page="usage">
                        <i>📖</i>进阶使用
                    </a>
                    <a class="nav-item" data-page="custom-annotations">
                        <i>📝</i>自定义注释
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">支持</div>
                    <a class="nav-item" data-page="troubleshooting">
                        <i>🔍</i>故障排除
                    </a>
                    <a class="nav-item" data-page="release-notes">
                        <i>📋</i>发版记录
                    </a>
                </div>

                <div class="nav-section">
                    <div class="nav-section-title">外部链接</div>
                    <a href="http://localhost:8108/api-docs" class="nav-item" target="_blank">
                        <i>🌐</i>实时文档
                    </a>
                    <a href="https://swagger.io/specification/" class="nav-item" target="_blank">
                        <i>📚</i>OpenAPI 规范
                    </a>
                </div>
            </div>
        </nav>

        <!-- 右侧内容区域 -->
        <main class="content">

            <div class="content-body" id="content-body">
                <!-- 产品概览页面 -->
                <div class="page-content active" id="overview-content">
                    <!-- 产品介绍 -->
                    <section class="product-intro">
                        <div class="intro-hero">
                            <div class="hero-content">
                                <h2>🚀 什么是 Strapi Swagger Plugin？</h2>
                                <p class="hero-description">
                                    一个专为 Strapi 项目打造的智能 API 文档生成插件，基于 OpenAPI 3.0 规范，
                                    <strong>无需手动配置</strong>，即可自动生成美观、完整、交互式的 API 文档。
                                </p>
                                <div class="hero-stats">
                                    <div class="stat-item">
                                        <div class="stat-number">0</div>
                                        <div class="stat-label">配置文件</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">100%</div>
                                        <div class="stat-label">自动扫描</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-number">5分钟</div>
                                        <div class="stat-label">快速集成</div>
                                    </div>
                                </div>
                            </div>
                            <div class="hero-demo">
                                <div class="demo-preview">
                                    <div class="demo-header">
                                        <div class="demo-dots">
                                            <span></span><span></span><span></span>
                                        </div>
                                        <span class="demo-title">API 文档预览</span>
                                    </div>
                                    <div class="demo-content">
                                        <div class="demo-endpoint">
                                            <span class="method get">GET</span>
                                            <span class="path">/api/products</span>
                                        </div>
                                        <div class="demo-endpoint">
                                            <span class="method post">POST</span>
                                            <span class="path">/api/products</span>
                                        </div>
                                        <div class="demo-endpoint">
                                            <span class="method put">PUT</span>
                                            <span class="path">/api/products/:id</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- 核心价值 -->
                    <section class="value-proposition">
                        <h2>🎯 为什么选择我们？</h2>
                        <div class="value-cards">
                            <div class="value-card">
                                <div class="value-icon">⚡</div>
                                <h3>零配置启动</h3>
                                <p>无需编写任何配置文件，插件会自动扫描你的 Strapi 项目，识别所有 API 接口并生成文档。</p>
                            </div>
                            <div class="value-card">
                                <div class="value-icon">🎨</div>
                                <h3>专业级界面</h3>
                                <p>现代化的响应式设计，支持模块过滤、权限标注、实时测试等功能，提供卓越的开发体验。</p>
                            </div>
                            <div class="value-card">
                                <div class="value-icon">🔄</div>
                                <h3>实时同步</h3>
                                <p>开发环境下代码变更即时反映到文档中，无需手动更新，始终保持文档与代码同步。</p>
                            </div>
                        </div>
                    </section>


                    <!-- 功能特性 -->
                    <section class="features">
                        <h2>🌟 强大功能</h2>
                        <div class="features-grid">
                            <div class="feature-card">
                                <div class="feature-icon">🔍</div>
                                <h3>智能接口扫描</h3>
                                <p>自动扫描 Strapi 项目中的所有 API 接口，无需手动配置即可生成完整文档</p>
                                <div class="feature-highlight">支持标准 CRUD 路由自动识别</div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">📂</div>
                                <h3>模块化文档</h3>
                                <p>支持按模块和控制器过滤文档，提供下拉选择器快速切换不同模块的接口文档</p>
                                <div class="feature-highlight">业务模块与扩展模块分离</div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">🔐</div>
                                <h3>权限感知</h3>
                                <p>自动识别公开接口和需认证接口，在文档中明确标注权限要求</p>
                                <div class="feature-highlight">基于 permission.js 配置</div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">📝</div>
                                <h3>自定义注释</h3>
                                <p>支持 JSDoc @swagger 注释，可自定义接口文档内容和参数说明</p>
                                <div class="feature-highlight">完全兼容 OpenAPI 3.0</div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">🎨</div>
                                <h3>现代化界面</h3>
                                <p>采用响应式设计，支持粘性头部导航，优化移动端浏览体验</p>
                                <div class="feature-highlight">支持深色/浅色主题</div>
                            </div>

                            <div class="feature-card">
                                <div class="feature-icon">🔄</div>
                                <h3>热重载</h3>
                                <p>开发环境下配置修改即时生效，支持实时预览文档变更</p>
                                <div class="feature-highlight">开发调试更高效</div>
                            </div>
                        </div>
                    </section>

                    <!-- 下一步引导 -->
                    <section class="next-steps">
                        <h2>🚀 开始你的 API 文档之旅</h2>
                        <div class="next-steps-content">
                            <div class="next-step-card">
                                <div class="next-step-icon">🚀</div>
                                <h3>快速开始</h3>
                                <p>详细的安装步骤和配置说明，快速集成到你的项目中</p>
                                <a href="#" class="next-step-link" data-page="installation">立即开始 →</a>
                            </div>
                            <div class="next-step-card">
                                <div class="next-step-icon">📖</div>
                                <h3>进阶使用</h3>
                                <p>了解如何使用API端点、模块过滤、扩展配置等功能</p>
                                <a href="#" class="next-step-link" data-page="usage">查看进阶使用 →</a>
                            </div>
                            <div class="next-step-card">
                                <div class="next-step-icon">📝</div>
                                <h3>自定义接口注释</h3>
                                <p>学习如何为自定义接口添加详细的 Swagger 注释</p>
                                <a href="#" class="next-step-link" data-page="custom-annotations">学习注释语法 →</a>
                            </div>
                        </div>
                        <div class="support-section">
                            <h3>💬 需要帮助？</h3>
                            <p>遇到问题？查看 <a href="#" data-page="troubleshooting">故障排除指南</a> 或查看 <a href="#" data-page="release-notes">最新更新记录</a></p>
                        </div>
                    </section>
                </div>


                <!-- 其他页面内容将通过 JavaScript 动态加载 -->
                <div class="page-content" id="loading-content">
                    <div class="loading"></div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面内容配置
        const pageContents = {
            'overview': {
                title: '🚀 Strapi Swagger 插件',
                description: '基于 OpenAPI 3.0 规范的智能 API 文档生成插件，自动扫描接口并生成交互式文档'
            },
            'installation': {
                title: '🚀 快速开始',
                description: 'Strapi Swagger Plugin 详细安装指南'
            },
            'usage': {
                title: '📖 进阶使用',
                description: 'Strapi Swagger Plugin 进阶使用'
            },
            'custom-annotations': {
                title: '📝 自定义接口注释',
                description: '使用 @swagger 注释为自定义接口生成完整的 API 文档'
            },
            'troubleshooting': {
                title: '🔍 故障排除',
                description: 'Strapi Swagger Plugin 常见问题解决方案'
            },
            'release-notes': {
                title: '📋 发版记录',
                description: 'Strapi Swagger Plugin 版本更新日志'
            }
        };


        // 代码复制功能
        function copyCode(button) {
            const codeBlock = button.nextSibling;
            const text = button.parentElement.querySelector("pre").innerText;

            navigator.clipboard.writeText(text).then(function() {
                button.textContent = '成功';
                button.classList.add('copied');

                setTimeout(function() {
                    button.textContent = '复制';
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }

        // 加载页面内容
        async function loadPageContent(pageName) {
            const contentBody = document.getElementById('content-body');
            const loadingContent = document.getElementById('loading-content');

            // 显示加载状态
            document.querySelectorAll('.page-content').forEach(content => {
                content.classList.remove('active');
            });
            loadingContent.classList.add('active');

            try {
                // 如果是内置页面，直接显示
                if (pageName === 'overview') {
                    loadingContent.classList.remove('active');
                    document.getElementById(pageName + '-content').classList.add('active');
                    return;
                }

                // 动态加载外部页面内容
                console.log(`正在加载页面: ${pageName}.html`);
                const response = await fetch(`${pageName}.html`);
                if (response.ok) {
                    const html = await response.text();
                    console.log(`页面 ${pageName}.html 加载成功，HTML 长度: ${html.length}`);

                    // 解析 HTML 并提取内容
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');

                    // 尝试多种选择器来查找内容
                    let extractedContent = null;

                    // 首先尝试查找带有左右布局的 .content-body
                    const layoutContent = doc.querySelector('.layout .content .content-body');
                    if (layoutContent) {
                        extractedContent = layoutContent;
                        console.log('找到左右布局的内容');
                    }

                    // 如果没有找到，尝试普通的 .content-body
                    if (!extractedContent) {
                        extractedContent = doc.querySelector('.content-body');
                        if (extractedContent) {
                            console.log('找到普通的 .content-body');
                        }
                    }

                    // 如果没有找到，尝试 .main 内的内容
                    if (!extractedContent) {
                        const mainElement = doc.querySelector('.main');
                        if (mainElement) {
                            console.log('找到 .main 元素');
                            // 排除头部，只取内容部分
                            const bodyElement = mainElement.querySelector('.content-body');
                            if (bodyElement) {
                                extractedContent = bodyElement;
                                console.log('从 .main 中找到 .content-body');
                            } else {
                                // 如果没有 .content-body，取除了头部之外的所有内容
                                const tempDiv = document.createElement('div');
                                Array.from(mainElement.children).forEach(child => {
                                    if (!child.classList.contains('content-header') && !child.classList.contains('nav-back')) {
                                        tempDiv.appendChild(child.cloneNode(true));
                                    }
                                });
                                extractedContent = tempDiv;
                                console.log('从 .main 中提取非头部内容');
                            }
                        }
                    }

                    // 如果还是没有找到，尝试查找包含主要内容的 div
                    if (!extractedContent) {
                        const containerElements = doc.querySelectorAll('div');
                        for (let element of containerElements) {
                            if (element.innerHTML.includes('section') || element.innerHTML.includes('table') || element.innerHTML.includes('code-block')) {
                                extractedContent = element;
                                console.log('找到包含主要内容的容器');
                                break;
                            }
                        }
                    }

                    if (extractedContent) {
                        // 创建新的页面内容容器
                        let pageContent = document.getElementById(pageName + '-content');
                        if (!pageContent) {
                            pageContent = document.createElement('div');
                            pageContent.className = 'page-content';
                            pageContent.id = pageName + '-content';
                            document.getElementById('content-body').appendChild(pageContent);
                        }

                        pageContent.innerHTML = extractedContent.innerHTML;
                        console.log(`页面 ${pageName} 内容长度: ${extractedContent.innerHTML.length}`);

                        // 重新绑定复制按钮事件
                        pageContent.querySelectorAll('.copy-button').forEach(button => {
                            button.addEventListener('click', function() {
                                copyCode(this);
                            });
                        });

                        loadingContent.classList.remove('active');
                        pageContent.classList.add('active');
                    } else {
                        console.error(`无法在 ${pageName}.html 中找到内容`);
                        console.log('页面结构:', doc.documentElement.outerHTML.substring(0, 1000));

                        // 作为备选方案，尝试显示整个 body 内容
                        const bodyElement = doc.querySelector('body');
                        if (bodyElement) {
                            console.log('尝试显示整个 body 内容');
                            let pageContent = document.getElementById(pageName + '-content');
                            if (!pageContent) {
                                pageContent = document.createElement('div');
                                pageContent.className = 'page-content';
                                pageContent.id = pageName + '-content';
                                document.getElementById('content-body').appendChild(pageContent);
                            }

                            // 创建一个容器来包装内容
                            const wrapper = document.createElement('div');
                            wrapper.innerHTML = bodyElement.innerHTML;

                            // 移除不需要的元素
                            wrapper.querySelectorAll('script, .sidebar').forEach(el => el.remove());

                            pageContent.innerHTML = wrapper.innerHTML;

                            // 重新绑定复制按钮事件
                            pageContent.querySelectorAll('.copy-button').forEach(button => {
                                button.addEventListener('click', function() {
                                    copyCode(this);
                                });
                            });

                            loadingContent.classList.remove('active');
                            pageContent.classList.add('active');
                        } else {
                            throw new Error('无法找到页面内容');
                        }
                    }
                } else {
                    throw new Error('页面加载失败');
                }
            } catch (error) {
                loadingContent.classList.remove('active');
                // 创建错误页面
                let errorContent = document.getElementById(pageName + '-content');
                if (!errorContent) {
                    errorContent = document.createElement('div');
                    errorContent.className = 'page-content';
                    errorContent.id = pageName + '-content';
                    document.getElementById('content-body').appendChild(errorContent);
                }

                errorContent.innerHTML = `
                    <div class="warning-box">
                        <h4>⚠️ 页面暂时无法加载</h4>
                        <p>请稍后再试或联系技术支持。错误信息: ${error.message}</p>
                        <p>尝试直接访问: <a href="${pageName}.html" target="_blank">${pageName}.html</a></p>
                    </div>
                `;
                errorContent.classList.add('active');
            }
        }

        // 切换页面
        function switchPage(pageName, updateHistory = true) {
            // 更新页面标题和描述（如果元素存在）
            if (pageContents[pageName]) {
                const titleElement = document.getElementById('page-title');
                const descElement = document.getElementById('page-description');
                if (titleElement) titleElement.textContent = pageContents[pageName].title;
                if (descElement) descElement.textContent = pageContents[pageName].description;
            }

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-page="${pageName}"]`).classList.add('active');

            // 加载页面内容
            loadPageContent(pageName);

            // 更新 URL 和浏览器标题
            if (updateHistory) {
                const newUrl = pageName === 'overview' ? '.' : `#${pageName}`;
                window.history.pushState({ page: pageName }, '', newUrl);

                // 更新浏览器标题
                updateBrowserTitle(pageName);
            }

            // 移动端自动关闭侧边栏
            if (window.innerWidth <= 768) {
                document.getElementById('sidebar').classList.remove('open');
            }
        }

        // 更新浏览器标题
        function updateBrowserTitle(pageName) {
            const titles = {
                'overview': 'Strapi Swagger Plugin - 产品概览',
                'version': 'Strapi Swagger Plugin - 版本信息',
                'features': 'Strapi Swagger Plugin - 功能特性',
                'installation': 'Strapi Swagger Plugin - 安装配置',
                'usage': 'Strapi Swagger Plugin - 使用指南',
                'custom-annotations': 'Strapi Swagger Plugin - 自定义接口注释',
                'api-reference': 'Strapi Swagger Plugin - API 参考',
                'troubleshooting': 'Strapi Swagger Plugin - 故障排除',
                'release-notes': 'Strapi Swagger Plugin - 发版记录'
            };

            document.title = titles[pageName] || 'Strapi Swagger Plugin';
        }

        // 从 URL 获取当前页面
        function getCurrentPageFromUrl() {
            const hash = window.location.hash.slice(1); // 移除 # 号
            return hash || 'overview'; // 默认显示概览页面
        }

        // 处理浏览器前进后退
        window.addEventListener('popstate', function(event) {
            const pageName = event.state ? event.state.page : getCurrentPageFromUrl();
            switchPage(pageName, false); // 不更新历史记录，避免无限循环
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 绑定导航点击事件
            document.querySelectorAll('.nav-item[data-page]').forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageName = this.getAttribute('data-page');
                    switchPage(pageName);
                });
            });

            // 绑定下一步引导链接点击事件
            document.querySelectorAll('.next-step-link[data-page]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageName = this.getAttribute('data-page');
                    switchPage(pageName);
                });
            });

            // 绑定支持区域链接点击事件
            document.querySelectorAll('.support-section a[data-page]').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const pageName = this.getAttribute('data-page');
                    switchPage(pageName);
                });
            });

            // 根据 URL 加载初始页面
            const initialPage = getCurrentPageFromUrl();

            // 设置初始的历史状态
            if (!window.history.state) {
                window.history.replaceState({ page: initialPage }, '', window.location.href);
            }

            switchPage(initialPage, false); // 不更新历史记录，因为这是初始加载

            // 移动端点击内容区域关闭侧边栏
            document.querySelector('.content').addEventListener('click', function() {
                if (window.innerWidth <= 768) {
                    document.getElementById('sidebar').classList.remove('open');
                }
            });

            // 监听窗口大小变化
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    document.getElementById('sidebar').classList.remove('open');
                }
            });
        });
    </script>
</body>
</html>
