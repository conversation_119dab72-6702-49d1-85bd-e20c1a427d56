const { CurdRouter } = require('accel-utils')
const moment = require('moment')
const curdRouter = new (class extends CurdRouter {
  // ...
  async create(ctx) {
    const { data } = this._parseCtx(ctx)
    data.creator = [ctx.state.user.mingdaoId]
    data.operator = [ctx.state.user.mingdaoId]
    // if (data.serviceIsAddWx || data.salesIsAddWx) {
    //   // await strapi.query('customer-op-log').create({
    //   //   customerService: data.customerService,
    //   //   operator: data.operator,
    //   //   type: 'wx',
    //   // })
    //   let updateData = {}
    //   if (data.serviceIsAddWx) {
    //     updateData.serviceIsAddWx = true
    //   }
    //   if (data.salesIsAddWx) {
    //     updateData.salesIsAddWx = true
    //   }
    //   await strapi.query('customer-service').update({
    //     id: data.customerService?.[0]
    //   }, updateData)
    // }
    return super.create(ctx)
  }

  async update(ctx) {
    const { data } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    // if (data.serviceIsAddWx || data.salesIsAddWx) {
    //   // const oldContact = await strapi.query('customer-contact').findOne({ id: data.id })
    //   // if (moment(oldContact.createdAt).endOf('day').valueOf() > moment().valueOf()
    //   //   && (!data.serviceIsAddWx || !data.salesIsAddWx)) {
    //   //   await strapi.query('customer-op-log').create({
    //   //     customerService: data.customerService,
    //   //     operator: data.operator,
    //   //     type: 'wx',
    //   //   })
    //   // }
    //   let updateData = {}
    //   if (data.serviceIsAddWx) {
    //     updateData.serviceIsAddWx = true
    //   }
    //   if (data.salesIsAddWx) {
    //     updateData.salesIsAddWx = true
    //   }
    //   await strapi.query('customer-service').update({
    //     id: data.customerService?.[0]
    //   }, updateData)
    // }
    return super.update(ctx)
  }

  async updateMany(ctx) {
    const { data: { filter, data } } = this._parseCtx(ctx)
    data.operator = [ctx.state.user.mingdaoId]
    const result = await super.updateMany(ctx)
    return result
  }
})('customer-contact')

module.exports = {
  ...curdRouter.createHandlers(),
}
