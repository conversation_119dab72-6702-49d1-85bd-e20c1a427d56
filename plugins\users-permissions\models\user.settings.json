{"collectionName": "users-permissions_user", "info": {"name": "user", "label": "用户", "description": "用户信息包含用户名、邮箱、密码等基础信息，也用于存储管理三方、登录验证等信息"}, "options": {"draftAndPublish": false, "timestamps": true, "privateAttributes": ["password"]}, "attributes": {"username": {"label": "用户名", "type": "string", "configurable": false, "required": true}, "account": {"label": "账号", "type": "string"}, "phone": {"label": "手机号码", "type": "string"}, "email": {"label": "邮箱", "type": "email", "minLength": 6, "configurable": false}, "provider": {"label": "用户来源", "type": "string", "configurable": false}, "password": {"label": "密码", "type": "password", "minLength": 6, "configurable": false, "private": true}, "resetPasswordToken": {"label": "密码重置Token", "type": "string", "configurable": false, "private": true}, "confirmationToken": {"label": "确认Token", "type": "string", "configurable": false, "private": true}, "confirmed": {"label": "确认状态", "type": "boolean", "default": false, "configurable": false}, "blocked": {"label": "是否禁用", "type": "boolean", "default": false, "configurable": false}, "role": {"label": "当前角色", "plugin": "users-permissions", "model": "role", "configurable": false}, "roles": {"label": "可用角色", "plugin": "users-permissions", "collection": "role", "configurable": false, "required": true}, "pBranch": {"label": "当前租户", "plugin": "users-permissions", "model": "branch", "configurable": false}, "pBranches": {"label": "可用租户", "plugin": "users-permissions", "collection": "branch", "configurable": false, "private": true}, "pBranchConfigs": {"label": "可用租户信息", "type": "json", "editable": true, "jsonSchema": {"title": "租户信息列表", "type": "array", "items": {"title": "租户信息", "type": "object", "properties": {"branchId": {"title": "租户ID", "type": "string"}, "blocked": {"title": "是否屏蔽", "type": "boolean"}, "role": {"title": "当前角色", "type": "string"}, "roles": {"title": "可用角色", "type": "array", "items": {"title": "角色ID", "type": "string"}}, "username": {"title": "用户名", "type": "string"}, "phone": {"title": "手机号码", "type": "string"}, "email": {"title": "邮箱", "type": "string"}}}}, "private": true}, "thirdParties": {"label": "关联三方", "type": "dynamiczone", "components": ["account.wechat-app"]}, "scene": {"label": "公众号扫码登录Ticket", "type": "string", "private": true}, "loginCode": {"label": "短信登录验证码", "type": "string", "private": true}, "loginCodeTime": {"label": "短信登录验证码过期时间", "type": "datetime", "private": true}, "loginCodeTryCount": {"label": "短信登录验证码重试次数", "type": "integer", "default": 0, "min": 0, "private": true}, "loginPasswordTryTime": {"label": "密码登录重试时间", "type": "datetime", "private": true}, "loginPasswordTryCount": {"label": "密码登录重试次数", "type": "integer", "default": 0, "min": 0, "private": true}, "description": {"label": "简介", "type": "string"}, "avatar": {"label": "头像", "model": "file", "via": "related", "allowedTypes": ["images", "files", "videos"], "plugin": "upload", "required": false, "pluginOptions": {}}, "customId": {"label": "自定义Id", "type": "string", "configurable": false}}}