'use strict';

module.exports = {
    collectionName: 'huoban-msg-queue',
    info: {
        name: 'HuobansagQueue',
        label: '伙伴订阅消息队列',
        description: '伙伴订阅消息队列'
    },
    options: {
        draftAndPublish: false,
        timestamps: true,
        // indexes: [
        // { keys: { key: 1 }, options: { unique: true } },
        // ],
    },
    pluginOptions: {},
    attributes: {
        status: {
            label: '状态',
            type: 'string',
            editable: false,
            required: true,
            default: 'prepared',
            options: [
                {
                    value: 'prepared',
                    label: '待更新'
                },
                // {
                //     value: 'doing',
                //     label: '进行中'
                // },
                {
                    value: 'success',
                    label: '成功'
                },
                {
                    value: 'failed',
                    label: '失败'
                },
                {
                    value: 'cancelled',
                    label: '已取消'
                }
            ]
        },
        eventId: {
            label: '事件ID',
            type: 'string',
            editable: false,
            required: true,
        },
        eventType: {
            label: '事件类型',
            type: 'string',
            options: [
                {
                    value: 'item.create',
                    label: '创建'
                },
                {
                    value: 'item.update',
                    label: '更新'
                },
                {
                    value: 'item.delete',
                    label: '删除'
                },
                {
                    value: 'bulk_result',
                    label: '批量接口结果通知'
                }
            ],
            editable: false,
            required: true,
        },
        tableId: {
            label: '表格ID',
            type: 'string',
            options: [
                {
                    value: '2100000018480321',
                    label: '项目管理'
                },
                {
                    value: '2100000021897176',
                    label: '合作学校'
                },
                {
                    value: '2100000060996421',
                    label: '业务消息通知'
                },
                {
                    value: '2100000059700201',
                    label: '直营跟进记录'
                },
                {
                    value: '2100000059498808',
                    label: '申请开拓授权'
                }
            ],
            editable: false,
            required: true,
        },
        bulk: {
            label: '是否为批量操作',
            "type": "boolean",
            editable: false,
            required: true,
        },
        itemId: {
            label: '数据ID',
            type: 'string',
            editable: false,
            required: true,
        },
        data: {
            label: '数据',
            editable: false,
            type: 'json',
        },
    },
}