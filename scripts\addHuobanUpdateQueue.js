const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://localhost:27017/testwly-boss-back'
    // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const BOSS_DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://boss:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testboss'
    : '*************************************************************************************************'

let db, dbClient
let bossDb, bossDbClient
let now = new Date();

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    bossDbClient = await MongoClient.connect(BOSS_DB_URL)
    bossDb = bossDbClient.db()

    try {
        let start = Date.now()
        const lock = await db.collection('db-cache').findOne({
            key: 'addHuobanUpdateQueue-lock',
            type: 'lock',
        });
        if (!lock) {
            await db.collection('db-cache').insertOne({
                key: 'addHuobanUpdateQueue-lock',
                type: 'lock',
                expireAt: moment().add(10, 'm').toDate()
            });

            logger('sync start')
            await main()
            logger('sync end')

            await db.collection('db-cache').deleteOne({
                key: 'addHuobanUpdateQueue-lock',
                type: 'lock',
            });
        }
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {
        await dbClient.close()
        await bossDbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('createSchoolQueue start')
    await createSchoolQueue()
    logger('createSchoolQueue end')

    logger('updateAppUsageQueue start')
    await updateAppUsageQueue()
    logger('updateAppUsageQueue end')

    logger('updateAgentAuthQueue start')
    await updateAgentAuthQueue()
    logger('updateAgentAuthQueue end')

    logger('updateTagQueue start')
    await updateTagQueue()
    logger('updateTagQueue end')
}

async function createSchoolQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'createSchoolQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            // _id: ObjectId('672b102d0000c8840210859b'),
            // customer_id: ObjectId('672b102d0000068402108592'),
            // name: '贵阳市第二实验中学麓山校区',
            // handler: { user_id: '6662df80000004a70245cd07', name: '张东茹' },
            // action: 'Create',
            // target: 'yuejuanWebsite20',
            // desc: '阅卷2.0系统学校建站，开通学校：贵阳市第二实验中学麓山校区。创建成功',
            // create_time: ISODate('2024-11-06T06:43:57.482Z'),
            // _schemaid: 524
            id = '672b102d0000c8840210859b';
            await db.collection('db-cache').insertOne({
                key: 'createSchoolQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 5000
        let lastSyncObjectId = id
        while (true) {
            const logCond = {
                target: 'yuejuanWebsite20',
                _id: { '$gt': ObjectId(lastSyncObjectId) },
            }
            const logs = await bossDb.collection('@CustomerActionLog').find(logCond).sort({ _id: 1 }).limit(limit).toArray();
            // const logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@CustomerActionLog',
            //     filter: logCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });
            if (logs.length === 0) { break }

            let insertArray = []
            for (const log of logs) {
                insertArray.push({
                    type: 'schoolWebsite',
                    key: `${log._id.toString()}-schoolWebsite`,
                    customerId: log.customer_id.toString(),
                    customer: log.customer_id,
                    relatedId: log._id.toString(),
                    status: 'prepared',
                    createdAt: now,
                    updatedAt: now,
                })
            }

            if (insertArray.length > 0) {
                const results = await db.collection('huoban-queue').insertMany(insertArray)
            }

            lastSyncObjectId = logs.length > 0 ? logs[logs.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('db-cache').updateOne({
                key: 'createSchoolQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (logs.length < limit) { break }
        }
    } catch (e) {
        logger(e)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `createSchoolQueue err, 原因:${e}`,
            }
        });
    }
}

async function updateAppUsageQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'updateAppUsageQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            // _id: ObjectId('672b368e0001378402109ee8'),
            // customer_id: '5a41b043000006e627a005a6',
            // customer_name: '横州市百合中学',
            // app_category: 'singleSchool',
            // app_version_type: 3,
            // enabled: 1,
            // is_trial: 0,
            // status: 'active',
            // is_base: 0,
            // begin_time: ISODate('2022-09-30T16:00:00.000Z'),
            // end_time: ISODate('2024-11-12T15:59:59.999Z'),
            // params: [ { name: '访问周期', value: [ '2020-09-14', '2024-11-12' ] } ],
            // source: { type: 'add', data: null },
            // submitter: { user_id: '6662df80000004a70245cd07', name: '张东茹' },
            // remark: 'ZX-240913-174单章暂时开通',
            // create_time: ISODate('2024-11-06T09:27:42.276Z'),
            // update_time: ISODate('2024-11-06T09:27:42.276Z'),
            // _schemaid: 791
            id = '672b368e0001378402109ee8';
            await db.collection('db-cache').insertOne({
                key: 'updateAppUsageQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 5000
        let lastSyncObjectId = id
        while (true) {
            const logCond = {
                enabled: 1,
                // is_trial: 0,
                _id: { '$gt': ObjectId(lastSyncObjectId) },
            }
            const logs = await bossDb.collection('@CustomerAppUsageConfig').find(logCond).sort({ _id: 1 }).limit(limit).toArray();
            // const logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@CustomerAppUsageConfig',
            //     filter: logCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });
            if (logs.length === 0) { break }

            let insertArray = []
            for (const log of logs) {
                insertArray.push({
                    type: 'appUsage',
                    key: `${log._id.toString()}-appUsage`,
                    customerId: log.customer_id,
                    customer: ObjectId(log.customer_id),
                    relatedId: log._id.toString(),
                    status: 'prepared',
                    createdAt: now,
                    updatedAt: now,
                })
            }

            if (insertArray.length > 0) {
                const results = await db.collection('huoban-queue').insertMany(insertArray)
            }

            lastSyncObjectId = logs.length > 0 ? logs[logs.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('db-cache').updateOne({
                key: 'updateAppUsageQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (logs.length < limit) { break }
        }
    } catch (e) {
        logger(e)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `updateAppUsageQueue err, 原因:${e}`,
            }
        });
    }
}

async function updateAgentAuthQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'updateAgentAuthQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            id = '67639f84000149b409431dec';
            await db.collection('db-cache').insertOne({
                key: 'updateAgentAuthQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 5000
        let lastSyncObjectId = id
        while (true) {
            const logCond = {
                target: 'Customer',
                'product.sign': 'saas',
                _id: { '$gt': ObjectId(lastSyncObjectId) },
            }
            const logs = await bossDb.collection('@AgentAuthActionLog').find(logCond).sort({ _id: 1 }).limit(limit).toArray();
            // const logs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     coll: '@AgentAuthActionLog',
            //     filter: logCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });
            if (logs.length === 0) { break }

            let insertArray = []
            for (const log of logs) {
                insertArray.push({
                    type: 'agentAuth',
                    key: `${log._id.toString()}-agentAuth`,
                    customerId: log.customer.id,
                    customer: ObjectId(log.customer.id),
                    relatedId: log._id.toString(),
                    status: 'prepared',
                    createdAt: now,
                    updatedAt: now,
                })
            }

            if (insertArray.length > 0) {
                const results = await db.collection('huoban-queue').insertMany(insertArray)
            }

            lastSyncObjectId = logs.length > 0 ? logs[logs.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('db-cache').updateOne({
                key: 'updateAgentAuthQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (logs.length < limit) { break }
        }
    } catch (e) {
        logger(e)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `updateAgentAuthQueue err, 原因:${e}`,
            }
        });
    }
}

async function updateTagQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'updateTagQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            id = '000000000000000000000000';
            await db.collection('db-cache').insertOne({
                key: 'updateTagQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 5000
        let lastSyncObjectId = id
        while (true) {
            const logCond = {
                type: 'yjTag',
                _id: { '$gt': ObjectId(lastSyncObjectId) }
            }
            const opLogs = await db.collection('customer-op-log').find(logCond).sort({ _id: 1 }).limit(limit).toArray();
            // const opLogs = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     db: 'wly_school',
            //     coll: 'customer-op-log',
            //     filter: logCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });

            if (opLogs.length === 0) { break }

            const customerCond = {
                _id: { '$in': opLogs.map(e => ObjectId(e.customerService)) }
            }
            const customers = await db.collection('customer-service').find(customerCond).sort({ _id: 1 }).limit(limit).toArray();
            // const customers = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     db: 'wly_school',
            //     coll: 'customer-service',
            //     filter: customerCond,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });


            let insertArray = []
            for (const opLog of opLogs) {
                const curCustomer = customers.find(e => e._id.toString() === opLog.customerService.toString())
                insertArray.push({
                    type: 'schoolTag',
                    key: `${opLog._id.toString()}-schoolTag`,
                    customerId: curCustomer.customerId,
                    customer: ObjectId(curCustomer.customerId),
                    relatedId: opLog._id.toString(),
                    status: 'prepared',
                    createdAt: now,
                    updatedAt: now,
                })
            }

            if (insertArray.length > 0) {
                const results = await db.collection('huoban-queue').insertMany(insertArray)
            }

            lastSyncObjectId = opLogs.length > 0 ? opLogs[opLogs.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('db-cache').updateOne({
                key: 'updateTagQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (opLogs.length < limit) { break }
        }
    } catch (e) {
        logger(e)
        await axios.post('https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=b22cb7f5-d702-471a-9c56-813ea22ae05d', {
            msgtype: 'text',
            text: {
                content: `updateTagQueue err, 原因:${e}`,
            }
        });
    }
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
