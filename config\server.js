module.exports = ({ env }) => {

  const port = env.int('NODE_PORT', 3015)

  const serverConfig = {
    prod: {
      serverUrl: `https://wly-boss-api-lan.iyunxiao.com`,
      serverWanUrl: `https://wly-boss-api-wan.iyunxiao.com`,
      adminUrl: `https://wly-boss.iyunxiao.com`,
      crmUrl: `https://boss-crm.iyunxiao.com/wly/contract`,
      yjApiServer: {
        gwUrl: 'http://yj-apigw-lan.haofenshu.com',
        gwJwtSecret: '462fd506cf7c463caa4bdfa94fad5ea3',
        yezhiUrl: 'https://yezhi.haofenshu.com'
      },
      bossApi: {
        url: `http://boss-api.iyunxiao.com`,
        apikey: `1c0b988c876e84c3729fce1fc23a5336`,
        token: `752925bb4635621c68c2098ae73e18db`,
      },
      bossWfUrl: `http://boss-wf.iyunxiao.com`,
      bossSs: {
        url: `http://boss-ss-api.iyunxiao.com`,
        apikey: `1c0b988c876e84c3729fce1fc23a5336`,
        token: `752925bb4635621c68c2098ae73e18db`,
      },
      huobanApiUrl: `https://api.huoban.com/openapi`,
      goodManagerApiUrl: `http://good-manager-lan.yunxiao.com`,
      bossSecret: `Ci23fWtahDYE3dfirAHrJhzrUEoslIxqwcDN9VNhRJCWf8Tyc1F1mqYrjGYF`,
      wlyConfig: {
        url: `https://wly-config-api.iyunxiao.com`,
        accessKey: 'elDar9quajPi8IGN8MOLnMNp',
        adminAccessKey: '<EMAIL>',
      },
      wlySpaceApi: {
        url: `http://wly-space-api-lan.iyunxiao.com`,
      },
      cron: {
        enabled: true
      },
      "msgCentre": {
        "host": "http://msg-center.yunxiao.com",
        "apiKey": "87b0c367a2f00bc014ffdbda6acf8d13",
        "app": {
          "shortMsg": {
            "bdy": "23"
          }
        }
      },
    },
    test: {
      serverUrl: `https://testwly-boss-api-lan.iyunxiao.com`,
      serverWanUrl: `https://testwly-boss-api-wan.iyunxiao.com`,
      adminUrl: `http://localzhxy-dev.yunxiao.com:${port + 1}`,
      crmUrl: `https://testboss-crm.iyunxiao.com/wly/contract`,
      yjApiServer: {
        gwUrl: 'http://yj-apigw-lan.haofenshu.com',
        gwJwtSecret: '462fd506cf7c463caa4bdfa94fad5ea3',
        yezhiUrl: 'https://yezhi.haofenshu.com'
      },
      bossApi: {
        url: `http://testboss-api.iyunxiao.com`,
        apikey: `123456`,
        token: `123456`,
      },
      bossWfUrl: `http://testboss-wf.iyunxiao.com`,
      bossSs: {
        url: `http://testboss-ss-api.iyunxiao.com`,
        apikey: `123456`,
        token: `123456`,
      },
      huobanApiUrl: `https://api.huoban.com/openapi`,
      goodManagerApiUrl: `http://testgood-manager.yunxiao.com`,
      bossSecret: `HrJhzrUEoslIxqwcdfirAVqYrjGYFDN9CNhRJCWf8Tyc1F1mi23fWtahDYE3`,
      wlyConfig: {
        url: `https://testwly-config-api.iyunxiao.com`,
        accessKey: 'xPIQu5QcAjB36sHEC4uJ1Wlu',
        adminAccessKey: '<EMAIL>',
      },
      wlySpaceApi: {
        url: `http://testwly-space-api-lan.iyunxiao.com`,
      },
      cron: {
        enabled: true
      },
      "msgCentre": {
        "host": "http://msg-center.yunxiao.com",
        "apiKey": "87b0c367a2f00bc014ffdbda6acf8d13",
        "app": {
          "shortMsg": {
            "bdy": "23"
          }
        }
      },
    },
    local: {
      serverUrl: `http://localhost:${port}`,
      serverWanUrl: `https://testwly-boss-api-wan.iyunxiao.com`,
      adminUrl: `http://localzhxy-dev.yunxiao.com:${port + 1}`,
      crmUrl: `https://testboss-crm.iyunxiao.com/wly/contract`,
      yjApiServer: {
        gwUrl: 'http://yj-apigw-lan.haofenshu.com',
        gwJwtSecret: '462fd506cf7c463caa4bdfa94fad5ea3',
        yezhiUrl: 'https://yezhi.haofenshu.com'
      },
      bossApi: {
        url: `http://testboss-api.iyunxiao.com`,
        apikey: `123456`,
        token: `123456`,
      },
      bossWfUrl: `http://testboss-wf.iyunxiao.com`,
      bossSs: {
        url: `http://testboss-ss-api.iyunxiao.com`,
        apikey: `123456`,
        token: `123456`,
      },
      huobanApiUrl: `https://api.huoban.com/openapi`,
      goodManagerApiUrl: `http://testgood-manager.yunxiao.com`,
      bossSecret: `HrJhzrUEoslIxqwcdfirAVqYrjGYFDN9CNhRJCWf8Tyc1F1mi23fWtahDYE3`,
      wlyConfig: {
        url: `http://testwly-config-api.iyunxiao.com`,
        accessKey: 'xPIQu5QcAjB36sHEC4uJ1Wlu',
        adminAccessKey: '<EMAIL>',
      },
      wlySpaceApi: {
        url: `http://testwly-space-api-lan.iyunxiao.com`,
      },
      cron: {
        enabled: true
      },
      "msgCentre": {
        "host": "http://msg-center.yunxiao.com",
        "apiKey": "87b0c367a2f00bc014ffdbda6acf8d13",
        "app": {
          "shortMsg": {
            "bdy": "23"
          }
        }
      },
    },
  }[env('SERVER', 'prod')]

  const webhookConfig = {
    // api报错时的webhook
    prod: {
      webhookUrl: '',
    },
    test: {
      webhookUrl: '',
    },
    local: {
      webhookUrl: '',
    },
  }[env('SERVER', 'prod')]

  const mingdaoConfig = {
    prod: {
      managerId: '672d7718444eb62fc450ecbe',
      managerGroupId: '672d7816444eb62fc450ecd3',
      managerUnitId: '6751991aba60f67ec34c3903',
      customerContactId: '672d8ee8962c1f8ca4a20857',
      customerServiceId: '672d912b962c1f8ca4a20961',
      customerInactivePoolId: '677f39839e13a09bfff91cf3',
      customerSalesFollowRecordId: '672db14c63106d1d595a52cd',
      customerServiceFollowRecordId: '672db04463106d1d595a5298',
      managerProblemId: '674681e3ba60f67ec34b2cb8',
      salesProjectId: '6759790dba60f67ec34d0c5b',
      salesProjectItemId: '6759790dba60f67ec34d0c5a',
      provinceId: '673b0186ba60f67ec3495203',
      serviceDistributeRecordId: '6751991aba60f67ec34c3902',
      salesDistributeRecordId: '6751991aba60f67ec34c3901',
      // 招投标
      biddingProjectId: '676938269e13a09bfff3ba97',
      biddingS1ProjectId: '67ae0a799e13a09bff06f171',
      biddingS1ProjectLogId: '67ae0a799e13a09bff06f170',
      biddingS2ProjectId: '67ae0a799e13a09bff06f16f',
      // 阅卷家菜单管理
      brandMenuId: '67510d79ba60f67ec34c2080',
      // 项目管理
      saasProjectId: '67b549c39e13a09bff09f167',
      saasRequirementId: '67b456359e13a09bff09b527',
      saasQuestionId: '67b549fc9e13a09bff09f174',
      saasDailySummaryId: '67b54a229e13a09bff09f17f',
      // 回收明细
      recycleUnitDetailId: '6790a76d9e13a09bfffeca61',
      recycleClueDetailId: '67a568969e13a09bff029de9',
    },
    test: {
      managerId: '67495d23d96c43d48af86e8a',
      managerGroupId: '67495d23d96c43d48af86e89',
      managerUnitId: '67504174ba60f67ec34c1d50',
      customerContactId: '67495d23d96c43d48af86e86',
      customerServiceId: '67495d23d96c43d48af86e85',
      customerInactivePoolId: '677f3b889e13a09bfff92240',
      customerSalesFollowRecordId: '67495d23d96c43d48af86e7f',
      customerServiceFollowRecordId: '67495d23d96c43d48af86e83',
      managerProblemId: '67495d23d96c43d48af86e76',
      salesProjectId: '6758f80fba60f67ec34cd357',
      salesProjectItemId: '67590336ba60f67ec34cd715',
      provinceId: '67495d23d96c43d48af86e7a',
      serviceDistributeRecordId: '67518809ba60f67ec34c3761',
      salesDistributeRecordId: '6751888fba60f67ec34c3787',
      // 招投标
      biddingProjectId: '67693db39e13a09bfff3be65',
      biddingS1ProjectId: '678fb8c79e13a09bfffe9289',
      biddingS1ProjectLogId: '678fba549e13a09bfffe92db',
      biddingS2ProjectId: '678fbba69e13a09bfffe9319',
      // 阅卷家菜单管理
      brandMenuId: '67510d79ba60f67ec34c2080',
      // 项目管理
      saasProjectId: '67b549c39e13a09bff09f167',
      saasRequirementId: '67b456359e13a09bff09b527',
      saasQuestionId: '67b549fc9e13a09bff09f174',
      saasDailySummaryId: '67b54a229e13a09bff09f17f',
      // 回收明细
      recycleUnitDetailId: '67ae09c89e13a09bff06f0ea',
      recycleClueDetailId: '67ae09c89e13a09bff06f0e9',
    },
    local: {
      managerId: '67495d23d96c43d48af86e8a',
      managerGroupId: '67495d23d96c43d48af86e89',
      managerUnitId: '67504174ba60f67ec34c1d50',
      customerContactId: '67495d23d96c43d48af86e86',
      customerServiceId: '67495d23d96c43d48af86e85',
      customerInactivePoolId: '677f3b889e13a09bfff92240',
      customerSalesFollowRecordId: '67495d23d96c43d48af86e7f',
      customerServiceFollowRecordId: '67495d23d96c43d48af86e83',
      managerProblemId: '67495d23d96c43d48af86e76',
      salesProjectId: '6758f80fba60f67ec34cd357',
      salesProjectItemId: '67590336ba60f67ec34cd715',
      provinceId: '67495d23d96c43d48af86e79',
      serviceDistributeRecordId: '67518809ba60f67ec34c3761',
      salesDistributeRecordId: '6751888fba60f67ec34c3787',
      // 招投标
      biddingProjectId: '67693db39e13a09bfff3be65',
      biddingS1ProjectId: '678fb8c79e13a09bfffe9289',
      biddingS1ProjectLogId: '678fba549e13a09bfffe92db',
      biddingS2ProjectId: '678fbba69e13a09bfffe9319',
      // 阅卷家菜单管理
      brandMenuId: '67510d79ba60f67ec34c2080',
      // 项目管理
      saasProjectId: '67b549c39e13a09bff09f167',
      saasRequirementId: '67b456359e13a09bff09b527',
      saasQuestionId: '67b549fc9e13a09bff09f174',
      saasDailySummaryId: '67b54a229e13a09bff09f17f',
      // 回收明细
      recycleUnitDetailId: '67ae09c89e13a09bff06f0ea',
      recycleClueDetailId: '67ae09c89e13a09bff06f0e9',
    },
  }[env('SERVER', 'prod')]

  return {
    host: env('HOST', '0.0.0.0'),
    port: port,
    serverUrl: serverConfig.serverUrl,
    serverWanUrl: serverConfig.serverWanUrl,
    adminUrl: serverConfig.adminUrl,
    webhookUrl: webhookConfig.webhookUrl,
    resetPasswordUrl: serverConfig.resetPasswordUrl,
    crmUrl: serverConfig.crmUrl,
    yjApiServer: serverConfig.yjApiServer,
    bossApi: serverConfig.bossApi,
    bossWfUrl: serverConfig.bossWfUrl,
    bossSs: serverConfig.bossSs,
    bossSecret: serverConfig.bossSecret,
    goodManagerApiUrl: serverConfig.goodManagerApiUrl,
    wlyConfig: serverConfig.wlyConfig,
    wlySpaceApi: serverConfig.wlySpaceApi,
    msgCentre: serverConfig.msgCentre,
    cron: serverConfig.cron,
    huoban: {
      url: serverConfig.huobanApiUrl,
      space_id: 4000000003570865,
      auth_header: 'Bearer EzenEYv2W5F0R7DXzSLrZxMwP1teBrsiaSEPCosl',
      project_table_id: 2100000018480321,
      project_table: require('../api/contract/controllers/tables/业务管理/项目管理-2100000018480321.json'),
      agent_table: require('../api/contract/controllers/tables/业务管理/经销商档案-2100000018475755.json'),
      school_table_id: 2100000021897176,
      school_table: require('../api/contract/controllers/tables/业务管理/合作学校-2100000021897176.json'),
      staff_table: require('../api/contract/controllers/tables/业务管理/员工信息-2100000018513284.json'),
      saas_table: require('../api/contract/controllers/tables/业务管理/应续SaaS列表-2100000052919044.json'),
      province_table: require('../api/contract/controllers/tables/业务管理/省-2100000018524704.json'),
      city_table: require('../api/contract/controllers/tables/业务管理/市-2100000018524705.json'),
      district_table: require('../api/contract/controllers/tables/业务管理/区-县-2100000018524706.json'),
      sales_project_table: require('../api/contract/controllers/tables/业务管理/直营项目管理-2100000054080177.json'),
      apply_auth_table_id: 2100000059498808,
      apply_auth_table: require('../api/contract/controllers/tables/业务管理/申请开拓授权-2100000059498808.json'),
      agent_auth_table: require('../api/contract/controllers/tables/业务管理/授权记录-2100000059578666.json'),
      sales_follow_table: require('../api/contract/controllers/tables/业务管理/项目跟进记录（直营）-2100000059700201.json'),
      sales_saas_feature_table: require('../api/contract/controllers/tables/业务管理/直营SaaS项目特点-2100000059982481.json'),
      sales_saas_objective_table: require('../api/contract/controllers/tables/业务管理/直营SaaS谈判目的-2100000060066489.json'),
      sales_msg_table: require('../api/contract/controllers/tables/业务管理/消息队列-2100000060996421.json'),
      cost_type_table: require('../api/contract/controllers/tables/业务管理/AMB成本类型-2100000056577118.json'),
      business_unit_table: require('../api/contract/controllers/tables/业务管理/经营单元-2100000055636966.json'),
      expenditure_table: require('../api/contract/controllers/tables/业务管理/AMB支出明细-2100000055614225.json'),
    },
    mingdaoConfig: mingdaoConfig,
    payment: {
      wechatPay: {
        pay_notify_url: `${serverConfig.serverWanUrl}/third-party-payments/wechat/pay/notification`,
        refund_notify_url: `${serverConfig.serverWanUrl}/third-party-payments/wechat/refund/notification`,
      }
    },
    admin: {
      serveAdminPanel: false,
      auth: {
        secret: env('ADMIN_JWT_SECRET', 'Rq0JX4TiAH5ECBUpyLGf'),
      },
    },
    jwt: {
      expiresIn: 3600 * 24 * 30
    },
    // 小程序相关配置
    wechat: {
      appid: '',
      secret: '',
      token: ''
    },
    // 短信相关配置 - 当前支持腾讯云短信
    sms: {
      secretId: '',
      secretKey: '',
      region: '',
      SmsSdkAppId: '',
      SignName: ''
    },
    // 验证码相关配置 - 当前支持腾讯云滑动验证码
    captcha: {
      secretId: '',
      secretKey: '',
      CaptchaAppId: 2072317168,
      AppSecretKey: ''
    }
  }
}
