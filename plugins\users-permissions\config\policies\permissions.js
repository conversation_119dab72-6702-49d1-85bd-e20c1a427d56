'use strict'

const _ = require('lodash')

module.exports = async (ctx, next) => {
  let role

  if (ctx.state.user) {
    // request is already authenticated in a different way
    return next()
  }

  // 从 Header、URL Query、RequestBody 中获取 token
  const token = ctx.request?.header?.authorization
    || ctx.request?.query?.token
    || ctx.request?.body?.token

  if (token) {
    try {
      const { id, tokenId } = await strapi.plugins['users-permissions'].services.jwt.getToken(ctx)
      if (tokenId) {
        ctx.state.token = await strapi.query('token', 'users-permissions').findOne({ id: tokenId }, [])
      }
      if (id === undefined) {
        throw new Error('Invalid token: Token did not contain required fields')
      }
      // fetch authenticated user
      ctx.state.user = await strapi.plugins[
        'users-permissions'
        ].services.user.fetchAuthenticatedUser(id)
    } catch (err) {
      if (err.message === 'Invalid token.') {
        // Token 无效状态允许请求 public 接口
        const role = await strapi.query('role', 'users-permissions').findOne({ type: 'public' }, [])
        const route = ctx.request.route
        const permission = await strapi.query('permission', 'users-permissions').findOne(
          {
            role: role.id,
            type: route.plugin || 'application',
            controller: route.controller,
            action: route.action,
            enabled: true,
          },
          []
        )
        if (!permission) {
          return handleErrors(ctx, err, 'unauthorized')
        }
        // Execute the policies.
        if (permission.policy) {
          return await strapi.plugins['users-permissions'].config.policies[permission.policy](ctx, next)
        }
        return await next()
      }
      return handleErrors(ctx, err, 'unauthorized')
    }

    if (!ctx.state.user) {
      return handleErrors(ctx, 'User Not Found', 'unauthorized')
    }

    role = ctx.state.user.role

    if (role.type === 'root') {
      return await next()
    }

    const store = await strapi.store({
      type: 'plugin',
      name: 'users-permissions',
    })

    if (
      _.get(await store.get({ key: 'advanced' }), 'email_confirmation') &&
      !ctx.state.user.confirmed
    ) {
      return handleErrors(ctx, 'Your account email is not confirmed.', 'unauthorized')
    }
    // 平台屏蔽
    if (ctx.state.user.blocked) {
      return handleErrors(
        ctx,
        '您的账号已经被平台管理员屏蔽，如有疑问请联系平台管理员。',
        'unauthorized'
      )
    }
    // 租户内屏蔽
    const branch = ctx.state.user.pBranch
    if (ctx.state.user.pBranch) {
      const branchConfig = ctx.state.user.pBranchConfigs?.find(e => e.branchId === branch.id)
      if (branchConfig && branchConfig.blocked) {
        return handleErrors(
          ctx,
          '您的账号已经被企业管理员屏蔽，如有疑问请联系企业管理员。',
          'unauthorized'
        )
      }
    }
  }
  if (ctx.request.route.controller !== 'api') {
    // Retrieve `public` role.
    if (!role) {
      role = await strapi.query('role', 'users-permissions').findOne({ type: 'public' }, [])
    }

    const route = ctx.request.route
    const permission = await strapi.query('permission', 'users-permissions').findOne(
      {
        role: role.id,
        type: route.plugin || 'application',
        controller: route.controller,
        action: route.action,
        enabled: true,
      },
      []
    )

    if (!permission) {
      return handleErrors(ctx, undefined, 'forbidden')
    }

    // Execute the policies.
    if (permission.policy) {
      return await strapi.plugins['users-permissions'].config.policies[permission.policy](ctx, next)
    }
  }

  // 附加 Query Filter
  const queryFilters = strapi.config.permission.queryFilters
  const route = ctx.request.route
  if (queryFilters) {
    for (let queryFilter of queryFilters) {
      const type = route.plugin || 'application'
      const controller = route.controller
      const action = route.action
      if (
        (queryFilter.type === type || (_.isRegExp(queryFilter.type) && queryFilter.type?.test(type)))
        && (queryFilter.controller === controller || (_.isArray(queryFilter.controller) && queryFilter.controller.includes(controller)))
        && (queryFilter.action === action || (_.isRegExp(queryFilter.action) && queryFilter.action?.test(action)))
      ) {
        await queryFilter.callback(ctx)
      }
    }
  }
  // Execute the action.
  await next()
}

const handleErrors = (ctx, err = undefined, type) => {
  throw strapi.errors[type](err)
}
