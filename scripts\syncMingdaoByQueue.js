const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const _ = require('lodash');
const DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const SERVER_URL = (process.env.NODE_ENV !== 'production')
    ? 'http://localhost:3015'
    : 'https://wly-boss-api-lan.iyunxiao.com'

let db, dbClient
let retry = 3;

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    try {
        let start = Date.now()
        const lock = await db.collection('db-cache').findOne({
            key: 'syncMingdaoByQueue-lock',
            type: 'lock',
        });
        if (!lock) {
            await db.collection('db-cache').insertOne({
                key: 'syncMingdaoByQueue-lock',
                type: 'lock',
                expireAt: moment().add(60, 'm').toDate()
            });

            logger('sync start')
            await main()
            logger('sync end')

            await db.collection('db-cache').deleteOne({
                key: 'syncMingdaoByQueue-lock',
                type: 'lock',
            });
        }
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {
        await dbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('updateMingdaoQueue start')
    await updateMingdaoQueue()
    logger('updateMingdaoQueue end')
}


async function updateMingdaoQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'syncMingdaoByQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            id = '000000000000000000000000';
            await db.collection('db-cache').insertOne({
                key: 'syncMingdaoByQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 50
        let lastSyncObjectId = id
        while (true) {
            const conditions = {
                status: 'prepared',
                _id: { '$gt': ObjectId(lastSyncObjectId) }
            }
            const queueList = await db.collection('mingdao-queue').find(conditions).sort({ _id: 1 }).limit(limit).toArray();
            // const queueList = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     db: 'wly_boss',
            //     coll: 'mingdao-queue',
            //     filter: conditions,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });

            if (queueList.length === 0) { break }

            const res = await axios.post(`${SERVER_URL}/external/customer-services/updateMingdaoSchool`, { customerIds: _.uniq(queueList.map(e => e.customerId)) })

            lastSyncObjectId = queueList.length > 0 ? queueList[queueList.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            await db.collection('mingdao-queue').updateMany({
                _id: { $in: queueList.map(e => e._id) }
            }, {
                $set: {
                    status: 'success',
                }
            });
            await db.collection('db-cache').updateOne({
                key: 'syncMingdaoByQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (queueList.length < limit) { break }
            await sleep(3 * 1000);
        }
    } catch (e) {
        logger(`retry: ${retry}`)
        logger(e.message)
        if (retry > 0) {
            await sleep(3 * 60 * 1000);
            --retry
            await updateMingdaoQueue();
        }
        // throw e;
    }
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
