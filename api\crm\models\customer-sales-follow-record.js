module.exports = {
    "kind": "collectionType",
    "collectionName": strapi.config.server.mingdaoConfig.customerSalesFollowRecordId,
    "connection": "mingdao",
    info: {
        name: 'CustomerSalesFollowRecord',
        label: '直营经理跟进记录',
        description: '直营经理跟进记录'
    },
    "options": {},
    "pluginOptions": {},
    "attributes": {
        customerService: {
            // label: '客户',
            "ref": "6731c6571637ee6db9afde3c"
        },
        follower: {
            // label: '跟进人',
            mainField: 'username',
            "ref": "672db14c63106d1d595a52cf"
        },
        // operator: {
        //     // label: '操作人',
        //     mainField: 'username',
        //     "ref": "672db14c63106d1d595a52d0"
        // },
        contact: {
            // label: '联系人',
            mainField: 'title',
            "ref": "672db14c63106d1d595a52d1"
        },
        time: {
            // label: '跟进时间',
            "ref": "672db14c63106d1d595a52d2"
        },
        content: {
            // label: '沟通内容',
            "ref": "672db14c63106d1d595a52d3"
        },
        nextContent: {
            // label: '下次跟进方向',
            "ref": "672db14c63106d1d595a52d4"
        },
        nextTime: {
            // label: '下次跟进时间',
            "ref": "6732d8971637ee6db9afedd6"
        },
        recordInvalid: {
            // label: '记录无效',
            "ref": "6732d8971637ee6db9afedd7"
        },
        // status: {
        //     // label: '状态',
        //     "ref": "672db14c63106d1d595a52d7"
        // },
        // prevFollow: {
        //     // label: '上次服务',
        //     "ref": "672db14c63106d1d595a52d8"
        // },
        // nextFollow: {
        //     // label: '下次服务',
        //     "ref": "672db14c63106d1d595a52d9"
        // },
        // stage: {
        //     // label: '阶段',
        //     "ref": "672db14c63106d1d595a52da"
        // },
        // action: {
        //     // label: '动作',
        //     "ref": "672db14c63106d1d595a52db"
        // },
        // customerBargainCheck: {
        //     // label: '客户谈单检查',
        //     "ref": "672db14c63106d1d595a52dc"
        // },
        // salesId: {
        //     // label: '客户直营经理Id',
        //     "ref": "673593dd9b0246655126ac7b"
        // },
        // serviceId: {
        //     // label: '客户直服经理Id',
        //     "ref": "673593ea9b0246655126ac85"
        // },
        salesQxId: {
            // label: '直营经理自定义Id',
            "ref": "6764f2239e13a09bfff36734"
        },
        serviceQxId: {
            // label: '运营经理自定义Id',
            "ref": "6764f3129e13a09bfff36749"
        },
    }
}
