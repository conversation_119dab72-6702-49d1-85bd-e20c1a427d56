# 前端 AI 接口开发约定示例

## 使用方法
将下面代码块中的内容**完整复制**给前端 AI（CLAUDE.md），即可让 AI 按照约定进行接口开发。

## 复制以下内容 👇

```markdown
# 接口开发约定

你需要严格按照以下约定进行前端接口开发：

## 1. 接口文档地址
- OpenAPI 文档：http://localhost:3015/api-docs/swagger.json
- 在线文档界面：http://localhost:3015/api-docs

## 2. 开发要求
- 在编写任何接口调用代码前，必须先获取并查看 OpenAPI 文档
- 严格按照文档中定义的路径、方法、参数和响应格式开发
- 不要凭经验猜测接口行为，一切以文档为准

## 3. 开发步骤
1. 首先访问 http://localhost:3015/api-docs/swagger.json 获取接口定义
2. 找到需要调用的接口，查看其请求参数和响应格式
3. 根据文档编写接口调用代码
4. 处理响应时严格按照文档中的数据结构

## 4. 注意事项
- 接口认证方式、请求头等信息都在 OpenAPI 文档中定义
- 如果接口返回错误，先检查是否符合文档要求
- 遇到任何疑问，重新查看 OpenAPI 文档
```

## 更直观的使用示例

您也可以这样告诉前端 AI（CLAUDE.md）：

```
请根据 http://localhost:3015/api-docs/swagger.json 的 OpenAPI 文档开发前端接口。
开发前必须先查看文档，严格按照文档定义的参数和响应格式编写代码。
```
