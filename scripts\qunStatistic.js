const { ObjectId } = require('mongodb')
const { customerTypes } = require('../api/crm-mid/utils/crmTypes')

async function statistic(db, userGroupMap) {
  const userCustomerMap = await getUserCustomerMap(db)

  let list = await convertMap2Statistic(userCustomerMap, userGroupMap)
  // 删除
  await db.collection('qun-statistic').deleteMany({})
  const results = await db.collection('qun-statistic').insertMany(list)
  console.log('写入数量', results.insertedCount, results.acknowledged)
}

async function getUserCustomerMap(db) {
  const customers = await db.collection('qun-check').find({ 'customer_customerId': { $ne: null } }).toArray()
  const userMap = {}
  for (const customer of customers) {
    if (!customer.customer_directServiceManager) {
      continue
    }

    if (customer.customer_directServiceManager) {
      if (!userMap[customer.customer_directServiceManager.toString()]) {
        userMap[customer.customer_directServiceManager.toString()] = []
      }
      userMap[customer.customer_directServiceManager.toString()].push(customer)
    }
  }
  return userMap
}

async function convertMap2Statistic(userCustomerMap, userGroupMap) {
  const list = []
  Object.keys(userCustomerMap).forEach(key => {
    const customers = userCustomerMap[key]
    const types = handleStatistic(key, customers, userGroupMap)
    list.push(...types)
  })
  return list
}

const crmType = customerTypes

function handleStatistic(userId, customers, userGroupMap) {

  const statisticTmp = {
    manager: ObjectId(userId),
    team: userId ? userGroupMap[userId] : null,
    schoolCount: 0,
    qunCount: 0,
    noUserQunCount: 0,
    vipSchoolCount: 0,
    vipQunCount: 0,
    vipNoUserQunCount: 0,
    noVipSchoolCount: 0,
    noVipQunCount: 0,
    noVipNoUserQunCount: 0,
    todayVipNeedQunCount: 0,
    todayNoVipNeedQunCount: 0,
    future3DayVipNeedQunCount: 0,
    future3DayNoVipNeedQunCount: 0,
    future7DayVipNeedQunCount: 0,
    future7DayNoVipNeedQunCount: 0,
    month1VipSchoolCount: 0,
    month1VipQunCount: 0,
    month1VipNoUserQunCount: 0,
    month1NoVipSchoolCount: 0,
    month1NoVipQunCount: 0,
    month1NoVipNoUserQunCount: 0,
    month3VipSchoolCount: 0,
    month3VipQunCount: 0,
    month3VipNoUserQunCount: 0,
    month3NoVipSchoolCount: 0,
    month3NoVipQunCount: 0,
    month3NoVipNoUserQunCount: 0,
    month6VipSchoolCount: 0,
    month6VipQunCount: 0,
    month6VipNoUserQunCount: 0,
    month6NoVipSchoolCount: 0,
    month6NoVipQunCount: 0,
    month6NoVipNoUserQunCount: 0,
    month12VipSchoolCount: 0,
    month12VipQunCount: 0,
    month12VipNoUserQunCount: 0,
    month12NoVipSchoolCount: 0,
    month12NoVipQunCount: 0,
    month12NoVipNoUserQunCount: 0
  }

  const types = crmType.map(item => {
    return {
      crm_type: item.value,
      ...statisticTmp
    }
  })


  for (const customer of customers) {
    if (!customer.customer_crm_type || !types.some(item => customer.customer_crm_type === item.crm_type)){
      continue
    }
    const statistic  = types.find(item => customer.customer_crm_type === item.crm_type)
    const lastExamTime = customer.customer_lastExamTime
    const isVip = customer.customer_isVIP
    statistic.schoolCount++
    if (customer.qun_qunId) {
      statistic.qunCount++
      if (customer.qun_qunExternalUserCount === 0) {
        statistic.noUserQunCount++
      }
    }

    statistic[`${isVip ? 'vip' : 'noVip'}SchoolCount`]++
    if (customer.qun_qunId) {
      statistic[`${isVip ? 'vip' : 'noVip'}QunCount`]++
      if (customer.qun_qunExternalUserCount === 0) {
        statistic[`${isVip ? 'vip' : 'noVip'}NoUserQunCount`]++
      }
    }
    const month1Range = [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date(new Date().setHours(23, 59, 59, 999))]
    const month3Range = [new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)]
    const month6Range = [new Date(Date.now() - 180 * 24 * 60 * 60 * 1000), new Date(Date.now() - 90 * 24 * 60 * 60 * 1000)]
    const month12Range = [new Date(Date.now() - 360 * 24 * 60 * 60 * 1000), new Date(Date.now() - 180 * 24 * 60 * 60 * 1000)]
    // Date 类型判断区间
    if (lastExamTime >= month1Range[0] && lastExamTime < month1Range[1]) {
      monthStatistic(statistic, isVip, 1, customer.qun_qunId, customer.qun_qunExternalUserCount)
    }

    if (lastExamTime >= month3Range[0] && lastExamTime < month3Range[1]) {
      monthStatistic(statistic, isVip, 3, customer.qun_qunId, customer.qun_qunExternalUserCount)
    }

    if (lastExamTime >= month6Range[0] && lastExamTime < month6Range[1]) {
      monthStatistic(statistic, isVip, 6, customer.qun_qunId, customer.qun_qunExternalUserCount)
    }

    if (lastExamTime >= month12Range[0] && lastExamTime < month12Range[1]) {
      monthStatistic(statistic, isVip, 12, customer.qun_qunId, customer.qun_qunExternalUserCount)
    }

    const todayRange = [new Date(new Date().setHours(0, 0, 0, 0)), new Date(new Date().setHours(23, 59, 59, 999))]

    if (lastExamTime >= todayRange[0] && lastExamTime < todayRange[1]) {
      if (!customer.qun_qunId) {
        if (isVip){
          statistic.todayVipNeedQunCount++
        } else {
          statistic.todayNoVipNeedQunCount++
        }
      }
    }

    const future3DayRange = [new Date(new Date().setHours(0, 0, 0, 0)), new Date(Date.now() + 3 * 24 * 60 * 60 * 1000)]
    if (lastExamTime >= future3DayRange[0] && lastExamTime < future3DayRange[1]) {
      if (!customer.qun_qunId) {
        if (isVip){
          statistic.future3DayVipNeedQunCount++
        } else {
          statistic.future3DayNoVipNeedQunCount++
        }
      }
    }

    const future7DayRange = [new Date(new Date().setHours(0, 0, 0, 0)), new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)]
    if (lastExamTime >= future7DayRange[0] && lastExamTime < future7DayRange[1]) {
      if (!customer.qun_qunId) {
        if (isVip){
          statistic.future7DayVipNeedQunCount++
        } else {
          statistic.future7DayNoVipNeedQunCount++
        }
      }
    }
  }
  return types
}

function monthStatistic(statistic, isVip, month, qunId, userCount) {
  statistic[`month${month}${isVip ? 'Vip' : 'NoVip'}SchoolCount`]++
  if (qunId) {
    statistic[`month${month}${isVip ? 'Vip' : 'NoVip'}QunCount`]++
    if (userCount === 0) {
      statistic[`month${month}${isVip ? 'Vip' : 'NoVip'}NoUserQunCount`]++
    }
  }
}

module.exports = {
  statistic
}
