const { Readable, Writable } = require('stream')
const fs = require('fs')
const { createHash } = require('crypto')

const isStream = obj => obj instanceof Readable || obj instanceof Writable
const isBuffer = obj => Buffer.isBuffer(obj)
const isString = obj => typeof obj === 'string'

function streamToBuffer (stream) {
  return new Promise((resolve, reject) => {
    const chunks = []
    stream.on('data', chunk => {
      chunks.push(chunk)
    })
    stream.on('end', () => {
      resolve(Buffer.concat(chunks))
    })
    stream.on('error', reject)
  })
}

async function resolveFile (fileName, fileContent, dir = '') {
  let fileBuffer = fileContent
  if (isString(fileContent)) {
    const fileStream = fs.createReadStream(fileContent)
    fileBuffer = await streamToBuffer(fileStream)
  } else if (isStream(fileContent)) {
    fileBuffer = await streamToBuffer(fileContent)
  } else if (isBuffer(fileContent)) {
    fileBuffer = fileContent
  }
  // 计算文件名
  const hash = createHash('md5')
  const size = fileBuffer.length / 1024
  hash.update(fileBuffer)
  const postfix = (fileName.match(/\..*$/) || [''])[0]
  const fileHash = hash.digest('hex')
  let md5FileKey = `${fileHash}${postfix}`
  if (dir) {
    md5FileKey = dir.replace(/\/?$/, '/') + md5FileKey
  }
  return {
    key: md5FileKey,
    size: size,
    buffer: fileBuffer
  }
}

module.exports = {
  streamToBuffer,
  isStream,
  isBuffer,
  isString,
  resolveFile,
}
