'use strict';

const {initMingdaoBiddingProject} = require("../../api/crm/controllers/bidding-project");
/**
 * An asynchronous bootstrap function that runs before
 * your application gets started.
 *
 * This gives you an opportunity to set up your data model,
 * run jobs, or perform some special logic.
 *
 * See more details here: https://strapi.io/documentation/developer-docs/latest/setup-deployment-guides/configurations.html#bootstrap
 */

const apiConfig = {
    prod: {
        bpm项目方案与报价审批流程: {
            id: '65eeb43a0000076f5769f5fa',
            name: '项目方案与报价审批v24.03.a',
        },
        bpm项目合同制作审批流程: {
            id: '65a3b91500000742447697e9',
            name: '项目合同制作审批v24.01.a',
        },
    },
    prodDev: {
        bpm项目方案与报价审批流程: {
            id: '65eeb43a0000076f5769f5fa',
            name: '项目方案与报价审批v24.03.a',
        },
        bpm项目合同制作审批流程: {
            id: '65a3b91500000742447697e9',
            name: '项目合同制作审批v24.01.a',
        },
    },
    test: {
        bpm项目方案与报价审批流程: {
            id: '65a4969c000007de359e6271',
            name: '项目方案与报价审批v24.01.a',
        },
        bpm项目合同制作审批流程: {
            id: '65a49a16000007de359e62d8',
            name: '项目合同制作审批v24.01.a',
        },
    },
    local: {
        bpm项目方案与报价审批流程: {
            id: '65a4969c000007de359e6271',
            name: '项目方案与报价审批v24.01.a',
        },
        bpm项目合同制作审批流程: {
            id: '65a49a16000007de359e62d8',
            name: '项目合同制作审批v24.01.a',
        },
    },
}[process.env.DATABASE || 'prod']


// 初始化默认API
async function initDefaultApi() {
    const apis = [
        {
            'id': '65a4efa83ceaef33d03573a1',
            'type': 'bpm',
            'method': 'POST',
            'title': '发起bpm项目方案与报价审批',
            'description': '',
            'inputParam': [
                {
                    'key': 'id',
                    'description': 'id',
                    'required': true
                },
                {
                    'key': 'projectName',
                    'description': 'projectName',
                    'required': true
                },
                {
                    'key': 'wxid',
                    'required': true
                },
                {
                    'key': 'isStandard',
                    'required': true
                },
                {
                    'key': 'no',
                    'required': true
                },
                {
                    'key': 'itemId',
                    'required': true
                }
            ],
            'body': `{\n     \"modelId\": \"${apiConfig.bpm项目方案与报价审批流程.id}\",\n  \"modelName\": \"${apiConfig.bpm项目方案与报价审批流程.name}\",      ` + '\"formData\": {\n    \"id\": \"${id}\",\n    \"立项编号\": \"${no}\",\n    \"项目名称\": \"${projectName}\",\n    \"伙伴云项目地址\": \"https://app.huoban.com/tables/2100000018480321/items/${itemId}\",\n  \"项目方案地址\": \" ' + `${strapi.config.server.crmUrl}` + '?no=${no}\",\n    \"是否标准价格\": \"${isStandard}\"\n  },\n  \"promoter\": {\n    \"wxid\": \"${wxid}\"\n  }\n}',
            'url': `${strapi.config.server.bossWfUrl}/api/add_instance`,
            'published_at': new Date()
        },
        {
            'id': '65a4efbb3ceaef33d03573a5',
            'type': 'bpm',
            'method': 'POST',
            'title': '发起bpm合同用章审批',
            'description': '',
            'inputParam': [
                {
                    'key': 'id',
                    'description': 'id',
                    'required': true
                },
                {
                    'key': 'projectName',
                    'description': 'projectName',
                    'required': true
                },
                {
                    'key': 'wxid',
                    'required': true
                }
            ],
            'body': '{       "id":"${id}",   "wxid": "${wxid}"     } ',
            'url': `${strapi.config.server.serverUrl}/contracts/seal-process`,
            'published_at': new Date()
        },
        {
            'id': '65e7e04579e35d1d7f114321',
            'type': 'normal',
            'method': 'POST',
            'title': '合同方案字段检查',
            'description': '',
            'inputParam': [
                {
                    'key': 'id',
                    'description': 'id',
                    'required': true
                },
                {
                    'key' : 'approve',
                    'required' : true
                }
            ],
            'body': '{     "id": "${id}"  , "approve": "${approve}"   } ',
            'url': `${strapi.config.server.serverUrl}/contracts/check-field`,
            'published_at': new Date()
        }
    ]
    for (let api of apis) {
        const apiInfo = await strapi.query('api').findOne({
            id: api.id
        })
        if (!apiInfo) {
            await strapi.query('api').create({
                ...api,
            })
        }
    }
}

// 初始化默认workflow
async function initDefaultWorkflow() {
    const workflows = [
        {
            'id': '65a4f0083ceaef33d03573ad',
            'url': '/workflows/webhook/65a4f0083ceaef33d03573ad',
            'title': '发起bpm项目方案与报价审批工作流',
            'description': '',
            'type': 'webhook',
            'model': 'contract',
            'triggerParam': [],
            'updateParam': [
                {
                    'type': 'fixed',
                    'key': 'offerReviewState',
                    'valueType': 'string',
                    'reqKey': '',
                    'value': '2'
                }
            ],
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'id'
                }
            ],
            'inputParam': [
                {
                    'key': 'id',
                    'required': true
                },
                {
                    'key': 'projectName',
                    'required': true
                },
                {
                    'key': 'wxid',
                    'userValue': 'customId'
                },
                {
                    'key': 'isStandard',
                    'required': true
                },
                {
                    'key': 'itemId',
                    'required': true
                },
                {
                    'key': 'no',
                    'required': true
                }
            ],
            'published_at': new Date(),
            'api': '65a4efa83ceaef33d03573a1'
        }, {
            'id': '65a4f05b3ceaef33d03573b2',
            'url': '/workflows/webhook/65a4f05b3ceaef33d03573b2',
            'title': '发起bpm合同用章审批工作流',
            'description': '',
            'type': 'webhook',
            'model': 'contract',
            'triggerParam': [],
            'updateParam': [],
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'id'
                }
            ],
            'inputParam': [
                {
                    'key': 'id',
                    'required': true
                },
                {
                    'key': 'projectName',
                    'required': true
                },
                {
                    'key': 'wxid',
                    'userValue': 'customId'
                }
            ],
            'published_at': new Date(),
            'api': '65a4efbb3ceaef33d03573a5'
        }, {
            'id': '65b77db66640cde37a52ed09',
            'url': '/workflows/webhook/65b77db66640cde37a52ed09',
            'title': 'bpm项目方案与报价审批流程-完善方案-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '2' : '10'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                }
            ],
            'published_at': new Date(),
            'api': null
        }, {
        }, {
            'id': '65b77e206640cde37a52ed0d',
            'url': '/workflows/webhook/65b77e206640cde37a52ed0d',
            'title': 'bpm项目方案与报价审批流程-业务决策委员会审批节点-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '3' : '4'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                }
            ],
            'published_at': new Date(),
            'api': null
        }, {
            'id': '65b77ea26640cde37a52ed11',
            'url': '/workflows/webhook/65b77ea26640cde37a52ed11',
            'title': 'bpm项目方案与报价审批流程-CEO审批节点-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '5' : '4'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                }
            ],
            'published_at': new Date(),
            'api': null
        }, {
            'id': '65e7ee964dbbed547ac1971c',
            'url': '/workflows/webhook/65e7ee964dbbed547ac1971c',
            'title': 'bpm项目方案与报价审批流程-完善合同方案节点-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '6' : '4'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                }
            ],
            'published_at': new Date(),
            'api': '65e7e04579e35d1d7f114321',
        }, {
            'id': '65e7efc44dbbed547ac19723',
            'url': '/workflows/webhook/65e7efc44dbbed547ac19723',
            'title': 'bpm项目方案与报价审批流程-核准合同价格节点-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    "value": "params.approve === 'true' ? '6' : '5'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                },
                {
                    'type': 'expression',
                    'key': 'isLocked',
                    'value': "params.approve === 'true' ? '是' : '否'"
                }
            ],
            'published_at': new Date(),
            'api': null
        }, {
            'id': '65e8109fc8c2d1206a19abe2',
            'url': '/workflows/webhook/65e8109fc8c2d1206a19abe2',
            'title': 'bpm项目方案与报价审批流程-复核合同价格-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '7' : '6'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                },
                {
                    'type': 'expression',
                    'key': 'isLocked',
                    'value': "params.approve === 'true' ? '是' : '否'"
                }
            ],
            'published_at': new Date(),
            'api': null
        }, {
            'id': '65e7f0084dbbed547ac19727',
            'url': '/workflows/webhook/65e7f0084dbbed547ac19727',
            'title': 'bpm项目方案与报价审批流程-确认合同与报价单节点-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '11' : '9'"
                },
                {
                    'key': 'offerReviewNotes',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.offerReviewNotes ? ('\\n' + modelData?.offerReviewNotes) : '') "
                }
            ],
            'published_at': new Date(),
            'api': null
        // }, {
        //     'id': '65f10b4c72adda19a62f197e',
        //     'url': '/workflows/webhook/65f10b4c72adda19a62f197e',
        //     'title': 'bpm项目方案与报价审批流程-合同制作-回调',
        //     'description': '',
        //     'model': 'contract',
        //     'queryParam': [
        //         {
        //             'key': 'id',
        //             'reqKey': 'data.id'
        //         }
        //     ],
        //     'triggerParam': [],
        //     'type': 'webhook',
        //     'updateParam': [
        //         {
        //             'type': 'expression',
        //             'key': 'offerReviewState',
        //             'value': "params.approve === 'true' ? '11' : '4'"
        //         },
        //         {
        //             'key': 'contractFileRemarks',
        //             'type': 'expression',
        //             'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.contractFileRemarks ? ('\\n' + modelData?.contractFileRemarks) : '') "
        //         }
        //     ],
        //     'published_at': new Date(),
        //     'api': '65e7e04579e35d1d7f114321',
        }, {
            'id': '65f10c9672adda19a62f198c',
            'url': '/workflows/webhook/65f10c9672adda19a62f198c',
            'title': 'bpm项目方案与报价审批流程-合同文书审批-回调',
            'description': '',
            'model': 'contract',
            'queryParam': [
                {
                    'key': 'id',
                    'reqKey': 'data.id'
                }
            ],
            'triggerParam': [],
            'type': 'webhook',
            'updateParam': [
                {
                    'type': 'expression',
                    'key': 'offerReviewState',
                    'value': "params.approve === 'true' ? '12' : '6'"
                },
                {
                    'key': 'contractFileRemarks',
                    'type': 'expression',
                    'value': "'【' + moment().format('YYMMDD') + '】' + params.assigneeName + '：' + (params.approve === 'true' ? '通过' : '打回')+ (params?.remark ? ('，'+ params?.remark) : '') + ( modelData?.contractFileRemarks ? ('\\n' + modelData?.contractFileRemarks) : '') "
                }
            ],
            'published_at': new Date(),
            'api': null
        }
    ]
    for (let workflow of workflows) {
        const workflowInfo = await strapi.query('workflow').findOne({
            id: workflow.id
        })
        if (!workflowInfo) {
            await strapi.query('workflow').create({
                ...workflow,
            })
        }
    }
}
//初始化招投标项目配置
async function initProjectConfig(params) {
  const exists = await strapi.query('bidding-project-config').findOne({  })
  if (exists) return
  else{
    const configs=[{
      configid: "jianyu-account",
      name: "剑鱼账号",
      value: "***********",
    },{
      configid: "jianyu-password",
    name: "剑鱼密码",
    value: "",
    },{
      configid: "jianyu-cookie",
    name: "剑鱼cookie",
    value: "",
    },{
      configid: "jianyu-keywords",
    name: "剑鱼爬取关键词",
    value: "阅卷,精准教学,统考,质量监测,学业水平监测,考试系统,新高考,选课,走班,排课,生涯规划,选科,考试诊断分析,智慧教育,智慧校园,智慧课堂,联考,教学质量,题库,资源库,课件",
    },{
      configid: "spider-heartbeat",
    name: "爬虫数据同步上次心跳时间",
    value: "2024-12-31 00:00:00",
    }]
    for (let config of configs) {
      await strapi.query('bidding-project-config').create({...config})
    }
  }
}

async function initBiddingProjectS1(){
  await initMingdaoBiddingProject()
}

module.exports = async () => {
    console.log('bootstrap init Default...')
    console.time('bootstrap init Default...')
    await initDefaultApi()
    await initDefaultWorkflow()
    await initProjectConfig()
    initBiddingProjectS1().then((res) => {
      console.log("Init BiddingProjectS1 success")
    }).catch((err) => {
      console.error("Init BiddingProjectS1 error", err)
    })
    console.timeEnd('bootstrap init Default...')
}
