{"collectionName": "api", "info": {"name": "Api", "label": "api", "description": "api库"}, "options": {"draftAndPublish": true, "timestamps": true}, "pluginOptions": {}, "attributes": {"title": {"label": "接口", "type": "string"}, "description": {"label": "描述", "type": "text"}, "type": {"label": "类型", "type": "string", "default": "normal", "options": [{"value": "normal", "label": "普通"}, {"value": "bpm", "label": "bpm流程"}]}, "inputParam": {"label": "输入参数", "type": "json", "jsonSchema": {"title": "参数对象列表", "type": "array", "items": {"title": "参数对象", "type": "object", "properties": {"key": {"title": "key", "type": "string"}, "default": {"title": "默认值", "type": "string"}, "required": {"title": "是否必传", "type": "boolean", "default": false, "options": [{"value": false, "label": "否"}, {"value": true, "label": "是"}]}, "description": {"title": "描述", "type": "string"}}}}}, "headers": {"title": "headers对象", "type": "text"}, "query": {"title": "query对象", "type": "text"}, "body": {"label": "body对象", "type": "text"}, "url": {"label": "url", "type": "string"}, "method": {"label": "方法", "type": "enumeration", "enum": ["GET", "PUT", "PATCH", "POST", "DELETE"]}, "response": {"label": "返回值", "type": "json"}}}