const { createDefaultRoutes } = require('accel-utils')

const systemRequestLogRoutes = createDefaultRoutes({
  controller: 'system-request-log',
  basePath: '/system-request-logs',
  config: {
    'policies': [], 'prefix': '',
  },
})

const systemErrorLogRoutes = createDefaultRoutes({
  controller: 'system-error-log',
  basePath: '/system-error-logs',
  config: {
    'policies': [], 'prefix': '',
  },
})

module.exports = {
  'routes': [
    ...systemRequestLogRoutes,
    ...systemErrorLogRoutes,
  ]
}
