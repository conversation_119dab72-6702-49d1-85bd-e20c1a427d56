// 应用
import { MingdaoDateRangeEnum, MingdaoFilter } from './mingdao.api'

interface MingdaoApp {
  projectId: string;     // 网络ID？
  appId: string;         // 应用ID
  name: string;          // 应用名称
  iconUrl: string;       // 图标地址
  color: string;         // 图标颜色
  desc: string;          // 应用描述
  sections: MingdaoSection[];   // 应用分组
  createType: 0 | 1;     // 0 -普通？
}

// 模块/子模块
interface MingdaoSection {
  sectionId: string;    // 应用分组 Id
  name: string;         // 分组名称
  items: MingdaoSectionItem[];        // 分组下应用项
  childSections: MingdaoSection[]; // 子分组
}

// 菜单项
interface MingdaoSectionItem {
  id: string;           // 分组下应用项ID
                        // type=1 则ID为工作表ID
                        // type=2 则ID为页面ID
  name: string;         // 应用项名称
  type: number;         // 应用项类型 0:工作表、1:自定义页面、2：子分组
  iconUrl: string;      // 应用项图标地址
  status: number;       // 状态码？
  alias: string;        // 工作表别名
  notes: string;        // 开发者备注
}

// 工作表
interface MingdaoWorksheetData {
  worksheetId: string;   // 工作表ID
  name: string;          // 工作表名称

  views: MingdaoWorksheetView[];  // 视图数组
  controls: MingdaoControl[];     // 控件数组
  template: {
    sourceId: string,       // 数据源ID（与工作表ID相同）
    worksheetId: string,    // 工作表ID
    projectId: string,      // 项目ID
    version: number,        // 版本
    controls: MingdaoControl[]
  },

  count: number,        // 记录数量
  alias: string,        // 别名
  desc: string,         // 描述
  projectId: string,    // 项目ID
  entityName: string,   // 实体名称 default='记录'

  // 业务规则
  rules: {
    ruleId: string,
    name: string,
    checkType: 0 | 1,
    controlIds: string[],
    disabled: boolean,
    // 满足条件组
    filters: MingdaoFilter[],
    hintType: 0 | 1,
    // 规则动作组
    ruleItems: {
      isAll: boolean,   // 影响全部字段
      message: string,  // 提示消息
      // 动作类型
      // 1.显示 2.隐藏 3.可编辑 4.只读 5.必填 6.只读所有字段
      type: 1 | 2 | 3 | 4 | 5 | 6,
      // 影响字段
      controls: {
        childControlIds: string[],
        controlId: string
      }[]
    }[]
  }[]
}

// 工作表视图
interface MingdaoWorksheetView {
  viewId: string;  // 视图ID
  // 0:表格 1：看板 2：层级 3：画廊 4：日历 5：甘特图 6：详情
  viewType: 0 | 1 | 2 | 3 | 4 | 5 | 6, // 视图类型

  name: string;               // 视图名称
  controls: string[],         // 隐藏字段列表（过滤需要隐藏字段）
  displayControls: string[],  // 显示字段列表（卡片）

  filters: MingdaoFilter[],   // 筛选项

  viewControl: string,    // 视图维度ID(分组ID) - 例如看板视图类型分组
  sortType: 0 | 1,        // 视图主字段排序 0.倒序 1.正序

  viewControls: string[], // 列表主字段外展示字段ID列表（多表层级视图控件）

  controlsSorts: string[],    // 可排序字段
  moreSort: MingdaoFilter[],  // 排序规则

  sortCid: string,        // 默认排序字段 - 可为空，疑似使用 viewControl 替代
  fastFilters: any[],     // ?
  showControls: any[],    // ?

  advancedSetting: {
    title?: string,         // 表单自定义标题
    sectionshow?: string,   // 分段展示风格
    tabposition?: string,   // 标签组位置
    deftabname?: string,    // 默认标签组名称
  }
}

// 字段控件配置
interface MingdaoControlOption {
  value: string;   // 当type=11/10时,表示选项名称
  index: string;   // 排序
}

// 控件（字段）
interface MingdaoControl {
  // 基础属性
  controlId: string;              // 控件ID
  controlName: string;            // 控件名称
  type: number;                   // 控件类型，参考枚举 DataTypeEnum

  attribute: string;              // 属性 1：标题
  row: number;                    // 行号
  col: number;                    // 列号
  size: number;                   // 尺寸
  default: string;                // 默认值
  hint: string;                   // 引导文字
  desc: string;                   // 字段描述
  remark: string;                 // 字段备注

  dot?: string;                   // 当type=6时，表示保留小数位（0-14）
  unit?: string;                  // 单位，当type=46时，1：时分，6：时分秒
  enumDefault?: string;           // 1单选, 2多选
  enumDefault2?: string;          // 其他枚举默认值
  defaultMen: string[];           // 默认选中的人员

  /* 关联类型字段配置 - 关联记录、子表... */
  dataSource: string;             // 关联表ID
  sourceControlId: string;        // 关联控件ID [关联表] [双向关联:对应表字段情况下]
  sourceControlType: number;      // 关联他表字段控件类型
  sourceTitleControlId: string;   // 关联表标题控件ID
  sourceEntityName: string,       // 关联表实体名称 eg.'记录'
  showControls: string[];         // 卡片展示字段(Web显示字段列表)

  noticeItem?: string;            // 当type=26时，通知项0：不通知 1：添加通知
  userPermission?: string;        // 当type=26时，权限 0：仅录入 1：成员  2：拥有者

  options?: MingdaoControlOption[];      // 控件选项

  required: string;               // true：必填,false：非必填
  half: boolean;                  // 是否占用半格
  relationControls: string[];     // 关联控件
  viewId: string;                 // 视图Id
  controlPermissions: string;     // 控件权限 "111" ？

  unique: boolean;                // 是否唯一
  coverCid: string;               // 封面控件id
  strDefault?: string;            // 通用string字段,参考说明

  alias: string;                  // 别名（API用）

  fieldPermission: string;        // 字段权限默认 "111"
  // 三位数值: 隐藏[0]、只读[0]、新增记录时隐藏[0]
  // 0.选项勾选  1.选项取消

  sectionId: string,              // 标签页控件ID 默认 "",

  advancedSetting: {
    icon: string,

    // 样式
    titlecolor: string,
    titlestyle: string,
    titlesize: string,
    // 过滤器
    filters: MingdaoFilter[],
    // 默认值
    defsource: {
      cid: string,          // 来源字段ID eg.[图书.分类]
      rcid: string,         // 主记录ID ?
      staticValue: string,  // 静态默认值 - 例如子表默认值
      isAsync: boolean,     // ?
      type: number          // ?
    }[],

    // 关联记录
    allowdelete: '0' | '1',     // 关联记录(表格) 允许删除记录
    allowexport: '0' | '1',     // 关联记录(表格) 允许导出
                                // 子表 允许导出
    allowedit: '0' | '1',       // 子表 可编辑已有明细
    allowbatch: '0' | '1',      // 关联记录(表格) 允许批量操作
    allowlink: '0' | '1',       // 关联记录(表格) 允许打开记录
    sheettype: '0' | '1',       // 关联记录(表格) 表格交互方式 0.经典模式 1.电子表格模式
    showquick: '0' | '1',       // 关联记录(表格) 显示记录快捷方式
    // 子表配置
    allowadd: '0' | '1',        // 关联记录 允许新增记录
                                // 子表 允许新增明细 - 开启可配置允许新增方式 allowsingle batchcids
                                // 单选（下拉） 允许用户增加选项
                                // 多选（下拉） 允许用户增加选项
    allowcancel: '0' | '1',     // 关联记录(表格&卡片) 允许取消关联
                                // 子表 可删除已有明细(取消关联&删除记录)
    allowsingle: '0' | '1',     // 子表 单行新增
    batchcids: string,          // 子表 选择关联记录字段新增（基于指定批量选中） eg. "[\"6715c02bae4fd9d1ba4f1bbd\"]"
    alternatecolor: '0' | '1',  // 关联记录(表格) 交替显示行颜色
    hidenumber: '0' | '1',      // 子表 显示序号（隐藏）

    rowheight: '0' | '1' | '2' | '3',   // 关联记录(表格) 行高
                                        // 子表 行高

    enablelimit: '0' | '1',     // 子表 限制添加行数
    min: string,                // 子表 限制最小行数 eg. '2'
    max: string,                // 子表 限制最大行数 eg. '5'
    rownum: string,             // 子表 最大高度（每页行数） eg. '15'

    uniqueControlIds: string[], // 不允许重复字段列表
    searchrange: '0' | '1',     // 启用查询设置
    searchcontrol: string,      // 搜索字段 eg. '66d911343e5e32c422860109'
    searchfilters: string,      // 筛选字段列表  eg. "[{\"controlId\":\"66d911343e5e32c422860109\"},{\"controlId\":\"66d911343e5e32c42286010b\"}]"
    searchtype: '0' | '1',      // 搜索方式 0.模糊搜索 1.精确搜索

    // 控件展示配置
    // 子表展示配置 '1':滚动 '2':翻页
    // 关联记录展示配置 '1':卡片 '3':下拉框 '5':表格 '6':标签页表格
    // 单选展示配置 '0':下拉菜单 '1':平铺 '2':进度
    showtype: '0' | '1' | '2' | '3' | '4' | '5' | '6',
    // 多选选项配置
    // '0' - 平铺
    // '1' - 下拉菜单
    checktype: '0' | '1',

    // 多选 - 选项排列方式
    // '0' - 矩阵排列
    // '1' - 纵向排列
    // '2' - 横向排列
    direction: '0' | '1' | '2',
  }
}
