// const triggerCurdRouter = require('../utils/triggerCurdRouter')
// const curdRouter = new triggerCurdRouter('contract')
const { CurdRouter } = require('accel-utils')
// const curdRouter = new CurdRouter('contract')
const axios = require('axios');
const _ = require('lodash');
const jwt = require('jsonwebtoken');
const moment = require('moment');
const { ObjectId } = require('mongodb');
const huoban = require('../utils/huoban');
const huobanConfig = strapi.config.server.huoban;
const { getOrgUser } = require('../services/contract');

const curdRouter = new (class extends CurdRouter {
    // ...
    async _getQueryByUser(query, user) {
        // 新经理为本人 没有运营组或没有直营组 的学校
        let roles = user.roles.map(e => e.type);
        let zxRole = 'zx-cloud-member'
        let allowRoles = ['admin', 'SuperAdmin', 'bossSalesManagement', 'business-owner']
        if (_.intersection(roles, allowRoles.concat([zxRole])).length === 0) {
            const allUsers = await getOrgUser();
            query.partASalesWxId_in = allUsers?.[user.customId]?.users || []
        }
        // 中校云只能查看中校云的数据
        if (_.intersection(roles, [zxRole]).length > 0) {
            query.amoeba_eq = '集团-中校云'
        }
        return query
    }

    async count(ctx) {
        const user = ctx.state.user
        let { query } = this._parseCtx(ctx)
        query = await this._getQueryByUser(query, user)
        return super.count(ctx)
    }

    async find(ctx) {
        const user = ctx.state.user
        let { query } = this._parseCtx(ctx)
        query = await this._getQueryByUser(query, user)
        return super.find(ctx)
    }

    async update(ctx) {
        const { data } = this._parseCtx(ctx)
        const oldContract = await strapi.query('contract').findOne({ id: data.id })
        if (data.offerReviewState && oldContract.offerReviewState !== data.offerReviewState) {
            return ctx.wrapper.error('HANDLE_ERROR', '源数据已更新，请刷新后重新编辑')
        }
        return super.update(ctx)
    }
})('contract')

async function importCrmById(ctx) {
    try {
        const { id } = ctx.params;
        const crmContractId = await _importCrmById(ctx, id);
        return {
            code: 0,
            msg: 'OK',
            data: { crmContractId }
        }
    } catch (e) {
        console.log(e);
        return ctx.badRequest(e.message || e)
    }
}

async function _importCrmById(ctx, id) {
    const schoolTypeEnumsTransfer = {
        '普通': 'school',
        '教研室': 'jiaoyanshi',
        '联盟': 'lianmeng',
        '集团校': 'jituanxiao',
        '联考': 'liankao',
        '教育局': 'jiaoyuju',
        '培训机构': 'peixunjigou',
        '中心校': 'zhongxinxiao',
    }

    const contract = await strapi.query('contract').findOne({ id: id });
    if (contract.crmContractId) return contract.crmContractId
    if (contract.offerReviewState !== '12') return ctx.badData('合同状态必须是「合同审批完毕」')
    // if (!['8', '11', '12'].includes(contract.offerReviewState)) return ctx.badData('合同审批状态异常')
    if (!contract.amount && contract.amount !== 0) return ctx.badData('合同金额未填写')
    if (!contract.beginTime) return ctx.badData('开始时间未填写')
    if (!contract.endTime) return ctx.badData('结束时间未填写')
    if (!contract.template) return ctx.badData('合同模版名称未填写')
    if (!contract.installments) return ctx.badData('回款计划未填写')
    for (const installment of contract.installments) {
        if (!installment.planTime) return ctx.badData('回款计划时间未填写')
    }

    let partB = {};
    if (contract.cooperation === '直营') {
        if (contract.schools && contract.schools.length > 0) {
            partB = {
                'type': schoolTypeEnumsTransfer[contract?.schools?.[0]?.type],
                'id': contract?.schools?.[0]?.id,
                'user_id': '',
                'name': contract?.schools?.[0]?.name,
            }
        }
    } else {
        if (!contract?.agent?.id) {
            if (contract.schools && contract.schools.length > 0) {
                partB = {
                    'type': schoolTypeEnumsTransfer[contract?.schools?.[0]?.type],
                    'id': contract?.schools?.[0]?.id,
                    'user_id': '',
                    'name': contract?.schools?.[0]?.name,
                }
            } else {
                return ctx.badData('合同经销商未找到')
            }
        } else {
            partB = {
                'type': 'agent',
                'id': contract?.agent?.id,
                'user_id': contract?.agent?.userId,
                'name': contract?.agent?.company,
                'corporation': {
                    'name': contract?.partBCorporation,
                    'phone': contract?.partBPhone,
                }
            }
        }
    }

    let targets = contract?.schools?.map(e => {
        return {
            'type': schoolTypeEnumsTransfer[contract?.schools?.[0]?.type],
            'id': e.id,
            'user_id': '',
            'name': e.name
        }
    }) || [];

    let userResponse = await axios.post(`${strapi.config.server.bossApi.url}/api/user/get_user_by_wxid`, {
        wxid: contract.partASalesWxId,
    });
    let salesInfo;
    if (userResponse.status === 200) {
        salesInfo = userResponse.data;
    }
    if (!salesInfo) return ctx.badData('该公司业务经理crm中未找到')

    let attachments = contract?.attachments?.map(e => {
        return {
            name: e.name,
            src: e.url,
            size: _.floor(e.size * 1024),
        }
    })
    let importData = {
        'contract': {
            // 'id': '',                    // 合同Id（非必填），添加时不需要，更新时需要
            'category': '1',
            status: 1,
            'no': contract.no,
            'type': contract.template,
            'count': contract.count,
            'project_name': contract.projectName,
            'part_a_name': contract.partAName,
            'part_b': partB,
            'application_targets': targets,
            'products': [],
            'amount': parseFloat(contract.amount / 100).toFixed(2),
            'margin': contract.margin ? parseFloat(contract.margin / 100).toFixed(2) : 0,
            'sign_address': contract.signAddress,
            'sign_time': moment(contract.signTime).format('YYYY-MM-DD'),
            'begin_time': moment(contract.beginTime).format('YYYY-MM-DD'),
            'end_time': moment(contract.endTime).format('YYYY-MM-DD'),
            // 上传
            'attachments': attachments,
            'terms': contract.terms,
            'remark': contract.remark,
            'is_sealed': contract.isSealed,
            // 销售经理
            'sales': {
                'user_id': salesInfo?.id,
                'name': salesInfo?.name,
                'zq': '',
                'tuan': ''
            },
            'workflow_id': '',
            'billing': {
                'category': contract.billingCategory,
                'status': 1
            },
            'performances': [],

        },
        installments: contract?.installments?.map(e => {
            return {
                plan_time: e.planTime,
                plan_amount: parseFloat(e.planAmount / 100).toFixed(2),
                remark: e.remark,
            }
        })

    }
    // if (contract.crmContractId) importData.contract.id = contract.crmContractId;
    let response = await axios.post(`${strapi.config.server.bossApi.url}/external/api/to_wly/contract/import`, importData, {
        headers: {
            'apikey': `${strapi.config.server.bossApi.apikey}`
        }
    });
    if (response.status === 200 && response.data.code === 1) {
        instanceData = response.data.data;
    } else {
        console.log(response)
        throw new Error(response.data)
    }
    if (!contract.crmContractId) {
        await strapi.query('contract').update({
            _id: contract._id,
        }, {
            crmContractId: instanceData?.contractId,
        });

    }
    return instanceData?.contractId;
}

async function loginByBossToken(ctx) {
    // const { token } = ctx.request.body;
    // if (!token) {
    //     return ctx.wrapper.error('AUTH_ERROR', '无可用 boss token')
    // }
    let token = ctx.cookies.get('BOSSID');
    if (!token) {
        return ctx.wrapper.error('AUTH_ERROR', '无可用 boss token')
    }

    let cookieData = jwt.verify(token, strapi.config.server.bossSecret);

    let response = await axios.post(`${strapi.config.server.bossApi.url}/user/get_user_by_id`, {
        id: cookieData.login_user_id,
        fields: ['weixin_id', 'name']
    });

    // 获取wxid
    let instanceData;
    if (response.status === 200) {
        instanceData = response.data;
    } else {
        return ctx.wrapper.error('AUTH_ERROR', 'boss token 错误')
    }

    const iPlugin = strapi.plugins['users-permissions']
    const bossRole = await strapi.query('role', 'users-permissions').findOne({ type: 'boss' })
    const branch = await iPlugin.services['branch'].getCtxDefaultBranch(ctx)
    let user = await iPlugin.services['user'].findOne({ customId: instanceData.weixin_id })

    // 用户不存在，先创建一个
    if (!user) {
        user = await iPlugin.services['user'].createNewUser(ctx, {
            id: cookieData.user_id,
            username: instanceData.name,
            account: cookieData.employee_no,
            pBranch: branch.id,
            customId: instanceData.weixin_id,
            provider: 'yxWeCom',
            role: bossRole.id
        }, null)
        // console.log(`用户同步完成`)
    }
    return ctx.wrapper.succ(iPlugin.services['user'].getFullAuthData(user))
}

async function initHuobanJson(ctx) {
    await huoban.initHuobanJson();

    return ctx.wrapper.succ({});
}

async function checkContractFieldByBoss(ctx) {
    try {
        const { id, approve } = ctx.request.body;
        const contract = await strapi.query('contract').findOne({ id: id });
        if (!contract) return ctx.badData('PARAMETERS_ERROR', 'contract ID 错误')
        if (approve == 'false') return ctx.wrapper.succ({});

        if (contract.offerReviewState === '5') {
            let msg = '请完整填写信息后再通过流程：1. 合同金额、回款计划、开始日期、结束日期为必填字段。2. 对于每个选择打开的合同模块，其内容必须填写完整。';
            if (contract.isApplicationTarget && _.isEmpty(contract.applicationTarget)) return ctx.badData(msg)
            if (contract.isApplicationTarget && !_.isEmpty(contract.applicationTarget) && contract.applicationTarget.find(e => !e.school || !e.goods)) return ctx.badData(msg)
            if (contract.isQuan && _.isEmpty(contract.quan)) return ctx.badData(msg)
            if (contract.isQuan && !_.isEmpty(contract.quan) && contract.quan.find(e => !e.quanGoods)) return ctx.badData(msg)
            if (contract.isRightsSchool && _.isEmpty(contract.rightsSchool)) return ctx.badData(msg)
            if (contract.isRightsSchool && !_.isEmpty(contract.rightsSchool) && contract.rightsSchool.find(e => !e.schools || !e.agentType)) return ctx.badData(msg)
            if (contract.isRights && _.isEmpty(contract.rights)) return ctx.badData(msg)
            if (contract.isExemptSchool && _.isEmpty(contract.exemptSchool)) return ctx.badData(msg)
            if (contract.isExemptSchool && !_.isEmpty(contract.exemptSchool) && contract.exemptSchool.find(e => !e.sid)) return ctx.badData(msg)
            if (contract.isExpansion && _.isEmpty(contract.expansion)) return ctx.badData(msg)
            if (contract.isMargin && !contract.margin) return ctx.badData(msg)
            if (!contract.amount && contract.amount !== 0) return ctx.badData(msg)
            if (!contract.installments) return ctx.badData(msg)
            if (!contract.beginTime) return ctx.badData(msg)
            if (!contract.endTime) return ctx.badData(msg)
        }
        if (contract.offerReviewState === '6') {
            let msg = '请完整填写信息后再通过流程：1. 合同金额、回款计划、开始日期、结束日期为必填字段。2. 对于每个选择打开的合同模块，其内容必须填写完整。';
            if (contract.isApplicationTarget && _.isEmpty(contract.applicationTarget)) return ctx.badData(msg)
            if (contract.isApplicationTarget && !_.isEmpty(contract.applicationTarget) && contract.applicationTarget.find(e => !e.school || !e.goods)) return ctx.badData(msg)
            if (contract.isQuan && _.isEmpty(contract.quan)) return ctx.badData(msg)
            if (contract.isQuan && !_.isEmpty(contract.quan) && contract.quan.find(e => !e.quanGoods)) return ctx.badData(msg)
            if (contract.isRightsSchool && _.isEmpty(contract.rightsSchool)) return ctx.badData(msg)
            if (contract.isRightsSchool && !_.isEmpty(contract.rightsSchool) && contract.rightsSchool.find(e => !e.schools || !e.agentType)) return ctx.badData(msg)
            if (contract.isRights && _.isEmpty(contract.rights)) return ctx.badData(msg)
            if (contract.isExemptSchool && _.isEmpty(contract.exemptSchool)) return ctx.badData(msg)
            if (contract.isExemptSchool && !_.isEmpty(contract.exemptSchool) && contract.exemptSchool.find(e => !e.sid)) return ctx.badData(msg)
            if (contract.isExpansion && _.isEmpty(contract.expansion)) return ctx.badData(msg)
            if (contract.isMargin && !contract.margin) return ctx.badData(msg)
            if (!contract.amount && contract.amount !== 0) return ctx.badData(msg)
            if (!contract.installments) return ctx.badData(msg)
            if (!contract.beginTime) return ctx.badData(msg)
            if (!contract.endTime) return ctx.badData(msg)
            if (contract.attachments.length === 0) return ctx.badData('需要提交合同附件')
        }

        return ctx.wrapper.succ({});
    } catch (e) {
        return ctx.badRequest(e.message)
    }
}

async function contractSealProcessToBoss(ctx) {
    try {
        let { id, wxid } = ctx.request.body;
        const contract = await strapi.query('contract').findOne({ id: id });
        if (!contract) return ctx.badData('PARAMETERS_ERROR', 'contract ID 错误')
        if (contract.offerReviewState !== '12') return ctx.badData('合同状态必须是「合同审批完毕」')
        const schoolTypeEnumsTransfer = {
            'agent': '经销商',
            'school': '普通',
            'jiaoyanshi': '教研室',
            'lianmeng': '联盟',
            'jituanxiao': '集团校',
            'liankao': '联考',
            'jiaoyuju': '教育局',
            'peixunjigou': '培训机构',
            'zhongxinxiao': '中心校',
        }

        let details = { 用章文件: [] };
        contract.attachments.forEach(e => {
            details['用章文件'].push({
                '文件名称': e.name,
                '附件': {
                    attachments: [{
                        url: e.url,
                        name: e.name
                    }]
                },
            })
        });
        let response = await axios.post(`${strapi.config.server.bossWfUrl}/api/add_instance`, {
            'modelName': '项目合同用章流程v24.03.a',
            // 'modelName': '合同用章流程v24.03.a',
            'formData': {
                'id': contract.id,
                '立项编号': contract.no,
                '项目名称': contract.projectName,
                '伙伴云项目地址': `https://app.huoban.com/tables/2100000018480321/items/${contract.itemId}`,
                '项目方案地址': `${strapi.config.server.crmUrl}/wly/contract?no=${contract.no}`,
                '项目经理': contract.partASalesName,
                '我方公司主体': contract.partAName,
                '签约对象类型': schoolTypeEnumsTransfer[contract.partBType],
                '签约对象名称': contract.partBName,
                '合同份数': contract.count,
                'details': !_.isEmpty(details?.用章文件) ? details : {},
                '是否需要邮寄': contract.needMailing ? '需要' : '不需要',
                '合同收件人姓名': contract.mailingName,
                '合同收件人电话': contract.mailingPhone,
                '盖章邮寄备注': contract.mailingRemark,
                '合同收件地址': contract.mailingAddress
            },
            'promoter': {
                'wxid': wxid
            }

        });
        let instanceData;
        if (response.status === 200) {
            instanceData = response.data;
        }

        // const processRecord = await strapi.query('process-record').create({
        //     "bossId": instanceData.id,
        //     "model": 'contract',
        //     "modelId": instanceData.id,
        //     "api": 'workflow.api',
        //     "workflow": 'id'
        // });
        // await strapi.query('contract').model.updateOne({ _id: ObjectId(modelData.id) }, { $push: { 'processRecord': processRecord._id } });

        return instanceData
    } catch (e) {
        console.log(e);
        return ctx.badRequest(e.message || e)
    }
}

async function deleteProcessToBoss(ctx) {
    try {
        const { id } = ctx.params;
        const { processId } = ctx.request.body;
        const contract = await strapi.query('contract').findOne({ id: id });
        if (!contract) return ctx.badData('PARAMETERS_ERROR', 'contract ID 错误')
        if (!contract?.processRecord?.find(e => e.id === processId)) return ctx.badData('PARAMETERS_ERROR', 'process ID 错误')
        const bossProcessId = contract?.processRecord?.find(e => e.id === processId).bossId;
        const processRecord = (contract?.processRecord?.filter(e => e.id !== processId)).map(e => e._id);

        await strapi.query('contract').update({
            _id: contract._id,
        }, {
            processRecord: processRecord,
        });

        const response = await axios.post(`${strapi.config.server.bossWfUrl}/api/instance/cancel`, {
            'instanceId': bossProcessId,
        });
        let instanceData;
        if (response.status === 200) {
            instanceData = response.data;
        }

        return ctx.wrapper.succ({});
    } catch (e) {
        if (e?.response?.data?.error === '流程已完成，无法取消') return ctx.wrapper.succ({});
        console.log(e);
        return ctx.badRequest(e.message || e)
    }
}

module.exports = {
    initHuobanJson,
    importCrmById,
    contractSealProcessToBoss,
    loginByBossToken,
    checkContractFieldByBoss,
    deleteProcessToBoss,
    ...curdRouter.createHandlers(),
}

