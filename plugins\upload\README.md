# 上传与文件管理插件

提供上传与文件管理相关功能

## 百度云对象存储配置

插件配置文件位置 `/config/plugins.js`

### 配置说明
```typescript
interface UploadConfig {
  objectStorage: {
    // 对象存储类型 bos-百度云 cos-腾讯云
    target: 'bos' | 'cos'
    // 根路径 默认: 'test/'
    baseDir?: string,
    // 默认上传根路径
    // 默认值: 'upload/'
    uploadPath?: string,
    // 富文本上传根路径
    // 默认值: 'upload/rich-text/'
    richTextUploadPath?: string,
    // 对象存储配置 参考下方配置示例
    config:
      { // 百度云对象存储（bos）配置
        AK: string,
        SK: string,
        Bucket: string,
        Endpoint: string
      }
      |
      { // 腾讯云对象存储（cos）配置
        SecretId: string,
        SecretKey: string,
        Bucket: string,
        Region: string,
        Proxy?: string,
        // 有效期
        DurationSeconds?: number,
        AllowPrefix?: string[],
        AllowActions?: string[],
      }
  }
}

```

### 百度云配置示例
```js

module.exports = ({ env }) => ({
  // ...
  upload: {
    objectStorage: {
      target: 'bos',
      config: {
        // Bos Config
        AK: '',
        SK: '',
        Bucket: 'ayx-pk',
        Endpoint: 'https://ayx-pk.bj.bcebos.com'
      },
    },
  }
})
```

### 腾讯云配置示例
```js
module.exports = ({ env }) => ({
  // ...
  upload: {
    objectStorage: {
      target: 'cos',
      config: {
        // COS Config
        SecretId: '',
        SecretKey: '',
        Bucket: '',
        Region: '',
        Proxy: '',
        // 有效期
        DurationSeconds: 1800,
        AllowPrefix: '*',
        AllowActions: ['*'],
      },
    },
  }
})
```
