{"collectionName": "agent-payment", "info": {"name": "agentPayment", "label": "经销商付款", "description": "经销商付款"}, "options": {"draftAndPublish": false, "timestamps": false}, "pluginOptions": {}, "attributes": {"typeDesc": {"label": "充值类型", "type": "string", "required": true, "placeholder": "请输入充值类型, 该项用户扫码可见，如：BOSS账户充值"}, "agent": {"label": "经销商", "model": "boss-agent", "required": true}, "merchant": {"label": "收款商户", "type": "string", "size": 6, "required": true}, "merchantId": {"label": "商户号", "type": "string", "size": 6, "editable": false, "required": true}, "amount": {"label": "充值面额", "format": "rmb", "type": "number", "required": true}, "remark": {"label": "备注", "type": "string", "required": true, "placeholder": "请输入订单备注，用户扫码不可见，仅在订单显示"}, "qrCode": {"label": "付款二维码", "type": "string", "required": true, "editable": false}, "recordId": {"label": "付款记录", "model": "third-party-payment", "mainField": "status", "required": true, "editable": false, "visible": false}}}