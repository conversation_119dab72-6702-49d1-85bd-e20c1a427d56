const { CurdRouter } = require('accel-utils')
const axios = require("axios")
const { ObjectId } = require('mongodb');

// 缓存有效时长
const cacheExpirationTime = 60 * 60 * 1000
// 缓存失效时间
let cacheExpiration = null
// 缓存数据
let wxPayConfigList = []

const curdRouter = new CurdRouter('agent-payment')

async function getWxPayConfigList(ctx) {
  const list = await _getWxPayConfigList();

  return ctx.wrapper.succ(list.map(item => {
    return {
      merchantId: item.merchantId,
      merchantName: item.merchantName,
    }
  }));
}

async function _getWxPayConfigList() {
  if (wxPayConfigList && wxPayConfigList.length > 0 && cacheExpiration > Date.now()) {
    return wxPayConfigList;
  }
  cacheExpiration = Date.now() + cacheExpirationTime;
  let res = await axios.get(`${strapi.config.server.wlyConfig.url}/pay-wechat/publicFind`, {
    headers: {
      'access-key': strapi.config.server.wlyConfig.accessKey
    }
  })
  const wechatPayConfigList = res.data
  if (wechatPayConfigList && wechatPayConfigList.length > 0) {
    wxPayConfigList = wechatPayConfigList
  }
  return wxPayConfigList;
}

async function payNative(ctx) {
  return ctx.wrapper.succ({
    payUrl: 'weixin://wxpay/bizpayurl?pr=0PojE3Zzz',
    recordId: ObjectId().toString()
  })
}

async function forwardBossApi(ctx){
  let url = ctx.request.query.url;
  let cookie = ctx.request.query.cookie;
  let config = {}
  if (cookie) {
    config['headers'] = {
      'cookie': cookie
    }
  }
  console.log(url)
  const res = await axios.get(url, config)
  return ctx.wrapper.send(res.data)
}

module.exports = {
  _getWxPayConfigList,
  getWxPayConfigList,
  payNative,
  forwardBossApi,
  ...curdRouter.createHandlers()
}
