const { createDefaultRoutes } = require('accel-utils')
const fileRoutes = createDefaultRoutes({
  controller: 'file',
  basePath: '/files',
  config: {
    'policies': [], 'prefix': '',
  },
})

module.exports = {
  'routes': [
    // 文件管理接口
    ...fileRoutes,
    // 普通文件上传
    {
      'method': 'POST',
      'path': '/external/file',
      'handler': 'storage.upload',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 富文本文件上传
    {
      'method': 'POST',
      'path': '/external/rich-text-file',
      'handler': 'storage.richTextUpload',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 普通文件上传到本地文件系统
    {
      'method': 'POST',
      'path': '/external/local/file',
      'handler': 'storage.uploadToLocal',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 富文本文件上传到本地文件系统
    {
      'method': 'POST',
      'path': '/external/local/rich-text-file',
      'handler': 'storage.richTextUploadToLocal',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 浏览器直接对接对象存储上传
    {
      'method': 'GET',
      'path': '/cos/sts',
      'handler': 'storage.getSts',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
    // 上传到 File 实例
    {
      'method': 'POST',
      'path': '/upload/create',
      'handler': 'upload.create',
      'config': {
        'policies': [], 'prefix': '',
      }
    },
  ]
}
