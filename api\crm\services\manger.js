const _ = require('lodash')
const axios = require('axios')
const { getToken } = require('../../xiaoyun/utils/goToken')
async function syncBusinessUser (ctx, departments = ['新项目联盟', '综合联盟', '新业务部']) {
    let token = await getToken('org')
    const allList = await axios.get(`http://org.yunxiao.io/node/list_all?__go_token=${token}`).then(res => res.data)

    let customIds = []
    for (const departmentName in allList?.['爱云校']?.[2]) {
        const userMap = _getOrgUser(allList?.['爱云校']?.[2][departmentName])
        if (departments.includes(departmentName)) {
            customIds = customIds.concat(Object.keys(userMap) || [])
        }
    }
    let role = await strapi.query('role', 'users-permissions').findOne({ type: 'business-member' }, [])
    await strapi.query('user', 'users-permissions').model.updateMany({ customId: { $in: customIds } }, { $set: { role: role._id }, $addToSet: { 'roles': role._id } })
}

let defaultDepartments = ['业务一部', '业务二部', '业务三部', '直营联盟', '会员流量', '校端产品', '职能总部', '阅卷云']
async function syncQxUserByDepartment (ctx, departments = defaultDepartments) {
    let token = await getToken('org')
    const allList = await axios.get(`http://org.yunxiao.io/node/list_all?__go_token=${token}`).then(res => res.data)

    const orgUserList = []
    const allOrgUserMap = {}
    allList['爱云校'][2].ceo = [1, allList?.['爱云校']?.[1], {}, allList?.['爱云校']?.[1]]
    for (const departmentName in allList?.['爱云校']?.[2]) {
        const userMap = _getOrgUser(allList?.['爱云校']?.[2][departmentName])
        if (departments.includes(departmentName)) {
            for (const qxId in userMap) {
                orgUserList.push({
                    name: userMap[qxId]?.[0],
                    customId: qxId,
                    no: userMap[qxId]?.[4],
                })
            }
        }
        for (const qxId in userMap) {
            allOrgUserMap[qxId] = {
                name: userMap[qxId]?.[0],
                customId: qxId,
                no: userMap[qxId]?.[4],
            }
        }
    }

    let managers = await strapi.query('manager').find({ _limit: 9999 })
    const groups = await strapi.query('manager-group').find({ _limit: 9999 })
    const users = await strapi.query('user', 'users-permissions').find({
        // customId: { $in: customIds },
        provider: 'yxWeCom',
        _limit: 9999,
        _projection: { customId: 1, id: 1, mingdaoId: 1 }
    }, [])
    let managerMap = {}, userMap = {}, groupMap = {}
    for (const manager of managers) {
        managerMap[manager.customId] = manager
    }
    for (const user of users) {
        userMap[user.customId] = user
    }
    for (const group of groups) {
        groupMap[group.id] = group
    }
    // console.log('orgUserList: ', orgUserList.length)
    // console.log('allOrgUserMap: ', Object.keys(allOrgUserMap).length)

    const iPlugin = strapi.plugins['users-permissions']
    // const bossRole = await strapi.query('role', 'users-permissions').findOne({ type: 'boss' }, [])
    const branch = await iPlugin.services['branch'].getCtxDefaultBranch(ctx)
    let hasNewManger = false
    // 创建新用户
    for (const user of orgUserList) {
        let curManger = managerMap[user.customId]
        const curUser = userMap[user.customId]
        if (!curManger) {
            curManger = await strapi.query('manager').create({
                username: user.name,
                customId: user.customId,
                provider: 'yxWeCom',
            })
            // curManger = curManger.data
            // curManger.id = curManger.rowid
            hasNewManger = true
        }
        if (!curUser) {
            await iPlugin.services['user'].createNewUser(ctx, {
                account: user.customId,
                username: user.name,
                pBranch: branch.id,
                customId: user.customId,
                provider: 'yxWeCom',
                // role: bossRole.id,
                mingdaoId: curManger?.id,
            }, null)
        }
    }

    if (hasNewManger) {
        managers = await strapi.query('manager').find({ _limit: 9999 })
        for (const manager of managers) {
            managerMap[manager.customId] = manager
        }
    }

    // 检查离职员工
    // 更新伙伴云和明道云用户表关联
    // 更新明道云用户 所属单元 字段
    for (const user of users) {
        const curOrgUser = allOrgUserMap[user.customId]
        const curManger = managerMap[user.customId]
        const mingdaoId = curManger?.id
        let curMingdaoUnitIds = []
        const mingdaoGroupIds = curManger?.groups?.map(e => {
            const groupInfo = groupMap[e.id]
            if (groupInfo?.unit?.[0]?.id) curMingdaoUnitIds.push(groupInfo?.unit?.[0]?.id)
            return e.id
        })
        curMingdaoUnitIds = _.union(curMingdaoUnitIds)
        const mingdaoUnitIds = _.union(curManger?.units?.map(e => e.id))
        let userUpdate = {}
        let managerUpdate = {}
        if (curManger) {
            userUpdate.mingdaoId = mingdaoId
            userUpdate.mingdaoGroupIds = mingdaoGroupIds
            userUpdate.mingdaoUnitIds = curMingdaoUnitIds
            userUpdate.provinces = curManger?.provinces?.map(e => e.name)
        }
        if (mingdaoUnitIds?.toString() !== curMingdaoUnitIds?.toString()) {
            managerUpdate.units = curMingdaoUnitIds
        }
        if (!curOrgUser) {
            userUpdate.blocked = true
            userUpdate.leaved = true
            if (curManger && (curManger.blocked !== true || curManger.leaved !== true)) {
                managerUpdate.blocked = true
                managerUpdate.leaved = true
            }
        }
        if (!_.isEmpty(userUpdate)) {
            await strapi.query('qx-user').update({
                id: user.id,
            }, userUpdate)
        }

        if (!_.isEmpty(managerUpdate)) {
            await strapi.entityService.update({
                params: {
                    id: curManger.id,
                },
                data: managerUpdate
            }, { model: 'manager' })
        }
    }
}

function _getOrgUser (groupMap) {
    let userMap = {}
    if (_.isEmpty(groupMap[2]) && (!_.isEmpty(groupMap[1]) || !_.isEmpty(groupMap[3]))) {
        return Object.assign(userMap, groupMap[1], groupMap[3])
    } else if (!_.isEmpty(groupMap[2])) {
        for (const groupName in groupMap[2]) {
            userMap = Object.assign(userMap, groupMap[1], groupMap[3], _getOrgUser(groupMap[2][groupName]))
        }
    }
    return userMap
}

module.exports = {
    syncBusinessUser,
    syncQxUserByDepartment,
}
