const { <PERSON>urdRouter } = require('accel-utils');
const curdRouter = new CurdRouter('data-sync');

async function dataSyncExecute(ctx) {
    const { id } = ctx.params;
    let queryData = {
        id: id,
        _publicationState: 'live'
    };
    const dataSync = await strapi.query('data-sync').findOne(queryData);

    // for (const dataSync of dataSyncs) {
    await internalDataSyncExecute(dataSync);
    // }
    return ctx.wrapper.succ({});
}

async function internalDataSyncExecute(dataSync) {
    let sourceDbClient
    try {
        const { MongoClient } = require('mongodb');
        const { js, model, dataSource } = dataSync;
        sourceDbClient = await MongoClient.connect(dataSource?.url);
        const sourceDb = sourceDbClient.db();
        const _executeFn = new Function(js);
        await _executeFn()(sourceDb, model, require);
        // await _executeFn(sourceDb, model, require);
        return;
    } catch (e) {
        console.log(e.stack || 'err');
    } finally {
        await sourceDbClient.close();
    }
}

// async function _executeFn(sourceDb, model, require) {
//     const bulkWriteArray = [];
//     let lastSyncObjectId;
//     const limit = 100, count = 5000;
//     let curCount = 0;
//     const { MongoClient, ObjectId } = require('mongodb');
//     const _ = require('lodash');
//     const bossSsDbClient = await MongoClient.connect(`mongodb://boss:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testboss?replicaSet=ReplsetTest&readPreference=primaryPreferred`);
//     const bossSsDb = bossSsDbClient.db();

//     const orders = await bossSsDb.collection('@SaleSystemOrder').find({}, { _id: 1, no: 1, buyer: 1 }).sort({ _id: -1 }).toArray();
//     const contracts = await sourceDb.collection('@Contract').find({}, { _id: 1, no: 1, part_b: 1 }).sort({ _id: -1 }).toArray();
//     let orderMap = {}, contractMap = {}
//     for (const order of orders) {
//         orderMap[order.no] = order
//     }
//     for (const contract of contracts) {
//         contractMap[contract.no] = contract
//     }
//     await bossSsDbClient.close();
//     while (true) {
//         const conditions = {};
//         if (lastSyncObjectId) {
//             conditions._id = { '$lt': ObjectId(lastSyncObjectId) };
//         }
//         const usageList = await sourceDb.collection('@CustomerAppUsageConfig').find(conditions).sort({ _id: -1 }).limit(limit).toArray();
//         let customerIds = [], customers = [], appVersionTypes = [], appVersions = [];
//         usageList.forEach(e => {
//             customerIds.push(e.customer_id);
//             appVersionTypes.push(e.app_version_type);
//         });
//         customerIds = _.union(_.compact(customerIds).filter(e => e.length === 24));
//         appVersionTypes = _.union(_.compact(appVersionTypes));

//         if (customerIds.length > 0) customers = await strapi.query('boss-school').find({ id: { $in: customerIds } });
//         if (appVersionTypes.length > 0) appVersions = await strapi.query('customer-app-version').find({ type: { $in: appVersionTypes } });

//         for (let usage of usageList) {
//             let remark = usage.remark && _.trim(usage.remark) || '';
//             let orderNo = remark.match(/\d{16}/) && remark.match(/\d{16}/)[0];
//             let contractNo = _.toUpper(remark.match(/[A-Z]{2,3}-\d{4}-\d{2}-\d{4}/) && remark.match(/[A-Z]{2,3}-\d{4}-\d{2}-\d{4}/)[0]) || (_.toUpper(remark.match(/[A-Z]{2}-\d{6}-\d{3,4}/) && remark.match(/[A-Z]{2}-\d{6}-\d{3,4}/)[0]));
//             let orderId, contractId, crmUrl, agent
//             if (orderNo) {
//                 orderId = orderMap[orderNo]?._id?.toString()
//                 crmUrl = orderId ? `https://boss-crm.iyunxiao.com/order-detail?id=${orderId}` : null
//                 agent = orderMap[orderNo]?.buyer?.type === 'agent' ? ObjectId(orderMap[orderNo]?.buyer?.id) : null
//             }
//             if (contractNo) {
//                 contractId = contractMap[contractNo]?._id?.toString()
//                 crmUrl = contractId ? `https://boss-crm.iyunxiao.com/ground-contract-management-detail?type=ground&id=${contractId}` : null
//                 agent = contractMap[contractNo]?.part_b?.type === 'agent' ? ObjectId(contractMap[contractNo]?.part_b?.id) : null
//             }

//             let no = usage?.source?.order_no || orderNo || contractNo;
//             const curCustomer = customers.find(e => e.id === usage.customer_id);
//             const curAppVersion = appVersions.find(e => e.type === usage.app_version_type);

//             let defaultFields = {
//                 _id: usage._id,
//                 no: no,
//                 customer: curCustomer?.id,
//                 customerType: curCustomer?.type,
//                 customerAppVersion: curAppVersion?.id,
//                 enabled: usage.enabled ? '是' : '否',
//                 isTrial: usage.is_trial ? '是' : '否',
//                 isBase: usage.is_base ? '是' : '否',
//                 duration: usage.duration,
//                 status: usage.status,
//                 beginTime: usage.begin_time && new Date(usage.begin_time),
//                 endTime: usage.end_time && new Date(usage.end_time),
//                 createTime: usage.create_time && new Date(usage.create_time),
//                 updateTime: usage.update_time && new Date(usage.update_time),
//                 sourceType: usage?.source?.type,
//                 durationParams: usage.params?.find(e => e.name === '访问周期')?.value,
//                 params: usage.params,
//                 submitter: usage?.submitter?.name,
//                 remark: usage.remark,
//                 orderId: orderId,
//                 contractId: contractId,
//                 crmUrl: crmUrl,
//                 agent: agent,
//             };
//             bulkWriteArray.push({ updateOne: { filter: { _id: usage._id }, update: { $set: defaultFields }, upsert: true } });
//         }
//         if (usageList.length === 0) { break; }
//         lastSyncObjectId = usageList[usageList.length - 1]._id.toString();
//         if (usageList.length < limit) { break; }
//         curCount = curCount + limit;
//         if (curCount >= count) { break; }
//     }
//     await strapi.query(model).model.bulkWrite(bulkWriteArray);
//     return;
// }
// return _executeFn;
module.exports = {
    dataSyncExecute,
    ...curdRouter.createHandlers(),
}