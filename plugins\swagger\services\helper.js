/**
 * Swagger 文档辅助工具
 * 用于生成标准 CRUD 操作的 Swagger 文档
 */

const generateCrudSwaggerDocs = (modelName, basePath, description = '') => {
  const modelNameCapitalized = modelName.charAt(0).toUpperCase() + modelName.slice(1);
  const modelNamePlural = modelName + 's';
  
  return {
    find: {
      tags: [modelNameCapitalized],
      summary: `获取${description || modelNamePlural}列表`,
      description: `查询${description || modelNamePlural}列表，支持分页、排序和筛选`,
      parameters: [
        {
          name: '_limit',
          in: 'query',
          description: '每页返回数量',
          required: false,
          schema: { type: 'integer', default: 10 }
        },
        {
          name: '_start',
          in: 'query',
          description: '偏移量',
          required: false,
          schema: { type: 'integer', default: 0 }
        },
        {
          name: '_sort',
          in: 'query',
          description: '排序字段:排序方式 (如: name:ASC)',
          required: false,
          schema: { type: 'string' }
        },
        {
          name: '_q',
          in: 'query',
          description: '搜索关键词',
          required: false,
          schema: { type: 'string' }
        }
      ],
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: {
                type: 'array',
                items: { $ref: `#/components/schemas/${modelNameCapitalized}` }
              }
            }
          }
        }
      }
    },
    
    findOne: {
      tags: [modelNameCapitalized],
      summary: `根据ID获取${description || modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: `${modelName} ID`,
          required: true,
          schema: { type: 'string' }
        }
      ],
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: { $ref: `#/components/schemas/${modelNameCapitalized}` }
            }
          }
        },
        404: {
          description: '未找到',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    },
    
    create: {
      tags: [modelNameCapitalized],
      summary: `创建${description || modelName}`,
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: `#/components/schemas/${modelNameCapitalized}Input` }
          }
        }
      },
      responses: {
        200: {
          description: '创建成功',
          content: {
            'application/json': {
              schema: { $ref: `#/components/schemas/${modelNameCapitalized}` }
            }
          }
        },
        400: {
          description: '请求参数错误',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    },
    
    update: {
      tags: [modelNameCapitalized],
      summary: `更新${description || modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: `${modelName} ID`,
          required: true,
          schema: { type: 'string' }
        }
      ],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: `#/components/schemas/${modelNameCapitalized}Input` }
          }
        }
      },
      responses: {
        200: {
          description: '更新成功',
          content: {
            'application/json': {
              schema: { $ref: `#/components/schemas/${modelNameCapitalized}` }
            }
          }
        },
        404: {
          description: '未找到',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    },
    
    delete: {
      tags: [modelNameCapitalized],
      summary: `删除${description || modelName}`,
      parameters: [
        {
          name: 'id',
          in: 'path',
          description: `${modelName} ID`,
          required: true,
          schema: { type: 'string' }
        }
      ],
      responses: {
        200: {
          description: '删除成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  id: { type: 'string' }
                }
              }
            }
          }
        },
        404: {
          description: '未找到',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/Error' }
            }
          }
        }
      }
    },
    
    count: {
      tags: [modelNameCapitalized],
      summary: `获取${description || modelNamePlural}总数`,
      responses: {
        200: {
          description: '成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  count: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    },
    
    deleteMany: {
      tags: [modelNameCapitalized],
      summary: `批量删除${description || modelNamePlural}`,
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: {
              type: 'object',
              properties: {
                ids: {
                  type: 'array',
                  items: { type: 'string' }
                }
              }
            }
          }
        }
      },
      responses: {
        200: {
          description: '删除成功',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  count: { type: 'integer' }
                }
              }
            }
          }
        }
      }
    }
  };
};

/**
 * 生成 Swagger 路径对象
 */
const generateSwaggerPaths = (basePath, modelName, description, customRoutes = []) => {
  const crudDocs = generateCrudSwaggerDocs(modelName, basePath, description);
  const paths = {};
  
  // 标准 CRUD 路由
  paths[basePath] = {
    get: crudDocs.find,
    post: crudDocs.create
  };
  
  paths[`${basePath}/count`] = {
    get: crudDocs.count
  };
  
  paths[`${basePath}/:id`] = {
    get: crudDocs.findOne,
    put: crudDocs.update,
    delete: crudDocs.delete
  };
  
  paths[`${basePath}/actions/deleteMany`] = {
    post: crudDocs.deleteMany
  };
  
  // 自定义路由
  customRoutes.forEach(route => {
    const path = route.path.replace(/:(\w+)/g, '{$1}');
    if (!paths[path]) {
      paths[path] = {};
    }
    paths[path][route.method.toLowerCase()] = route.swagger || {
      tags: [modelName.charAt(0).toUpperCase() + modelName.slice(1)],
      summary: route.summary || '自定义接口',
      description: route.description || '',
      responses: {
        200: { description: '成功' }
      }
    };
  });
  
  return paths;
};

module.exports = {
  generateCrudSwaggerDocs,
  generateSwaggerPaths
};