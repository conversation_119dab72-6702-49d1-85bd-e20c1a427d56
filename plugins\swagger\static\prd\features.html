<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="深入了解 Strapi Swagger Plugin 的核心功能特性，包括智能接口扫描、模块化文档生成、现代化界面设计等">
    <meta name="keywords" content="Strapi, Swagger, API 文档, 功能特性">
    <title>功能特性 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="layout">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>📚 Strapi Swagger</h1>
                <p>API 文档生成插件</p>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-back">
                    <a href="index.html">← 返回首页</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">功能介绍</div>
                    <a href="#scan-feature" class="nav-item active">
                        <i>🔍</i>
                        <span>智能接口扫描</span>
                    </a>
                    <a href="#module-feature" class="nav-item">
                        <i>📂</i>
                        <span>模块化文档</span>
                    </a>
                    <a href="#ui-feature" class="nav-item">
                        <i>🎨</i>
                        <span>现代化界面</span>
                    </a>
                    <a href="#auth-feature" class="nav-item">
                        <i>🔐</i>
                        <span>权限感知</span>
                    </a>
                    <a href="#performance-feature" class="nav-item">
                        <i>⚡</i>
                        <span>性能优化</span>
                    </a>
                    <a href="#tech-feature" class="nav-item">
                        <i>🛠️</i>
                        <span>技术架构</span>
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <div class="content-body">
            <section id="scan-feature" class="section">
                <h2>🔍 智能接口扫描</h2>
                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">🤖</div>
                        <h3>自动发现</h3>
                        <p>插件会自动扫描项目中的所有 API 接口，包括：</p>
                        <ul>
                            <li>标准 CRUD 接口（GET, POST, PUT, DELETE）</li>
                            <li>自定义控制器方法</li>
                            <li>扩展模块接口</li>
                            <li>第三方插件接口</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📝</div>
                        <h3>注释支持</h3>
                        <p>支持 JSDoc @swagger 注释进行精确控制：</p>
                        <div class="code-block">/**
 * @swagger
 * /api/products/search:
 *   get:
 *     tags: [产品管理]
 *     summary: 搜索产品
 */</div>
                    </div>
                </div>

                <div class="tip-box">
                    <h4>💡 智能识别</h4>
                    <p>插件能够智能识别接口的业务逻辑，自动生成合适的请求/响应示例，并根据 Strapi 模型自动推导参数类型。</p>
                </div>
            </section>

            <section id="module-feature" class="section">
                <h2>📂 模块化文档生成</h2>
                <div class="card">
                    <h3>🎯 精准过滤</h3>
                    <p>支持多层级的文档过滤，帮助开发者快速定位所需接口：</p>
                    
                    <div class="example-box">
                        <h4>访问方式</h4>
                        <code>http://localhost:8108/api-docs</code>
                        <p>完整接口文档</p>
                        
                        <code>http://localhost:8108/api-docs/base</code>
                        <p>仅显示 base 模块的接口</p>
                        
                        <code>http://localhost:8108/api-docs/base/user</code>
                        <p>仅显示 base 模块下 user 控制器的接口</p>
                    </div>

                    <div class="example-box">
                        <h4>JSON API 端点</h4>
                        <code>http://localhost:8108/api-docs/swagger.json</code>
                        <code>http://localhost:8108/api-docs/swagger.json/base</code>
                        <code>http://localhost:8108/api-docs/swagger.json/base/user</code>
                    </div>
                </div>

                <table class="table">
                    <thead>
                        <tr>
                            <th>过滤级别</th>
                            <th>包含内容</th>
                            <th>适用场景</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>完整文档</td>
                            <td>所有模块的所有接口</td>
                            <td>系统概览、完整测试</td>
                        </tr>
                        <tr>
                            <td>模块过滤</td>
                            <td>指定模块的所有接口</td>
                            <td>模块开发、功能测试</td>
                        </tr>
                        <tr>
                            <td>控制器过滤</td>
                            <td>指定控制器的接口</td>
                            <td>精确调试、接口验证</td>
                        </tr>
                    </tbody>
                </table>
            </section>

            <section id="ui-feature" class="section">
                <h2>🎨 现代化界面设计</h2>
                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>响应式设计</h3>
                        <p>完美适配各种设备尺寸：</p>
                        <ul>
                            <li>桌面端：完整功能展示</li>
                            <li>平板端：优化布局结构</li>
                            <li>移动端：简化操作界面</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🎭</div>
                        <h3>粘性头部导航</h3>
                        <p>固定在顶部的导航栏，包含：</p>
                        <ul>
                            <li>文档标题和描述</li>
                            <li>模块选择下拉框</li>
                            <li>JSON API 访问链接</li>
                            <li>外部参考文档链接</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🌈</div>
                        <h3>视觉效果</h3>
                        <p>现代化的视觉设计：</p>
                        <ul>
                            <li>渐变背景色</li>
                            <li>毛玻璃效果（backdrop-filter）</li>
                            <li>平滑过渡动画</li>
                            <li>优雅的阴影效果</li>
                        </ul>
                    </div>
                </div>

                <div class="info-box">
                    <h4>🎮 交互体验</h4>
                    <p>提供直观的下拉选择器，支持快速切换不同模块的文档，同时保持界面的整洁和美观。</p>
                </div>
            </section>

            <section id="auth-feature" class="section">
                <h2>🔐 权限感知系统</h2>
                <div class="card">
                    <h3>🛡️ 自动识别</h3>
                    <p>插件能够自动识别接口的权限要求：</p>
                    
                    <div class="example-box">
                        <h4>权限分类</h4>
                        <ul>
                            <li><strong>公开接口</strong>：无需认证即可访问</li>
                            <li><strong>认证接口</strong>：需要用户登录</li>
                            <li><strong>权限接口</strong>：需要特定权限</li>
                        </ul>
                    </div>

                    <p>基于项目的 permission.js 配置自动标注接口权限等级，在文档中清晰展示安全要求。</p>
                </div>
            </section>

            <section id="performance-feature" class="section">
                <h2>⚡ 性能与开发体验</h2>
                <div class="features">
                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <h3>热重载</h3>
                        <p>开发环境特性：</p>
                        <ul>
                            <li>配置修改即时生效</li>
                            <li>接口变更自动更新</li>
                            <li>无需重启服务器</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h3>缓存优化</h3>
                        <p>生产环境优化：</p>
                        <ul>
                            <li>智能缓存机制</li>
                            <li>按需生成文档</li>
                            <li>最小化资源占用</li>
                        </ul>
                    </div>
                </div>
            </section>

            <section id="tech-feature" class="section">
                <h2>🛠️ 技术架构</h2>
                <div class="tech-stack">
                    <span class="tech-badge">OpenAPI 3.0</span>
                    <span class="tech-badge">Swagger UI</span>
                    <span class="tech-badge">Koa Router</span>
                    <span class="tech-badge">swagger-jsdoc</span>
                    <span class="tech-badge">Marked</span>
                    <span class="tech-badge">CSS Grid</span>
                    <span class="tech-badge">Flexbox</span>
                </div>

                <div class="info-box">
                    <h4>🏗️ 架构特点</h4>
                    <p>采用模块化设计，支持插件化扩展，遵循 OpenAPI 3.0 标准，确保与各种工具的兼容性。</p>
                </div>
            </section>
            </div>
        </main>
    </div>

    <script src="scripts.js"></script>
</body>
</html>