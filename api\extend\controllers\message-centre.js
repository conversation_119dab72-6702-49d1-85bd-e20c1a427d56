const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('message-centre')
const axios = require('axios')
const _ = require('lodash');
const moment = require('moment');

let codeCache = {}

async function mingdaoMsg(ctx) {
    try {
        let { Type, Data } = ctx.request.body;

        // let str = await rediser.redis.get(`tiku:send:msg:count:${phone}`);
        // if (str > 5) return resWrap.error('PARAMETERS_ERROR', '手机号发送短信次数已达上线');
        let phone = Data.Mobiles[0];
        if (!phone || ! Data?.Message)   return ctx.badData('参数异常')
        const key = `mingdao-${phone}`;
        let cache = codeCache[key];
        const vCode = Data?.Message?.replace(/[^\d]/g, '');

        if (cache) {
            if (cache.last_time > moment().startOf('day').valueOf() && cache.count > 5) {
                return ctx.badData('手机号发送短信次数已达上线')
            } else if (cache.last_time > moment().startOf('day').valueOf()) {
                codeCache[key] = {
                    last_time: Date.now(),
                    code: vCode,
                    phone: phone,
                    count: cache.count++
                }
            } else {
                codeCache[key] = {
                    last_time: Date.now(),
                    code: vCode,
                    phone: phone,
                    count: 1
                }
            }
        } else {
            codeCache[key] = {
                last_time: Date.now(),
                code: vCode,
                phone: phone,
                count: 1
            }
        }

        const content = Data.Message;
        const templateId = '55';
        const contentVar = {
            vCode: `${vCode}`
        };
        const createFields = {
            type: 'shortMsg',
            situation: 'mingdao',
            key: `mingdao-${phone}-${Date.now()}`,
            phone: phone,
            status: 'prepared',
            shortMsgContent: content,
            shortMsgConfig: {
                platform: 'bdy',
                template_id: templateId,
                content_var: contentVar
            }
        };

        const createResult =  await strapi.query('message-centre').create(createFields);

        const msgResult = await _sendMsg(templateId, [phone], contentVar);

        const updateFields = {
            sendTime: new Date(),
            status: 'success'
        };
        // const result = { id: msgId.toString() };
        if (msgResult) {
            await strapi.query('message-centre').update({ id: createResult.id }, updateFields);
        }
        // if (process.env.NODE_ENV !== 'production') {
        //     result.test = {
        //         vCode: vCode
        //     };
        // }
        return ctx.wrapper.succ({});
    } catch (e) {
        return ctx.badRequest(e)
    }
}

async function _sendMsg(content, phones, contentVar) {
    // phones = filterShortMsgWhiteList(phones);
    if (_.isEmpty(phones)) return false;

    const msgConfig = strapi.config.server.msgCentre
    const appId = msgConfig?.app?.shortMsg?.bdy;
    try {
        let result = await axios.post(`${msgConfig.host}/v1/sms/phone`, {
            content, phone: phones, appId, contentVar,
        }, { headers: { 'apikey': msgConfig.apiKey }, timeout: 50000 });

        if (result?.data?.code !== 0) {
            console.error('发送端信通知失败：', JSON.stringify(result), contentVar, JSON.stringify(phones));
            return false;
        }
        return result?.data;
    } catch (err) {
        console.error('发送短信通知失败：', err);
        return false;
    }
}


module.exports = {
    mingdaoMsg,
    ...curdRouter.createHandlers(),
}
