const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const _ = require('lodash');
const DB_URL = (process.env.NODE_ENV !== 'production')
    // ? 'mongodb://localhost:27017/testwly-boss-back-1'
    ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

const SERVER_URL = (process.env.NODE_ENV !== 'production')
    ? 'http://localhost:3015'
    : 'https://wly-boss-api-lan.iyunxiao.com'

let db, dbClient
let retry = 3;

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    try {
        let start = Date.now()
        const lock = await db.collection('db-cache').findOne({
            key: 'syncByHuobanMsgQueue-lock',
            type: 'lock',
        });
        if (!lock) {
            await db.collection('db-cache').insertOne({
                key: 'syncByHuobanMsgQueue-lock',
                type: 'lock',
                expireAt: moment().add(60, 'm').toDate()
            });

            logger('sync start')
            await main()
            logger('sync end')

            await db.collection('db-cache').deleteOne({
                key: 'syncByHuobanMsgQueue-lock',
                type: 'lock',
            });
        }
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {
        await dbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('syncByHuobanMsgQueue start')
    await syncByHuobanMsgQueue()
    logger('syncByHuobanMsgQueue end')
}


async function syncByHuobanMsgQueue() {
    try {
        const cache = await db.collection('db-cache').findOne({
            key: 'syncByHuobanMsgQueue-cache',
            type: 'cache',
        });
        let id = cache?.content;
        if (!cache) {
            id = '000000000000000000000000';
            await db.collection('db-cache').insertOne({
                key: 'syncByHuobanMsgQueue-cache',
                type: 'cache',
                content: id
            });
        }
        const limit = 30
        let lastSyncObjectId = id
        while (true) {
            let now = new Date()
            const conditions = {
                status: 'prepared',
                _id: { '$gt': ObjectId(lastSyncObjectId) }
            }
            const queueList = await db.collection('huoban-msg-queue').find(conditions).sort({ _id: 1 }).limit(limit).toArray();
            // const queueList = await axios.post('https://wly-boss-api-wan.iyunxiao.com/utils/dbQuery', {
            //     db: 'wly_boss',
            //     coll: 'huoban-msg-queue',
            //     filter: conditions,
            //     sort: { _id: 1 },
            //     limit: limit
            // }).then(({ data }) => { return data });

            if (queueList.length === 0) { break }

            const uniqQueueList = _.uniqBy(queueList, 'itemId');
            for (const queueInfo of uniqQueueList) {
                const res = await axios.post(`${SERVER_URL}/huoban/action/syncByHuobanId`, {
                    tableId: queueInfo.tableId, itemId: queueInfo.itemId,
                }, { timeout: 50000 })
                await db.collection('huoban-msg-queue').updateMany({
                    status: 'prepared',
                    tableId: queueInfo.tableId,
                    itemId: queueInfo.itemId,
                }, {
                    $set: {
                        status: 'success',
                        updatedAt: now,
                    }
                });
                await sleep(100);
            }

            lastSyncObjectId = queueList.length > 0 ? queueList[queueList.length - 1]._id.toString() : lastSyncObjectId
            logger(`lastSyncObjectId: ${lastSyncObjectId}`)
            // await db.collection('huoban-msg-queue').updateMany({
            //     _id: { $in: queueList.map(e => e._id) }
            // }, {
            //     $set: {
            //         status: 'success',
            //         updatedAt: now,
            //     }
            // });
            await db.collection('db-cache').updateOne({
                key: 'syncByHuobanMsgQueue-cache',
                type: 'cache',
            }, {
                $set: { content: lastSyncObjectId.toString(), }
            });
            if (queueList.length < limit) { break }
            await sleep(3 * 1000);
        }
    } catch (e) {
        logger(`retry: ${retry}`)
        logger(e.message)
        if (retry > 0) {
            await sleep(3 * 60 * 1000);
            --retry
            await syncByHuobanMsgQueue();
        }
        // throw e;
    }
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}

async function sleep(time) {
    return new Promise((resolve) => setTimeout(resolve, time));
}
