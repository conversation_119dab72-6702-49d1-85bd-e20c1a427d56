// import { DatabaseManager, Repository } from 'strapi-database';

type DatabaseManager = any

interface Repository {

  model: any,
  orm: any,
  primaryKey: string,
  associations: any,

  custom (mapping: any): any

  // Base Query
  findOne (params): Promise<object>
  findOne (params, populate): Promise<object>

  find (params): Promise<object[]>
  find (params, populate): Promise<object[]>

  count (params): Promise<number>

  search (params): Promise<object[]>
  search (params, populate): Promise<object[]>

  countSearch (params): Promise<number>

  create (entity: object): object

  update (params: object, values: object): object

  delete (params): Promise<object[]>

  // Page Query
  findPage (params): Promise<{
    results: object[],
    pagination: number
  }>
  findPage (params, populate): Promise<{
    results: object[],
    pagination: number
  }>

  searchPage (params): Promise<{
    results: object[],
    pagination: number
  }>
  searchPage (params, populate): Promise<{
    results: object[],
    pagination: number
  }>

  // Relation Query
  findWithRelationCounts (params): Promise<{
    results: object[],
    pagination: number
  }>

  searchWithRelationCounts (params): Promise<{
    results: object[],
    pagination: number
  }>
}

interface Strapi {
  db: DatabaseManager;

  query (model: string, plugin?: string): Repository;
}

export default function createStrapi (opts: any): Strapi;

declare global {
  const strapi: Strapi
}
