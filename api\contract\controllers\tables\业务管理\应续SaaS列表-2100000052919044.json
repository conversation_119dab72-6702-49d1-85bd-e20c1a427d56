{"fieldsMap": {"立项编号": "2200000447999018", "跟进状态": "2200000441735967", "合作学校": "2200000441737067", "经销商": "2200000441735958", "部门": "2200000441735127", "省份": "2200000441735959", "地市": "2200000441735960", "区县": "2200000441735961", "付费版本": "2200000441735962", "到期月份": "2200000441735965", "权益开始日期": "2200000441735963", "权益到期日期": "2200000441735964", "续约状态": "2200000441735968", "风险程度": "2200000441735969", "是否新款": "2200000441735970", "预计新款金额": "2200000441735971", "预计回款日期": "2200000441735972", "下次跟进日期-商": "2200000441735973", "渠道经理B跟进记录-商": "2200000441735974", "下次跟进日期-校": "2200000441735975", "渠道经理B跟进记录-校": "2200000441735976", "直服沟通结果": "2200000442933078", "直服跟进记录-校": "2200000441735977", "阅卷跟进记录(同步)": "2200000450279660", "立项编号-本月实际回款（业务自行登记）": "1134001189000000", "立项编号-立项审核": "1134001151000000", "立项编号-项目状态": "1134001201000000", "立项编号-审批备注": "1134001152000000", "合作学校-学校名称": "1132001102000000", "合作学校-学校ID": "1132001101000000", "合作学校-账号类型": "1132001103000000", "合作学校-区县": "1132001106000000"}, "table_id": "2100000052919044", "name": "应续SaaS列表", "alias": "", "space_id": "4000000003570865", "created_on": "2023-08-22 09:15:10", "fields": [{"field_id": "2200000447999018", "name": "立项编号", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000441735967", "name": "跟进状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "待跟进"}, {"id": "2", "name": "跟进中"}, {"id": "3", "name": "已完成"}]}}, {"field_id": "2200000441737067", "name": "合作学校", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000441735958", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000441735127", "name": "部门", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "渠道一部"}, {"id": "2", "name": "渠道二部"}, {"id": "3", "name": "渠道三部"}, {"id": "4", "name": "渠道四部"}, {"id": "5", "name": "渠道五部"}, {"id": "6", "name": "渠道六部"}]}}, {"field_id": "2200000441735959", "name": "省份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000441735960", "name": "地市", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524705", "space_id": "4000000003570865"}}, {"field_id": "2200000441735961", "name": "区县", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524706", "space_id": "4000000003570865"}}, {"field_id": "2200000441735962", "name": "付费版本", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "专业版"}, {"id": "2", "name": "旗舰版"}, {"id": "3", "name": "A版"}, {"id": "4", "name": "B版"}, {"id": "5", "name": "C版"}, {"id": "6", "name": "D版"}, {"id": "7", "name": "S版"}, {"id": "8", "name": "地市版"}, {"id": "9", "name": "区县版"}, {"id": "10", "name": "片区版"}]}}, {"field_id": "2200000441735965", "name": "到期月份", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000441735963", "name": "权益开始日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000441735964", "name": "权益到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000441735968", "name": "续约状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "4", "name": "1-待续约"}, {"id": "1", "name": "2-已续约"}, {"id": "3", "name": "3-延期未续"}, {"id": "2", "name": "4-确认流失"}]}}, {"field_id": "2200000441735969", "name": "风险程度", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "基本确定"}, {"id": "2", "name": "有风险"}]}}, {"field_id": "2200000441735970", "name": "是否新款", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}, {"id": "3", "name": "部分"}]}}, {"field_id": "2200000441735971", "name": "预计新款金额", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "万元", "is_percent": 0}}, {"field_id": "2200000441735972", "name": "预计回款日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000441735973", "name": "下次跟进日期-商", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000441735974", "name": "渠道经理B跟进记录-商", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "格式：倒叙【日期】 沟通前提：先查看学校的使用情况，最近是否考试等； 续签失败：竞品是哪家，是否签约？续签失败的真正原因-客情&价格&产品&服务？ 客情-经销商未做客情；换领导来的其他品牌； 其他商客情关系更好等 价格-经销商报价；竞品报价，最终实际签约价；学校预算等 产品-经销商是否传递到产品价值，与竞品的优势对比，是否清晰介绍了阅卷+分析+题库等产品 服务-经销商服务是否到位（是否提供入校服务等) 续签延期：与商反馈有出入直接联系学校，学校对产品和服务的使用满意度，是否有问题及建议；了解学校的考试计划，是否有人跟其联系续签，具体是谁；学校目前的进度-是否上会、支付流程等；是否有其他竞品与学校接触-具体哪家，产品使用情况；预计报价？学校的实际预算？学校之前的签约价格及合同？", "config": {}}, {"field_id": "2200000441735975", "name": "下次跟进日期-校", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000441735976", "name": "渠道经理B跟进记录-校", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "格式：倒叙【日期】 续签延期： 1. 学校最近考试情况： 1.1 近期考试频次： 1.2 最近一次考试时间：  如和商描述的有区别，则直接联系学校： 1.  学校对我们的产品和服务是否满意： 2. 学校对商的服务是否满意： 3. 学校针对产品和服务是否有建议： 4. 学校近期的考试安排： 5. 学校是否有需要我们协助解决的问题：  6. 告知学校的阅卷到期时间，问学校是否商已经跟学校联系续签： 7. 商的哪个人跟学校联系的： 8. 目前学校续签的打算：是否续签，续签金额，预计续签时间 9. 目前是否有试用其他阅卷产品，哪家产品： 10. 试用情况如何： 11. 试用品的预计价格： 12. 学校之前的签约价格： 续签失败 1. 学校最近考试情况： 1.1 近期考试频次：、 最近一次考试时间：  2. 学校是否切换了其他品牌阅卷？ 2.1 学校切换的竞品品牌： 2.2 是经销商切换了竞品品牌吗： 2.3 是经销商失去了学校客情，被其他商切走了吗： 2.4 如果是经销商主动更换了竞品，更换原因是什么？  3. 学校是否已经和竞品签约： 学校的签约价格： 竞品给商的价格： 4. 经销商是否给学校讲过我们的产品和竞品的区别/优势 5. 经销商是否知道我们的产品和竞品的区别/优势： 6. 学校对经销商/我们的服务是否满意，是否有不到位的地方？", "config": {}}, {"field_id": "2200000442933078", "name": "直服沟通结果", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "有续费意向"}, {"id": "2", "name": "价格问题"}, {"id": "3", "name": "对商不满意"}, {"id": "4", "name": "正常使用"}, {"id": "5", "name": "已续约"}, {"id": "6", "name": "无需跟进"}, {"id": "7", "name": "多品牌共用"}, {"id": "8", "name": "竞品介入中"}, {"id": "9", "name": "待跟进"}, {"id": "10", "name": "其他"}]}}, {"field_id": "2200000441735977", "name": "直服跟进记录-校", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "格式：倒叙【日期】", "config": {}}, {"field_id": "2200000450279660", "name": "阅卷跟进记录(同步)", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "1134001189000000", "name": "本月实际回款（业务自行登记）", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000447999018}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "元", "is_percent": 0}}, {"field_id": "1134001151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000447999018}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "直营待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1134001201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000447999018}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "1134001152000000", "name": "审批备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000447999018}, "required": false, "description": "", "config": {}}, {"field_id": "1132001102000000", "name": "学校名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000441737067}, "required": true, "description": "", "config": {}}, {"field_id": "1132001101000000", "name": "学校ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000441737067}, "required": true, "description": "", "config": {}}, {"field_id": "1132001103000000", "name": "账号类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000441737067}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "学校"}, {"id": "3", "name": "统考平台"}, {"id": "4", "name": "联盟平台"}, {"id": "7", "name": "集团校平台"}, {"id": "9", "name": "中心校平台"}, {"id": "5", "name": "单次联考"}, {"id": "2", "name": "教育局"}, {"id": "6", "name": "培训机构"}, {"id": "8", "name": "其他"}]}}, {"field_id": "1132001106000000", "name": "区县", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000441737067}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524706", "space_id": "4000000003570865"}}]}