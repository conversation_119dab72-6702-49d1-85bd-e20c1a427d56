beforeAll(async () => {
  process.env.NODE_PORT = '8999'
  process.env.DATABASE = 'local'
  process.env.SERVER = 'local'
  const strapi = require('strapi')
  await strapi().start()
})

test('loginByTokenKey', async () => {
  const { loginByTokenKey } = require('../controllers/auth-extend')
  const mockCtx = {
    request: {
      body: {
        tokenKey: 'OMRFzPxbv5qWE8tITpFAVVNL'
      }
    }
  }
  const info = await loginByTokenKey(mockCtx)
  expect(info).toEqual(expect.any(Object))
})
