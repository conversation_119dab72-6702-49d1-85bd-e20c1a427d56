export namespace UserPermission {

  type ObjectId = string;

  interface UploadFile {
    name: string;
    alternativeText: string;
    caption: string;
    width: number;
    height: number;
    formats: string[];
    hash: string;
    ext: string;
    mime: string;
    size: number;
    url: string;
    previewUrl: string;
    provider: string;
    provider_metadata: object;
    // "related": {
    //   "collection": "*",
    //   "filter": "field",
    //   "configurable": false
    // }
    related: {
      _id: ObjectId,
      ref: ObjectId,
      kind: string,     // e.g. Card
      field: string,    // e.g. avatar
    };
  }

  // 权限数据模型
  interface Permission {
    type: string;
    controller: string;
    action: string;
    enabled: boolean;
    policy: string;
    role: Role;         // via Role.permissions
  }

  // 页面数据模型
  interface Page {
    isPreset: string    // 是否预置
    cover: UploadFile   // 视图封面
    sId: string         // 视图编号
    name: string        // 视图名称
  }

  // 功能数据模型
  interface Group {
    name: string; // 功能名称
    sId: string;  // 功能编号
    cover: UploadFile;        // 功能封面
    apiPermissions: object[]; // 功能接口
    pages: Page[]             // 功能页面
  }

  // 角色数据模型
  interface Role {
    name: string,               // 角色名称
    description: string,        // 角色描述
    type: string,               // 角色编号
    permissions: Permission[],  // 角色接口权限
    modules: string[],          // 角色功能权限
  }

  // 租户数据模型
  interface Branch {
    name: string,       // 名称
    shortName: string,  // 简称
    type: string,       // 租户编号
  }

  // 用户数据模型
  interface User {
    username: string;           // 用户名
    phone: string;              // 手机号码
    email: string;              // 邮箱
    provider: string;           // 用户来源
    password: string;           // 密码
    resetPasswordToken: string; // 密码重置Token
    confirmationToken: string;  // 确认Token
    confirmed: boolean;         // 确认状态
    role: Role;                 // 当前角色
    roles: Role[];              // 可用角色
    pBranch: Branch;            // 当前租户
    pBranches: Branch[];        // 可用租户
    pBranchConfigs: {
      branchId: string,         // 租户ID
      blocked: string,          // 是否屏蔽
      role: string,             // 当前角色 - 角色ID
      roles: string[],          // 可用角色 - 角色ID
      username: string,         // 用户名
      phone: string,            // 手机号码
      email: string,            // 邮箱
    }[];
  }
}
