const settings = require('../../../extensions/users-permissions/models/User.settings.js')
module.exports = {
  collectionName: 'users-permissions_user',
  duplicateCollection: true,
  info: {
    name: 'ManagerUser',
    label: '企业微信用户',
    description: '企业微信用户'
  },
  options: {
    draftAndPublish: false,
    timestamps: false,
    indexes: [
      { keys: { customId: 1 }, options: { unique: true, sparse: true } },
    ],
  },
  pluginOptions: {},
  attributes: Object.assign({}, settings.attributes, {
    username: {
      label: '用户名',
      type: 'string',
      configurable: false,
      required: true,
    },
    phone: {
      label: '手机号',
      type: 'string',
      configurable: false
    },
    showName: {
      label: '显示名称',
      type: 'string',
    },
    blocked: {
      label: '是否禁用',
      type: 'boolean',
      'default': false,
      configurable: false,
    },
    mingdaoId: {
      label: '明道云rowid',
      type: 'string'
    },
    leaved: {
      label: '是否离职',
      type: 'boolean',
      'default': false,
      configurable: false
    },
    provider: {
      label: '用户来源',
      type: 'string',
      configurable: false,
      editable: false
    },
    customId: {
      label: '自定义Id',
      type: 'string',
      configurable: false,
      editable: false
    },
    role: {
      label: '当前角色',
      plugin: 'users-permissions',
      model: 'role',
      configurable: false,
      required: true,
      meta: {
        query: {
          ['type']: ['service-admin', 'service-group-leader', 'service-group-member', 'sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-worker']
        }
      }
    },
    roles: {
      label: '可用角色',
      plugin: 'users-permissions',
      collection: 'role',
      configurable: false,
      required: true,
      meta: {
        query: {
          ['type']: ['service-admin', 'service-group-leader', 'service-group-member', 'sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-worker']
        }
      }
    },
    serviceManagerQrCode: {
      label: '企微二维码',
      model: 'file',
      via: 'related',
      allowedTypes: [
        'images'
      ],
      plugin: 'upload',
      required: false,
      pluginOptions: {}
    }
  })
}
