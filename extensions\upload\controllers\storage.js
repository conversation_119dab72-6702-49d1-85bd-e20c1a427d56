'use strict'
const axios = require('axios')
const storageExports = require('../../../plugins/upload/controllers/storage')

async function proxyDownload(ctx) {
    const { url } = ctx.request.query
    try {
        const response = await axios({
            method: 'GET',
            url: url,
            responseType: 'stream',
        })
        ctx.set(response.headers)
        ctx.body = response.data
    } catch (error) {
        console.error('转发文件时出错:', error);
        throw error
    }
}

module.exports = {
    ...storageExports,
    proxyDownload,
}


