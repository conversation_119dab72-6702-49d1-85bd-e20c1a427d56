function retry(fn, times = 0, delay = 0, ...args) {
  // 返回一个新的 Promise
  return new Promise((resolve, reject) => {
    // 定义异步的内部函数 inner
    let inner = async function () {
      try {
        const result = await fn(...args); // 尝试执行 fn，并传递参数
        resolve(result); // 如果 fn 执行成功，使用 resolve 方法解决外部 Promise
      } catch (error) { // 如果执行失败，捕获到异常
        if (times-- > 0) { // 检查剩余重试次数
          setTimeout(inner, delay * 2); // 如果还有剩余次数，等待 delay*2 ms 后调用 inner 以进行重试
        } else {
          reject('多次尝试失败', error); // 如果没有剩余次数，用 reject 拒绝外部 Promise 并提供错误信息
        }
      }
    };
    inner(); // 初次调用 inner 函数以启动逻辑
  });
}

module.exports = {
  retry
}
