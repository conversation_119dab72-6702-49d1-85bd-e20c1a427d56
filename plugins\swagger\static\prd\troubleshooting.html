<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Strapi Swagger Plugin 故障排除指南，包含常见问题解决方案和调试技巧">
    <meta name="keywords" content="Strapi, Swagger, 故障排除, 问题解决, 调试">
    <title>故障排除 - Strapi Swagger Plugin</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="layout">
        <aside class="sidebar">
            <div class="sidebar-header">
                <h1>📚 Strapi Swagger</h1>
                <p>故障排除指南</p>
            </div>
            <nav class="sidebar-nav">
                <div class="nav-back">
                    <a href="index.html">← 返回首页</a>
                </div>
                <div class="nav-section">
                    <div class="nav-section-title">问题分类</div>
                    <a href="#installation-issues" class="nav-item active">
                        <i>🛠️</i>
                        <span>安装问题</span>
                    </a>
                    <a href="#configuration-issues" class="nav-item">
                        <i>⚙️</i>
                        <span>配置问题</span>
                    </a>
                    <a href="#ui-issues" class="nav-item">
                        <i>🎨</i>
                        <span>界面问题</span>
                    </a>
                    <a href="#performance-issues" class="nav-item">
                        <i>🚀</i>
                        <span>性能问题</span>
                    </a>
                    <a href="#diagnostic-checklist" class="nav-item">
                        <i>🔍</i>
                        <span>诊断清单</span>
                    </a>
                    <a href="#support" class="nav-item">
                        <i>🆘</i>
                        <span>获取支持</span>
                    </a>
                </div>
            </nav>
        </aside>

        <main class="content">
            <div class="content-body">
            <div class="toc">
                <h3>📋 问题目录</h3>
                <ul>
                    <li><a href="#installation-issues">安装问题</a></li>
                    <li><a href="#configuration-issues">配置问题</a></li>
                    <li><a href="#documentation-issues">文档没有更新</a></li>
                    <li><a href="#interface-display-issues">接口没有显示</a></li>
                    <li><a href="#permission-issues">权限标记不正确</a></li>
                    <li><a href="#performance-issues">性能问题</a></li>
                    <li><a href="#diagnostic-checklist">诊断检查清单</a></li>
                    <li><a href="#support">获取支持</a></li>
                </ul>
            </div>

            <section class="section" id="installation-issues">
                <h2>🛠️ 安装问题</h2>

                <div class="error-box">
                    <h4>❌ 子模块添加失败</h4>
                    <p><strong>症状：</strong>执行 <code>git submodule add</code> 命令时出现权限错误或网络错误</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查 SSH 密钥配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 测试 SSH 连接
ssh -T ********************

# 如果失败，检查密钥
ssh-add -l
                    </div>

                    <p>2. 使用 HTTPS 方式（如果 SSH 不可用）：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
git submodule add https://git.iyunxiao.com/wly/strapi-swagger.git plugins/swagger
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 插件依赖安装失败</h4>
                    <p><strong>症状：</strong>npm install 执行失败，缺少必要依赖</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 清理缓存并重新安装：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 清理 npm 缓存
npm cache clean --force

# 删除 node_modules 重新安装
rm -rf node_modules package-lock.json
npm install
                    </div>

                    <p>2. 检查 Node.js 版本：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 查看当前版本
node --version
npm --version

# 需要 Node.js >= 16.20.0
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 插件未被识别</h4>
                    <p><strong>症状：</strong>启动项目后插件没有加载，访问 /api-docs 返回 404</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查中间件配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
// config/middleware.js
module.exports = {
  settings: {
    swagger: {
      enabled: true
    }
  }
};
                    </div>

                    <p>2. 确认插件目录结构：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
plugins/
└── swagger/
    ├── package.json
    ├── config/
    ├── controllers/
    ├── middlewares/
    └── services/
                    </div>
                </div>
            </section>

            <section class="section" id="configuration-issues">
                <h2>⚙️ 配置问题</h2>

                <div class="error-box">
                    <h4>❌ 接口文档为空或不完整</h4>
                    <p><strong>症状：</strong>文档页面显示但没有接口，或者缺少某些接口</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查路由配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
// 确保路由文件使用标准格式
// api/*/config/routes.js
const { createDefaultRoutes } = require('accel-utils');

module.exports = {
  routes: [
    ...createDefaultRoutes({
      basePath: '/products',
      controller: 'product'
    })
  ]
};
                    </div>

                    <p>2. 检查控制器导出：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
// 确保控制器正确导出
// api/*/controllers/*.js
module.exports = {
  async find(ctx) {
    // 方法实现
  },

  async findOne(ctx) {
    // 方法实现
  }
};
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 模块过滤不生效</h4>
                    <p><strong>症状：</strong>访问 /api-docs/module 仍显示所有接口</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 清理 require 缓存：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 重启开发服务器
npm run dev

# 或强制清理缓存
rm -rf .tmp
                    </div>

                    <p>2. 检查模块名称：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 确认模块目录存在
ls -la api/
# 应该看到对应的模块文件夹
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 扩展配置加载失败</h4>
                    <p><strong>症状：</strong>users-permissions 等插件接口没有出现在文档中</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>检查扩展配置文件：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 确认文件存在和格式正确
cat config/swagger-extensions.yaml

# 或检查 JavaScript 配置
cat config/swagger-extensions.js
                    </div>
                </div>
            </section>

            <section class="section" id="documentation-issues">
                <h2>📚 文档没有更新</h2>

                <div class="error-box">
                    <h4>❌ 文档内容没有更新</h4>
                    <p><strong>症状：</strong>修改了代码但文档页面内容没有变化</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查代码是否有语法错误：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 检查 JavaScript 语法
node -c api/*/controllers/*.js

# 查看控制台错误信息
npm run dev
                        </pre>
                    </div>

                    <p>2. 重启服务器：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 停止服务器（Ctrl+C）然后重启
npm run dev

# 或者使用 nodemon 自动重启
npm run develop
                        </pre>
                    </div>

                    <p>3. 清除浏览器缓存：</p>
                    <ul>
                        <li>按 Ctrl+F5 强制刷新页面</li>
                        <li>清除浏览器缓存和 Cookie</li>
                        <li>尝试无痕浏览模式访问</li>
                    </ul>
                </div>
            </section>

            <section class="section" id="interface-display-issues">
                <h2>🔗 接口没有显示</h2>

                <div class="error-box">
                    <h4>❌ 接口在文档中不显示</h4>
                    <p><strong>症状：</strong>定义的接口在 Swagger 文档中看不到</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 确认路由使用了标准格式：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
// api/product/config/routes.js
const { createDefaultRoutes } = require('accel-utils');

module.exports = {
  routes: [
    ...createDefaultRoutes({
      basePath: '/products',
      controller: 'product'
    })
  ]
};
                        </pre>
                    </div>

                    <p>2. 自定义接口需要添加 @swagger 注释：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
/**
 * @swagger
 * /api/products/search:
 *   get:
 *     tags:
 *       - 产品管理
 *     summary: 搜索产品
 *     parameters:
 *       - in: query
 *         name: keyword
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: 搜索成功
 */
async search(ctx) {
  // 实现逻辑
}
                        </pre>
                    </div>

                    <p>3. 检查控制器是否正确导出：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
// 确保控制器正确导出
// api/*/controllers/*.js
module.exports = {
  async find(ctx) {
    // 方法实现
  },

  async findOne(ctx) {
    // 方法实现
  }
};
                        </pre>
                    </div>
                </div>
            </section>

            <section class="section" id="permission-issues">
                <h2>🔐 权限标记不正确</h2>

                <div class="error-box">
                    <h4>❌ 接口权限标记错误</h4>
                    <p><strong>症状：</strong>公开接口显示为需要认证，或认证接口显示为公开</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查 permission.js 配置：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
// config/permission.js
{
  name: 'PublicFunction',
  apiPermissions: [
    // 单个接口
    { type: 'application', controller: 'product', action: 'search' },
    
    // 整个控制器
    ...createDefaultPermissions({ type: 'application', controller: 'news' })
  ]
}
                        </pre>
                    </div>

                    <p>2. 确认控制器和动作名称正确：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 检查控制器文件名和方法名
ls -la api/*/controllers/
cat api/product/controllers/product.js
                        </pre>
                    </div>

                    <p>3. 重启服务使权限配置生效：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
                        <pre>
# 权限配置修改后需要重启服务
npm run dev
                        </pre>
                    </div>
                </div>
            </section>

            <section class="section" id="ui-issues">
                <h2>🎨 界面问题</h2>

                <div class="error-box">
                    <h4>❌ 页面样式错误或布局混乱</h4>
                    <p><strong>症状：</strong>文档页面显示异常，CSS 样式未加载</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 检查静态资源路径：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 访问静态资源测试
curl -I http://localhost:8108/plugins/swagger/static/swagger-ui/5.0.0/swagger-ui.css
                    </div>

                    <p>2. 清理浏览器缓存：</p>
                    <ul>
                        <li>按 Ctrl+F5 强制刷新</li>
                        <li>清除浏览器缓存和 Cookie</li>
                        <li>尝试无痕浏览模式</li>
                    </ul>
                </div>

                <div class="error-box">
                    <h4>❌ 下拉选择器不工作</h4>
                    <p><strong>症状：</strong>模块选择器点击无反应或跳转错误</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>检查浏览器控制台错误：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 打开浏览器开发者工具
# 查看 Console 标签页
# 查找 JavaScript 错误信息
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 移动端显示异常</h4>
                    <p><strong>症状：</strong>在手机或平板上页面布局错乱</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>确认视口配置和响应式样式：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 检查是否有视口元标签
&lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;
                    </div>
                </div>
            </section>

            <section class="section" id="performance-issues">
                <h2>🚀 性能问题</h2>

                <div class="error-box">
                    <h4>❌ 文档加载缓慢</h4>
                    <p><strong>症状：</strong>访问文档页面响应时间过长</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>1. 启用生产模式缓存：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 设置环境变量
NODE_ENV=production
                    </div>

                    <p>2. 减少扫描范围：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
// 在 swagger.js 中增加忽略配置
ignore: {
  controllers: ['debug-*', 'test-*'],
  paths: ['/internal/*']
}
                    </div>
                </div>

                <div class="error-box">
                    <h4>❌ 内存占用过高</h4>
                    <p><strong>症状：</strong>服务器内存使用率持续增长</p>
                </div>

                <div class="tip-box">
                    <h4>✅ 解决方案</h4>
                    <p>检查缓存清理机制：</p>
                    <div class="code-block">
                        <button class="copy-button" onclick="copyCode(this)">复制</button>
# 监控内存使用
node --inspect app.js

# 或使用 PM2 监控
pm2 monit
                    </div>
                </div>
            </section>

            <section class="section" id="diagnostic-checklist">
                <h2>🔍 诊断检查清单</h2>

                <h3>基础检查</h3>
                <ul>
                    <li>确认 Node.js 版本 >= 16.20.0</li>
                    <li>确认 npm 版本 >= 6.0.0</li>
                    <li>确认项目使用 Strapi 4.x</li>
                    <li>确认插件目录存在于 plugins/swagger</li>
                    <li>确认 package.json 依赖完整</li>
                </ul>

                <h3>配置检查</h3>
                <ul>
                    <li>检查 config/middleware.js 中插件启用状态</li>
                    <li>检查 config/swagger.js 配置文件存在</li>
                    <li>检查 API 路由文件格式正确</li>
                    <li>检查控制器方法正确导出</li>
                    <li>检查权限配置文件 permission.js</li>
                </ul>

                <h3>运行时检查</h3>
                <ul>
                    <li>检查服务器启动日志无错误</li>
                    <li>检查 /api-docs 端点可访问</li>
                    <li>检查 /api-docs/swagger.json 返回有效 JSON</li>
                    <li>检查浏览器控制台无 JavaScript 错误</li>
                    <li>检查网络请求无 404 错误</li>
                </ul>

                <h3>环境检查</h3>
                <ul>
                    <li>确认环境变量设置正确</li>
                    <li>确认端口没有被其他服务占用</li>
                    <li>确认防火墙配置允许访问</li>
                    <li>确认反向代理配置正确（如果使用）</li>
                </ul>

                <div class="tip-box">
                    <h4>💡 调试技巧</h4>
                    <p>使用浏览器开发者工具的网络标签页查看所有 HTTP 请求，找出失败的请求并分析原因。</p>
                </div>
            </section>

            <section class="section" id="support">
                <h2>🆘 获取支持</h2>

                <h3>自助诊断</h3>
                <div class="table-container">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>问题类型</th>
                                <th>检查方法</th>
                                <th>常见原因</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="symptom">无法访问文档</td>
                                <td>curl http://localhost:8108/api-docs</td>
                                <td class="cause">插件未启用或端口错误</td>
                            </tr>
                            <tr>
                                <td class="symptom">接口列表为空</td>
                                <td>检查路由和控制器配置</td>
                                <td class="cause">路由配置错误或控制器未导出</td>
                            </tr>
                            <tr>
                                <td class="symptom">样式显示异常</td>
                                <td>检查静态资源加载</td>
                                <td class="cause">CSS 文件加载失败</td>
                            </tr>
                            <tr>
                                <td class="symptom">模块过滤失效</td>
                                <td>重启服务器清除缓存</td>
                                <td class="cause">require 缓存未清理</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <h3>收集诊断信息</h3>
                <p>如需寻求技术支持，请提供以下信息：</p>

                <div class="code-block">
                    <button class="copy-button" onclick="copyCode(this)">复制</button>
# 系统信息
node --version
npm --version
cat package.json | grep strapi

# 插件信息
ls -la plugins/swagger/
cat plugins/swagger/package.json

# 配置信息
cat config/middleware.js
cat config/swagger.js

# 错误日志
npm run dev 2>&1 | grep -i error
                </div>

                <h3>联系支持</h3>
                <div class="warning-box">
                    <h4>📞 技术支持</h4>
                    <p>提交问题时请附上完整的错误日志、配置文件和复现步骤，这将有助于快速定位和解决问题。</p>
                </div>

                <ul>
                    <li><strong>项目文档：</strong>查看 README.md 和相关技术文档</li>
                    <li><strong>GitHub Issues：</strong>搜索或提交新的问题报告</li>
                    <li><strong>技术团队：</strong>联系项目维护者</li>
                    <li><strong>社区论坛：</strong>参与技术讨论和经验分享</li>
                </ul>
            </section>
        </main>
    </div>

    <script src="scripts.js"></script>
    <script>
        function copyCode(button) {
            const codeBlock = button.nextSibling;
            const text = codeBlock.textContent.trim();

            navigator.clipboard.writeText(text).then(function() {
                button.textContent = '成功';
                button.classList.add('copied');

                setTimeout(function() {
                    button.textContent = '复制';
                    button.classList.remove('copied');
                }, 2000);
            }).catch(function(err) {
                console.error('复制失败:', err);
            });
        }

        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 检查清单交互
        document.querySelectorAll('.checklist li').forEach(item => {
            item.addEventListener('click', function() {
                this.style.opacity = this.style.opacity === '0.5' ? '1' : '0.5';
                this.style.textDecoration = this.style.textDecoration === 'line-through' ? 'none' : 'line-through';
            });
        });
    </script>
</body>
</html>
