const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const { parseQunInfo, getQunGroup, paikeTagId, dupTagId, todoTagId } = require('../api/xiaoyun/utils/qunGetUtil')
const { getToken } = require('../api/xiaoyun/utils/goToken')
const { sendRobotMsg, logger } = require('./utils')
const moment = require('moment/moment')
const DB_URL = (process.env.NODE_ENV !== 'production')
  ? 'mongodb://localhost:27017/testwly-boss-back'
  // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
  : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

let db, dbClient

(async function () {
  dbClient = await MongoClient.connect(DB_URL)
  db = dbClient.db()

  try {
    let start = Date.now()
    logger('sync start')
    let token = await getToken('ask')

    const res = await axios.get(`http://ask.yunxiao.io/qun/list?__go_token=${token}`)
    const list = res.data.filter(item => !item.closed)
    const qunMap = {}
    for (const qun of list) {
      qunMap[qun._id] = qun
    }
    addMsg('群总数量', res.data.length)
    addMsg('解散数量', res.data.length - list.length)

    await main(list, qunMap)
    logger('sync end')
    logger(`sync cost ${(Date.now() - start) / 1000}s`)
  } catch (e) {
    logger(e.stack || 'err')
  } finally {
    setTimeout(() => {
      process.exit(1)
    }, 5000)
  }
})()

let isSendMsg = false

async function main (data, qunMap) {
  logger('sync qun start')
  const qunUserList = await updateQunUsers(data)
  logger('用户总数量', qunUserList.length)
  const qunList = await updateQun(data, qunUserList)
  logger('sync qun end')
  await updateSchoolQunUserCount(qunUserList, qunList)
  const dupQunList = await updateDupQunList(qunList)
  addMsg('重复群数量', dupQunList.length)
  if (qunList && qunList.length > 0) {
    isSendMsg = true
  }
  if (qunList && qunList.length > 0) {
    logger('sync customer-service start')
    let memberGroup = await db.collection('member-group').find({}).toArray()
    let userGroupMap = {}

    memberGroup.forEach(item => {
      item.members.forEach(member => {
        if (userGroupMap[member.toString()]) {
          userGroupMap[member.toString()].push(item._id)
        } else {
          userGroupMap[member.toString()] = [item._id]
        }
      })
    })
    await updateCustomerQun(userGroupMap)
    await updateQunServiceManager(userGroupMap)
    await sendRobotMsg(msgs.join('\n'))
    logger('sync customer-service end')
    // logger('sync qun-check start')
    // await updateQunCheck(db)
    // logger('sync qun-check end')
    // logger('sync qun-statistics start')
    // await statistic(db, userGroupMap)
    // logger('sync qun-statistics end')
  } else {
    logger('no qun')
  }
}

const schoolCreatorMap = {
  '1688855828587333': '客户服务助手',
  '1688854305554904': '好分数服务总监',
  '1688857577566284': '客户服务🛎︎',
}

const qunCreatorMap = {
  ...schoolCreatorMap,
  '1688856746625841': '机器人(只做群管理，无法回答问题)',
}

async function getUsersInfo (ids) {
  let token = await getToken('nids')
  // console.log('---', token, ids)
  let idMap = {}
  const res = await axios.post(`http://nids.yunxiao.io/user/info?__go_token=${token}`, {
    id: ids
  })

  res.data.forEach(item => {
    idMap[item[0]] = item
  })
  return idMap
}

async function getOrgUserIds () {
  let token = await getToken('org')
  let res = await axios.get(`http://org.yunxiao.io/node/list_all?__go_token=${token}`)
  let userIds = []
  let agentUserIds = []
  if (res.data) {
    getOrgGroupUsers(res.data, userIds, agentUserIds)
  }

  let nidToken = await getToken('nids')
  res = await axios.post(`http://nids.yunxiao.io/user/info?__go_token=${nidToken}`, {
    id: userIds
  })

  let userList = res.data.map(item => item[0])

  res = await axios.post(`http://nids.yunxiao.io/user/info?__go_token=${nidToken}`, {
    id: agentUserIds
  })
  let agentList = res.data.map(item => item[0])
  console.log('内部员工', userList.length)
  console.log('经销商', agentList.length)
  return {
    internalIds: userList,
    agentIds: agentList
  }
}

function getOrgGroupUsers (groupMap, userIds, agentUserIds, teamNames = []) {
  for (const orgKey of Object.keys(groupMap)) {
    let data = groupMap[orgKey]
    const id = data[0]
    // const leader = data[1]
    const group = data[2]

    if (group && Object.keys(group).length) {
      getOrgGroupUsers(group, userIds, agentUserIds, teamNames.concat(orgKey))
    }
    const userMap = data[3]
    for (const userId of Object.keys(userMap)) {
      let isAgent = teamNames.length >= 1 && teamNames[1] === '合作伙伴'
      if (!isAgent) {
        if (!userIds.includes(userId)) {
          userIds.push(userId)
        }
      } else {
        if (!userIds.includes(userId) && !agentUserIds.includes(userId)) {
          agentUserIds.push(userId)
        }
      }
    }
  }
}

async function updateQunUsers (data) {
  let oldUsers = await db.collection('qun-user').find({}).toArray()
  const oldUserMap = {}
  for (const oldUser of oldUsers) {
    oldUserMap[oldUser.userId] = oldUser
  }
  let allUsers = []
  let { internalIds, agentIds } = await getOrgUserIds()
  for (const info of data) {
    const users = Object.keys(info.members)
    if (users && users.length > 0) {
      users.forEach(user => {
        let userIndex = allUsers.findIndex(e => e.userId === user)
        if (userIndex === -1) {
          allUsers.push({ userId: user, name: qunCreatorMap[user] || user, qunCount: 1 })
        } else {
          allUsers[userIndex].qunCount += 1
        }
      })
    }
  }
  let ids = allUsers.map(item => item.userId)

  let userInfos = await getUsersInfo(ids)
  allUsers = allUsers.map(item => {
    if (userInfos[item.userId]) {
      item.name = userInfos[item.userId][1]
      item.avatar = userInfos[item.userId][2]
    }
    return item
  })
  let qunCountGt1Count = 0
  let bulkWriteArray = []
  for (const user of allUsers) {
    let exist = oldUserMap[user.userId]
    const insertUser = {
      userId: user.userId,
      name: user.name,
      avatar: user.avatar,
      qunCount: user.qunCount
    }

    let role = null
    if (internalIds.includes(user.userId)) {
      role = '内部人员'
    } else if (agentIds.includes(user.userId)) {
      role = '经销商'
    }

    if (exist) {
      insertUser.role = exist.role || role
      insertUser.isDeleted = null
      bulkWriteArray.push({ updateOne: { filter: { _id: exist._id }, update: { $set: insertUser } } })
      delete oldUserMap[user.userId]
    } else {
      insertUser.role = role
      insertUser.isDeleted = null
      bulkWriteArray.push({ insertOne: { document: insertUser } })
    }
    if (insertUser.qunCount > 1 && insertUser.role !== '内部人员') {
      qunCountGt1Count += 1
    }
  }

  let deletedUserList = Object.values(oldUserMap)
  if (deletedUserList.length) {
    addMsg('已退出群的用户', deletedUserList.length)
    for (const user of deletedUserList) {
      bulkWriteArray.push({
        updateOne: {
          filter: { _id: user._id },
          update: {
            $set: {
              qunCount: null,
              schoolQunCount: null,
              isDeleted: true
            }
          }
        }
      })
    }
  }

  addMsg('用户所在群大于1数量', qunCountGt1Count)
  if (qunCountGt1Count > 0) {
    isSendMsg = true
  }
  if (bulkWriteArray.length > 0) {
    const results = await db.collection('qun-user').bulkWrite(bulkWriteArray)
    console.log('修改用户结果', results)
    return await db.collection('qun-user').find({ isDeleted: { $ne: true } }).toArray()
  }
  return []
}

async function updateQun (data, userList) {
  let qunList = await db.collection('xiaoyun-qun').find({}).toArray()
  let bulkWriteArray = []
  let qunMap = {}
  for (const qun of qunList) {
    qunMap[qun.qunId] = qun
  }
  for (const info of data) {
    const id = info._id
    let originQunInfo = qunMap[id]

    let defaultFields = parseQunInfo(info, originQunInfo, userList)

    if (originQunInfo) {
      bulkWriteArray.push({ updateOne: { filter: { _id: originQunInfo._id }, update: { $set: defaultFields } } })
      delete qunMap[id]
    } else {
      bulkWriteArray.push({ insertOne: { document: defaultFields } })
    }
  }

  let closedQunList = Object.values(qunMap)
  if (closedQunList.length) {
    addMsg('已解散的群数量', closedQunList.length)
    for (const qun of closedQunList) {
      bulkWriteArray.push({ updateOne: { filter: { _id: qun._id }, update: { $set: { isDeleted: true } } } })
    }
  }

  if (bulkWriteArray.length > 0) {
    const results = await db.collection('xiaoyun-qun').bulkWrite(bulkWriteArray)
    console.log(results)
    return await db.collection('xiaoyun-qun').find({ isDeleted: { $ne: true } }).toArray()
  } else {
    return []
  }
}

async function updateSchoolQunUserCount (userList, qunList) {
  let schoolQunMap = {}
  for (const qun of qunList) {
    if (qun.qunGroup !== 1) continue
    let userIds = qun.qunUsers.map(user => user.toString())
    for (const userId of userIds) {
      if (schoolQunMap[userId]) {
        schoolQunMap[userId] += 1
      } else {
        schoolQunMap[userId] = 1
      }
    }
  }

  let qunCount = 0
  let bulkWriteArray = []
  for (const user of userList) {
    let count = schoolQunMap[user._id.toString()] || 0
    if (count > 0) {
      if (count > 1 && user.role !== '内部人员') {
        qunCount++
      }
      bulkWriteArray.push({
        updateOne: {
          filter: { _id: user._id },
          update: { $set: { schoolQunCount: schoolQunMap[user._id.toString()] } }
        }
      })
    }
  }
  addMsg('用户所在学校群大于1数量', qunCount)

  if (bulkWriteArray.length > 0) {
    const results = await db.collection('qun-user').bulkWrite(bulkWriteArray)
    console.log(results)
  }
}

async function updateDupQunList (data) {
  let customerNameMap = {}
  const dupList = []
  for (const info of data) {
    if (info.qunTags && info.qunTags.some(e => e.toString() === todoTagId)) {
      continue
    }

    let customerName = info.parseCustomerName

    if (customerName) {
      // todo 重复群
      if (customerNameMap[customerName]) {
        customerNameMap[customerName].push(info)
      } else {
        customerNameMap[customerName] = [info]
      }
    }
  }

  for (const name in customerNameMap) {
    if (customerNameMap[name].length > 1) {
      dupList.push(...customerNameMap[name])
    }
  }

  const bulkWriteArray = []
  for (const dupItem of dupList) {
    const qunTags = dupItem.qunTags || []
    if (!qunTags.some(e => e.toString() === dupTagId)) {
      qunTags.push(ObjectId(dupTagId))
    }
    bulkWriteArray.push({ updateOne: { filter: { _id: dupItem._id }, update: { $set: { qunTags: qunTags } } } })
  }
  if (bulkWriteArray.length > 0) {
    const results = await db.collection('xiaoyun-qun').bulkWrite(bulkWriteArray)
    console.log(results)
    return dupList
  }
  return []
}

async function updateCustomerQun (userGroupMap) {
  const qunList = await db.collection('xiaoyun-qun').find({ isDeleted: { $ne: true } }).toArray()

  // 已匹配群，则不会重新匹配， 后续可手动调整。
  // todo 对于已解散群， 原有客户关联处理。
  const customers = await db.collection('customer-service').find({
    'qun': { $eq: null },
    'deleted': { $ne: true },
    'isTest': { $ne: true }
  }).toArray()
  logger('---- 未匹配群客户数量 ----', customers.length)
  let bulkWriteArray = []
  let updateMingdaoQueue = []
  let qunBulkWriteArray = []
  const unMatchQunList = qunList.filter(e => e.qunGroup === 1 && e.parseCustomerName && !e.customer && !e.qunTags.some(i => i.toString() === todoTagId))
  logger('---- 未被匹配有效群数量 ----', unMatchQunList.length)
  const unMatchQunMap = {}
  for (const qun of unMatchQunList) {
    if (qun.parseCustomerName) {
      if (!unMatchQunMap[qun.parseCustomerName]) {
        unMatchQunMap[qun.parseCustomerName] = qun
      }
    }
  }
  for (const customer of customers) {
    // 通过学校名称匹配群名。
    let qunInfo = unMatchQunMap[customer.name]
    if (qunInfo) {
      let defaultFields = {
        qun: qunInfo._id,
        qunId: qunInfo.qunId,
      }

      let qunCustomer = {
        customer: customer._id
      }
      qunBulkWriteArray.push({ updateOne: { filter: { _id: qunInfo._id }, update: { $set: qunCustomer } } })
      bulkWriteArray.push({
        updateOne: {
          filter: { customerId: customer.customerId },
          update: { $set: defaultFields }
        }
      })
      updateMingdaoQueue.push({
        type: 'school',
        customerId: customer.customerId,
        customer: customer._id,
        status: 'prepared',
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
    }
  }

  let unMatchHasUserQunCount = 0
  let unMatchSchoolQunList = unMatchQunList.filter(e => !e.qunTags.some(i => i.toString() === paikeTagId))
  let unMatchPaikeQunList = unMatchQunList.filter(e => e.qunTags.some(i => i.toString() === paikeTagId))
  for (const qun of unMatchSchoolQunList) {
    if (!qun.qunTags.some(i => i.toString() === paikeTagId) && qun.qunExternalUserCount > 0) {
      unMatchHasUserQunCount++
    }
  }

  let unMatchHasUserPaikeQunCount = 0
  for (const qun of unMatchPaikeQunList) {
    if (qun.qunTags.some(i => i.toString() === paikeTagId) && qun.qunExternalUserCount > 0) {
      unMatchHasUserPaikeQunCount++
    }
  }
  addMsg('未匹配学校群数量', unMatchSchoolQunList.length)
  addMsg('未匹配学校群数量（含外部成员）', unMatchHasUserQunCount)
  if (unMatchSchoolQunList.length > 0 || unMatchHasUserQunCount > 0) {
    isSendMsg = true
  }
  // addMsg('未匹配排课群数量', unMatchPaikeQunList.length)
  // addMsg('未匹配排课群数量（含外部成员）', unMatchHasUserPaikeQunCount)

  addMsg('新增匹配学校数量', bulkWriteArray.length)
  if (bulkWriteArray.length > 0) {
    isSendMsg = true
  }
  if (bulkWriteArray.length > 0) {
    const results = await db.collection('customer-service').bulkWrite(bulkWriteArray)
    // 将新增匹配群，加入到队列表中，更新明道云
    await db.collection('mingdao-queue').insertMany(updateMingdaoQueue)
    console.log(results)
  }
  if (qunBulkWriteArray.length > 0) {
    const results = await db.collection('xiaoyun-qun').bulkWrite(qunBulkWriteArray)
    console.log(results)
  }
}

async function updateQunServiceManager (userGroupMap) {
  let qunList = await db.collection('xiaoyun-qun').find({ isDeleted: { $ne: true } }).toArray()
  let bulkWriteArray = []
  const customers = await db.collection('customer-service').find({
    'qun': { $ne: null },
    'deleted': { $ne: true },
    'isTest': { $ne: true }
  }).toArray()
  const customerMap = {}
  for (const customer of customers) {
    customerMap[customer.qun.toString()] = customer
  }
  let tagMap = await getTagMap()
  let schoolTags = ['体验校', '临期校（1个月内）', '付费校（1-3个月）', '付费校（＞3个月）', '历史付费校'].map(e => tagMap[e])

  for (const qun of qunList) {
    let customer = customerMap[qun._id.toString()]
    if (customer) {
      const fields = {
        schoolId: customer.schoolId,
        serviceManager: customer.directServiceManager || null,
        serviceManagerTeam: customer.directServiceManager ? (userGroupMap[customer.directServiceManager.toString()] || null) : null,
        salesManager: customer.directSalesManager || null,
        salesManagerTeam: customer.directSalesManager ? (userGroupMap[customer.directSalesManager.toString()] || null) : null,
      }
      let qunTags = qun.qunTags || []
      qunTags = qunTags.filter(e => !schoolTags.includes(e.toString()))
      let type = getCustomerType(customer)
      if (type) {
        let typeTagId = tagMap[type]
        qunTags.push(ObjectId(typeTagId))
      }
      fields.qunTags = qunTags
      bulkWriteArray.push({ updateOne: { filter: { _id: qun._id }, update: { $set: fields } } })
    } else {
      const fields = {
        schoolId: null,
        serviceManager: null,
        serviceManagerTeam: null,
        salesManager: null,
        salesManagerTeam: null
      }
      bulkWriteArray.push({ updateOne: { filter: { _id: qun._id }, update: { $set: fields } } })
    }
  }
  console.log('---- 已匹配客户含服务经理数量 ----', bulkWriteArray.length)
  if (bulkWriteArray.length > 0) {
    const results = await db.collection('xiaoyun-qun').bulkWrite(bulkWriteArray)
    console.log(results)
  }
}

const msgs = []

function addMsg (name, value) {
  logger(name, value)
  msgs.push(`${name}: ${value}`)
}

async function getTagMap () {
  const tagList = await db.collection('qun-tag').find({}).toArray()
  const tagMap = {}
  for (const tag of tagList) {
    tagMap[tag.name] = tag._id.toString()
  }
  return tagMap
}

function getCustomerType (customer) {
  let type = null
  const usages = customer.usages || []
  const yjUsage = usages.find(e => e.appVersion && e.appVersion.product_category === 'saas')

  if (!yjUsage) {
    return ''
  }
  let yjVersion = yjUsage.appVersion.version_name
  let today = moment().startOf('days')
  // 获取3个月前的时间
  let threeMonthsAfter = moment().add(3, 'months')
  let oneMonthAfter = moment().add(1, 'months')
  let yjStartTime = moment(yjUsage.begin_time)
  let yjEndTime = moment(yjUsage.end_time)

  let isTrial = yjUsage.is_trial
  let diff = yjEndTime.diff(yjStartTime, 'days')

  // 已过期
  //    体验版：时间差， 7天试用，其他历史付费
  //    其他版本：历史付费
  // 未过期
  //    体验版：体验校
  //    其他版本：三个月内，临期校，其他 当前付费校

  if (yjEndTime.isBefore(today)) {
    // 已过期
    if (yjVersion === '体验版') {
      if (diff < 40) {
        type = '体验校'
      } else if (yjEndTime.isAfter(moment('2022-02-01')) && !isTrial) {
        type = '历史付费校'
      } else {
        type = '体验校'
      }
    } else {
      if (diff < 40) {
        type = '体验校'
      } else {
        type = '历史付费校'
      }
    }
  } else {
    // 未过期
    if (yjVersion === '体验版') {
      type = '体验校'
    } else if (diff < 40) {
      type = '体验校'
    } else if (yjEndTime.isBetween(today, oneMonthAfter, null, '[]')) {
      type = '临期校（1个月内）'
    } else if (yjEndTime.isBetween(oneMonthAfter, threeMonthsAfter, null, '[]')) {
      type = '付费校（1-3个月）'
    } else {
      type = '付费校（＞3个月）'
    }
  }

  return type
}
