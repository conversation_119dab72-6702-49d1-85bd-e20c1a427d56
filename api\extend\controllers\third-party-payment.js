const { CurdRouter } = require('accel-utils')
const axios = require('axios')
const uuid = require('uuid')
const _ = require('lodash');
const { getPayment, getPaymentConfigList } = require('../services/wx-payments')

const curdRouter = new CurdRouter('third-party-payment')

async function wxPay(ctx) {
    const { type } = ctx.params;
    let { 'access-key': key } = _.assign({}, ctx.request.query, ctx.request.body, ctx.request.header);
    if (ctx.state?.user?.id && !key) key = strapi.config.server.wlyConfig.accessKey;
    if (!key) return ctx.wrapper.error('PARAMETERS_ERROR', 'key错误');
    let { amount, attach, merchantId, description, remark } = ctx.request.body;
    if (!merchantId) return ctx.wrapper.error('PARAMETERS_ERROR', 'merchantId参数错误');
    if (!amount) return ctx.wrapper.error('PARAMETERS_ERROR', '金额错误');
    amount = +amount;
    if (!amount || amount < 0) return ctx.wrapper.error('PARAMETERS_ERROR', '金额错误');

    // 发起支付
    const payment = await getPayment(merchantId, key);
    const wxPayConfigList = await getPaymentConfigList(true);
    let source;
    for (const config of wxPayConfigList) {
        if (config.mchid === merchantId) source = _.pick(config.accessKeys.find(ak => ak.status && ak.key === key), ['id', 'name']);
        if (source?.id) break;
    }
    const tradeNo = _genOrderUUID();

    let payThrough;
    switch (type) {
        case 'native':
            payThrough = '微信支付-二维码';
            break
        // case 'h5':
        //     payThrough = '微信支付-H5';
        //     break
        // case 'jsapi':
        //     payThrough = '微信支付-JSAPI';
        //     break
        // case 'app':
        //     payThrough = '微信支付-APP';
        //     break
        default:
            return ctx.wrapper.error('PARAMETERS_ERROR', '该支付方式暂不支持');
    }

    let result = await payment[type]({
        description: description || '云校支付',
        out_trade_no: tradeNo,
        amount: {
            total: amount
        }
    });
    if (result.status !== 200) {
        return ctx.wrapper.error('HANDLE_ERROR', '微信支付错误');
    }
    let resultData = result.data && JSON.parse(result.data);

    const paymentInfo = await strapi.query('third-party-payment').create({
        source: source,
        attach: attach,
        remark: remark,
        amount: amount,
        status: '发起支付',
        payThrough: payThrough,
        tradeNo: tradeNo,
        merchantId: merchantId,
    });
    const resObj = {
        qrUrl: resultData.code_url,
        prepayId: resultData.prepay_id,
        h5Url: resultData.h5_url,
        id: paymentInfo.id,
    };
    return ctx.wrapper.succ(resObj)
}

// 生成商户订单号（UUID）
function _genOrderUUID() {
    return uuid.v4().split('-').join('');               // 商户系统内部的订单号,32个字符内、可包含字母
}

async function findByIds(ctx) {
    const { ids } = ctx.request.body;
    const paymentInfos = await strapi.query('third-party-payment').find({ id: { $in: ids }, });
    return ctx.wrapper.succ({ paymentInfos })
};

async function findByQuery(ctx) {
    let { 'access-key': key, limit = 10, start = 0, status } = _.assign({}, ctx.request.query, ctx.request.body, ctx.request.header);
    if (ctx.state?.user?.id && !key) key = strapi.config.server.wlyConfig.accessKey;
    if (!key) return ctx.wrapper.error('PARAMETERS_ERROR', 'key错误');

    const wxPayConfigList = await getPaymentConfigList(true);
    let sourceId;
    for (const config of wxPayConfigList) {
        sourceId = config.accessKeys.find(ak => ak.status && ak.key === key)?.id;
        if (sourceId) break;
    }
    if (!sourceId) return ctx.wrapper.error('PARAMETERS_ERROR', 'key错误');

    let cond = { 'source.id': sourceId }
    if (status) { cond.status = status; }
    const total = await strapi.query('third-party-payment').model.countDocuments(cond);
    const paymentIds = await strapi.query('third-party-payment').model.find(cond, { id: 1 }).limit(+limit).skip(+start).sort({ _id: -1 });
    const paymentInfos = await strapi.query('third-party-payment').find({ id: { $in: paymentIds } });

    return ctx.wrapper.succ({ total: total, list: paymentInfos })
};

// 提交支付回调 微信支付 QR
async function wxPayNotification(ctx) {
    const { mchid } = ctx.params;
    const { resource } = ctx.request.body;
    const payment = await getPayment(mchid);

    const notification = payment.decodeResource(resource);
    const tradeNo = notification.out_trade_no;
    if (notification.trade_state !== 'SUCCESS') {
        return ctx.wrapper.error('HANDLE_ERROR', 'trade state err');
    }

    const paymentInfo = await strapi.query('third-party-payment').findOne({ tradeNo: tradeNo, });
    if (!paymentInfo) {
        return ctx.wrapper.error('HANDLE_ERROR', 'not found trade');
    }
    if (paymentInfo.status !== '发起支付') {
        return ctx.wrapper.error('HANDLE_ERROR', 'status err');
    }
    if (notification.amount && notification.amount.total !== paymentInfo.amount) {
        return ctx.wrapper.error('HANDLE_ERROR', 'amount err');
    }
    await strapi.query('third-party-payment').update({ id: paymentInfo.id }, {
        status: '支付成功',
        payTime: new Date(),
    });

    return ctx.wrapper.succ({})
};

async function wxRefund(ctx) {
    let { 'access-key': key } = _.assign({}, ctx.request.query, ctx.request.body, ctx.request.header);
    if (ctx.state?.user?.id && !key) key = strapi.config.server.wlyConfig.accessKey;
    if (!key) return ctx.wrapper.error('PARAMETERS_ERROR', 'key错误');
    let { amount, id, attach, reason, remark } = ctx.request.body;
    if (!amount) return ctx.wrapper.error('PARAMETERS_ERROR', '金额错误');

    amount = +amount;
    if (!amount || amount < 0) return ctx.wrapper.error('PARAMETERS_ERROR', '金额错误');

    const paymentInfo = await strapi.query('third-party-payment').findOne({ id: id, });
    if (paymentInfo.status !== '支付成功') return ctx.wrapper.error('PARAMETERS_ERROR', '订单状态异常');

    const merchantId = paymentInfo.merchantId;
    let totalRefundAmount = paymentInfo.refundRecord.reduce((acc, current) => {
        return acc + (current.status === '退款成功' ? current.amount : 0);
    }, 0);
    if (totalRefundAmount + amount > paymentInfo.amount) return ctx.wrapper.error('PARAMETERS_ERROR', '退费金额超出');

    // 发起支付
    const payment = await getPayment(merchantId, key);
    const refundNo = _genOrderUUID();
    let result = await payment.refund({
        out_trade_no: paymentInfo.tradeNo,
        out_refund_no: refundNo,
        reason: reason,
        notify_url: `${strapi.config.server.payment.wechatPay.refund_notify_url}/${merchantId}`,

        amount: {
            currency: 'CNY',
            total: paymentInfo.amount,
            refund: amount
        }
    });
    if (result.status !== 200) {
        return ctx.wrapper.error('HANDLE_ERROR', '微信退费失败');
    }
    // qrUrl = result.data && JSON.parse(result.data).code_url;
    let resultData = result.data && JSON.parse(result.data);

    const refundInfo = await strapi.query('third-party-refund').create({
        attach: attach,
        remark: remark,
        reason: reason,
        merchantId: merchantId,
        amount: amount,

        status: '发起退款',
        tradeNo: paymentInfo.tradeNo,
        refundNo: refundNo,
        paymentRecord: paymentInfo.id,
    });

    const resObj = {
        // data: resultData,
        id: refundInfo.id,
    };
    return ctx.wrapper.succ(resObj)
}

// 提交支付回调 微信支付 QR
async function wxRefundNotification(ctx) {
    const { mchid } = ctx.params;
    const { resource } = ctx.request.body;
    const payment = await getPayment(mchid);

    const notification = payment.decodeResource(resource);
    const refundNo = notification.out_refund_no;
    if (notification.refund_status !== 'SUCCESS') {
        return ctx.wrapper.error('HANDLE_ERROR', 'refund state err');
    }

    const refundInfo = await strapi.query('third-party-refund').findOne({ refundNo: refundNo, });
    if (!refundInfo) {
        return ctx.wrapper.error('HANDLE_ERROR', 'not found trade');
    }
    if (refundInfo.status !== '发起退款') {
        return ctx.wrapper.error('HANDLE_ERROR', 'status err');
    }
    if (notification.amount && notification.amount.refund !== refundInfo.amount) {
        return ctx.wrapper.error('HANDLE_ERROR', 'amount err');
    }
    await strapi.query('third-party-refund').update({ id: refundInfo.id }, {
        status: '退款成功',
        refundTime: new Date(),
    });

    return ctx.wrapper.succ({})
};

module.exports = {
    wxPay,
    findByIds,
    findByQuery,
    wxPayNotification,
    wxRefund,
    wxRefundNotification,
    ...curdRouter.createHandlers()
}
