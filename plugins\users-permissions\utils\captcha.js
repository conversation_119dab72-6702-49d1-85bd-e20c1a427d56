const tencentcloud = require('tencentcloud-sdk-nodejs')
const captchaConfig = strapi.config.server.captcha

// 导入对应产品模块的 client models
const CaptchaClient = tencentcloud.captcha.v20190722.Client;
const clientConfig = {
  // 腾讯云认证信息
  credential: {
    secretId: captchaConfig.secretId,
    secretKey: captchaConfig.secretKey,
  },
  // 产品地域
  region: 'ap-shanghai',
  // 可选配置实例
  profile: {
    signMethod: 'HmacSHA256', // 签名方法
    httpProfile: {
      reqMethod: 'POST', // 请求方法
      reqTimeout: 30, // 请求超时时间，默认60s
    },
  },
}


async function verifyCaptcha (ticket, randStr, userIp) {
  // 实例化要请求产品(以cvm为例)的client对象
  const client = new CaptchaClient(clientConfig)
  const params = {
    CaptchaType: 9,
    CaptchaAppId: captchaConfig.CaptchaAppId,
    AppSecretKey: captchaConfig.AppSecretKey,
    // 前端回调函数返回的用户验证票据
    Ticket: ticket,
    // 业务侧获取到的验证码使用者的外网 IP
    UserIp: userIp,
    // 前端回调函数返回的随机字符串
    Randstr: randStr,
  }
  return await client.DescribeCaptchaResult(params)
}

module.exports = {
  verifyCaptcha
}
