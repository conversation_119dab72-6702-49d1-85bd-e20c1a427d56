const { MongoClient, ObjectId } = require('mongodb')
const axios = require('axios')
const moment = require('moment/moment')
const _ = require('lodash');
const DB_URL = (process.env.NODE_ENV !== 'production')
    ? 'mongodb://localhost:27017/testwly-boss-back'
    // ? 'mongodb://WLY:<EMAIL>:6010,n01.devrs.jcss.iyunxiao.com:6010,n02.devrs.jcss.iyunxiao.com:6010/testwly-boss?replicaSet=ReplsetTest&readPreference=primaryPreferred'
    : 'mongodb://wly_write:<EMAIL>:6010,n01.rs00.iyunxiao.com:6010,n02.rs00.iyunxiao.com:6010/wly_boss?replicaSet=Replset00&readPreference=primary'

let db, dbClient

let oneMonthsBefore = moment().startOf('d').subtract(1, 'M').toDate()
let towWeeksBefore = moment().startOf('d').subtract(14, 'd').toDate()
let defaultLimit = 10 * 10000
const rules = [{
    collectionName: 'SystemRequestLog',
    query: {
        requestAt: { $lt: oneMonthsBefore },
    },
}, {
    collectionName: 'mingdao-queue',
    query: {
        createdAt: { $lt: towWeeksBefore },
    },
}, {
    collectionName: 'huoban-queue',
    query: {
        createdAt: { $lt: oneMonthsBefore },
    },
}, {
    collectionName: 'huoban-msg-queue',
    query: {
        createdAt: { $lt: towWeeksBefore },
    },
}];

(async function () {
    dbClient = await MongoClient.connect(DB_URL)
    db = dbClient.db()
    try {
        let start = Date.now()
        logger('sync start')
        await main()
        logger('sync end')
        logger(`sync cost ${(Date.now() - start) / 1000}s`)
    } catch (e) {
        logger(e.stack || 'err')
    } finally {
        await dbClient.close()
        setTimeout(() => {
            process.exit(1)
        }, 5000)
    }
})()

async function main() {
    logger('deleteLogData start')
    await deleteLogData()
    logger('deleteLogData end')
}

async function deleteLogData() {
    for (const rule of rules) {
        let result = await db.collection(rule.collectionName).deleteMany(rule.query);
        console.log(result)
    }
}

function logger(...msg) {
    const dateStr = moment().format('YYYY-MM-DD HH:mm:ss SSS')
    console.log(`${dateStr}: ${msg.map(item => JSON.stringify(item)).join(' ')}`)
}
