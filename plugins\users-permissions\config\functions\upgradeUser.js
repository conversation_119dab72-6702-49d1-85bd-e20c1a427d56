// 根据视图配置初始化默认视图
module.exports = async () => {

  // 初始化默认应用
  console.time('Users-Permissions Upgrade User for multi-role...')
  console.time('Users-Permissions Upgrade User for multi-role')
  const userPlugin = strapi.plugins['users-permissions']

  const allUsers = await strapi.query('user', 'users-permissions').find({
    _limit: -1
  })

  for (let user of allUsers) {
    try {
      await userPlugin.services['user'].syncUserBranchConfigs(user)
    } catch (e) {
      console.error('Upgrade User Error: ', e)
    }
  }

  console.timeEnd('Users-Permissions Upgrade User for multi-role')
}
