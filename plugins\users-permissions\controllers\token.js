'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const { CurdRouter } = require('accel-utils')

const curdRouter = new (class extends CurdRouter {
  async findOne (ctx) {
    let token
    if (ctx.params.id.length === 24) {
      token = await super.findOne(ctx)
    }
    if (!token) {
      token = await strapi.query('token', 'users-permissions').findOne({ key: ctx.params.id }, [])
    }
    return token
  }
})('token', { pluginName: 'users-permissions' })

module.exports = {
  ...curdRouter.createHandlers()
}
