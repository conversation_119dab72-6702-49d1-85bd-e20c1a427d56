'use strict'
/**
 * Read the documentation (https://strapi.io/documentation/developer-docs/latest/development/backend-customization.html#core-controllers)
 * to customize this controller
 */
const { CurdRouter } = require('accel-utils')
const { omit } = require('lodash')
const pluginConfig = strapi.config.get('plugins.usersPermissions') || { branchMode: 'normal' }

const branchRouter = new (class extends CurdRouter {
  constructor () {
    super('branch', { pluginName: 'users-permissions' })
  }

  async find (ctx) {
    const branches = await super.find(ctx)
    // 补充租户下前十名的用户
    const iPlugin = strapi.plugins['users-permissions']
    for (const branch of branches) {
      branch.users = await iPlugin.services.user.find({
        pBranch: branch.id,
        _limit: 10,
      })
    }
    return branches
  }

  async fetchDomainBranchInfo (ctx) {
    const iPlugin = strapi.plugins['users-permissions']
    const branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
    return omit(branch, ['inviteToken', 'inviteTokenExpiredAt', 'updatedAt'])
  }

  async fetchCurrentBranch (ctx) {
    let branch
    if (pluginConfig.branchMode === 'domain') {
      const iPlugin = strapi.plugins['users-permissions']
      branch = await iPlugin.services['branch'].getCtxDomainBranch(ctx)
    } else if (ctx.state.user) {
      const branchId = ctx.state.user.pBranch?.id
      branch = await strapi.query('branch', 'users-permissions').findOne({ id: branchId })
    }
    return branch
  }

  async updateCurrentBranch (ctx) {
    const branch = await this.fetchCurrentBranch(ctx)
    if (!branch) throw Error('没有找到租户')
    const data = ctx.request.body
    return await strapi.query('branch', 'users-permissions').update({ id: branch.id }, data)
  }

})

module.exports = {
  ...branchRouter.createHandlers()
}
