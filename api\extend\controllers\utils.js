const { MongoClient, ObjectId } = require('mongodb')
const _ = require('lodash')
async function getQrCode(ctx) {
    const { url, size, type } = ctx.request.query;
    if (!url) {
        return ctx.wrapper.error(400, '参数错误');
    }
    if (type && type !== 'png' && type !== 'jpeg' && type !== 'jpg') {
        return ctx.wrapper.error(400, '参数错误');
    }

    const qr = require('qr-image');
    const code = qr.imageSync(url, { type: type || 'jpeg', size: +size || 150 });

    let contentType = type === 'png' ? 'image/png' : 'image/jpeg'
    ctx.res.setHeader('Content-Type', contentType)
    return ctx.wrapper.send(code);
}

async function dbQuery(ctx) {
    let sourceDbClient;
    try {
        let { db, coll, filter, sort = { _id: -1 }, skip = 0, limit = 10, withObjectIds = ['_id'], projection } = ctx.request.body;
        // TODO 日志存储时，存在 $ 会导致存储失败暂时覆盖request.body值
        ctx.request.body = {};

        if (!db) { db = 'boss-io' }
        if (!coll || !filter) {
            return ctx.wrapper.error('HANDLE_ERROR', 'filter or coll is required')
        }
        // if (skip > 100) { skip = 100 }
        // if (limit > 30) { limit = 30 }

        const dataSource = await strapi.query('data-source').findOne({ name: db });
        sourceDbClient = await MongoClient.connect(dataSource?.url);
        const sourceDb = sourceDbClient.db();
        const result = await sourceDb.collection(coll).find(_parseConditions(filter, withObjectIds), { projection: projection }).sort(sort).skip(skip).limit(limit).toArray();
        return result

        // return ctx.wrapper.send(result)
    } catch (e) {
        console.log(e);
        console.error(e);
        return ctx.badRequest(e)
    } finally {
        if (sourceDbClient) {
            await sourceDbClient.close();
        }
    }
};

function _parseConditions(obj, withObjectIds) {
    const queryObj = {};
    for (let key of _.keys(obj)) {
        queryObj[key] = _recursive(key, obj[key], withObjectIds);
    }
    return queryObj;
}


function _recursive(key, value, withObjectIds, withObjectId = false) {
    if (withObjectIds.includes(key)) {
        withObjectId = true;
    } else {
        withObjectId = false;
    }
    if (_.isArray(value)) {
        const arr = [];
        for (let item of value) {
            arr.push(_recursive(key, item, withObjectIds, withObjectId));
        }
        return arr;
    } else if (typeof value === 'object') {
        const result = {};
        for (let _key of _.keys(value)) {
            result[_key] = _recursive(key, value[_key], withObjectIds, withObjectId);
        }
        return result;
    } else if (typeof value === 'string') {
        if (/\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/.test(value)) {
            return new Date(value);
        }

        if (withObjectId) {
            return new ObjectId(value);
        }

        return value;
    } else if (typeof value === 'number') {
        return value;
    } else if (typeof value === 'boolean') {
        return value;
    }

    return value;
}

async function bookToken(ctx) {
    // 获取 URL 参数
    const { url, token } = ctx.request.query;


    // 设置 Cookie
    ctx.cookies.set('GO_TOKEN', token, {
        httpOnly: true,  // 仅通过 HTTP(S) 传输
        maxAge: 1000 * 60 * 60 * 48,  // Cookie 有效期为 1 小时
        domain: '.iyunxiao.com'  // 设置 Cookie 的域名
    });

    // 重定向到目标 URL
    ctx.redirect(url);
}

module.exports = {
    dbQuery,
    getQrCode,
    bookToken
}
