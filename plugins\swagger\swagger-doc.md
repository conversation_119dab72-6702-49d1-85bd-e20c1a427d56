# Swagger 自定义接口注释规范

本文档定义了项目中自定义接口的 `@swagger` 注释规范，用于自动生成 Swagger API 文档。

## 1. 注释格式说明

### 1.1 基础格式

Swagger 插件通过扫描 JSDoc 中的 `@swagger` 注释来识别自定义接口，格式必须是标准的 **OpenAPI 3.0 YAML 格式**：

```javascript
/**
 * @swagger
 * /api/endpoint:
 *   method:
 *     tags:
 *       - 标签名称
 *     summary: 简短描述
 *     description: 详细描述
 *     parameters:
 *       - in: query
 *         name: param1
 *         required: true
 *         schema:
 *           type: string
 *         description: 参数说明
 *     responses:
 *       200:
 *         description: 成功
 */
async function customEndpoint(ctx) {
  // 实现逻辑
}
```

### 1.2 重要说明

- ✅ **仅为自定义接口添加注释**：标准 CRUD 接口会自动生成文档
- ✅ **必须使用真实的 API 路径**：从 routes.js 中获取准确路径
- ✅ **路径格式**：使用 `{id}` 而不是 `:id`
- ✅ **YAML 格式**：严格遵循 OpenAPI 3.0 YAML 语法

## 2. GET 接口注释模板

### 2.1 基础 GET 接口

```javascript
/**
 * @swagger
 * /subjectActive/getStudentSingleSubjectData:
 *   get:
 *     tags:
 *       - 选科管理
 *     summary: 获取学生单科数据
 *     description: 根据学生ID和活动ID获取学生的选科详细数据
 *     parameters:
 *       - in: query
 *         name: studentId
 *         required: true
 *         schema:
 *           type: string
 *         description: 学生ID
 *         example: "123456"
 *       - in: query
 *         name: activityId
 *         required: true
 *         schema:
 *           type: string
 *         description: 选科活动ID
 *         example: "act_001"
 *     responses:
 *       200:
 *         description: 成功返回学生选科数据
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 studentId:
 *                   type: string
 *                   description: 学生ID
 *                 studentName:
 *                   type: string
 *                   description: 学生姓名
 *                 selectedSubjects:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: 已选科目列表
 *       400:
 *         description: 参数错误
 *       404:
 *         description: 学生或活动不存在
 */
async function getStudentSingleSubjectData(ctx) {
  // 实现逻辑
}
```

### 2.2 带路径参数的 GET 接口

```javascript
/**
 * @swagger
 * /growth-activity/{id}/details:
 *   get:
 *     tags:
 *       - 成长活动
 *     summary: 获取活动详情
 *     description: 根据活动ID获取活动的详细信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 活动ID
 *         example: "act_123"
 *       - in: query
 *         name: includeStats
 *         required: false
 *         schema:
 *           type: boolean
 *           default: false
 *         description: 是否包含统计信息
 *     responses:
 *       200:
 *         description: 成功返回活动详情
 *       404:
 *         description: 活动不存在
 */
```

## 3. POST 接口注释模板

### 3.1 基础 POST 接口

```javascript
/**
 * @swagger
 * /subjectActive/updateStudentSubject:
 *   post:
 *     tags:
 *       - 选科管理
 *     summary: 更新学生选科
 *     description: 更新指定学生在某个选科活动中的科目选择
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - studentId
 *               - activityId
 *               - subjects
 *             properties:
 *               studentId:
 *                 type: string
 *                 description: 学生ID
 *                 example: "123456"
 *               activityId:
 *                 type: string
 *                 description: 选科活动ID
 *                 example: "act_001"
 *               subjects:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 选择的科目列表
 *                 example: ["数学", "物理", "化学"]
 *               reason:
 *                 type: string
 *                 description: 选择理由（可选）
 *                 example: "根据个人兴趣选择"
 *     responses:
 *       200:
 *         description: 更新成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 message:
 *                   type: string
 *                 updatedSubjects:
 *                   type: array
 *                   items:
 *                     type: string
 *       400:
 *         description: 参数错误
 *       403:
 *         description: 选科活动已结束
 *       409:
 *         description: 科目时间冲突
 */
async function updateStudentSubject(ctx) {
  // 实现逻辑
}
```

### 3.2 文件上传接口

```javascript
/**
 * @swagger
 * /api/upload/document:
 *   post:
 *     tags:
 *       - 文件管理
 *     summary: 上传文档
 *     description: 上传学生作业或其他文档文件
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 文件
 *               studentId:
 *                 type: string
 *                 description: 学生ID
 *               category:
 *                 type: string
 *                 enum: ['homework', 'report', 'other']
 *                 description: 文件类别
 *               description:
 *                 type: string
 *                 description: 文件描述
 *     responses:
 *       200:
 *         description: 上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 fileId:
 *                   type: string
 *                 fileName:
 *                   type: string
 *                 fileUrl:
 *                   type: string
 *       400:
 *         description: 文件格式不支持
 *       413:
 *         description: 文件过大
 */
```

## 4. 认证和权限

### 4.1 需要认证的接口（默认）

```javascript
/**
 * @swagger
 * /api/user/profile:
 *   get:
 *     tags:
 *       - 用户管理
 *     summary: 获取用户档案
 *     # 默认需要 JWT 认证，无需额外声明
 *     responses:
 *       200:
 *         description: 成功
 *       401:
 *         description: 未授权
 */
```

### 4.2 公开接口（无需认证）

```javascript
/**
 * @swagger
 * /open/api/school-info:
 *   get:
 *     tags:
 *       - 开放接口
 *     summary: 获取学校信息
 *     description: 获取学校的基本公开信息
 *     security: []    # 明确声明无需认证
 *     responses:
 *       200:
 *         description: 成功
 */
```

### 4.3 Access Key 认证接口

```javascript
/**
 * @swagger
 * /external/api/sync-students:
 *   post:
 *     tags:
 *       - 外部接口
 *     summary: 同步学生数据
 *     description: 外部系统同步学生信息的接口
 *     security:
 *       - accessKey: []    # 使用 access-key 认证
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *     responses:
 *       200:
 *         description: 同步成功
 *       401:
 *         description: Access Key 无效
 */
```

## 5. 数据类型规范

### 5.1 基础数据类型

- `string` - 字符串
- `integer` - 整数
- `number` - 数字（含小数）
- `boolean` - 布尔值
- `array` - 数组
- `object` - 对象

### 5.2 格式化类型

```yaml
# 日期类型
schema:
  type: string
  format: date
  example: "2024-01-01"

# 日期时间类型
schema:
  type: string
  format: date-time
  example: "2024-01-01T10:30:00Z"

# 邮箱类型
schema:
  type: string
  format: email
  example: "<EMAIL>"

# 密码类型
schema:
  type: string
  format: password

# 二进制文件
schema:
  type: string
  format: binary
```

### 5.3 枚举类型

```yaml
schema:
  type: string
  enum: ['active', 'inactive', 'pending']
  description: 状态
  example: "active"
```

### 5.4 数组类型

```yaml
# 字符串数组
schema:
  type: array
  items:
    type: string
  example: ["数学", "物理", "化学"]

# 对象数组
schema:
  type: array
  items:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
```

## 6. 项目特定约定

### 6.1 标签命名规范

根据项目业务模块使用中文标签：

- **基础信息管理**：`基础信息-用户管理`、`基础信息-班级管理`、`基础信息-学校管理`
- **选科管理**：`选科管理`
- **成长发展**：`成长发展`、`教师发展活动`
- **考勤管理**：`考勤管理`
- **系统功能**：`文件上传`、`系统配置`、`权限管理`
- **特殊接口**：`开放接口`、`外部接口`

### 6.2 路径前缀约定

- `/open/*` - 公开接口，自动使用 "开放接口" 标签
- `/external/*` - 外部系统接口，自动使用 "外部接口" 标签
- 其他路径 - 使用业务相关标签

### 6.3 常见错误响应

```yaml
responses:
  400:
    description: 请求参数错误
  401:
    description: 未授权访问
  403:
    description: 权限不足
  404:
    description: 资源不存在
  409:
    description: 业务冲突
  422:
    description: 数据验证失败
  500:
    description: 服务器内部错误
```

## 7. Claude Code 自动生成指南

### 7.1 生成步骤

当使用 Claude Code 为现有接口添加注释时：

1. **分析 routes.js**：确定准确的路径和 HTTP 方法
2. **分析函数代码**：理解参数、业务逻辑和返回值
3. **确定标签**：根据业务模块选择合适的标签
4. **生成完整注释**：包含参数、响应和示例

### 7.2 质量要求

- ✅ **路径准确**：必须与 routes.js 中的配置完全一致
- ✅ **参数完整**：所有必填参数都要标记 `required: true`
- ✅ **类型正确**：使用准确的数据类型和格式
- ✅ **示例有效**：提供真实可用的示例值
- ✅ **响应全面**：包含成功和常见错误响应

### 7.3 常见注意事项

- ❌ 不要为 CRUD 接口添加注释（自动生成）
- ❌ 不要使用错误的路径格式（:id → {id}）
- ❌ 不要遗漏必填参数的 required 标记
- ❌ 不要使用不存在的标签名称
- ✅ 确保 YAML 格式正确（缩进、语法）
- ✅ 确保与实际代码逻辑一致
- ✅ 提供有意义的描述和示例

---

**注意**：本规范适用于项目中所有自定义接口的注释编写。遵循此规范可确保生成的 Swagger 文档质量和一致性，便于 API 使用者理解和调用接口。