# accel-utils

快速开发工具库，为 Strapi 项目提供常用的工具函数和基础类。

## 安装

```bash
npm install accel-utils
```

## 功能概览

- **CRUD 基础类** - 快速创建标准的增删改查功能
- **认证工具** - JWT 生成验证、密码加密等
- **路由助手** - 自动生成标准路由配置
- **数据处理** - 请求数据解析、字段处理等
- **Strapi 工具** - 扩展的 Strapi 实用函数

## API 文档

### CRUD 基础类

#### CurdRouter

快速创建 CRUD 控制器的基础类。

```javascript
const { CurdRouter } = require('accel-utils');

// 基础用法
const router = new CurdRouter('post', 'api::blog.post');

// 带配置的用法
const router = new CurdRouter('post', 'api::blog.post', {
  baseConfig: {
    searchFields: ['title', 'content'],
    sort: { createdAt: -1 },
    populate: ['author', 'category']
  }
});

// 导出控制器方法
module.exports = {
  ...router.createHandlers(),
};
```

**配置选项：**
- `searchFields` - 搜索字段列表
- `sort` - 默认排序规则
- `populate` - 默认关联查询
- `pluginName` - 插件名称（用于插件内的模型）

**生成的方法：**
- `find` - 查询列表（支持分页、搜索、过滤）
- `findOne` - 查询单条记录
- `create` - 创建记录
- `update` - 更新记录
- `delete` - 删除记录
- `deleteMany` - 批量删除
- `importData` - Excel 数据导入
- `exportData` - Excel 数据导出

#### UserCurdRouter

用户相关的 CRUD 路由，自动处理用户权限。

```javascript
const { UserCurdRouter } = require('accel-utils');

const router = new UserCurdRouter('order', 'api::shop.order');
// 自动添加用户过滤：只能查看自己的订单
```

#### BranchCurdRouter

租户相关的 CRUD 路由，自动处理租户隔离。

```javascript
const { BranchCurdRouter } = require('accel-utils');

const router = new BranchCurdRouter('product', 'api::shop.product');
// 自动添加租户过滤：只能查看当前租户的产品
```

#### PublicCurdRouter

公开的 CRUD 路由，不需要认证。

```javascript
const { PublicCurdRouter } = require('accel-utils');

const router = new PublicCurdRouter('article', 'api::blog.article');
// 无需认证即可访问
```

### 认证工具

#### JWT 相关

```javascript
const { jwtGen, jwtVerify } = require('accel-utils');

// 生成 JWT
const token = jwtGen({ userId: '123', role: 'admin' });

// 验证 JWT
try {
  const payload = jwtVerify(token);
  console.log(payload); // { userId: '123', role: 'admin' }
} catch (error) {
  console.error('Token 验证失败');
}
```

#### 密码处理

```javascript
const { passwordBcrypt, passwordCompare } = require('accel-utils');

// 加密密码
const hashedPassword = await passwordBcrypt('myPassword123');

// 验证密码
const isValid = await passwordCompare('myPassword123', hashedPassword);
console.log(isValid); // true
```

#### Token 生成

```javascript
const { createRandomToken, sha1 } = require('accel-utils');

// 生成随机 Token
const token = createRandomToken(); // 32位随机字符串

// SHA1 加密
const hash = sha1('myString');
```

### 路由助手

```javascript
const { createDefaultRoutes } = require('accel-utils');

// 生成标准 CRUD 路由配置
const routes = createDefaultRoutes({
  basePath: '/posts',
  controller: 'post'
});

// 生成的路由：
// GET    /posts         - post.find
// GET    /posts/:id     - post.findOne
// POST   /posts         - post.create
// PUT    /posts/:id     - post.update
// DELETE /posts/:id     - post.delete
// DELETE /posts         - post.deleteMany
// POST   /posts/import  - post.importData
// GET    /posts/export  - post.exportData
```

### 权限助手

```javascript
const { createDefaultPermissions } = require('accel-utils');

// 生成标准权限配置
const permissions = createDefaultPermissions('post');

// 生成的权限：
// post.find
// post.findOne
// post.create
// post.update
// post.delete
// post.deleteMany
// post.importData
// post.exportData
```

### 数据处理工具

#### 解析请求数据

```javascript
const { parseCtxData, parseCtxUserAndBranch } = require('accel-utils');

// 解析请求上下文数据
async function myController(ctx) {
  // 解析请求参数（合并 query、params、body）
  const data = parseCtxData(ctx);
  
  // 解析用户和租户信息
  const { user, branch } = parseCtxUserAndBranch(ctx);
  
  // 使用解析后的数据
  console.log('用户ID:', user.id);
  console.log('租户ID:', branch.id);
}
```

#### 字段处理

```javascript
const { dataFieldProcess } = require('accel-utils');

// 处理数据字段（过滤、转换等）
const processedData = dataFieldProcess(rawData, {
  allowedFields: ['name', 'email'],
  requiredFields: ['name'],
  defaults: { status: 'active' }
});
```

### Strapi 扩展工具

accel-utils 导出了所有 Strapi 内置的工具函数：

```javascript
const {
  buildQuery,
  sanitizeEntity,
  parseMultipartData,
  // ... 更多 Strapi 工具
} = require('accel-utils');
```

## 使用示例

### 创建博客 API

```javascript
// api/blog/controllers/post.js
const { CurdRouter } = require('accel-utils');

const router = new CurdRouter('post', 'api::blog.post', {
  baseConfig: {
    searchFields: ['title', 'content', 'author.name'],
    sort: { publishedAt: -1 },
    populate: ['author', 'category', 'tags']
  }
});

module.exports = {
  ...router.createHandlers(),
  
  // 自定义方法
  async publish(ctx) {
    const { id } = ctx.params;
    const post = await strapi.services.post.update(
      { id },
      { publishedAt: new Date() }
    );
    return ctx.send(post);
  }
};
```

### 创建用户订单 API

```javascript
// api/shop/controllers/order.js
const { UserCurdRouter } = require('accel-utils');

const router = new UserCurdRouter('order', 'api::shop.order');

module.exports = {
  ...router.createHandlers(),
  
  // 用户只能看到自己的订单
  // UserCurdRouter 会自动添加用户过滤
};
```

### 自定义认证中间件

```javascript
const { jwtVerify } = require('accel-utils');

module.exports = async (ctx, next) => {
  const token = ctx.request.header.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return ctx.unauthorized('缺少认证令牌');
  }
  
  try {
    const payload = jwtVerify(token);
    ctx.state.user = payload;
    await next();
  } catch (error) {
    return ctx.unauthorized('无效的认证令牌');
  }
};
```

## 版本历史

### v1.0
- 独立为 accel-utils 模块
- 迁入 prototypes 模块

### v0.1.1
- curdRouter 支持获取插件的模型和服务
- config 增加 pluginName 属性

### v0.1.0
- 初始版本

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT