{"fieldsMap": {"学校名称": "2200000200025415", "学校ID": "2200000200025414", "账号类型": "2200000200025416", "学制": "2200000491305625", "阅卷签约标签": "2200000486890562", "断续换账号": "2200000461137559", "原账号ID": "2200000461137560", "冲突项目数": "2200000489722265", "有效项目数": "2200000439342341", "省份": "2200000443142091", "地市": "2200000443142092", "区县": "2200000439343046", "230801至今考试次数（手更）": "2200000469235068", "最近一次考试日期（手更）": "2200000469235069", "考试数据更新日期": "2200000487259220", "阅卷版本": "2200000469235064", "阅卷到期日期": "2200000469235065", "分析版本": "2200000469235066", "分析到期日期": "2200000469235067", "阅卷SaaS权益展示字段": "2200000486988014", "账号到期分类": "2200000483224268", "授权经销商": "2200000459065770", "授权到期日期": "2200000459076052", "授权类型": "2200000486856104", "商认领人": "2200000459156176", "获得授权的项目": "2200000486856115", "直营流转": "2200000486875314", "直营项目": "2200000486875315", "服务经理": "2200000491434986", "备注": "2200000484635281", "管理关注": "2200000491622737", "会员校标签": "2200000491103215", "直营直服标签": "2200000488290040", "直营直服运营活动": "2200000488848436", "快速链接-S": "2200000487033834", "快速链接-L": "2200000487044093", "mark": "2200000487954134", "mark2": "2200000489350553", "远程立项任务": "2200000494264779", "远程项目经理": "2200000494264781", "远程项目标签": "2200000494264782", "远程沟通目的": "2200000494264783", "远程项目编号": "2200000511297004", "远程直营经理": "2200000511058578", "授权经销商（24年初）": "2200000483215275", "关联项目数": "2200000439342340", "（测试）有效项目数": "2200000485071243", "账号自动续约日期": "2200000483515096", "#备用#": "2200000482389986", "直营建档数": "2200000469232796", "直营有效立项数": "2200000504290125", "原到期日期": "2200000511055202", "续约判断": "2200000511055203", "省份-省名": "1107001101000000", "地市-市名": "1108001101000000", "区县-区/县名": "1106001101000000", "授权经销商-合作摘要": "1110001233000000", "授权经销商-简称": "1110001230000000", "授权经销商-公司名称": "1110001103000000", "授权经销商-25年认领单元": "1110001242000000", "授权经销商-经营单元": "1110001242001101", "授权经销商-25年负责人": "1110001239000000", "授权经销商-员工姓名": "1110001239001101", "授权经销商-25年回捞商": "1110001244000000", "商认领人-员工姓名": "1112001101000000", "商认领人-所属阿米巴单元": "1112001116000000", "获得授权的项目-立项编号": "1133001155000000", "获得授权的项目-项目属性": "1133001342000000", "获得授权的项目-项目经理": "1133001102000000", "获得授权的项目-经销商": "1133001112000000", "获得授权的项目-项目状态": "1133001201000000", "直营项目-立项编号": "1135011155000000", "直营项目-项目属性": "1135011342000000", "直营项目-项目类型": "1135011329000000", "直营项目-项目标签": "1135011348000000", "直营项目-沟通目的": "1135011349000000", "直营项目-项目名称": "1135011101000000", "直营项目-业绩归属单元": "1135011237000000", "直营项目-项目经理": "1135011102000000", "直营项目-企信ID": "1135011102001115", "直营项目-合作产品-backup": "1135011295000000", "直营项目-立项审核": "1135011151000000", "直营项目-项目状态": "1135011201000000", "直营项目-审批备注": "1135011152000000", "直营项目-项目阶段": "1135011330000000", "直营项目-产出达成率": "1135011352000000", "服务经理-员工姓名": "1152001101000000", "服务经理-所属阿米巴单元": "1152001116000000", "服务经理-单元负责人": "1152001116001114", "服务经理-部门负责人": "1152001114000000", "远程项目经理-所属阿米巴单元": "1157001116000000", "授权经销商（24年初）-公司名称": "1126001103000000", "授权经销商（24年初）-B资源认领单元": "1126001217000000", "授权经销商（24年初）-单元负责人": "1126001217001114", "授权经销商（24年初）-团队成员": "1126001217001115", "授权经销商（24年初）-B资源认领人": "1126001218000000", "授权经销商（24年初）-员工姓名": "1126001218001101"}, "table_id": "2100000021897176", "name": "合作学校", "alias": "", "space_id": "4000000003570865", "created_on": "2022-06-24 17:46:42", "fields": [{"field_id": "2200000200025415", "name": "学校名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000200025414", "name": "学校ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": true, "description": "", "config": {}}, {"field_id": "2200000200025416", "name": "账号类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "学校"}, {"id": "3", "name": "统考平台"}, {"id": "4", "name": "联盟平台"}, {"id": "7", "name": "集团校平台"}, {"id": "9", "name": "中心校平台"}, {"id": "5", "name": "单次联考"}, {"id": "2", "name": "教育局"}, {"id": "6", "name": "培训机构"}, {"id": "8", "name": "其他"}]}}, {"field_id": "2200000491305625", "name": "学制", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "小学"}, {"id": "2", "name": "初中"}, {"id": "3", "name": "高中"}, {"id": "4", "name": "九年一贯制"}, {"id": "5", "name": "完全中学"}, {"id": "6", "name": "十二年一贯制"}, {"id": "7", "name": "幼儿园-高中一贯制"}, {"id": "8", "name": "职业学校"}]}}, {"field_id": "2200000486890562", "name": "阅卷签约标签", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "新校 = 从未签约过的学校\n流失校 = 曾经有过付费，超过1年未付费\n付费校 = 最近一年内处于付费状态", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "体验校"}, {"id": "3", "name": "付费校"}, {"id": "5", "name": "应续校"}, {"id": "2", "name": "流失校"}]}}, {"field_id": "2200000461137559", "name": "断续换账号", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}, {"id": "2", "name": "否"}]}}, {"field_id": "2200000461137560", "name": "原账号ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000489722265", "name": "冲突项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000439342341", "name": "有效项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000443142091", "name": "省份", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524704", "space_id": "4000000003570865"}}, {"field_id": "2200000443142092", "name": "地市", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524705", "space_id": "4000000003570865"}}, {"field_id": "2200000439343046", "name": "区县", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018524706", "space_id": "4000000003570865"}}, {"field_id": "2200000469235068", "name": "230801至今考试次数（手更）", "alias": "", "field_type": "numeric", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000469235069", "name": "最近一次考试日期（手更）", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000487259220", "name": "考试数据更新日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000469235064", "name": "阅卷版本", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "体验版"}, {"id": "2", "name": "专业版"}, {"id": "3", "name": "旗舰版"}, {"id": "4", "name": "单次联考"}, {"id": "5", "name": "A版"}, {"id": "6", "name": "B版"}, {"id": "7", "name": "C版"}, {"id": "8", "name": "D版"}, {"id": "9", "name": "S版"}, {"id": "10", "name": "地市版"}, {"id": "11", "name": "区县版"}, {"id": "12", "name": "片区版"}, {"id": "13", "name": "小型"}, {"id": "14", "name": "中型"}, {"id": "15", "name": "大型"}, {"id": "16", "name": "小学版"}, {"id": "17", "name": "小初版"}, {"id": "18", "name": "单次联考-年度"}, {"id": "19", "name": "专业A版"}, {"id": "20", "name": "专业B版"}]}}, {"field_id": "2200000469235065", "name": "阅卷到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000469235066", "name": "分析版本", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "基础版"}, {"id": "2", "name": "专业版"}, {"id": "3", "name": "旗舰版"}, {"id": "4", "name": "统考地市版"}, {"id": "5", "name": "统考区县版"}, {"id": "6", "name": "统考片区版"}, {"id": "7", "name": "联盟版"}, {"id": "8", "name": "集团校版"}, {"id": "9", "name": "联考版"}, {"id": "10", "name": "外部联考版"}, {"id": "11", "name": "专业A版"}, {"id": "12", "name": "专业B版"}]}}, {"field_id": "2200000469235067", "name": "分析到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486988014", "name": "阅卷SaaS权益展示字段", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000483224268", "name": "账号到期分类", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "临期-1个月"}, {"id": "2", "name": "临期-2个月"}, {"id": "3", "name": "临期-3个月"}]}}, {"field_id": "2200000459065770", "name": "授权经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000459076052", "name": "授权到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000486856104", "name": "授权类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "5", "name": "开拓授权"}, {"id": "2", "name": "运营授权"}, {"id": "3", "name": "项目合作"}, {"id": "1", "name": "直营合作"}, {"id": "6", "name": "旧授权"}, {"id": "7", "name": "区域独家"}]}}, {"field_id": "2200000459156176", "name": "商认领人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000486856115", "name": "获得授权的项目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000486875314", "name": "直营流转", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "未建档"}, {"id": "2", "name": "已建档"}, {"id": "3", "name": "履约中"}, {"id": "4", "name": "无需建档"}, {"id": "5", "name": "暂缓跟进"}]}}, {"field_id": "2200000486875315", "name": "直营项目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000491434986", "name": "服务经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000484635281", "name": "备注", "alias": "", "field_type": "textarea", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000491622737", "name": "管理关注", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059983414", "space_id": "4000000003570865"}}, {"field_id": "2200000491103215", "name": "会员校标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059983414", "space_id": "4000000003570865"}}, {"field_id": "2200000488290040", "name": "直营直服标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000059983414", "space_id": "4000000003570865"}}, {"field_id": "2200000488848436", "name": "直营直服运营活动", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 1, "table_id": "2100000059983025", "space_id": "4000000003570865"}}, {"field_id": "2200000487033834", "name": "快速链接-S", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000487044093", "name": "快速链接-L", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000487954134", "name": "mark", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000489350553", "name": "mark2", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000494264779", "name": "远程立项任务", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "远程待立项"}, {"id": "2", "name": "远程立项成功"}, {"id": "3", "name": "远程立项失败"}]}}, {"field_id": "2200000494264781", "name": "远程项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000494264782", "name": "远程项目标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059982481", "space_id": "4000000003570865"}}, {"field_id": "2200000494264783", "name": "远程沟通目的", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060066489", "space_id": "4000000003570865"}}, {"field_id": "2200000511297004", "name": "远程项目编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000511058578", "name": "远程直营经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "2200000483215275", "name": "授权经销商（24年初）", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000439342340", "name": "关联项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000485071243", "name": "（测试）有效项目数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000483515096", "name": "账号自动续约日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000482389986", "name": "#备用#", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000469232796", "name": "直营建档数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000504290125", "name": "直营有效立项数", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "2200000511055202", "name": "原到期日期", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000511055203", "name": "续约判断", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "", "is_percent": 0}}, {"field_id": "1107001101000000", "name": "省名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000443142091}, "required": true, "description": "", "config": {}}, {"field_id": "1108001101000000", "name": "市名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000443142092}, "required": true, "description": "", "config": {}}, {"field_id": "1106001101000000", "name": "区/县名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000439343046}, "required": true, "description": "", "config": {}}, {"field_id": "1110001233000000", "name": "合作摘要", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {}}, {"field_id": "1110001230000000", "name": "简称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {}}, {"field_id": "1110001103000000", "name": "公司名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459065770}, "required": true, "description": "", "config": {}}, {"field_id": "1110001242000000", "name": "25年认领单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1110001242001101", "name": "经营单元", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {}}, {"field_id": "1110001239000000", "name": "25年负责人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1110001239001101", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459065770}, "required": true, "description": "", "config": {}}, {"field_id": "1110001244000000", "name": "25年回捞商", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000459065770}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "是"}]}}, {"field_id": "1112001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000459156176}, "required": true, "description": "", "config": {}}, {"field_id": "1112001116000000", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000459156176}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1133001155000000", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486856115}, "required": false, "description": "", "config": {}}, {"field_id": "1133001342000000", "name": "项目属性", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486856115}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059740536", "space_id": "4000000003570865"}}, {"field_id": "1133001102000000", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486856115}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1133001112000000", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486856115}, "required": true, "description": "如果搜不到经销商，请通过BOSS提交《经销商资质信息审核》流程", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "1133001201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486856115}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "1135011155000000", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {}}, {"field_id": "1135011342000000", "name": "项目属性", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059740536", "space_id": "4000000003570865"}}, {"field_id": "1135011329000000", "name": "项目类型", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692209", "space_id": "4000000003570865"}}, {"field_id": "1135011348000000", "name": "项目标签", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000059982481", "space_id": "4000000003570865"}}, {"field_id": "1135011349000000", "name": "沟通目的", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000060066489", "space_id": "4000000003570865"}}, {"field_id": "1135011101000000", "name": "项目名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {}}, {"field_id": "1135011237000000", "name": "业绩归属单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1135011102000000", "name": "项目经理", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1135011102001115", "name": "企信ID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {}}, {"field_id": "1135011295000000", "name": "合作产品-backup", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {"is_multi": 1, "is_tile": 1, "options": [{"id": "2", "name": "阅卷"}, {"id": "9", "name": "排课"}, {"id": "13", "name": "云平台"}, {"id": "15", "name": "阅卷家"}, {"id": "16", "name": "阅卷云"}, {"id": "4", "name": "360会员"}, {"id": "5", "name": "错题本"}, {"id": "6", "name": "统考"}, {"id": "8", "name": "联考"}, {"id": "10", "name": "作业系统"}, {"id": "12", "name": "分析"}, {"id": "11", "name": "其他"}, {"id": "14", "name": "云校智学"}]}}, {"field_id": "1135011151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "直营待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1135011201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}, {"field_id": "1135011152000000", "name": "审批备注", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {}}, {"field_id": "1135011330000000", "name": "项目阶段", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000486875315}, "required": true, "description": "", "config": {"is_multi": 0, "table_id": "2100000059692208", "space_id": "4000000003570865"}}, {"field_id": "1135011352000000", "name": "产出达成率", "alias": "", "field_type": "calculation", "data_type": "numeric", "from_relation_field": {"field_id": 2200000486875315}, "required": false, "description": "", "config": {"precision": 0, "unit_prefix": "", "unit_surfix": "%", "is_percent": 1}}, {"field_id": "1152001101000000", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000491434986}, "required": true, "description": "", "config": {}}, {"field_id": "1152001116000000", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000491434986}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1152001116001114", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000491434986}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1152001114000000", "name": "部门负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000491434986}, "required": false, "description": "", "config": {"is_multi": 0}}, {"field_id": "1157001116000000", "name": "所属阿米巴单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000494264781}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1126001103000000", "name": "公司名称", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000483215275}, "required": true, "description": "", "config": {}}, {"field_id": "1126001217000000", "name": "B资源认领单元", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000483215275}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000055636966", "space_id": "4000000003570865"}}, {"field_id": "1126001217001114", "name": "单元负责人", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000483215275}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1126001217001115", "name": "团队成员", "alias": "", "field_type": "user", "data_type": "user", "from_relation_field": {"field_id": 2200000483215275}, "required": false, "description": "", "config": {"is_multi": 1}}, {"field_id": "1126001218000000", "name": "B资源认领人", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000483215275}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018513284", "space_id": "4000000003570865"}}, {"field_id": "1126001218001101", "name": "员工姓名", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000483215275}, "required": true, "description": "", "config": {}}]}