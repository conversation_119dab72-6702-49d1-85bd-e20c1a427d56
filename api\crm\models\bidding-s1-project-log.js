module.exports = {
  kind: 'collectionType',
  collectionName: strapi.config.server.mingdaoConfig.biddingS1ProjectLogId,
  connection: 'mingdao',
  info: {
    name: 'BiddingS1ProjectLog',
    label: '招标项目操作记录',
    description: '',
  },
  options: {},
  pluginOptions: {},
  attributes: {
    // 操作类型
    type: {
      ref: '678fba8c9e13a09bfffe92e6'
    },
    // 操作人
    operator: {
      ref: '678fbb129e13a09bfffe92f9'
    },
    // 招标项目
    s1Project: {
      ref: '678fbaf39e13a09bfffe92f3'
    },
    // 操作信息
    info: {
      ref: '678fba549e13a09bfffe92dd'
    },
  }
}
