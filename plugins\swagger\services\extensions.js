/**
 * Swagger 扩展配置加载器
 * 支持从 YAML 文件加载扩展配置，使文档维护更加方便
 * 支持外部扩展配置
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

/**
 * 加载 YAML 配置文件
 */
function loadYamlConfig() {
  // 支持多个配置文件位置
  const yamlPaths = [
    // 插件内部的配置文件
    path.join(__dirname, 'extensions.yaml'),
    // 项目根目录的配置文件（优先级更高）
    path.join(process.cwd(), 'config', 'swagger-extensions.yaml')
  ];
  
  let mergedConfig = {};
  
  for (const yamlPath of yamlPaths) {
    try {
      if (fs.existsSync(yamlPath)) {
        const fileContents = fs.readFileSync(yamlPath, 'utf8');
        const config = yaml.load(fileContents);
        console.log(`已加载 Swagger 扩展配置文件: ${yamlPath}`);
        
        // 合并配置，后加载的配置优先级更高
        mergedConfig = deepMerge(mergedConfig, config);
      }
    } catch (error) {
      console.error(`加载 ${yamlPath} 失败:`, error);
    }
  }
  
  // 如果没有加载到任何配置，使用默认配置
  if (Object.keys(mergedConfig).length === 0) {
    console.log('使用内置的 Swagger 扩展配置');
    return getDefaultConfig();
  }
  
  return mergedConfig;
}

/**
 * 深度合并两个对象
 * @param {Object} target 目标对象
 * @param {Object} source 源对象
 * @returns {Object} 合并后的对象
 */
function deepMerge(target, source) {
  const output = { ...target };
  
  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        if (target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])) {
          output[key] = deepMerge(target[key], source[key]);
        } else {
          output[key] = source[key];
        }
      } else {
        output[key] = source[key];
      }
    }
  }
  
  return output;
}

/**
 * 默认配置（作为备份）
 */
function getDefaultConfig() {
  return {};
}

/**
 * 加载外部 JS 扩展配置
 * @returns {Object} 外部配置对象
 */
function loadExternalExtensions() {
  const externalConfigPath = path.join(process.cwd(), 'config', 'swagger-extensions.js');
  
  try {
    if (fs.existsSync(externalConfigPath)) {
      // 清除 require 缓存以支持热重载
      delete require.cache[externalConfigPath];
      const externalConfig = require(externalConfigPath);
      console.log(`已加载外部 Swagger 扩展配置: ${externalConfigPath}`);
      return externalConfig;
    }
  } catch (error) {
    console.error(`加载外部扩展配置失败:`, error);
  }
  
  return {};
}

/**
 * 获取最终的扩展配置
 * @param {string|Array} filterModules - 要包含的模块，如果为空则包含全部
 * @returns {Object} 合并后的扩展配置
 */
function getExtensions(filterModules = null) {
  // 首先加载 YAML 配置
  const yamlConfig = loadYamlConfig();
  
  // 然后加载 JS 配置
  const jsConfig = loadExternalExtensions();
  
  // 合并配置，JS 配置优先级更高
  let mergedConfig = deepMerge(yamlConfig, jsConfig);
  
  // 如果指定了模块过滤，只保留指定的模块
  if (filterModules !== null) {
    const targetModules = Array.isArray(filterModules) ? filterModules : [filterModules];
    const filteredConfig = {};
    
    // 如果是空数组，表示不要任何扩展模块
    if (targetModules.length === 0) {
      console.log('扩展模块过滤: 不加载任何扩展模块');
      return {};
    }
    
    console.log(`扩展模块过滤: 只加载 ${targetModules.join(', ')}`);
    
    // 保留 components 等全局配置
    if (mergedConfig.components) {
      filteredConfig.components = mergedConfig.components;
    }
    
    // 只保留指定的模块
    targetModules.forEach(moduleName => {
      if (mergedConfig[moduleName]) {
        filteredConfig[moduleName] = mergedConfig[moduleName];
      }
    });
    
    return filteredConfig;
  }
  
  return mergedConfig;
}

// 导出配置
module.exports = getExtensions();

// 同时导出工具函数供其他模块使用
module.exports.getExtensions = getExtensions;
module.exports.loadYamlConfig = loadYamlConfig;
module.exports.loadExternalExtensions = loadExternalExtensions;