const { CurdRouter } = require('accel-utils')
const curdRouter = new CurdRouter('question-collection')
const _ = require('lodash')

async function updateQuestion(ctx) {
    const { id } = ctx.params;
    const { user, content, status, result, operator } = ctx.request.body;

    let updateData = {};
    if (user) updateData.user = user;
    if (content) updateData.content = content;
    if (status) updateData.status = status;
    if (result) updateData.result = result;
    if (operator) updateData.operator = operator;
    if (_.isEmpty(updateData)) return ctx.wrapper.error(400, '参数错误');

    const updateResult = await strapi.query('question-collection').update({ id: id }, updateData)

    return ctx.wrapper.succ(updateResult);
}

async function deleteQuestion(ctx) {
    const { id } = ctx.params;
    const updateResult = await strapi.query('question-collection').delete({ id: id })
    return ctx.wrapper.succ(updateResult);
}

module.exports = {
    updateQuestion,
    deleteQuestion,
    ...curdRouter.createHandlers(),
}