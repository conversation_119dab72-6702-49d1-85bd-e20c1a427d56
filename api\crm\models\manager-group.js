module.exports = {
  "kind": "collectionType",
  "collectionName": strapi.config.server.mingdaoConfig.managerGroupId,
  "connection": "mingdao",
  "info": {
    name: 'ManagerGroup',
    label: '企信用户组',
    description: ''
  },
  "options": {},
  "pluginOptions": {},
  "attributes": {
    name: {
      // label: '名称',
      "ref": "672d7816444eb62fc450ecd4"
    },
    unit: {
      // label: '单元',
      "ref": "6750436aba60f67ec34c1d5e"
    },
    type: {
      // label: '类型',
      "ref": "672d7816444eb62fc450ecd6"
    },
    leader: {
      // label: '组长',
      "ref": "672d7816444eb62fc450ecd7"
    },
    members: {
      // label: '组员',
      "ref": "672d7816444eb62fc450ecd8"
    },
    manager: {
      // label: '阿米巴',
      "ref": "672d7816444eb62fc450ecd9"
    },
  }
}

// module.exports = {
//   collectionName: 'member-group',
//   info: {
//     name: 'ManagerGroup',
//     label: '企信用户组',
//     description: '企信用户组'
//   },
//   options: {
//     draftAndPublish: false,
//     timestamps: true
//   },
//   pluginOptions: {},
//   attributes: {
//     name: {
//       label: '名称',
//       type: 'string',
//       required: true,
//     },
//     unit: {
//       label: '单元',
//       type: 'string',
//     },
//     type: {
//       label: '类型',
//       type: 'string',
//       required: true,
//       options: [
//         { label: '直营小组', value: 'sales', },
//         { label: '运营小组', value: 'service', },
//       ],
//     },
//     leader: {
//       label: '组长',
//       model: 'user',
//       plugin: 'users-permissions',
//       mainField: 'username',
//       meta: {
//         query: {
//           ['roles.type']: "{{ $formData.type ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-admin', 'service-group-leader', 'service-group-member'] :  ($formData.type === 'sales' ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member'] : ['service-admin', 'service-group-leader', 'service-group-member']) }}"
//         }
//       }
//     },
//     members: {
//       label: '组员',
//       collection: 'manager',
//       via: 'managerGroup',
//       mainField: 'username',
//       dominant: true,
//       meta: {
//         query: {
//           ['roles.type']: "{{ $formData.type ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member', 'service-admin', 'service-group-leader', 'service-group-member'] :  ($formData.type === 'sales' ? ['sales-admin', 'sales-manager', 'sales-observer', 'sales-group-leader', 'sales-group-member'] : ['service-admin', 'service-group-leader', 'service-group-member']) }}"
//         }
//       }
//     },
//     manager: {
//       label: '阿米巴',
//       model: 'user',
//       plugin: 'users-permissions',
//       mainField: 'username',
//       meta: {
//         query: {
//           ['roles.type']: ['sales-manager']
//         }
//       }
//     },
//   }
// }
