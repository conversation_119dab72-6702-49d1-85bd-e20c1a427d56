module.exports = {
  kind: "collectionType",
  collectionName: "67493d7aba60f67ec34bafe9",
  connection: "mingdao",
  info: {
    name: "InformationModel",
    label: "信息模型"
  },
  options: {},
  pluginOptions: {},
  attributes: {
     // 组件类型
    type:{
      ref:"674da190ba60f67ec34bdf8d"
    },
    // 内容别名
    alias:{
      ref:"67493d7aba60f67ec34bafea"
    },
    // 内容描述
    description:{
      ref:"67493d7aba60f67ec34bafeb"
    },
    // 顺序编号
    sort:{
      ref:"674da190ba60f67ec34bdf8e"
    },
    // 子
    children:{
      ref:"674da190ba60f67ec34bdf90"
    },
    // 上一级模块信息
    parent:{
      ref:"674da190ba60f67ec34bdf8f"
    },
    // 附件
    attachments:{
      ref:"67493d7aba60f67ec34bafec"
    },
    // 页面链接
    pageLink:{
      ref:"675f94139e13a09bfff14314"
    }
  }
};
