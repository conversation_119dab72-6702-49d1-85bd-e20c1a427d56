'use strict';

module.exports = {
    collectionName: 'huoban-queue',
    info: {
        name: 'HuobanQueue',
        label: '伙伴更新队列',
        description: '伙伴更新队列'
    },
    options: {
        draftAndPublish: false,
        timestamps: true,
        indexes: [
            { keys: { key: 1 }, options: { unique: true } },
        ],
    },
    pluginOptions: {},
    attributes: {
        type: {
            label: '类型',
            type: 'string',
            editable: false,
            required: true,
            options: [
                {
                    value: 'schoolWebsite',
                    label: '学校建站'
                },
                {
                    value: 'appUsage',
                    label: '学校权益'
                },
                {
                    value: 'agentAuth',
                    label: '经销商授权'
                },
                {
                    value: 'schoolTag',
                    label: '学校标签'
                },
                {
                    value: 'salesFollow',
                    label: '直营跟进'
                },
            ]
        },
        key: {
            label: '唯一键',
            editable: false,
            "unique": true,
            type: 'string',
        },
        customerId: {
            label: '客户id',
            type: 'string',
            editable: false,
            required: true,
        },
        "customer": {
            "label": "学校",
            "editable": false,
            "mainField": "showName",
            "model": "boss-school"
        },
        relatedId: {
            label: '关联id',
            type: 'string',
            editable: false,
            // required: true,
        },
        status: {
            label: '状态',
            type: 'string',
            editable: false,
            required: true,
            default: 'prepared',
            options: [
                {
                    value: 'prepared',
                    label: '待更新'
                },
                // {
                //     value: 'doing',
                //     label: '进行中'
                // },
                {
                    value: 'success',
                    label: '成功'
                },
                {
                    value: 'failed',
                    label: '失败'
                },
                {
                    value: 'cancelled',
                    label: '已取消'
                }
            ]
        },
    }
}