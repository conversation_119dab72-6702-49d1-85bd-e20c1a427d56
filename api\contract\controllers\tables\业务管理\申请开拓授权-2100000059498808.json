{"fieldsMap": {"项目": "2200000485065942", "学校/教育局": "2200000485065943", "经销商": "2200000485559106", "产品": "2200000485559108", "授权类型": "2200000485559107", "授权审批状态": "2200000485559109", "授权开始时间": "2200000485559110", "授权结束时间": "2200000485559111", "CRMID": "2200000485563104", "同步CRM状态": "2200000485589581", "同步失败原因": "2200000486791488", "项目-立项编号": "1113001155000000", "项目-经销商": "1113001112000000", "项目-立项审核": "1113001151000000", "项目-项目状态": "1113001201000000"}, "table_id": "2100000059498808", "name": "申请开拓授权", "alias": "", "space_id": "4000000003570865", "created_on": "2024-10-11 18:14:59", "fields": [{"field_id": "2200000485065942", "name": "项目", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018480321", "space_id": "4000000003570865"}}, {"field_id": "2200000485065943", "name": "学校/教育局", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000021897176", "space_id": "4000000003570865"}}, {"field_id": "2200000485559106", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "2200000485559108", "name": "产品", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "阅卷"}, {"id": "2", "name": "360会员"}, {"id": "3", "name": "错题本"}]}}, {"field_id": "2200000485559107", "name": "授权类型", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "开拓授权"}, {"id": "2", "name": "运营授权"}, {"id": "3", "name": "直营合作"}, {"id": "4", "name": "项目合作"}]}}, {"field_id": "2200000485559109", "name": "授权审批状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "待审批"}, {"id": "2", "name": "授权成功"}, {"id": "3", "name": "待定"}, {"id": "4", "name": "授权失败"}]}}, {"field_id": "2200000485559110", "name": "授权开始时间", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000485559111", "name": "授权结束时间", "alias": "", "field_type": "date", "data_type": "date", "from_relation_field": {}, "required": false, "description": "", "config": {"precision": "date"}}, {"field_id": "2200000485563104", "name": "CRMID", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "2200000485589581", "name": "同步CRM状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 0, "options": [{"id": "1", "name": "待同步"}, {"id": "2", "name": "已同步"}, {"id": "3", "name": "同步失败"}]}}, {"field_id": "2200000486791488", "name": "同步失败原因", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {}, "required": false, "description": "", "config": {}}, {"field_id": "1113001155000000", "name": "立项编号", "alias": "", "field_type": "input", "data_type": "text", "from_relation_field": {"field_id": 2200000485065942}, "required": false, "description": "", "config": {}}, {"field_id": "1113001112000000", "name": "经销商", "alias": "", "field_type": "relation", "data_type": "relation", "from_relation_field": {"field_id": 2200000485065942}, "required": false, "description": "如果搜不到经销商，请通过BOSS提交《经销商资质信息审核》流程", "config": {"is_multi": 0, "table_id": "2100000018475755", "space_id": "4000000003570865"}}, {"field_id": "1113001151000000", "name": "立项审核", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000485065942}, "required": false, "description": "", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "4", "name": "新提交"}, {"id": "6", "name": "待审批"}, {"id": "8", "name": "直营待审批"}, {"id": "3", "name": "待定"}, {"id": "1", "name": "立项成功"}, {"id": "2", "name": "立项失败"}, {"id": "5", "name": "立项过期"}, {"id": "7", "name": "立项延期申请"}]}}, {"field_id": "1113001201000000", "name": "项目状态", "alias": "", "field_type": "category", "data_type": "category", "from_relation_field": {"field_id": 2200000485065942}, "required": false, "description": "已出具合同 && 首期款回款，则认定为成功。如果【30天超时】未成功，则项目关闭", "config": {"is_multi": 0, "is_tile": 1, "options": [{"id": "1", "name": "进行中"}, {"id": "7", "name": "合同制作"}, {"id": "8", "name": "合同制作异常"}, {"id": "9", "name": "已签约未回款"}, {"id": "5", "name": "分期回款中"}, {"id": "6", "name": "回款完毕"}, {"id": "2", "name": "完成"}, {"id": "4", "name": "关闭/正常终结"}, {"id": "3", "name": "中止/项目放弃"}]}}]}