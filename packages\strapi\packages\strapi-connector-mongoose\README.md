# strapi-connector-mongoose

[![npm version](https://img.shields.io/npm/v/strapi-connector-mongoose.svg)](https://www.npmjs.org/package/strapi-connector-mongoose)
[![npm downloads](https://img.shields.io/npm/dm/strapi-connector-mongoose.svg)](https://www.npmjs.org/package/strapi-connector-mongoose)
[![npm dependencies](https://david-dm.org/strapi/strapi-connector-mongoose.svg)](https://david-dm.org/strapi/strapi-connector-mongoose)
[![Build status](https://travis-ci.org/strapi/strapi-connector-mongoose.svg?branch=master)](https://travis-ci.org/strapi/strapi-connector-mongoose)
[![Slack status](https://slack.strapi.io/badge.svg)](https://slack.strapi.io)

This built-in connector allows you to use the [Mongoose ORM](http://mongoosejs.com/).

[Mongoose ORM](http://mongoosejs.com/) provides a straight-forward, schema-based solution to model your application data. It includes built-in type casting, validation, query building, business logic connectors and more, out of the box.

## Resources

- [License](LICENSE)

## Links

- [Strapi website](https://strapi.io/)
- [Strapi community on Slack](https://slack.strapi.io)
- [Strapi news on Twitter](https://twitter.com/strapijs)
